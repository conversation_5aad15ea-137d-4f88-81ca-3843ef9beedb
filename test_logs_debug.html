<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 Teste do Sistema de Logs</title>
    <style>
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            margin: 0; 
            padding: 20px; 
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%); 
            color: white; 
            min-height: 100vh;
        }
        .container { 
            max-width: 1000px; 
            margin: 0 auto; 
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(99, 102, 241, 0.3);
        }
        .info-box {
            background: #2a2a2a;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            font-size: 14px;
        }
        .status.success {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid #4CAF50;
            color: #4CAF50;
        }
        .status.error {
            background: rgba(244, 67, 54, 0.2);
            border: 1px solid #f44336;
            color: #f44336;
        }
        .status.warning {
            background: rgba(255, 193, 7, 0.2);
            border: 1px solid #ffc107;
            color: #ffc107;
        }
        button {
            padding: 12px 20px;
            background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
            margin: 5px;
        }
        button:hover {
            background: linear-gradient(135deg, #5b21b6 0%, #7c3aed 100%);
            transform: translateY(-2px);
        }
        .controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        .controls button {
            flex: 1;
            min-width: 200px;
        }
        .code-block {
            background: #1a1a1a;
            border: 1px solid #444;
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            overflow-x: auto;
        }
        .highlight {
            color: #6366f1;
            font-weight: bold;
        }
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        @media (max-width: 768px) {
            .grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 Teste do Sistema de Logs de Debug</h1>
            <p>Verificando se os logs estão sendo controlados corretamente</p>
        </div>
        
        <div class="controls">
            <button onclick="testarLogsJavaScript()">🌐 Testar Logs JavaScript</button>
            <button onclick="testarRequisicoes()">📡 Testar Requisições</button>
            <button onclick="verificarConsole()">👁️ Verificar Console</button>
            <button onclick="limparConsole()">🧹 Limpar Console</button>
        </div>
        
        <div id="status"></div>
        
        <div class="grid">
            <div class="info-box">
                <h3>🎛️ Configuração Atual</h3>
                <div id="config-status" class="code-block">
                    Verificando configuração...
                </div>
            </div>
            
            <div class="info-box">
                <h3>📊 Resultado dos Testes</h3>
                <div id="test-results" class="code-block">
                    Execute os testes para ver os resultados...
                </div>
            </div>
        </div>
        
        <div class="info-box">
            <h3>📋 Instruções</h3>
            <div class="code-block">
                <strong>Como controlar os logs:</strong><br><br>
                
                <span class="highlight">1. Para DESATIVAR logs (recomendado):</span><br>
                • Edite <code>config_debug.py</code> → <code>DEBUG_MODE = False</code><br>
                • Edite <code>game/static/js/main.js</code> → <code>const DEBUG_MODE = false;</code><br>
                • Reinicie o servidor<br><br>
                
                <span class="highlight">2. Para ATIVAR logs (desenvolvimento):</span><br>
                • Edite <code>config_debug.py</code> → <code>DEBUG_MODE = True</code><br>
                • Edite <code>game/static/js/main.js</code> → <code>const DEBUG_MODE = true;</code><br>
                • Reinicie o servidor<br><br>
                
                <span class="highlight">3. Verificar resultado:</span><br>
                • Abra F12 (Console do navegador)<br>
                • Execute os testes acima<br>
                • Com DEBUG_MODE = false, o console deve ficar limpo<br>
                • Com DEBUG_MODE = true, logs detalhados aparecerão
            </div>
        </div>
        
        <div class="info-box">
            <h3>🎯 Benefícios</h3>
            <div class="code-block">
                <span class="highlight">✅ Com DEBUG_MODE = false:</span><br>
                • Console F12 limpo (sem logs de debug)<br>
                • Terminal do servidor limpo<br>
                • Melhor performance<br>
                • Experiência profissional<br><br>
                
                <span class="highlight">🔍 Com DEBUG_MODE = true:</span><br>
                • Logs detalhados para desenvolvimento<br>
                • Debug completo de problemas<br>
                • Monitoramento de performance<br>
                • Troubleshooting avançado
            </div>
        </div>
    </div>

    <script>
        // Verificar configuração atual
        function verificarConfiguracao() {
            const debugMode = typeof DEBUG_MODE !== 'undefined' ? DEBUG_MODE : false;
            const configElement = document.getElementById('config-status');
            
            configElement.innerHTML = `
                <strong>JavaScript DEBUG_MODE:</strong> <span class="highlight">${debugMode}</span><br>
                <strong>Funções de debug disponíveis:</strong><br>
                • debugLog: ${typeof debugLog !== 'undefined' ? '✅ Disponível' : '❌ Não disponível'}<br>
                • debugError: ${typeof debugError !== 'undefined' ? '✅ Disponível' : '❌ Não disponível'}<br>
                • debugWarn: ${typeof debugWarn !== 'undefined' ? '✅ Disponível' : '❌ Não disponível'}<br><br>
                
                <strong>Status:</strong> ${debugMode ? 
                    '<span style="color: #ffc107;">⚠️ Logs ATIVOS (desenvolvimento)</span>' : 
                    '<span style="color: #4CAF50;">✅ Logs OCULTOS (produção)</span>'
                }
            `;
        }
        
        function showStatus(message, type = 'success') {
            const status = document.getElementById('status');
            status.innerHTML = `<div class="status ${type}">${message}</div>`;
            
            if (type === 'success') {
                setTimeout(() => {
                    status.innerHTML = '';
                }, 3000);
            }
        }
        
        function testarLogsJavaScript() {
            const results = document.getElementById('test-results');
            let output = '<strong>🌐 Teste de Logs JavaScript:</strong><br><br>';
            
            // Testar se as funções existem
            if (typeof debugLog !== 'undefined') {
                output += '• debugLog: ✅ Função existe<br>';
                debugLog('🧪 Teste de debugLog - esta mensagem só aparece se DEBUG_MODE = true');
            } else {
                output += '• debugLog: ❌ Função não encontrada<br>';
            }
            
            if (typeof debugError !== 'undefined') {
                output += '• debugError: ✅ Função existe<br>';
                debugError('🧪 Teste de debugError - esta mensagem só aparece se DEBUG_MODE = true');
            } else {
                output += '• debugError: ❌ Função não encontrada<br>';
            }
            
            if (typeof debugWarn !== 'undefined') {
                output += '• debugWarn: ✅ Função existe<br>';
                debugWarn('🧪 Teste de debugWarn - esta mensagem só aparece se DEBUG_MODE = true');
            } else {
                output += '• debugWarn: ❌ Função não encontrada<br>';
            }
            
            // Testar console.log normal (sempre aparece)
            console.log('🧪 Teste de console.log normal - esta mensagem SEMPRE aparece');
            output += '• console.log normal: ✅ Executado (sempre visível)<br><br>';
            
            const debugMode = typeof DEBUG_MODE !== 'undefined' ? DEBUG_MODE : false;
            output += `<strong>Resultado:</strong><br>`;
            output += `DEBUG_MODE = ${debugMode}<br>`;
            output += debugMode ? 
                'Logs de debug DEVEM aparecer no console F12' : 
                'Logs de debug NÃO devem aparecer no console F12';
            
            results.innerHTML = output;
            showStatus('✅ Teste de logs JavaScript executado! Verifique o console F12');
        }
        
        async function testarRequisicoes() {
            const results = document.getElementById('test-results');
            let output = '<strong>📡 Teste de Requisições:</strong><br><br>';
            
            try {
                // Fazer algumas requisições para testar logs de network
                output += '• Fazendo requisição de teste...<br>';
                
                const response = await fetch('/api/auth/session-test');
                const data = await response.json();
                
                output += `• Resposta recebida: ${response.status}<br>`;
                output += `• Dados: ${data.sucesso ? '✅ Sucesso' : '❌ Erro'}<br><br>`;
                
                output += '<strong>Verificação:</strong><br>';
                output += '• Abra F12 → Aba Network<br>';
                output += '• Verifique se há logs de requisições<br>';
                output += '• Com DEBUG_MODE = false, logs devem ser mínimos<br>';
                output += '• Com DEBUG_MODE = true, logs devem ser detalhados';
                
                results.innerHTML = output;
                showStatus('✅ Teste de requisições executado! Verifique a aba Network no F12');
                
            } catch (error) {
                output += `• ❌ Erro na requisição: ${error.message}<br>`;
                results.innerHTML = output;
                showStatus('❌ Erro no teste de requisições', 'error');
            }
        }
        
        function verificarConsole() {
            const results = document.getElementById('test-results');
            
            // Contar mensagens no console (aproximação)
            const originalLog = console.log;
            const originalError = console.error;
            const originalWarn = console.warn;
            
            let logCount = 0;
            let errorCount = 0;
            let warnCount = 0;
            
            // Interceptar temporariamente
            console.log = function(...args) {
                logCount++;
                originalLog.apply(console, args);
            };
            
            console.error = function(...args) {
                errorCount++;
                originalError.apply(console, args);
            };
            
            console.warn = function(...args) {
                warnCount++;
                originalWarn.apply(console, args);
            };
            
            // Executar alguns logs de teste
            console.log('🧪 Log de teste 1');
            console.error('🧪 Error de teste 1');
            console.warn('🧪 Warning de teste 1');
            
            if (typeof debugLog !== 'undefined') {
                debugLog('🧪 Debug log de teste');
            }
            
            // Restaurar console original
            setTimeout(() => {
                console.log = originalLog;
                console.error = originalError;
                console.warn = originalWarn;
                
                const debugMode = typeof DEBUG_MODE !== 'undefined' ? DEBUG_MODE : false;
                
                let output = '<strong>👁️ Verificação do Console:</strong><br><br>';
                output += `• Logs normais detectados: ${logCount}<br>`;
                output += `• Errors detectados: ${errorCount}<br>`;
                output += `• Warnings detectados: ${warnCount}<br><br>`;
                output += `<strong>DEBUG_MODE atual:</strong> ${debugMode}<br><br>`;
                output += '<strong>Instruções:</strong><br>';
                output += '• Abra F12 → Console<br>';
                output += '• Verifique a quantidade de mensagens<br>';
                output += debugMode ? 
                    '• Com DEBUG_MODE = true, deve haver muitas mensagens' :
                    '• Com DEBUG_MODE = false, deve haver poucas mensagens';
                
                results.innerHTML = output;
                showStatus('✅ Verificação do console concluída!');
            }, 100);
        }
        
        function limparConsole() {
            console.clear();
            showStatus('🧹 Console limpo!');
        }
        
        // Inicialização
        window.onload = function() {
            verificarConfiguracao();
            console.log('🌐 Página de teste de logs carregada');
            console.log('📋 Para testar o sistema de logs, use os botões acima');
        };
    </script>
</body>
</html>
