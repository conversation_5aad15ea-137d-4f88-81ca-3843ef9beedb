<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste Ranking Frontend</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #1a1a1a; color: white; }
        .container { max-width: 800px; margin: 0 auto; }
        .ranking-item { background: #2a2a2a; margin: 10px 0; padding: 15px; border-radius: 8px; }
        .position { font-weight: bold; color: #4CAF50; }
        .nick { font-size: 18px; font-weight: bold; }
        .points { color: #2196F3; font-size: 20px; font-weight: bold; }
        .group { color: #9C27B0; }
        .error { color: #f44336; }
        .success { color: #4CAF50; }
        button { background: #2196F3; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin: 10px 5px; }
        button:hover { background: #1976D2; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏆 Teste do Ranking Individual de Deface</h1>
        
        <div>
            <button onclick="testLogin()">1. Fazer Login</button>
            <button onclick="testRankingAPI()">2. Testar API</button>
            <button onclick="testRankingDisplay()">3. Testar Exibição</button>
        </div>
        
        <div id="status"></div>
        <div id="ranking-container"></div>
    </div>

    <script>
        let session = null;
        
        async function testLogin() {
            const status = document.getElementById('status');
            status.innerHTML = '<p>🔄 Fazendo login...</p>';
            
            try {
                const response = await fetch('/api/auth/auto-login');
                const data = await response.json();
                
                if (response.ok) {
                    status.innerHTML = '<p class="success">✅ Login realizado com sucesso!</p>';
                    session = true;
                } else {
                    status.innerHTML = `<p class="error">❌ Erro no login: ${data.mensagem}</p>`;
                }
            } catch (error) {
                status.innerHTML = `<p class="error">❌ Erro: ${error.message}</p>`;
            }
        }
        
        async function testRankingAPI() {
            const status = document.getElementById('status');
            status.innerHTML = '<p>🔄 Testando API de ranking...</p>';
            
            try {
                const response = await fetch('/api/ranking/deface-individual');
                const data = await response.json();
                
                if (response.ok && data.sucesso) {
                    status.innerHTML = `<p class="success">✅ API funcionando! ${data.total_jogadores} jogadores encontrados</p>`;
                    console.log('Dados da API:', data);
                    
                    // Mostrar estrutura dos dados
                    const estrutura = document.createElement('div');
                    estrutura.innerHTML = `
                        <h3>📊 Estrutura dos Dados:</h3>
                        <pre style="background: #333; padding: 10px; border-radius: 5px; overflow-x: auto;">
${JSON.stringify(data, null, 2)}
                        </pre>
                    `;
                    status.appendChild(estrutura);
                } else {
                    status.innerHTML = `<p class="error">❌ Erro na API: ${data.mensagem || 'Erro desconhecido'}</p>`;
                }
            } catch (error) {
                status.innerHTML = `<p class="error">❌ Erro: ${error.message}</p>`;
            }
        }
        
        async function testRankingDisplay() {
            const status = document.getElementById('status');
            const container = document.getElementById('ranking-container');
            
            status.innerHTML = '<p>🔄 Testando exibição do ranking...</p>';
            container.innerHTML = '';
            
            try {
                const response = await fetch('/api/ranking/deface-individual');
                const data = await response.json();
                
                if (response.ok && data.sucesso) {
                    status.innerHTML = '<p class="success">✅ Exibindo ranking...</p>';
                    
                    if (data.ranking && data.ranking.length > 0) {
                        container.innerHTML = `
                            <h3>🏆 Ranking Individual de Deface</h3>
                            ${data.ranking.map((jogador, index) => `
                                <div class="ranking-item">
                                    <div class="position">#${index + 1}</div>
                                    <div class="nick">${jogador.nick || 'Sem nick'}</div>
                                    <div class="points">${jogador.deface_points || 0} pontos</div>
                                    <div class="group">Grupo: ${jogador.grupo_nome || 'Sem grupo'}</div>
                                </div>
                            `).join('')}
                        `;
                    } else {
                        container.innerHTML = '<p>❌ Nenhum jogador encontrado no ranking</p>';
                    }
                } else {
                    status.innerHTML = `<p class="error">❌ Erro: ${data.mensagem || 'Erro desconhecido'}</p>`;
                }
            } catch (error) {
                status.innerHTML = `<p class="error">❌ Erro: ${error.message}</p>`;
            }
        }
        
        // Auto-executar teste completo
        window.onload = async function() {
            await testLogin();
            setTimeout(testRankingAPI, 1000);
            setTimeout(testRankingDisplay, 2000);
        };
    </script>
</body>
</html>
