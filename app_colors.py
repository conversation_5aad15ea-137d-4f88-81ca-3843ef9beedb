#!/usr/bin/env python3
"""
Versão simplificada do SHACK com foco nas APIs de cores de nickname
"""

import os
import sys
from flask import Flask, render_template, request, jsonify, session, redirect, url_for
from dotenv import load_dotenv

# Carregar variáveis de ambiente
load_dotenv()

# Adicionar o diretório atual ao path
sys.path.append('.')

try:
    from database.supabase_client import supabase_client
    from game import new_models as models
    from game.routes import main
    print("✅ Importações básicas carregadas")
except Exception as e:
    print(f"❌ Erro nas importações: {e}")
    sys.exit(1)

# Criar aplicação Flask
app = Flask(__name__, 
           template_folder='game/templates',
           static_folder='game/static')

app.secret_key = os.getenv('SECRET_KEY', 'dev-secret-key-change-in-production')

# Registrar blueprints
app.register_blueprint(main)

# Configurações básicas
app.config['SESSION_COOKIE_SECURE'] = False  # Para desenvolvimento
app.config['SESSION_COOKIE_HTTPONLY'] = True
app.config['SESSION_COOKIE_SAMESITE'] = 'Lax'

@app.route('/')
def index():
    """Página inicial"""
    return render_template('index.html')

@app.route('/health')
def health_check():
    """Health check simples"""
    return jsonify({
        "status": "ok",
        "message": "Servidor SHACK (versão cores) funcionando",
        "database": "connected" if supabase_client.is_connected() else "disconnected"
    })

# Middleware para CORS (se necessário)
@app.after_request
def after_request(response):
    response.headers.add('Access-Control-Allow-Origin', '*')
    response.headers.add('Access-Control-Allow-Headers', 'Content-Type,Authorization')
    response.headers.add('Access-Control-Allow-Methods', 'GET,PUT,POST,DELETE,OPTIONS')
    return response

# Handler de erro
@app.errorhandler(Exception)
def handle_exception(e):
    print(f"Erro não tratado: {e}")
    return jsonify({"erro": "Erro interno do servidor"}), 500

if __name__ == "__main__":
    print("🎨 Iniciando SHACK - Versão Cores de Nickname")
    print("=" * 50)
    
    try:
        # Verificar conexão com banco
        if supabase_client.is_connected():
            print("✅ Conexão com banco de dados: OK")
        else:
            print("⚠️ Conexão com banco de dados: FALHOU")
        
        print("🌐 Servidor iniciando em: http://localhost:5000")
        print("🎨 APIs de cores disponíveis:")
        print("   - GET  /api/configuracoes/cores-nickname")
        print("   - POST /api/configuracoes/cores-nickname/aplicar")
        print("   - POST /api/configuracoes/cores-nickname/remover")
        print("=" * 50)
        
        # Iniciar servidor sem threads de background por enquanto
        app.run(
            debug=False,  # Desabilitar debug para evitar problemas
            host="0.0.0.0",
            port=5000,
            threaded=True,
            use_reloader=False  # Desabilitar reloader
        )
        
    except Exception as e:
        print(f"❌ Erro ao iniciar servidor: {e}")
        sys.exit(1)
