#!/usr/bin/env python3
"""
Teste simples do sistema de chat
"""

import sys
import requests
sys.path.append('.')

def test_chat_api():
    """Testa as APIs do chat"""
    # print de debug removido
    
    try:
        # Criar sessão
        session = requests.Session()
        
        # 1. Fazer login
        print("1. 🔑 Fazendo login...")
        login_response = session.get("http://localhost:5000/api/auth/auto-login")
        if login_response.status_code != 200:
            # print de debug removido
            return False
        
        # print de debug removido
        
        # 2. Testar envio de mensagem
        print("2. 📤 Testando envio via API...")
        send_response = session.post("http://localhost:5000/api/chat/enviar", 
                                   json={'texto': 'Olá! Esta é uma mensagem de teste do novo sistema de chat!'})
        
        print(f"Status: {send_response.status_code}")
        
        if send_response.status_code == 200:
            send_data = send_response.json()
            print(f"Resposta: {send_data}")
            
            if send_data.get('sucesso'):
                # print de debug removido
            else:
                # print de debug removido}")
        else:
            # print de debug removido
            print(f"Resposta: {send_response.text}")
        
        # 3. Testar busca de mensagens
        print("3. 📥 Testando busca via API...")
        get_response = session.get("http://localhost:5000/api/chat/mensagens?limite=5")
        
        print(f"Status busca: {get_response.status_code}")
        
        if get_response.status_code == 200:
            get_data = get_response.json()
            print(f"Dados: {get_data}")
            
            if get_data.get('sucesso'):
                total = get_data.get('total', 0)
                # print de debug removido
                
                if total > 0:
                    print("📝 Mensagens encontradas:")
                    for msg in get_data.get('mensagens', []):
                        print(f"   - {msg.get('nick')}: {msg.get('message')}")
            else:
                # print de debug removido}")
        else:
            # print de debug removido
            print(f"Resposta: {get_response.text}")
        
        # 4. Testar anti-spam
        print("4. 🚫 Testando anti-spam...")
        spam_response = session.post("http://localhost:5000/api/chat/enviar", 
                                   json={'texto': 'Olá! Esta é uma mensagem de teste do novo sistema de chat!'})
        
        if spam_response.status_code == 200:
            spam_data = spam_response.json()
            print(f"Resposta anti-spam: {spam_data}")
            
            if not spam_data.get('sucesso'):
                # print de debug removido
            else:
                # print de debug removido
        
        return True
        
    except Exception as e:
        # print de debug removido
        import traceback
        print(traceback.format_exc())
        return False

if __name__ == "__main__":
    print("🚀 Teste simples do sistema de chat...")
    test_chat_api()
    # print de debug removido
