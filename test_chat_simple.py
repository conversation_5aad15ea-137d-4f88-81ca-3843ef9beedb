#!/usr/bin/env python3
"""
Teste simples do sistema de chat
"""

import sys
import requests
sys.path.append('.')

def test_chat_api():
    """Testa as APIs do chat"""
    print("🌐 Testando APIs do chat...")
    
    try:
        # Criar sessão
        session = requests.Session()
        
        # 1. Fazer login
        print("1. 🔑 Fazendo login...")
        login_response = session.get("http://localhost:5000/api/auth/auto-login")
        if login_response.status_code != 200:
            print(f"❌ Erro no login: {login_response.status_code}")
            return False
        
        print("✅ Login OK")
        
        # 2. Testar envio de mensagem
        print("2. 📤 Testando envio via API...")
        send_response = session.post("http://localhost:5000/api/chat/enviar", 
                                   json={'texto': 'Olá! Esta é uma mensagem de teste do novo sistema de chat!'})
        
        print(f"Status: {send_response.status_code}")
        
        if send_response.status_code == 200:
            send_data = send_response.json()
            print(f"Resposta: {send_data}")
            
            if send_data.get('sucesso'):
                print("✅ Envio via API funcionando!")
            else:
                print(f"⚠️ API retornou erro: {send_data.get('mensagem')}")
        else:
            print(f"❌ Erro HTTP no envio: {send_response.status_code}")
            print(f"Resposta: {send_response.text}")
        
        # 3. Testar busca de mensagens
        print("3. 📥 Testando busca via API...")
        get_response = session.get("http://localhost:5000/api/chat/mensagens?limite=5")
        
        print(f"Status busca: {get_response.status_code}")
        
        if get_response.status_code == 200:
            get_data = get_response.json()
            print(f"Dados: {get_data}")
            
            if get_data.get('sucesso'):
                total = get_data.get('total', 0)
                print(f"✅ Busca via API funcionando! {total} mensagens")
                
                if total > 0:
                    print("📝 Mensagens encontradas:")
                    for msg in get_data.get('mensagens', []):
                        print(f"   - {msg.get('nick')}: {msg.get('message')}")
            else:
                print(f"⚠️ API de busca retornou erro: {get_data.get('mensagem')}")
        else:
            print(f"❌ Erro HTTP na busca: {get_response.status_code}")
            print(f"Resposta: {get_response.text}")
        
        # 4. Testar anti-spam
        print("4. 🚫 Testando anti-spam...")
        spam_response = session.post("http://localhost:5000/api/chat/enviar", 
                                   json={'texto': 'Olá! Esta é uma mensagem de teste do novo sistema de chat!'})
        
        if spam_response.status_code == 200:
            spam_data = spam_response.json()
            print(f"Resposta anti-spam: {spam_data}")
            
            if not spam_data.get('sucesso'):
                print("✅ Anti-spam funcionando! Mensagem duplicada bloqueada")
            else:
                print("⚠️ Anti-spam pode não estar funcionando")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro nos testes de API: {e}")
        import traceback
        print(traceback.format_exc())
        return False

if __name__ == "__main__":
    print("🚀 Teste simples do sistema de chat...")
    test_chat_api()
    print("✅ Teste concluído!")
