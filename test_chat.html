<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🗨️ Teste do Sistema de Chat</title>
    <style>
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            margin: 0; 
            padding: 20px; 
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%); 
            color: white; 
            min-height: 100vh;
        }
        .container { 
            max-width: 800px; 
            margin: 0 auto; 
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(76, 175, 80, 0.3);
        }
        .chat-container {
            background: #2a2a2a;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        }
        .messages {
            height: 400px;
            overflow-y: auto;
            border: 1px solid #444;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            background: #1a1a1a;
        }
        .message {
            margin: 10px 0;
            padding: 10px;
            background: #333;
            border-radius: 8px;
            border-left: 4px solid #4CAF50;
        }
        .message-header {
            font-size: 12px;
            color: #888;
            margin-bottom: 5px;
        }
        .message-text {
            font-size: 14px;
        }
        .input-container {
            display: flex;
            gap: 10px;
        }
        input[type="text"] {
            flex: 1;
            padding: 12px;
            border: 1px solid #444;
            border-radius: 8px;
            background: #333;
            color: white;
            font-size: 14px;
        }
        input[type="text"]:focus {
            outline: none;
            border-color: #4CAF50;
        }
        button {
            padding: 12px 20px;
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        button:hover {
            background: linear-gradient(135deg, #45a049 0%, #3d8b40 100%);
            transform: translateY(-2px);
        }
        button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 8px;
            font-size: 14px;
        }
        .status.success {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid #4CAF50;
            color: #4CAF50;
        }
        .status.error {
            background: rgba(244, 67, 54, 0.2);
            border: 1px solid #f44336;
            color: #f44336;
        }
        .status.warning {
            background: rgba(255, 193, 7, 0.2);
            border: 1px solid #ffc107;
            color: #ffc107;
        }
        .controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        .controls button {
            flex: 1;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🗨️ Sistema de Chat - Teste Completo</h1>
            <p>Teste do novo sistema com anti-spam e auto-limpeza</p>
        </div>
        
        <div class="controls">
            <button onclick="fazerLogin()">🔑 Login</button>
            <button onclick="carregarMensagens()">📥 Carregar Mensagens</button>
            <button onclick="limparChat()">🧹 Limpar</button>
        </div>
        
        <div id="status"></div>
        
        <div class="chat-container">
            <div id="messages" class="messages">
                <div style="text-align: center; color: #888; padding: 20px;">
                    Clique em "Login" e depois "Carregar Mensagens" para começar
                </div>
            </div>
            
            <div class="input-container">
                <input type="text" id="messageInput" placeholder="Digite sua mensagem..." maxlength="200" disabled>
                <button id="sendBtn" onclick="enviarMensagem()" disabled>Enviar</button>
            </div>
        </div>
    </div>

    <script>
        let loggedIn = false;
        let updateInterval = null;
        
        function showStatus(message, type = 'success') {
            const status = document.getElementById('status');
            status.innerHTML = `<div class="status ${type}">${message}</div>`;
            
            if (type === 'success') {
                setTimeout(() => {
                    status.innerHTML = '';
                }, 3000);
            }
        }
        
        async function fazerLogin() {
            try {
                showStatus('🔄 Fazendo login...', 'warning');
                
                const response = await fetch('/api/auth/auto-login');
                const data = await response.json();
                
                if (response.ok && data.sucesso) {
                    loggedIn = true;
                    document.getElementById('messageInput').disabled = false;
                    document.getElementById('sendBtn').disabled = false;
                    showStatus('✅ Login realizado com sucesso!');
                    
                    // Carregar mensagens automaticamente
                    setTimeout(carregarMensagens, 500);
                } else {
                    showStatus('❌ Erro no login: ' + (data.mensagem || 'Erro desconhecido'), 'error');
                }
            } catch (error) {
                showStatus('❌ Erro de conexão no login', 'error');
                console.error('Erro no login:', error);
            }
        }
        
        async function carregarMensagens() {
            if (!loggedIn) {
                showStatus('⚠️ Faça login primeiro', 'warning');
                return;
            }
            
            try {
                const response = await fetch('/api/chat/mensagens?limite=20');
                const data = await response.json();
                
                if (response.ok && data.sucesso) {
                    const messagesDiv = document.getElementById('messages');
                    
                    if (data.mensagens && data.mensagens.length > 0) {
                        const mensagensHTML = data.mensagens.reverse().map(msg => `
                            <div class="message">
                                <div class="message-header">
                                    ${msg.nick} • ${msg.time_ago || 'agora'}
                                </div>
                                <div class="message-text">${escapeHtml(msg.message)}</div>
                            </div>
                        `).join('');
                        
                        messagesDiv.innerHTML = mensagensHTML;
                        messagesDiv.scrollTop = messagesDiv.scrollHeight;
                        
                        showStatus(`✅ ${data.mensagens.length} mensagens carregadas`);
                    } else {
                        messagesDiv.innerHTML = `
                            <div style="text-align: center; color: #888; padding: 20px;">
                                💬 Nenhuma mensagem ainda. Seja o primeiro a enviar!
                            </div>
                        `;
                        showStatus('📭 Nenhuma mensagem encontrada');
                    }
                } else {
                    showStatus('❌ Erro ao carregar mensagens: ' + (data.mensagem || 'Erro desconhecido'), 'error');
                }
            } catch (error) {
                showStatus('❌ Erro de conexão ao carregar mensagens', 'error');
                console.error('Erro ao carregar mensagens:', error);
            }
        }
        
        async function enviarMensagem() {
            if (!loggedIn) {
                showStatus('⚠️ Faça login primeiro', 'warning');
                return;
            }
            
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            
            if (!message) {
                showStatus('⚠️ Digite uma mensagem', 'warning');
                return;
            }
            
            try {
                const sendBtn = document.getElementById('sendBtn');
                sendBtn.disabled = true;
                sendBtn.textContent = '🔄 Enviando...';
                
                const response = await fetch('/api/chat/enviar', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ texto: message })
                });
                
                const data = await response.json();
                
                if (response.ok && data.sucesso) {
                    input.value = '';
                    showStatus('✅ Mensagem enviada!');
                    
                    // Recarregar mensagens após um pequeno delay
                    setTimeout(carregarMensagens, 200);
                } else {
                    showStatus('❌ ' + (data.mensagem || 'Erro ao enviar mensagem'), 'error');
                }
            } catch (error) {
                showStatus('❌ Erro de conexão ao enviar mensagem', 'error');
                console.error('Erro ao enviar:', error);
            } finally {
                const sendBtn = document.getElementById('sendBtn');
                sendBtn.disabled = false;
                sendBtn.textContent = 'Enviar';
            }
        }
        
        function limparChat() {
            document.getElementById('messages').innerHTML = `
                <div style="text-align: center; color: #888; padding: 20px;">
                    Chat limpo. Clique em "Carregar Mensagens" para recarregar.
                </div>
            `;
            showStatus('🧹 Chat limpo');
        }
        
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }
        
        // Event listeners
        document.getElementById('messageInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                enviarMensagem();
            }
        });
        
        // Auto-atualização a cada 3 segundos quando logado
        setInterval(() => {
            if (loggedIn) {
                carregarMensagens();
            }
        }, 3000);
        
        // Inicialização
        window.onload = function() {
            console.log('🌐 Página de teste do chat carregada');
        };
    </script>
</body>
</html>
