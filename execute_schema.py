#!/usr/bin/env python3
"""
Script para executar o schema da tabela torneio_pontuacoes
"""

import sys
import os
sys.path.append('.')

try:
    from database.supabase_client import SupabaseClient
    
    def execute_schema():
        # print de debug removido
        
        # Conectar ao Supabase
        supabase_client = SupabaseClient()
        
        if not supabase_client.is_connected():
            # print de debug removido
            return False
        
        # Ler o arquivo SQL
        try:
            with open('database/torneio_pontuacoes_schema.sql', 'r', encoding='utf-8') as f:
                sql_content = f.read()
            
            print("📄 Schema carregado com sucesso")
            
            # Executar o SQL
            try:
                # Dividir em comandos individuais
                commands = sql_content.split(';')
                
                for i, command in enumerate(commands):
                    command = command.strip()
                    if command and not command.startswith('--'):
                        try:
                            # print de debug removido
                            result = supabase_client.client.rpc('exec_sql', {'sql_query': command}).execute()
                            # print de debug removido
                        except Exception as cmd_error:
                            # Alguns comandos podem falhar se já existirem, isso é normal
                            # print de debug removido: {cmd_error}")
                
                # print de debug removido
                return True
                
            except Exception as e:
                # print de debug removido
                return False
                
        except Exception as e:
            # print de debug removido
            return False
    
    def test_table_structure():
        print("\n🔍 Testando estrutura da tabela...")
        
        supabase_client = SupabaseClient()
        
        try:
            # Testar se a tabela existe e tem dados
            response = supabase_client.client.table('torneio_pontuacoes').select('*').limit(5).execute()
            
            if response.data:
                # print de debug removido} registros (amostra)")
                
                # Mostrar estrutura dos dados
                if response.data:
                    primeiro_registro = response.data[0]
                    # print de debug removido
                    for campo, valor in primeiro_registro.items():
                        print(f"  - {campo}: {type(valor).__name__} = {valor}")
            else:
                # print de debug removido
                
            return True
            
        except Exception as e:
            # print de debug removido
            return False
    
    def add_test_data():
        print("\n🧪 Adicionando dados de teste...")
        
        supabase_client = SupabaseClient()
        
        try:
            # Verificar se já existem dados
            response = supabase_client.client.table('torneio_pontuacoes').select('*').eq('tipo_torneio', 'deface').execute()
            
            if response.data and len(response.data) > 0:
                # print de debug removido} registros de deface")
                return True
            
            # Adicionar alguns dados de teste
            test_data = [
                {
                    'jogador_uid': 'test-uid-1',
                    'tipo_torneio': 'deface',
                    'pontos_individuais': 150,
                    'group_points': 50,
                    'nick': 'TestPlayer001',
                    'grupo_id': 'test-group-1',
                    'grupo_nome': 'Test Group Alpha'
                },
                {
                    'jogador_uid': 'test-uid-2',
                    'tipo_torneio': 'deface',
                    'pontos_individuais': 90,
                    'group_points': 30,
                    'nick': 'TestPlayer002',
                    'grupo_id': 'test-group-2',
                    'grupo_nome': 'Test Group Beta'
                }
            ]
            
            for data in test_data:
                try:
                    supabase_client.client.table('torneio_pontuacoes').insert(data).execute()
                    # print de debug removido
                except Exception as e:
                    # print de debug removido
            
            return True
            
        except Exception as e:
            # print de debug removido
            return False
    
    if __name__ == "__main__":
        print("🚀 Iniciando configuração da tabela torneio_pontuacoes...")
        
        # 1. Executar schema
        if execute_schema():
            print("\n" + "="*50)
            
            # 2. Testar estrutura
            if test_table_structure():
                print("\n" + "="*50)
                
                # 3. Adicionar dados de teste se necessário
                add_test_data()
                
                print("\n✅ Configuração concluída com sucesso!")
            else:
                print("\n❌ Falha ao testar estrutura")
        else:
            print("\n❌ Falha ao executar schema")

except ImportError as e:
    # print de debug removido
    # print de debug removido
except Exception as e:
    # print de debug removido
