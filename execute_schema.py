#!/usr/bin/env python3
"""
Script para executar o schema da tabela torneio_pontuacoes
"""

import sys
import os
sys.path.append('.')

try:
    from database.supabase_client import SupabaseClient
    
    def execute_schema():
        print("🔧 Executando schema da tabela torneio_pontuacoes...")
        
        # Conectar ao Supabase
        supabase_client = SupabaseClient()
        
        if not supabase_client.is_connected():
            print("❌ Erro: Não foi possível conectar ao Supabase")
            return False
        
        # Ler o arquivo SQL
        try:
            with open('database/torneio_pontuacoes_schema.sql', 'r', encoding='utf-8') as f:
                sql_content = f.read()
            
            print("📄 Schema carregado com sucesso")
            
            # Executar o SQL
            try:
                # Dividir em comandos individuais
                commands = sql_content.split(';')
                
                for i, command in enumerate(commands):
                    command = command.strip()
                    if command and not command.startswith('--'):
                        try:
                            print(f"⚡ Executando comando {i+1}...")
                            result = supabase_client.client.rpc('exec_sql', {'sql_query': command}).execute()
                            print(f"✅ Comando {i+1} executado com sucesso")
                        except Exception as cmd_error:
                            # Alguns comandos podem falhar se já existirem, isso é normal
                            print(f"⚠️ Comando {i+1} falhou (pode ser normal): {cmd_error}")
                
                print("✅ Schema executado com sucesso!")
                return True
                
            except Exception as e:
                print(f"❌ Erro ao executar SQL: {e}")
                return False
                
        except Exception as e:
            print(f"❌ Erro ao ler arquivo SQL: {e}")
            return False
    
    def test_table_structure():
        print("\n🔍 Testando estrutura da tabela...")
        
        supabase_client = SupabaseClient()
        
        try:
            # Testar se a tabela existe e tem dados
            response = supabase_client.client.table('torneio_pontuacoes').select('*').limit(5).execute()
            
            if response.data:
                print(f"✅ Tabela existe e tem {len(response.data)} registros (amostra)")
                
                # Mostrar estrutura dos dados
                if response.data:
                    primeiro_registro = response.data[0]
                    print("📊 Estrutura dos dados:")
                    for campo, valor in primeiro_registro.items():
                        print(f"  - {campo}: {type(valor).__name__} = {valor}")
            else:
                print("⚠️ Tabela existe mas está vazia")
                
            return True
            
        except Exception as e:
            print(f"❌ Erro ao testar tabela: {e}")
            return False
    
    def add_test_data():
        print("\n🧪 Adicionando dados de teste...")
        
        supabase_client = SupabaseClient()
        
        try:
            # Verificar se já existem dados
            response = supabase_client.client.table('torneio_pontuacoes').select('*').eq('tipo_torneio', 'deface').execute()
            
            if response.data and len(response.data) > 0:
                print(f"✅ Já existem {len(response.data)} registros de deface")
                return True
            
            # Adicionar alguns dados de teste
            test_data = [
                {
                    'jogador_uid': 'test-uid-1',
                    'tipo_torneio': 'deface',
                    'pontos_individuais': 150,
                    'group_points': 50,
                    'nick': 'TestPlayer001',
                    'grupo_id': 'test-group-1',
                    'grupo_nome': 'Test Group Alpha'
                },
                {
                    'jogador_uid': 'test-uid-2',
                    'tipo_torneio': 'deface',
                    'pontos_individuais': 90,
                    'group_points': 30,
                    'nick': 'TestPlayer002',
                    'grupo_id': 'test-group-2',
                    'grupo_nome': 'Test Group Beta'
                }
            ]
            
            for data in test_data:
                try:
                    supabase_client.client.table('torneio_pontuacoes').insert(data).execute()
                    print(f"✅ Dados de teste adicionados para {data['nick']}")
                except Exception as e:
                    print(f"⚠️ Erro ao adicionar {data['nick']}: {e}")
            
            return True
            
        except Exception as e:
            print(f"❌ Erro ao adicionar dados de teste: {e}")
            return False
    
    if __name__ == "__main__":
        print("🚀 Iniciando configuração da tabela torneio_pontuacoes...")
        
        # 1. Executar schema
        if execute_schema():
            print("\n" + "="*50)
            
            # 2. Testar estrutura
            if test_table_structure():
                print("\n" + "="*50)
                
                # 3. Adicionar dados de teste se necessário
                add_test_data()
                
                print("\n✅ Configuração concluída com sucesso!")
            else:
                print("\n❌ Falha ao testar estrutura")
        else:
            print("\n❌ Falha ao executar schema")

except ImportError as e:
    print(f"❌ Erro de importação: {e}")
    print("🔧 Verifique se o módulo supabase está instalado")
except Exception as e:
    print(f"❌ Erro inesperado: {e}")
