#!/usr/bin/env python3
"""
Teste direto da mineração
"""

import os
import sys
from dotenv import load_dotenv

# Carrega variáveis de ambiente
load_dotenv()

# Adiciona o diretório atual ao path
sys.path.append(os.getcwd())

print("🔍 Teste direto da mineração...")

# Importar e testar
try:
    from database.supabase_client import supabase_client
    from game.new_models import processar_mineracao_automatica_dinheiro
    
    print("✅ Importações OK")
    
    # Testar conexão
    if not supabase_client.is_connected():
        print("❌ Supabase não conectado")
        sys.exit(1)
    
    print("✅ Supabase conectado")
    
    # Buscar um usuário para teste
    usuarios = supabase_client.get_ranking_usuarios(1)
    if not usuarios:
        print("❌ Nenhum usuário encontrado")
        sys.exit(1)
    
    usuario = usuarios[0]
    uid = usuario['uid']
    nick = usuario.get('nick', 'Unknown')
    dinheiro_antes = usuario.get('dinheiro', 0)
    nivel = usuario.get('nivel_mineradora', 1)
    
    print(f"👤 Usuário: {nick}")
    print(f"💰 Dinheiro antes: ${dinheiro_antes}")
    print(f"⛏️ Nível: {nivel}")
    
    # Executar mineração
    print("\n⚡ Executando mineração...")
    resultado = processar_mineracao_automatica_dinheiro()
    
    print(f"📊 Resultado: {resultado}")
    
    # Verificar se funcionou
    if resultado.get('sucesso') and resultado.get('jogadores_processados', 0) > 0:
        print("✅ Mineração executada com sucesso!")
        
        # Verificar saldo atualizado
        usuario_atualizado = supabase_client.get_user_by_uid(uid)
        if usuario_atualizado:
            dinheiro_depois = usuario_atualizado.get('dinheiro', 0)
            diferenca = dinheiro_depois - dinheiro_antes
            
            print(f"💰 Dinheiro depois: ${dinheiro_depois}")
            print(f"📈 Diferença: +${diferenca}")
            
            if diferenca > 0:
                print("🎉 SUCESSO! Dinheiro foi incrementado!")
            else:
                print("❌ Dinheiro não foi incrementado")
        else:
            print("❌ Erro ao buscar usuário atualizado")
    else:
        print("❌ Mineração falhou ou não processou ninguém")
        
except Exception as e:
    print(f"❌ Erro: {e}")
    import traceback
    traceback.print_exc()
