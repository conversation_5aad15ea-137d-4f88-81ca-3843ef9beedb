-- Atualizar função de chat para incluir cores de nickname
-- Esta função substitui a função existente para incluir nick_color

-- Atualizar função de busca de mensagens para incluir nick_color
CREATE OR REPLACE FUNCTION get_recent_chat_messages(limite INTEGER DEFAULT 50)
RETURNS TABLE(
    id UUID,
    user_uid TEXT,
    nick VARCHAR(100),
    nick_color VARCHAR(7),
    message TEXT,
    created_at TIMESTAMP WITH TIME ZONE,
    time_ago TEXT,
    is_blocked BOOLEAN
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        c.id,
        c.user_uid,
        u.nick,
        u.nick_color,  -- Incluir cor do nickname
        c.message,
        c.created_at,
        CASE
            WHEN c.created_at > NOW() - INTERVAL '1 minute' THEN 'agora'
            WHEN c.created_at > NOW() - INTERVAL '1 hour' THEN
                EXTRACT(EPOCH FROM (NOW() - c.created_at))::INTEGER / 60 || 'm'
            WHEN c.created_at > NOW() - INTERVAL '1 day' THEN
                EXTRACT(EPOCH FROM (NOW() - c.created_at))::INTEGER / 3600 || 'h'
            ELSE
                EXTRACT(EPOCH FROM (NOW() - c.created_at))::INTEGER / 86400 || 'd'
        END as time_ago,
        COALESCE(c.is_blocked, FALSE) as is_blocked
    FROM chat_messages c
    JOIN usuarios u ON c.user_uid = u.uid
    WHERE c.created_at > NOW() - INTERVAL '6 hours'  -- Mensagens das últimas 6 horas
    AND c.is_blocked = FALSE  -- Apenas mensagens não bloqueadas
    ORDER BY c.created_at DESC
    LIMIT limite;
END;
$$ LANGUAGE plpgsql;

-- Comentário atualizado
COMMENT ON FUNCTION get_recent_chat_messages(INTEGER) IS 'Busca mensagens recentes do chat incluindo cores de nickname dos usuários';

-- Verificar se a função foi atualizada corretamente
DO $$
BEGIN
    -- Testar a função
    IF EXISTS (
        SELECT 1 FROM information_schema.routines 
        WHERE routine_name = 'get_recent_chat_messages'
        AND routine_type = 'FUNCTION'
    ) THEN
        RAISE NOTICE '✅ Função get_recent_chat_messages atualizada com sucesso para incluir nick_color';
    ELSE
        RAISE NOTICE '❌ Erro: Função get_recent_chat_messages não foi encontrada';
    END IF;
END $$;
