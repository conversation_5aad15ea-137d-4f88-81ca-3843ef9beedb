<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste FetchAPI</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #1a1a1a; color: white; }
        .container { max-width: 800px; margin: 0 auto; }
        .log { background: #2a2a2a; padding: 15px; margin: 10px 0; border-radius: 5px; font-family: monospace; }
        button { background: #2196F3; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin: 5px; }
        button:hover { background: #1976D2; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Teste da Função fetchAPI</h1>
        
        <div>
            <button onclick="testFetchAPI()">Testar fetchAPI</button>
            <button onclick="testDirectFetch()">Testar fetch direto</button>
            <button onclick="clearLogs()">Limpar</button>
        </div>
        
        <div id="logs"></div>
    </div>

    <script type="module">
        // Importar a função fetchAPI do auth.js
        import { fetchAPI } from '/static/js/auth.js';
        
        function log(message) {
            const logs = document.getElementById('logs');
            const timestamp = new Date().toLocaleTimeString();
            logs.innerHTML += `<div class="log">[${timestamp}] ${message}</div>`;
            console.log(message);
        }
        
        window.testFetchAPI = async function() {
            log('🔄 Testando fetchAPI...');
            
            try {
                // Primeiro fazer login
                log('1. Fazendo login...');
                const loginResponse = await fetch('/api/auth/auto-login');
                if (loginResponse.ok) {
                    log('✅ Login OK');
                    
                    // Testar fetchAPI
                    log('2. Testando fetchAPI...');
                    const data = await fetchAPI('/api/ranking/deface-individual');
                    log(`✅ fetchAPI funcionou: ${JSON.stringify(data, null, 2)}`);
                } else {
                    log('❌ Erro no login');
                }
            } catch (error) {
                log(`❌ Erro no fetchAPI: ${error.message}`);
                console.error(error);
            }
        };
        
        window.testDirectFetch = async function() {
            log('🔄 Testando fetch direto...');
            
            try {
                // Fazer login primeiro
                log('1. Fazendo login...');
                const loginResponse = await fetch('/api/auth/auto-login');
                if (loginResponse.ok) {
                    log('✅ Login OK');
                    
                    // Testar fetch direto
                    log('2. Testando fetch direto...');
                    const response = await fetch('/api/ranking/deface-individual');
                    const data = await response.json();
                    log(`✅ Fetch direto funcionou: ${JSON.stringify(data, null, 2)}`);
                } else {
                    log('❌ Erro no login');
                }
            } catch (error) {
                log(`❌ Erro no fetch direto: ${error.message}`);
                console.error(error);
            }
        };
        
        window.clearLogs = function() {
            document.getElementById('logs').innerHTML = '';
        };
        
        log('🌐 Página carregada, pronto para testar!');
    </script>
</body>
</html>
