#!/usr/bin/env python3
"""
Teste com reload forçado do módulo
"""

import sys
import os
import importlib
from dotenv import load_dotenv

# Carrega variáveis de ambiente
load_dotenv()

def main():
    print("🔄 TESTE COM RELOAD FORÇADO")
    print("=" * 35)
    
    try:
        # Importar módulos
        print("📦 Importando módulos...")
        from database.supabase_client import supabase_client
        from game import new_models as models
        print("✅ Módulos importados")
        
        # Forçar reload do módulo
        print("\n🔄 Forçando reload do módulo...")
        importlib.reload(models)
        print("✅ Módulo recarregado")
        
        # Verificar se a função de teste existe agora
        print("\n🧪 Verificando função de teste...")
        if hasattr(models, 'teste_funcao_simples'):
            print("✅ Função de teste encontrada")
            resultado_simples = models.teste_funcao_simples()
            print(f"📊 Resultado: {resultado_simples}")
        else:
            print("❌ Função de teste NÃO encontrada")
            print("🔍 Funções disponíveis no módulo:")
            funcoes = [attr for attr in dir(models) if callable(getattr(models, attr)) and not attr.startswith('_')]
            for i, func in enumerate(funcoes[:10]):  # Mostrar apenas as primeiras 10
                print(f"   {i+1}. {func}")
            if len(funcoes) > 10:
                print(f"   ... e mais {len(funcoes) - 10} funções")
        
        # Testar função de mineração
        print("\n⛏️ Testando função de mineração...")
        if hasattr(models, 'processar_mineracao_automatica_dinheiro'):
            print("✅ Função de mineração encontrada")
            resultado_mineracao = models.processar_mineracao_automatica_dinheiro()
            print(f"📊 Resultado: {resultado_mineracao}")
        else:
            print("❌ Função de mineração NÃO encontrada")
        
    except Exception as e:
        print(f"❌ ERRO: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
