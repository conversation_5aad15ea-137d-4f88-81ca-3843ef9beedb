#!/usr/bin/env python3
"""
Teste para verificar se o problema está na busca de usuários
"""

import sys
import os
sys.path.append('.')

from database.supabase_client import supabase_client
from game import new_models as models

def main():
    print("🔍 TESTE: Verificando busca de usuários")
    print("=" * 50)
    
    # 1. Verificar conexão
    print("\n1. Verificando conexão...")
    if not supabase_client.is_connected():
        print("❌ Não conectado!")
        return
    print("✅ Conectado!")
    
    # 2. Buscar todos os usuários (método atual)
    print("\n2. Testando método atual (get_ranking_usuarios)...")
    try:
        usuarios_ranking = supabase_client.get_ranking_usuarios(10)
        print(f"📊 Método ranking retornou: {len(usuarios_ranking)} usuários")
        
        if usuarios_ranking:
            print("👥 Primeiros usuários do ranking:")
            for i, u in enumerate(usuarios_ranking[:3]):
                nick = u.get('nick', 'Unknown')
                nivel = u.get('nivel_mineradora', 'N/A')
                dinheiro = u.get('dinheiro', 'N/A')
                print(f"   {i+1}. {nick} - Nível: {nivel} - Dinheiro: ${dinheiro}")
    except Exception as e:
        print(f"❌ Erro no método ranking: {e}")
    
    # 3. Buscar diretamente da tabela
    print("\n3. Testando busca direta da tabela...")
    try:
        result = supabase_client.client.table('usuarios').select('uid, nick, nivel_mineradora, dinheiro').limit(10).execute()
        usuarios_direto = result.data if result.data else []
        print(f"📊 Busca direta retornou: {len(usuarios_direto)} usuários")
        
        if usuarios_direto:
            print("👥 Primeiros usuários da busca direta:")
            for i, u in enumerate(usuarios_direto[:3]):
                nick = u.get('nick', 'Unknown')
                nivel = u.get('nivel_mineradora', 'N/A')
                dinheiro = u.get('dinheiro', 'N/A')
                print(f"   {i+1}. {nick} - Nível: {nivel} - Dinheiro: ${dinheiro}")
    except Exception as e:
        print(f"❌ Erro na busca direta: {e}")
    
    # 4. Buscar apenas usuários com mineradora
    print("\n4. Testando busca de usuários com mineradora...")
    try:
        result = supabase_client.client.table('usuarios').select('uid, nick, nivel_mineradora, dinheiro').gte('nivel_mineradora', 1).execute()
        usuarios_mineradora = result.data if result.data else []
        print(f"📊 Usuários com mineradora: {len(usuarios_mineradora)}")
        
        if usuarios_mineradora:
            print("⛏️ Usuários com mineradora ativa:")
            for i, u in enumerate(usuarios_mineradora[:5]):
                nick = u.get('nick', 'Unknown')
                nivel = u.get('nivel_mineradora', 'N/A')
                dinheiro = u.get('dinheiro', 'N/A')
                print(f"   {i+1}. {nick} - Nível: {nivel} - Dinheiro: ${dinheiro}")
        else:
            print("❌ Nenhum usuário com mineradora encontrado!")
    except Exception as e:
        print(f"❌ Erro na busca de mineradora: {e}")
    
    # 5. Testar função de mineração
    print("\n5. Testando função de mineração...")
    try:
        resultado = models.processar_mineracao_automatica_dinheiro()
        print(f"📊 Resultado da mineração: {resultado}")
        
        sucesso = resultado.get('sucesso', False)
        processados = resultado.get('jogadores_processados', 0)
        total_gerado = resultado.get('total_dinheiro_gerado', 0)
        mensagem = resultado.get('mensagem', 'N/A')
        
        print(f"\n📈 RESUMO:")
        print(f"   Sucesso: {sucesso}")
        print(f"   Processados: {processados}")
        print(f"   Total gerado: ${total_gerado}")
        print(f"   Mensagem: {mensagem}")
        
        if processados > 0:
            print("\n🎉 MINERAÇÃO FUNCIONOU!")
        else:
            print("\n❌ Nenhum jogador processado!")
            
    except Exception as e:
        print(f"❌ Erro na mineração: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
