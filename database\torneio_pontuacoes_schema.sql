-- Schema para tabela torneio_pontuacoes com suporte a pontos de grupo
-- Esta tabela gerencia pontos individuais e de grupo para torneios de deface

-- Adicionar coluna group_points se não existir
ALTER TABLE torneio_pontuacoes 
ADD COLUMN IF NOT EXISTS group_points INTEGER DEFAULT 0;

-- Adici<PERSON>r coluna grupo_id se não existir (para facilitar consultas)
ALTER TABLE torneio_pontuacoes 
ADD COLUMN IF NOT EXISTS grupo_id TEXT;

-- Adicionar coluna grupo_nome se não existir (para facilitar consultas)
ALTER TABLE torneio_pontuacoes 
ADD COLUMN IF NOT EXISTS grupo_nome TEXT;

-- Adicionar coluna nick se não existir (para facilitar consultas)
ALTER TABLE torneio_pontuacoes 
ADD COLUMN IF NOT EXISTS nick TEXT;

-- Índices para melhor performance
CREATE INDEX IF NOT EXISTS idx_torneio_pontuacoes_uid ON torneio_pontuacoes(jogador_uid);
CREATE INDEX IF NOT EXISTS idx_torneio_pontuacoes_tipo ON torneio_pontuacoes(tipo_torneio);
CREATE INDEX IF NOT EXISTS idx_torneio_pontuacoes_grupo_id ON torneio_pontuacoes(grupo_id);
CREATE INDEX IF NOT EXISTS idx_torneio_pontuacoes_pontos_individuais ON torneio_pontuacoes(pontos_individuais DESC);
CREATE INDEX IF NOT EXISTS idx_torneio_pontuacoes_group_points ON torneio_pontuacoes(group_points DESC);

-- Índice composto para consultas de ranking
CREATE INDEX IF NOT EXISTS idx_torneio_pontuacoes_ranking ON torneio_pontuacoes(tipo_torneio, pontos_individuais DESC);
CREATE INDEX IF NOT EXISTS idx_torneio_pontuacoes_ranking_grupo ON torneio_pontuacoes(tipo_torneio, grupo_id, group_points DESC);

-- Função para adicionar pontos de deface
CREATE OR REPLACE FUNCTION adicionar_pontos_deface(
    p_jogador_uid TEXT,
    p_pontos_individuais INTEGER,
    p_pontos_grupo INTEGER,
    p_nick TEXT DEFAULT NULL,
    p_grupo_id TEXT DEFAULT NULL,
    p_grupo_nome TEXT DEFAULT NULL
)
RETURNS BOOLEAN AS $$
DECLARE
    registro_existente RECORD;
BEGIN
    -- Verificar se já existe registro para este jogador no torneio de deface
    SELECT * INTO registro_existente 
    FROM torneio_pontuacoes 
    WHERE jogador_uid = p_jogador_uid AND tipo_torneio = 'deface';
    
    IF FOUND THEN
        -- Atualizar registro existente
        UPDATE torneio_pontuacoes 
        SET 
            pontos_individuais = pontos_individuais + p_pontos_individuais,
            group_points = group_points + p_pontos_grupo,
            nick = COALESCE(p_nick, nick),
            grupo_id = COALESCE(p_grupo_id, grupo_id),
            grupo_nome = COALESCE(p_grupo_nome, grupo_nome),
            ultima_atualizacao = NOW()
        WHERE jogador_uid = p_jogador_uid AND tipo_torneio = 'deface';
    ELSE
        -- Inserir novo registro
        INSERT INTO torneio_pontuacoes (
            jogador_uid, 
            tipo_torneio, 
            pontos_individuais, 
            group_points,
            nick,
            grupo_id,
            grupo_nome,
            ultima_atualizacao
        ) VALUES (
            p_jogador_uid, 
            'deface', 
            p_pontos_individuais, 
            p_pontos_grupo,
            p_nick,
            p_grupo_id,
            p_grupo_nome,
            NOW()
        );
    END IF;
    
    RETURN TRUE;
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE 'Erro ao adicionar pontos de deface: %', SQLERRM;
        RETURN FALSE;
END;
$$ LANGUAGE plpgsql;

-- Função para obter ranking individual de deface
CREATE OR REPLACE FUNCTION get_ranking_individual_deface(limite INTEGER DEFAULT 50)
RETURNS TABLE(
    posicao INTEGER,
    jogador_uid TEXT,
    nick TEXT,
    pontos_individuais INTEGER,
    grupo_id TEXT,
    grupo_nome TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        ROW_NUMBER() OVER (ORDER BY tp.pontos_individuais DESC)::INTEGER as posicao,
        tp.jogador_uiduid,
        tp.nick,
        tp.pontos_individuais,
        tp.grupo_id,
        tp.grupo_nome
    FROM torneio_pontuacoes tp
    WHERE tp.tipo_torneio = 'deface' 
    AND tp.pontos_individuais > 0
    ORDER BY tp.pontos_individuais DESC
    LIMIT limite;
END;
$$ LANGUAGE plpgsql;

-- Função para obter ranking de grupos por deface
CREATE OR REPLACE FUNCTION get_ranking_grupos_deface(limite INTEGER DEFAULT 50)
RETURNS TABLE(
    posicao INTEGER,
    grupo_id TEXT,
    grupo_nome TEXT,
    total_pontos INTEGER,
    membros_count INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        ROW_NUMBER() OVER (ORDER BY SUM(tp.group_points) DESC)::INTEGER as posicao,
        tp.grupo_id,
        tp.grupo_nome,
        SUM(tp.group_points)::INTEGER as total_pontos,
        COUNT(DISTINCT tp.uid)::INTEGER as membros_count
    FROM torneio_pontuacoes tp
    WHERE tp.tipo_torneio = 'deface' 
    AND tp.grupo_id IS NOT NULL
    AND tp.group_points > 0
    GROUP BY tp.grupo_id, tp.grupo_nome
    ORDER BY SUM(tp.group_points) DESC
    LIMIT limite;
END;
$$ LANGUAGE plpgsql;

-- Comentários para documentação
COMMENT ON COLUMN torneio_pontuacoes.group_points IS 'Pontos de grupo para torneios de deface';
COMMENT ON COLUMN torneio_pontuacoes.grupo_id IS 'ID do grupo do jogador';
COMMENT ON COLUMN torneio_pontuacoes.grupo_nome IS 'Nome do grupo do jogador';
COMMENT ON COLUMN torneio_pontuacoes.nick IS 'Nick do jogador';

-- Verificar se a estrutura está correta
DO $$
BEGIN
    -- Verificar se as colunas foram criadas
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'torneio_pontuacoes' AND column_name = 'group_points') THEN
        RAISE NOTICE '✅ Coluna group_points criada com sucesso';
    ELSE
        RAISE NOTICE '❌ Erro: Coluna group_points não foi criada';
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'torneio_pontuacoes' AND column_name = 'grupo_id') THEN
        RAISE NOTICE '✅ Coluna grupo_id criada com sucesso';
    ELSE
        RAISE NOTICE '❌ Erro: Coluna grupo_id não foi criada';
    END IF;
END $$;
