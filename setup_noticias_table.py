#!/usr/bin/env python3
"""
Script para verificar e criar a tabela de notícias no Supabase
"""

import os
from dotenv import load_dotenv
from database.supabase_client import supabase_client

# Carrega variáveis de ambiente
load_dotenv()

def verificar_tabela_noticias():
    """Verifica se a tabela noticias existe e tem a estrutura correta"""
    try:
        if not supabase_client.is_connected():
            print("❌ Erro: Não foi possível conectar ao Supabase")
            return False
        
        # Tentar buscar dados da tabela para verificar se existe
        result = supabase_client.client.table('noticias').select("*").limit(1).execute()
        
        print("✅ Tabela 'noticias' existe e está acessível")
        print(f"📊 Registros encontrados: {len(result.data) if result.data else 0}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro ao verificar tabela 'noticias': {e}")
        return False

def criar_tabela_noticias():
    """Cria a tabela de notícias se não existir"""
    sql_create_table = """
    -- Criar tabela de notícias se não existir
    CREATE TABLE IF NOT EXISTS noticias (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        titulo VARCHAR(255) NOT NULL,
        conteudo TEXT NOT NULL,
        autor_uid VARCHAR(255) NOT NULL,
        autor_nick VARCHAR(100) NOT NULL,
        prioridade BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );

    -- Criar índices para melhor performance
    CREATE INDEX IF NOT EXISTS idx_noticias_created_at ON noticias(created_at DESC);
    CREATE INDEX IF NOT EXISTS idx_noticias_autor_uid ON noticias(autor_uid);
    CREATE INDEX IF NOT EXISTS idx_noticias_prioridade ON noticias(prioridade);

    -- Habilitar RLS (Row Level Security)
    ALTER TABLE noticias ENABLE ROW LEVEL SECURITY;

    -- Política para permitir leitura para todos os usuários autenticados
    CREATE POLICY IF NOT EXISTS "Permitir leitura de notícias para usuários autenticados" 
    ON noticias FOR SELECT 
    USING (auth.role() = 'authenticated');

    -- Política para permitir inserção apenas para administradores
    CREATE POLICY IF NOT EXISTS "Permitir criação de notícias para administradores" 
    ON noticias FOR INSERT 
    WITH CHECK (
        EXISTS (
            SELECT 1 FROM usuarios 
            WHERE uid = auth.uid()::text 
            AND is_admin = true
        )
    );

    -- Política para permitir exclusão apenas para administradores
    CREATE POLICY IF NOT EXISTS "Permitir exclusão de notícias para administradores" 
    ON noticias FOR DELETE 
    USING (
        EXISTS (
            SELECT 1 FROM usuarios 
            WHERE uid = auth.uid()::text 
            AND is_admin = true
        )
    );

    -- Função para atualizar updated_at automaticamente
    CREATE OR REPLACE FUNCTION update_updated_at_column()
    RETURNS TRIGGER AS $$
    BEGIN
        NEW.updated_at = NOW();
        RETURN NEW;
    END;
    $$ language 'plpgsql';

    -- Trigger para atualizar updated_at
    DROP TRIGGER IF EXISTS update_noticias_updated_at ON noticias;
    CREATE TRIGGER update_noticias_updated_at
        BEFORE UPDATE ON noticias
        FOR EACH ROW
        EXECUTE FUNCTION update_updated_at_column();
    """
    
    try:
        if not supabase_client.is_connected():
            print("❌ Erro: Não foi possível conectar ao Supabase")
            return False
        
        # Executar SQL
        result = supabase_client.client.rpc('exec_sql', {'sql': sql_create_table}).execute()
        
        print("✅ Tabela 'noticias' criada/verificada com sucesso!")
        print("📋 Estrutura da tabela:")
        print("   - id (UUID, PRIMARY KEY)")
        print("   - titulo (VARCHAR(255), NOT NULL)")
        print("   - conteudo (TEXT, NOT NULL)")
        print("   - autor_uid (VARCHAR(255), NOT NULL)")
        print("   - autor_nick (VARCHAR(100), NOT NULL)")
        print("   - prioridade (BOOLEAN, DEFAULT FALSE)")
        print("   - created_at (TIMESTAMP, DEFAULT NOW())")
        print("   - updated_at (TIMESTAMP, DEFAULT NOW())")
        print("🔒 Políticas de segurança configuradas")
        print("📈 Índices criados para performance")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro ao criar tabela: {e}")
        print("🔧 Tente executar o SQL manualmente no Supabase Dashboard:")
        print("   1. Acesse https://supabase.com/dashboard")
        print("   2. Vá em SQL Editor")
        print("   3. Execute o SQL acima")
        return False

def inserir_noticia_teste():
    """Insere uma notícia de teste"""
    try:
        # Buscar um usuário admin para usar como autor
        usuarios = supabase_client.client.table('usuarios').select("uid, nick, is_admin").eq('is_admin', True).limit(1).execute()
        
        if not usuarios.data:
            print("⚠️ Nenhum usuário admin encontrado para criar notícia de teste")
            return False
        
        admin = usuarios.data[0]
        
        # Dados da notícia de teste
        noticia_teste = {
            'titulo': '🎉 Sistema de Notícias Ativado!',
            'conteudo': 'O sistema de notícias do SHACK foi configurado e está funcionando perfeitamente! Agora os administradores podem publicar atualizações importantes para todos os jogadores.',
            'autor_uid': admin['uid'],
            'autor_nick': admin['nick'],
            'prioridade': True
        }
        
        result = supabase_client.client.table('noticias').insert(noticia_teste).execute()
        
        if result.data:
            print("✅ Notícia de teste criada com sucesso!")
            print(f"📰 Título: {noticia_teste['titulo']}")
            print(f"👤 Autor: {noticia_teste['autor_nick']}")
            return True
        else:
            print("❌ Erro ao criar notícia de teste")
            return False
            
    except Exception as e:
        print(f"❌ Erro ao inserir notícia de teste: {e}")
        return False

def main():
    """Função principal"""
    print("🔧 Configurando sistema de notícias...")
    print("=" * 50)
    
    # Verificar conexão
    if not supabase_client.is_connected():
        print("❌ Erro: Conexão com Supabase não disponível")
        print("🔧 Verifique as variáveis SUPABASE_URL e SUPABASE_ANON_KEY no .env")
        return
    
    print("✅ Conexão com Supabase estabelecida")
    
    # Verificar se tabela existe
    if verificar_tabela_noticias():
        print("✅ Tabela 'noticias' já existe e está funcionando")
    else:
        print("🔧 Criando tabela 'noticias'...")
        if not criar_tabela_noticias():
            print("❌ Falha ao criar tabela")
            return
    
    # Verificar se há notícias
    try:
        noticias = supabase_client.get_todas_noticias()
        print(f"📊 Total de notícias na database: {len(noticias)}")
        
        if len(noticias) == 0:
            print("📝 Criando notícia de teste...")
            inserir_noticia_teste()
        else:
            print("📰 Últimas notícias:")
            for i, noticia in enumerate(noticias[:3]):
                print(f"   {i+1}. {noticia.get('titulo', 'Sem título')} - {noticia.get('autor_nick', 'Autor desconhecido')}")
    
    except Exception as e:
        print(f"❌ Erro ao verificar notícias: {e}")
    
    print("\n🎉 Configuração do sistema de notícias concluída!")
    print("🔗 Acesse /news no jogo para ver as notícias")

if __name__ == "__main__":
    main()
