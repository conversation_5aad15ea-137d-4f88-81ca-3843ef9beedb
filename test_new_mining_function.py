#!/usr/bin/env python3
"""
Test the new automatic mining function
"""

import sys
import os
sys.path.append('.')

from database.supabase_client import supabase_client
from game import new_models as models

def main():
    print("🔧 TESTING NEW AUTOMATIC MINING FUNCTION")
    print("=" * 60)
    
    # 1. Test database connection
    print("\n1. Testing database connection...")
    if not supabase_client.is_connected():
        print("❌ Database not connected!")
        return
    print("✅ Database connected!")
    
    # 2. Check for miners in database
    print("\n2. Checking for miners in database...")
    try:
        result = supabase_client.client.table('usuarios').select(
            'uid, nick, dinheiro, nivel_mineradora'
        ).gte('nivel_mineradora', 1).limit(5).execute()
        
        miners = result.data if result.data else []
        print(f"📊 Found {len(miners)} miners in database")
        
        if miners:
            print("⛏️ Sample miners:")
            for i, miner in enumerate(miners[:3]):
                nick = miner.get('nick', 'Unknown')
                level = miner.get('nivel_mineradora', 1)
                money = miner.get('dinheiro', 0)
                print(f"   {i+1}. {nick} - Level {level} - ${money}")
        else:
            print("❌ No miners found! Creating a test miner...")
            # Create a test miner
            test_result = supabase_client.client.table('usuarios').insert({
                'uid': 'test-miner-001',
                'nick': 'TestMiner',
                'email': '<EMAIL>',
                'dinheiro': 100,
                'nivel_mineradora': 1
            }).execute()
            
            if test_result.data:
                print("✅ Test miner created!")
                miners = [test_result.data[0]]
            else:
                print("❌ Failed to create test miner")
                return
                
    except Exception as e:
        print(f"❌ Error checking miners: {e}")
        return
    
    # 3. Test the mining function
    print("\n3. Testing automatic mining function...")
    print("-" * 40)
    
    try:
        # Record balances before
        balances_before = {}
        for miner in miners:
            uid = miner.get('uid')
            balances_before[uid] = miner.get('dinheiro', 0)
        
        print("💰 Balances BEFORE mining:")
        for uid, balance in balances_before.items():
            nick = next((m['nick'] for m in miners if m['uid'] == uid), 'Unknown')
            print(f"   {nick}: ${balance}")
        
        # Execute mining function
        print("\n⚡ Executing mining function...")
        result = models.processar_mineracao_automatica_dinheiro()
        
        print("-" * 40)
        print(f"\n📊 MINING RESULT:")
        print(f"   Success: {result.get('sucesso', False)}")
        print(f"   Players processed: {result.get('jogadores_processados', 0)}")
        print(f"   Total money generated: ${result.get('total_dinheiro_gerado', 0)}")
        print(f"   Message: {result.get('mensagem', 'N/A')}")
        
        # Check balances after
        print("\n💰 Balances AFTER mining:")
        changes_detected = 0
        
        for uid in balances_before.keys():
            # Fetch updated balance
            user_result = supabase_client.client.table('usuarios').select('dinheiro, nick').eq('uid', uid).execute()
            if user_result.data:
                new_balance = user_result.data[0].get('dinheiro', 0)
                nick = user_result.data[0].get('nick', 'Unknown')
                old_balance = balances_before[uid]
                difference = new_balance - old_balance
                
                if difference > 0:
                    print(f"   {nick}: ${old_balance} → ${new_balance} (+${difference}) ✅")
                    changes_detected += 1
                else:
                    print(f"   {nick}: ${new_balance} (no change)")
        
        # Final assessment
        print(f"\n🎯 ASSESSMENT:")
        if result.get('sucesso') and result.get('jogadores_processados', 0) > 0:
            print("✅ Mining function executed successfully!")
            if changes_detected > 0:
                print("✅ Money was generated and added to player balances!")
                print("🎉 NEW MINING FUNCTION IS WORKING CORRECTLY!")
            else:
                print("⚠️ Function succeeded but no balance changes detected")
        else:
            print("❌ Mining function failed or processed 0 players")
            
    except Exception as e:
        print(f"❌ Error testing mining function: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
