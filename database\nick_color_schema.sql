-- Schema para sistema de cores de nickname
-- Adiciona suporte a cores personalizadas para nicknames de supporters

-- Adici<PERSON>r coluna nick_color à tabela usuarios
ALTER TABLE usuarios 
ADD COLUMN IF NOT EXISTS nick_color VARCHAR(7) DEFAULT NULL;

-- Adicionar comentário para documentação
COMMENT ON COLUMN usuarios.nick_color IS 'Cor personalizada do nickname em formato hex (#FF0000). Apenas para supporters.';

-- Criar índice para consultas por cor (opcional, para estatísticas)
CREATE INDEX IF NOT EXISTS idx_usuarios_nick_color ON usuarios(nick_color) WHERE nick_color IS NOT NULL;

-- Função para validar formato de cor hex
CREATE OR REPLACE FUNCTION validate_hex_color(color_value TEXT)
RETURNS BOOLEAN AS $$
BEGIN
    -- Verificar se é NULL (permitido)
    IF color_value IS NULL THEN
        RETURN TRUE;
    END IF;
    
    -- Verificar formato hex (#RRGGBB)
    IF color_value ~ '^#[0-9A-Fa-f]{6}$' THEN
        RETURN TRUE;
    END IF;
    
    RETURN FALSE;
END;
$$ LANGUAGE plpgsql;

-- Adicionar constraint para validar formato hex
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_constraint WHERE conname = 'check_nick_color_format'
    ) THEN
        ALTER TABLE usuarios
        ADD CONSTRAINT check_nick_color_format
        CHECK (validate_hex_color(nick_color));
    END IF;
END $$;

-- Função para definir cor do nickname (apenas para supporters)
CREATE OR REPLACE FUNCTION set_nick_color(
    p_user_uid TEXT,
    p_color VARCHAR(7)
)
RETURNS BOOLEAN AS $$
DECLARE
    user_is_supporter BOOLEAN;
BEGIN
    -- Verificar se o usuário é supporter
    SELECT COALESCE(is_supporter, FALSE) INTO user_is_supporter
    FROM usuarios 
    WHERE uid = p_user_uid;
    
    -- Se não encontrou o usuário
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Usuário não encontrado: %', p_user_uid;
    END IF;
    
    -- Se não é supporter, não pode ter cor
    IF NOT user_is_supporter AND p_color IS NOT NULL THEN
        RAISE EXCEPTION 'Apenas supporters podem ter cores personalizadas';
    END IF;
    
    -- Validar formato da cor
    IF NOT validate_hex_color(p_color) THEN
        RAISE EXCEPTION 'Formato de cor inválido. Use formato hex: #RRGGBB';
    END IF;
    
    -- Atualizar cor do usuário
    UPDATE usuarios 
    SET nick_color = p_color 
    WHERE uid = p_user_uid;
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- Função para remover cor do nickname
CREATE OR REPLACE FUNCTION remove_nick_color(p_user_uid TEXT)
RETURNS BOOLEAN AS $$
BEGIN
    UPDATE usuarios 
    SET nick_color = NULL 
    WHERE uid = p_user_uid;
    
    RETURN FOUND;
END;
$$ LANGUAGE plpgsql;

-- Função para buscar usuários com cores personalizadas (para estatísticas)
CREATE OR REPLACE FUNCTION get_users_with_colors(limit_count INTEGER DEFAULT 50)
RETURNS TABLE(
    uid TEXT,
    nick VARCHAR(100),
    nick_color VARCHAR(7),
    is_supporter BOOLEAN
) AS $$
BEGIN
    RETURN QUERY
    SELECT u.uid, u.nick, u.nick_color, COALESCE(u.is_supporter, FALSE)
    FROM usuarios u
    WHERE u.nick_color IS NOT NULL
    ORDER BY u.nick
    LIMIT limit_count;
END;
$$ LANGUAGE plpgsql;

-- Verificação final
DO $$
BEGIN
    -- Verificar se a coluna foi criada
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'usuarios' AND column_name = 'nick_color'
    ) THEN
        RAISE NOTICE '✅ Coluna nick_color criada com sucesso';
    ELSE
        RAISE NOTICE '❌ Erro: Coluna nick_color não foi criada';
    END IF;
    
    -- Verificar se as funções foram criadas
    IF EXISTS (
        SELECT 1 FROM information_schema.routines 
        WHERE routine_name = 'set_nick_color'
    ) THEN
        RAISE NOTICE '✅ Função set_nick_color criada com sucesso';
    ELSE
        RAISE NOTICE '❌ Erro: Função set_nick_color não foi criada';
    END IF;
    
    IF EXISTS (
        SELECT 1 FROM information_schema.routines 
        WHERE routine_name = 'validate_hex_color'
    ) THEN
        RAISE NOTICE '✅ Função validate_hex_color criada com sucesso';
    ELSE
        RAISE NOTICE '❌ Erro: Função validate_hex_color não foi criada';
    END IF;
END $$;

DROP FUNCTION IF EXISTS get_recent_chat_messages(integer);

-- Atualizar função de chat para incluir nick_color
CREATE OR REPLACE FUNCTION get_recent_chat_messages(limite INTEGER DEFAULT 50)
RETURNS TABLE(
    id BIGINT,
    user_uid TEXT,
    nick VARCHAR(100),
    nick_color VARCHAR(7),
    message TEXT,
    created_at TIMESTAMP WITH TIME ZONE,
    time_ago TEXT,
    is_blocked BOOLEAN
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        c.id,
        c.user_uid,
        u.nick,
        u.nick_color,  -- Incluir cor do nickname
        c.message,
        c.created_at,
        CASE
            WHEN c.created_at > NOW() - INTERVAL '1 minute' THEN 'agora'
            WHEN c.created_at > NOW() - INTERVAL '1 hour' THEN
                EXTRACT(EPOCH FROM (NOW() - c.created_at))::INTEGER / 60 || 'm'
            WHEN c.created_at > NOW() - INTERVAL '1 day' THEN
                EXTRACT(EPOCH FROM (NOW() - c.created_at))::INTEGER / 3600 || 'h'
            ELSE
                EXTRACT(EPOCH FROM (NOW() - c.created_at))::INTEGER / 86400 || 'd'
        END as time_ago,
        COALESCE(c.is_blocked, FALSE) as is_blocked
    FROM chat_messages c
    JOIN usuarios u ON c.user_uid = u.uid
    WHERE c.created_at > NOW() - INTERVAL '6 hours'  -- Mensagens das últimas 6 horas
    ORDER BY c.created_at DESC
    LIMIT limite;
END;
$$ LANGUAGE plpgsql;

-- Exemplos de uso:
--
-- -- Definir cor vermelha para um supporter
-- SELECT set_nick_color('user-uid-123', '#FF0000');
--
-- -- Remover cor
-- SELECT remove_nick_color('user-uid-123');
--
-- -- Listar usuários com cores
-- SELECT * FROM get_users_with_colors(10);
--
-- -- Buscar mensagens com cores
-- SELECT * FROM get_recent_chat_messages(20);
