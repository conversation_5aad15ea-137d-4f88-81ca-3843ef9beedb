#!/usr/bin/env python3
"""
Versão corrigida da função reset_deface_tournaments
"""

import sys
import os
from datetime import datetime, timezone
from typing import Dict, Any
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def reset_deface_tournaments_corrigido() -> Dict[str, Any]:
    """
    Versão corrigida do reset de torneios de deface com distribuição de recompensas
    """
    print("🏆 [RESET CORRIGIDO] Iniciando reset de torneios de deface...")
    
    try:
        # Import modules
        from database.supabase_client import supabase_client
        from game.new_models import get_jogador
        
        if not supabase_client.is_connected():
            return {"sucesso": False, "mensagem": "Database não disponível"}
        
        # ETAPA 1: Calcular rankings e distribuir recompensas
        print("💰 [RESET CORRIGIDO] Calculando rankings e distribuindo recompensas...")
        
        # Recompensas para grupos
        recompensas_grupos = {
            1: {"dinheiro": 100000, "shacks": 1000, "xp": 5000},
            2: {"dinheiro": 50000, "shacks": 500, "xp": 2500},
            3: {"dinheiro": 25000, "shacks": 250, "xp": 1250}
        }
        
        # Recompensas individuais
        recompensas_individuais = {
            1: {"dinheiro": 50000, "shacks": 500},
            2: {"dinheiro": 30000, "shacks": 300},
            3: {"dinheiro": 20000, "shacks": 200},
            4: {"dinheiro": 15000, "shacks": 150},
            5: {"dinheiro": 15000, "shacks": 150},
            6: {"dinheiro": 10000, "shacks": 100},
            7: {"dinheiro": 10000, "shacks": 100},
            8: {"dinheiro": 10000, "shacks": 100},
            9: {"dinheiro": 10000, "shacks": 100},
            10: {"dinheiro": 10000, "shacks": 100}
        }
        
        grupos_premiados = []
        jogadores_premiados = []
        
        # RANKING DE GRUPOS (usando tabela torneio_pontuacoes)
        print("🏆 [RESET CORRIGIDO] Processando ranking de grupos...")
        try:
            # Buscar grupos com pontos de deface da tabela torneio_pontuacoes
            # Agregar pontos por grupo
            grupos_result = supabase_client.client.rpc('get_ranking_grupos_deface', {'limite': 3}).execute()

            if not grupos_result.data:
                # Fallback: buscar da tabela grupos diretamente
                print("   ⚠️ Usando fallback para ranking de grupos...")
                grupos_result = supabase_client.client.table('grupos').select(
                    'id, nome, deface_points'
                ).gt('deface_points', 0).order('deface_points', desc=True).limit(3).execute()

                # Converter formato para compatibilidade
                if grupos_result.data:
                    grupos_data = []
                    for grupo in grupos_result.data:
                        grupos_data.append({
                            'grupo_id': grupo['id'],
                            'grupo_nome': grupo['nome'],
                            'total_pontos': grupo['deface_points']
                        })
                    grupos_result.data = grupos_data

            if grupos_result.data:
                for posicao, grupo in enumerate(grupos_result.data, 1):
                    if posicao in recompensas_grupos:
                        grupo_id = grupo.get('grupo_id')
                        grupo_nome = grupo.get('grupo_nome')
                        pontos = grupo.get('total_pontos', 0)
                        recompensa = recompensas_grupos[posicao]

                        print(f"🥇 [RESET CORRIGIDO] Grupo {posicao}º: {grupo_nome} ({pontos} pontos)")

                        # Buscar membros do grupo
                        membros_result = supabase_client.client.table('usuarios').select(
                            'uid, nick, dinheiro, shack, xp'
                        ).eq('grupo_id', grupo_id).execute()

                        if membros_result.data:
                            for membro in membros_result.data:
                                try:
                                    uid = membro['uid']
                                    nick = membro['nick']

                                    # Calcular novos valores
                                    novo_dinheiro = membro.get('dinheiro', 0) + recompensa['dinheiro']
                                    novo_shack = membro.get('shack', 0) + recompensa['shacks']
                                    novo_xp = membro.get('xp', 0) + recompensa['xp']

                                    # Atualizar membro
                                    supabase_client.client.table('usuarios').update({
                                        'dinheiro': novo_dinheiro,
                                        'shack': novo_shack,
                                        'xp': novo_xp
                                    }).eq('uid', uid).execute()

                                    print(f"   💰 {nick}: +${recompensa['dinheiro']}, +{recompensa['shacks']} SHACK, +{recompensa['xp']} XP")

                                except Exception as e:
                                    print(f"   ❌ Erro ao premiar {membro.get('nick', 'Unknown')}: {e}")

                        grupos_premiados.append({
                            'posicao': posicao,
                            'nome': grupo_nome,
                            'pontos': pontos,
                            'recompensa': recompensa
                        })
        except Exception as e:
            print(f"❌ [RESET CORRIGIDO] Erro no ranking de grupos: {e}")
            import traceback
            traceback.print_exc()
        
        # RANKING INDIVIDUAL (usando tabela torneio_pontuacoes)
        print("👤 [RESET CORRIGIDO] Processando ranking individual...")
        try:
            # Buscar jogadores da tabela torneio_pontuacoes
            jogadores_result = supabase_client.client.table('torneio_pontuacoes').select(
                'jogador_uid, nick, pontos_individuais'
            ).eq('tipo_torneio', 'deface').gt('pontos_individuais', 0).order('pontos_individuais', desc=True).limit(10).execute()

            if not jogadores_result.data:
                # Fallback: buscar da tabela usuarios
                print("   ⚠️ Usando fallback para ranking individual...")
                jogadores_result = supabase_client.client.table('usuarios').select(
                    'uid, nick, deface_points_individual, dinheiro, shack'
                ).gt('deface_points_individual', 0).order('deface_points_individual', desc=True).limit(10).execute()

                # Converter formato para compatibilidade
                if jogadores_result.data:
                    jogadores_data = []
                    for jogador in jogadores_result.data:
                        jogadores_data.append({
                            'jogador_uid': jogador['uid'],
                            'nick': jogador['nick'],
                            'pontos_individuais': jogador['deface_points_individual']
                        })
                    jogadores_result.data = jogadores_data

            if jogadores_result.data:
                for posicao, jogador in enumerate(jogadores_result.data, 1):
                    if posicao in recompensas_individuais:
                        uid = jogador['jogador_uid']
                        nick = jogador['nick']
                        pontos = jogador['pontos_individuais']
                        recompensa = recompensas_individuais[posicao]

                        print(f"🥇 [RESET CORRIGIDO] Jogador {posicao}º: {nick} ({pontos} pontos)")

                        try:
                            # Buscar dados atuais do jogador
                            jogador_atual = supabase_client.client.table('usuarios').select(
                                'dinheiro, shack'
                            ).eq('uid', uid).execute()

                            if jogador_atual.data:
                                dados_atuais = jogador_atual.data[0]

                                # Calcular novos valores
                                novo_dinheiro = dados_atuais.get('dinheiro', 0) + recompensa['dinheiro']
                                novo_shack = dados_atuais.get('shack', 0) + recompensa['shacks']

                                # Atualizar jogador
                                supabase_client.client.table('usuarios').update({
                                    'dinheiro': novo_dinheiro,
                                    'shack': novo_shack
                                }).eq('uid', uid).execute()

                                print(f"   💰 {nick}: +${recompensa['dinheiro']}, +{recompensa['shacks']} SHACK")

                                jogadores_premiados.append({
                                    'posicao': posicao,
                                    'nick': nick,
                                    'pontos': pontos,
                                    'recompensa': recompensa
                                })

                        except Exception as e:
                            print(f"   ❌ Erro ao premiar {nick}: {e}")
        except Exception as e:
            print(f"❌ [RESET CORRIGIDO] Erro no ranking individual: {e}")
            import traceback
            traceback.print_exc()
        
        # ETAPA 2: Reset dos pontos EM TODAS AS TABELAS
        print("🔄 [RESET CORRIGIDO] Resetando pontos em todas as tabelas...")

        grupos_resetados = 0
        jogadores_resetados = 0
        torneio_resetados = 0

        try:
            # 1. Reset tabela torneio_pontuacoes (SISTEMA TTL - PRINCIPAL)
            print("🔄 [RESET CORRIGIDO] Resetando tabela torneio_pontuacoes...")
            torneio_reset_result = supabase_client.client.table('torneio_pontuacoes').update({
                'pontos_individuais': 0,
                'group_points': 0
            }).eq('tipo_torneio', 'deface').execute()
            torneio_resetados = len(torneio_reset_result.data) if torneio_reset_result.data else 0
            print(f"   ✅ {torneio_resetados} registros resetados na tabela torneio_pontuacoes")

            # 2. Reset grupos (sistema antigo - compatibilidade)
            print("🔄 [RESET CORRIGIDO] Resetando pontos dos grupos...")
            grupos_reset_result = supabase_client.client.table('grupos').update({
                'deface_points': 0,
                'tournament_points': 0  # Reset também tournament_points
            }).gt('deface_points', 0).execute()
            grupos_resetados = len(grupos_reset_result.data) if grupos_reset_result.data else 0
            print(f"   ✅ {grupos_resetados} grupos resetados")

            # 3. Reset jogadores (sistema antigo - compatibilidade)
            print("🔄 [RESET CORRIGIDO] Resetando pontos dos jogadores...")
            jogadores_reset_result = supabase_client.client.table('usuarios').update({
                'deface_points_individual': 0
            }).gt('deface_points_individual', 0).execute()
            jogadores_resetados = len(jogadores_reset_result.data) if jogadores_reset_result.data else 0
            print(f"   ✅ {jogadores_resetados} jogadores resetados")

            print(f"✅ [RESET CORRIGIDO] Reset completo:")
            print(f"   - Torneio TTL: {torneio_resetados} registros")
            print(f"   - Grupos: {grupos_resetados} resetados")
            print(f"   - Jogadores: {jogadores_resetados} resetados")

        except Exception as e:
            print(f"❌ [RESET CORRIGIDO] Erro no reset: {e}")
            import traceback
            traceback.print_exc()
        
        # Resultado final
        resultado = {
            "sucesso": True,
            "mensagem": f"Reset de torneios de deface concluído com sucesso!",
            "resultados": {
                "grupos_premiados": grupos_premiados,
                "jogadores_premiados": jogadores_premiados,
                "grupos_resetados": grupos_resetados,
                "jogadores_resetados": jogadores_resetados
            }
        }
        
        print(f"🎉 [RESET CORRIGIDO] Concluído: {len(grupos_premiados)} grupos e {len(jogadores_premiados)} jogadores premiados")
        return resultado
        
    except Exception as e:
        print(f"❌ [RESET CORRIGIDO] Erro geral: {e}")
        import traceback
        traceback.print_exc()
        return {"sucesso": False, "mensagem": f"Erro no reset: {str(e)}"}

def main():
    """Teste da função corrigida"""
    print("🧪 TESTANDO RESET CORRIGIDO")
    print("=" * 40)
    
    resultado = reset_deface_tournaments_corrigido()
    
    print(f"\n📊 RESULTADO:")
    print(f"   Sucesso: {resultado.get('sucesso', False)}")
    print(f"   Mensagem: {resultado.get('mensagem', 'N/A')}")
    
    if resultado.get('resultados'):
        stats = resultado['resultados']
        print(f"   Grupos premiados: {len(stats.get('grupos_premiados', []))}")
        print(f"   Jogadores premiados: {len(stats.get('jogadores_premiados', []))}")
        print(f"   Grupos resetados: {stats.get('grupos_resetados', 0)}")
        print(f"   Jogadores resetados: {stats.get('jogadores_resetados', 0)}")

if __name__ == "__main__":
    main()
