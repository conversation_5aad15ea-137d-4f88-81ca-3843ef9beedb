#!/usr/bin/env python3
"""
Script para remover emojis restantes do main.js
"""

import re

def remove_remaining_emojis():
    """Remove emojis restantes do main.js"""
    file_path = 'game/static/js/main.js'
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Substituições específicas
        replacements = [
            # Habilidades
            (r'📚 Habilidades Adquiridas', 'Habilidades Adquiridas'),
            (r'🔮 Habilidades NFT', 'Habilidades NFT'),
            (r'🔮 Suas Habilidades NFT', 'Suas Habilidades NFT'),
            
            # Logs
            (r'📜 Logs de Segurança', 'Logs de Segurança'),
            
            # Botões e ações
            (r'📢 Publicar Notícia', 'Publicar Notícia'),
            (r'🚫 Limite de conexões atingido!', 'Limite de conexões atingido!'),
            (r'🔙 Voltando ao scan\.\.\.', 'Voltando ao scan...'),
            (r'🎁 ', ''),  # Remove emoji de presente das mensagens
            (r'🔌 Fechar Todas', 'Fechar Todas'),
            (r'🔗 Conexões Ativas', 'Conexões Ativas'),
            (r'🚫 Limite de conexões atingido! Feche algumas conexões para executar novos exploits\.', 'Limite de conexões atingido! Feche algumas conexões para executar novos exploits.'),
            
            # Indicadores de nível
            (r'🟢 Níveis 1-9: Só dinheiro', 'Níveis 1-9: Só dinheiro'),
            (r'🔵 Níveis 10\+: Dinheiro \+ Shacks', 'Níveis 10+: Dinheiro + Shacks'),
        ]
        
        changes_made = 0
        
        for pattern, replacement in replacements:
            matches = re.findall(pattern, content)
            if matches:
                content = re.sub(pattern, replacement, content)
                changes_made += len(matches)
                print(f"✅ Substituído: '{pattern}' -> '{replacement}' ({len(matches)} ocorrências)")
        
        # Salvar se houve mudanças
        if changes_made > 0:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"\n✅ {file_path}: {changes_made} emojis removidos")
            return changes_made
        else:
            print(f"⚪ {file_path}: Nenhum emoji restante encontrado")
            return 0
            
    except Exception as e:
        print(f"❌ Erro ao processar {file_path}: {e}")
        return 0

def main():
    """Função principal"""
    print("🧹 Removendo emojis restantes do main.js...")
    print("=" * 50)
    
    total_removed = remove_remaining_emojis()
    
    print(f"\n📊 RESUMO:")
    print(f"   Total de emojis removidos: {total_removed}")
    
    if total_removed > 0:
        print("\n✅ Remoção final de emojis concluída!")
        print("🎯 Emojis preservados apenas em:")
        print("   - Dados do usuário na página principal (💰 Cash, ⭐ Nível, 🧠 Shack)")
        print("   - Ícones dos upgrades (se houver)")
    else:
        print("\n⚪ Nenhum emoji restante foi encontrado")
    
    print("\n🔍 Para verificar se ainda há emojis:")
    print("   1. Abra o jogo no navegador")
    print("   2. Navegue pelas seções")
    print("   3. Verifique se apenas os dados do usuário têm emojis")

if __name__ == "__main__":
    main()
