#!/usr/bin/env python3
"""
Script simples para limpar logs sem quebrar sintaxe
"""

import os
import re

def clean_file(file_path):
    """Limpa um arquivo removendo prints e console logs problemáticos"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Corrigir linhas problemáticas específicas
        fixes = [
            # Corrigir prints quebrados
            (r'original_# print de debug removido', 'original_print("🔧 Print restaurado para debug")'),
            (r'# print de debug removido} registros encontrados"\)', 'pass  # print de debug removido'),
            (r'# print de debug removido carregado!"\)', 'pass  # print de debug removido'),
            (r'# print de debug removido}"\)', 'pass  # print de debug removido'),
            (r'# print de debug removido}\)', 'pass  # print de debug removido'),
            
            # Remover linhas de print isoladas que podem causar problemas
            (r'^\s*# print de debug removido\s*$', ''),
            
            # Corrigir blocos else vazios
            (r'else:\s*\n\s*# print de debug removido\s*\n', 'else:\n    pass  # print de debug removido\n'),
        ]
        
        for pattern, replacement in fixes:
            content = re.sub(pattern, replacement, content, flags=re.MULTILINE)
        
        # Salvar se houve mudanças
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ {file_path}: Corrigido")
            return True
        else:
            print(f"⚪ {file_path}: Nenhuma correção necessária")
            return False
            
    except Exception as e:
        print(f"❌ Erro ao processar {file_path}: {e}")
        return False

def main():
    """Função principal"""
    print("🧹 Limpando logs e corrigindo sintaxe...")
    
    files_to_clean = [
        'app.py',
        'auth_routes.py',
        'game/routes.py',
        'game/new_models.py',
    ]
    
    total_fixed = 0
    
    for file_path in files_to_clean:
        if os.path.exists(file_path):
            if clean_file(file_path):
                total_fixed += 1
        else:
            print(f"⚠️ Arquivo não encontrado: {file_path}")
    
    print(f"\n📊 RESUMO:")
    print(f"   Arquivos corrigidos: {total_fixed}")
    print(f"   Total verificados: {len(files_to_clean)}")
    
    if total_fixed > 0:
        print("\n✅ Correções aplicadas com sucesso!")
        print("🔧 Agora o sistema deve funcionar sem erros de sintaxe")
    else:
        print("\n⚪ Nenhuma correção foi necessária")

if __name__ == "__main__":
    main()
