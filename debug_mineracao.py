#!/usr/bin/env python3
"""
Debug detalhado da função de mineração
"""

import os
import sys
from dotenv import load_dotenv
from datetime import datetime, timezone

# Carrega variáveis de ambiente
load_dotenv()

from database.supabase_client import supabase_client
from game import new_models as models

def debug_mineracao_detalhado():
    """Executa a função de mineração com logs extremamente detalhados"""
    print("🔍 DEBUG DETALHADO DA MINERAÇÃO")
    print("=" * 40)
    
    # Simular a função de mineração com logs
    print("📊 Iniciando processamento...")
    
    if not supabase_client.is_connected():
        print("❌ Database não disponível")
        return
    
    # Buscar usuários
    usuarios = supabase_client.get_ranking_usuarios(1000)
    if not usuarios:
        print("❌ Nenhum usuário encontrado")
        return
    
    print(f"👥 {len(usuarios)} usuários encontrados")
    
    agora = datetime.now(timezone.utc)
    timestamp_atual = agora.timestamp()
    
    print(f"⏰ Timestamp atual: {timestamp_atual}")
    
    processados = 0
    
    for i, usuario in enumerate(usuarios):
        print(f"\n👤 Processando usuário {i+1}/{len(usuarios)}")
        
        try:
            uid = usuario.get('uid')
            nick = usuario.get('nick', 'Unknown')
            
            print(f"   UID: {uid}")
            print(f"   Nick: {nick}")
            
            if not uid:
                print("   ❌ UID inválido, pulando")
                continue
            
            # Verificar nível da mineradora
            nivel_mineradora = usuario.get('nivel_mineradora', 1)
            print(f"   Nível mineradora: {nivel_mineradora}")
            
            if nivel_mineradora < 1:
                print(f"   ❌ Mineradora inativa (nível {nivel_mineradora}), pulando")
                continue
            
            # Verificar cache
            print(f"   🔍 Verificando cache...")
            if hasattr(models, 'MINERACAO_AUTOMATICA_CACHE'):
                cache = models.MINERACAO_AUTOMATICA_CACHE
                print(f"   Cache existe: {cache}")
                
                if uid in cache:
                    ultimo_processamento = cache[uid]
                    tempo_desde_ultimo = timestamp_atual - ultimo_processamento
                    print(f"   Último processamento: {ultimo_processamento}")
                    print(f"   Tempo desde último: {tempo_desde_ultimo}s")
                    
                    if tempo_desde_ultimo < 50:
                        print(f"   ❌ Cooldown ativo ({tempo_desde_ultimo:.1f}s < 50s), pulando")
                        continue
                    else:
                        print(f"   ✅ Cooldown expirado ({tempo_desde_ultimo:.1f}s >= 50s)")
                else:
                    print(f"   ✅ Usuário não está no cache")
            else:
                print(f"   ⚠️ Cache não existe")
            
            # Calcular dinheiro
            dinheiro_base = 15 + (nivel_mineradora - 1) * 20 + (nivel_mineradora - 1) * 5
            print(f"   💰 Dinheiro base: ${dinheiro_base}/min")
            
            # Aplicar NFT (simplificado para debug)
            dinheiro_por_minuto = dinheiro_base
            print(f"   💎 Dinheiro final: ${dinheiro_por_minuto}/min")
            
            dinheiro_gerado = int(dinheiro_por_minuto)
            print(f"   📊 Dinheiro a gerar: ${dinheiro_gerado}")
            
            if dinheiro_gerado <= 0:
                print(f"   ❌ Dinheiro gerado = 0, pulando")
                continue
            
            # Buscar saldo atual
            dinheiro_atual = usuario.get('dinheiro', 0)
            novo_dinheiro = dinheiro_atual + dinheiro_gerado
            print(f"   🏦 ${dinheiro_atual} + ${dinheiro_gerado} = ${novo_dinheiro}")
            
            # Simular atualização
            print(f"   📝 Simulando atualização no banco...")
            resultado = supabase_client.atualizar_usuario(uid, {
                'dinheiro': novo_dinheiro
            })
            
            print(f"   📊 Resultado: {resultado}")
            
            if resultado.get('sucesso'):
                processados += 1
                print(f"   ✅ Usuário processado com sucesso!")
                
                # Atualizar cache
                if hasattr(models, 'MINERACAO_AUTOMATICA_CACHE'):
                    models.MINERACAO_AUTOMATICA_CACHE[uid] = timestamp_atual
                    print(f"   🔄 Cache atualizado")
            else:
                print(f"   ❌ Falha na atualização: {resultado.get('erro', 'Erro desconhecido')}")
            
        except Exception as e:
            print(f"   ❌ Erro ao processar usuário: {e}")
            continue
    
    print(f"\n📈 RESULTADO FINAL:")
    print(f"   Usuários processados: {processados}")
    
    if processados > 0:
        print("✅ Função funcionaria corretamente!")
    else:
        print("❌ Nenhum usuário foi processado!")

if __name__ == "__main__":
    debug_mineracao_detalhado()
