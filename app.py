from flask import Flask
import os
import threading
import time
import datetime
from dotenv import load_dotenv

# Carrega variáveis de ambiente do arquivo .env
load_dotenv()

# Cria a instância da aplicação Flask
app = Flask(
    __name__, 
    template_folder='game/templates', 
    static_folder='game/static'
)

# Chave secreta para segurança da sessão do Flask
app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', 'shack-game-secret-key-2024-csrf-protection-enabled')
app.config['CSRF_DISABLED'] = True
app.config['SESSION_COOKIE_SECURE'] = False
app.config['SESSION_COOKIE_HTTPONLY'] = True
app.config['SESSION_COOKIE_SAMESITE'] = 'Lax'
app.config['PERMANENT_SESSION_LIFETIME'] = 3600

print("🚀 Iniciando SHACK Game Server...")

# Importações com tratamento de erro
try:
    print("📦 Carregando rotas...")
    from game.routes import main as main_blueprint
    from auth_routes import auth_bp
    print("✅ Blueprint importado com sucesso!")
    app.register_blueprint(main_blueprint)
    app.register_blueprint(auth_bp)
    print("✅ Rotas registradas com sucesso!")
except Exception as e:
    print(f"❌ Erro ao carregar rotas: {e}")
    print("🔧 Criando rota básica de fallback...")
    
    @app.route('/')
    def home():
        return "<h1>SHACK Game - Modo de Recuperação</h1><p>Servidor iniciado com funcionalidade limitada</p>"

# Importação dos modelos com tratamento de erro
try:
    print("📊 Carregando modelos...")
    print("🚀 MIGRANDO COMPLETAMENTE PARA SUPABASE!")
    print("🔄 Removendo dependência do Firebase...")
    
    # FORÇANDO uso EXCLUSIVO do sistema Supabase
    from game import new_models as models
    print("✅ Sistema Supabase carregado com sucesso!")
    
    # Verificação imediata da conexão
    from database.supabase_client import supabase_client
    if supabase_client.is_connected():
        print("✅ Conexão Supabase confirmada!")
        
        # Teste crítico das tabelas
        try:
            vitimas = supabase_client.get_todas_vitimas()
            print(f"✅ Tabela vitimas: {len(vitimas)} registros encontrados")
            
            habilidades = supabase_client.get_all_nft_skills()
            print(f"✅ Tabela habilidades_nft: {len(habilidades)} registros encontrados")
            
            print("🎉 MIGRAÇÃO SUPABASE CONCLUÍDA!")
            print("🔥 Firebase completamente removido do sistema!")
            
        except Exception as e:
            print(f"❌ ERRO: Tabelas não existem no Supabase!")
            print(f"   Detalhes: {e}")
            print("� SOLUÇÃO:")
            print("   1. Acesse https://supabase.com/dashboard")
            print("   2. Vá no SQL Editor do seu projeto")
            print("   3. Execute TODO o conteúdo do arquivo database/schema.sql")
            print("   4. Reinicie este servidor")
            exit(1)  # Para o servidor se não conseguir acessar o Supabase
    else:
        print("❌ ERRO CRÍTICO: Não conseguiu conectar com Supabase!")
        print("🔧 Verifique:")
        print("   - SUPABASE_URL no arquivo .env")
        print("   - SUPABASE_ANON_KEY no arquivo .env")
        print("   - Conexão com internet")
        exit(1)  # Para o servidor se não conseguir conectar
        
except ImportError as e:
    print(f"❌ ERRO: Não conseguiu importar new_models: {e}")
    print("🔧 Possíveis causas:")
    print("   - Módulo supabase não instalado: pip install supabase")
    print("   - Arquivo game/new_models.py com erro de sintaxe")
    exit(1)
    
except Exception as e:
    print(f"❌ ERRO INESPERADO ao carregar Supabase: {e}")
    print("🔧 Sistema parando para evitar uso do Firebase")
    exit(1)

# Inicializa os sistemas de segurança com tratamento de erro
try:
    print("🛡️ Carregando sistemas de segurança...")
    from security import InputValidation, CSRFProtection, RateLimiter
    from security import ActivityMonitor, FirebaseSecurity, GameSecurity, AdvancedGameSecurity, CacheSecurity
    
    # Inicializa cada módulo de segurança
    input_validation = InputValidation(app)
    csrf_protection = CSRFProtection(app)
    rate_limiter = RateLimiter(app)
    activity_monitor = ActivityMonitor(app, log_dir="logs")
    firebase_security = FirebaseSecurity(app)
    game_security = GameSecurity(app)
    advanced_game_security = AdvancedGameSecurity(app)
    cache_security = CacheSecurity(app)
    
    print("✅ Sistemas de segurança inicializados com sucesso!")
    
except Exception as e:
    print(f"⚠️ Erro ao carregar sistemas de segurança: {e}")
    print("🔧 Usando sistemas de segurança dummy...")
    
    # Cria versões dummy das classes de segurança
    class DummySecurity:
        def __init__(self, *args, **kwargs):
            pass
        def __getattr__(self, name):
            if name == 'generate_csrf_token':
                import secrets
                return lambda: secrets.token_hex(32)
            elif name == 'validate_csrf_token':
                return lambda token: True  # Sempre válido no modo dummy
            return lambda *args, **kwargs: None
    
    input_validation = DummySecurity()
    csrf_protection = DummySecurity()
    rate_limiter = DummySecurity()
    activity_monitor = DummySecurity()
    firebase_security = DummySecurity()
    game_security = DummySecurity()
    advanced_game_security = DummySecurity()
    cache_security = DummySecurity()

# Disponibilizar os sistemas de segurança para as rotas
app.csrf_protection = csrf_protection
app.game_security = game_security
app.advanced_game_security = advanced_game_security
app.firebase_security = firebase_security
app.activity_monitor = activity_monitor
app.cache_security = cache_security

# Carrega o sistema de habilidades NFT com tratamento de erro
if models:
    try:
        print("🎮 Carregando habilidades NFT...")
        
        # Verifica se é o novo sistema Supabase
        if hasattr(models, 'HABILIDADES_NFT'):
            if models.HABILIDADES_NFT:
                print(f"✅ Sistema de habilidades NFT carregado com sucesso!")
                print(f"\n=== HABILIDADES NFT CARREGADAS: {len(models.HABILIDADES_NFT)} ===")
                for skill_id, skill in models.HABILIDADES_NFT.items():
                    print(f"- {skill['nome']} (ID: {skill_id})")
                    print(f"  Descrição: {skill['descricao']}")
                    print(f"  Efeito: {skill['efeito']} | Preço: {skill['preco_shack']} Shacks")
                    print(f"  Estoque: {skill['estoque_atual']}/{skill['estoque_maximo']}")
                    print("  -----------------------------------")
            else:
                print("⚠️ Nenhuma habilidade NFT carregada - verifique database")
        else:
            # Sistema antigo do Firebase
            result = models.carregar_habilidades_nft_do_firestore()
            print(f"✅ Sistema de habilidades NFT (Firebase) carregado!")
            
    except Exception as e:
        print(f"⚠️ Erro ao carregar habilidades NFT: {str(e)}")
        print("🔧 Executando sem habilidades NFT")
else:
    print("⚠️ Models não disponível - habilidades NFT desabilitadas")

# --- THREADS DE BACKGROUND (OPCIONAIS) ---
def iniciar_threads_background():
    """Inicia threads de background apenas se os models estiverem disponíveis"""
    if not models:
        print("⚠️ Threads de background desabilitadas - models não disponível")
        return
    
    try:
        # --- SISTEMA DE MINERAÇÃO OFFLINE AUTOMÁTICO ---
        def processar_mineracao_periodica():
            """Função que roda em background para processar mineração"""
            print("🔄 Executando processamento inicial de mineração offline...")
            try:
                resultado = models.processar_mineracao_offline_todos_jogadores()
                print(f"✅ Processamento inicial concluído: {resultado}")
            except Exception as e:
                print(f"❌ Erro no processamento inicial: {str(e)}")
            
            # Loop principal - processa a cada 1 hora
            while True:
                try:
                    time.sleep(3600)  # Espera 1 hora
                    print("🔄 Executando processamento automático de mineração offline...")
                    resultado = models.processar_mineracao_offline_todos_jogadores()
                    print(f"✅ Processamento automático concluído: {resultado}")
                except Exception as e:
                    print(f"❌ Erro no processamento automático: {str(e)}")

        # --- SISTEMA DE RESET AUTOMÁTICO DO TORNEIO ---
        def verificar_reset_torneio_periodico():
            """Função que roda em background para verificar reset do torneio"""
            print("🏆 Sistema de reset automático do torneio iniciado!")
            
            while True:
                try:
                    time.sleep(1800)  # Espera 30 minutos
                    
                    # Verificar se deve resetar o torneio
                    now = datetime.datetime.now(datetime.timezone.utc)
                    print(f"🕐 Verificando reset do torneio às {now.strftime('%H:%M:%S UTC')}")
                    
                    resultado = models.auto_reset_tournament_if_needed()
                    
                    if resultado.get('grupos_premiados'):
                        print(f"🏆 Torneio resetado automaticamente!")
                        print(f"Grupos premiados: {len(resultado.get('grupos_premiados', []))}")
                        for grupo in resultado.get('grupos_premiados', []):
                            print(f"  {grupo['posicao']}º lugar: {grupo['grupo']} ({grupo['pontos']} pts)")
                    else:
                        print(f"✅ Verificação concluída: {resultado.get('mensagem', 'Status verificado')}")
                        
                except Exception as e:
                    print(f"❌ Erro na verificação do torneio: {str(e)}")

        # Inicia as threads
        mineracao_thread = threading.Thread(target=processar_mineracao_periodica, daemon=True)
        mineracao_thread.start()
        print("🚀 Sistema de mineração offline iniciado!")

        torneio_thread = threading.Thread(target=verificar_reset_torneio_periodico, daemon=True)
        torneio_thread.start()
        print("🏆 Sistema de reset automático do torneio iniciado!")
        
    except Exception as e:
        print(f"⚠️ Erro ao iniciar threads de background: {e}")

# Inicia threads apenas se tudo estiver funcionando
iniciar_threads_background()

# === ROTAS PARA GERENCIAMENTO DE CONEXÕES ===

@app.route('/api/conexoes/status', methods=['POST'])
def api_status_conexoes():
    """Retorna status detalhado das conexões de um jogador"""
    try:
        from flask import request, jsonify
        data = request.get_json()
        uid = data.get('uid')
        
        if not uid:
            return jsonify({"sucesso": False, "mensagem": "UID não fornecido"})
        
        resultado = models.obter_detalhes_conexoes_para_ui(uid)
        return jsonify(resultado)
    except Exception as e:
        print(f"Erro na API status conexões: {e}")
        return jsonify({"sucesso": False, "mensagem": str(e)})

@app.route('/api/conexoes/fechar-todas', methods=['POST'])
def api_fechar_todas_conexoes():
    """Fecha todas as conexões ativas de um jogador"""
    try:
        from flask import request, jsonify
        data = request.get_json()
        uid = data.get('uid')
        
        if not uid:
            return jsonify({"sucesso": False, "mensagem": "UID não fornecido"})
        
        resultado = models.fechar_todas_conexoes(uid)
        return jsonify(resultado)
    except Exception as e:
        print(f"Erro na API fechar todas conexões: {e}")
        return jsonify({"sucesso": False, "mensagem": str(e)})

@app.route('/api/conexoes/fechar', methods=['POST'])
def api_fechar_conexao():
    """Fecha uma conexão específica"""
    try:
        from flask import request, jsonify
        data = request.get_json()
        uid = data.get('uid')
        alvo_ip = data.get('alvo_ip')
        
        if not uid or not alvo_ip:
            return jsonify({"sucesso": False, "mensagem": "UID e IP do alvo são obrigatórios"})
        
        resultado = models.fechar_conexao_especifica(uid, alvo_ip)
        return jsonify(resultado)
    except Exception as e:
        print(f"Erro na API fechar conexão: {e}")
        return jsonify({"sucesso": False, "mensagem": str(e)})

# === ROTAS DA API - HABILIDADES NFT ===

@app.route('/api/habilidades-nft/disponiveis', methods=['GET'])
def api_habilidades_nft_disponiveis():
    """Retorna todas as habilidades NFT disponíveis"""
    try:
        from flask import jsonify
        resultado = models.get_habilidades_nft_disponiveis()
        return jsonify(resultado)
    except Exception as e:
        print(f"Erro na API habilidades NFT disponíveis: {e}")
        from flask import jsonify
        return jsonify({"sucesso": False, "mensagem": str(e)})

@app.route('/api/habilidades-nft/comprar', methods=['POST'])
def api_comprar_habilidade_nft():
    """Compra uma habilidade NFT"""
    try:
        from flask import request, jsonify
        data = request.get_json()
        uid = data.get('uid')
        skill_id = data.get('skill_id')
        
        if not uid or not skill_id:
            return jsonify({"sucesso": False, "mensagem": "UID e skill_id são obrigatórios"})
        
        resultado = models.comprar_habilidade_nft(uid, skill_id)
        return jsonify(resultado)
    except Exception as e:
        print(f"Erro na API comprar habilidade NFT: {e}")
        return jsonify({"sucesso": False, "mensagem": str(e)})

@app.route('/api/habilidades-nft/jogador', methods=['POST'])
def api_habilidades_jogador():
    """Retorna as habilidades NFT de um jogador"""
    try:
        from flask import request, jsonify
        data = request.get_json()
        uid = data.get('uid')
        
        if not uid:
            return jsonify({"sucesso": False, "mensagem": "UID é obrigatório"})
        
        resultado = models.verificar_efeitos_habilidades_jogador(uid)
        return jsonify(resultado)
    except Exception as e:
        print(f"Erro na API habilidades do jogador: {e}")
        return jsonify({"sucesso": False, "mensagem": str(e)})

@app.route('/api/habilidades-nft/estatisticas', methods=['GET'])
def api_estatisticas_habilidades_nft():
    """Retorna estatísticas das habilidades NFT"""
    try:
        from flask import jsonify
        resultado = models.get_estatisticas_habilidades_nft()
        return jsonify(resultado)
    except Exception as e:
        print(f"Erro na API estatísticas habilidades NFT: {e}")
        from flask import jsonify
        return jsonify({"sucesso": False, "mensagem": str(e)})

@app.route('/api/admin/habilidades-nft/reestocar', methods=['POST'])
def api_admin_reestocar_habilidades():
    """Reestoca todas as habilidades NFT (função administrativa)"""
    try:
        from flask import request, jsonify
        data = request.get_json()
        skill_id = data.get('skill_id')  # Se específica, senão reestoca todas
        
        if skill_id:
            resultado = models.reestocar_habilidade_nft(skill_id)
        else:
            resultado = models.reestocar_todas_habilidades_nft()
        
        return jsonify(resultado)
    except Exception as e:
        print(f"Erro na API admin reestocar: {e}")
        return jsonify({"sucesso": False, "mensagem": str(e)})

print("🌐 Servidor pronto para inicializar!")
print("🔗 Acesse: http://localhost:5000")

if __name__ == '__main__':
    # app.run() inicia o servidor de desenvolvimento
    print("🚀 Iniciando servidor Flask...")
    try:
        app.run(host='0.0.0.0', port=5000, debug=True)
    except Exception as e:
        print(f"❌ Erro ao iniciar servidor: {e}")
        print("🔧 Tente executar novamente ou verifique se a porta 5000 está livre")
