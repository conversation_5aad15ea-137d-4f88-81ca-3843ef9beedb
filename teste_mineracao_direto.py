#!/usr/bin/env python3
"""
Teste direto da função de mineração automática
"""

import sys
import os
from dotenv import load_dotenv

# Carrega variáveis de ambiente
load_dotenv()

def main():
    print("🔧 TESTE DIRETO DA MINERAÇÃO AUTOMÁTICA")
    print("=" * 50)
    
    try:
        # Importar módulos
        print("📦 Importando módulos...")
        from database.supabase_client import supabase_client
        from game import new_models as models
        print("✅ Módulos importados com sucesso")
        
        # Verificar conexão
        print("\n🔗 Verificando conexão...")
        if not supabase_client.is_connected():
            print("❌ Supabase não conectado!")
            return
        print("✅ Supabase conectado")
        
        # Verificar se há usuários com mineradora
        print("\n👥 Verificando usuários com mineradora...")
        result = supabase_client.client.table('usuarios').select(
            'uid, nick, dinheiro, nivel_mineradora'
        ).gte('nivel_mineradora', 1).limit(3).execute()
        
        miners = result.data if result.data else []
        print(f"⛏️ Encontrados {len(miners)} mineradores")
        
        if not miners:
            print("❌ Nenhum minerador encontrado!")
            print("🔧 Criando um minerador de teste...")
            
            # Criar minerador de teste
            test_miner = {
                'uid': 'test-miner-123',
                'nick': 'TestMiner',
                'email': '<EMAIL>',
                'dinheiro': 100,
                'nivel_mineradora': 1,
                'ultimo_dinheiro_mineracao_timestamp': None
            }
            
            insert_result = supabase_client.client.table('usuarios').insert(test_miner).execute()
            if insert_result.data:
                print("✅ Minerador de teste criado!")
                miners = [insert_result.data[0]]
            else:
                print("❌ Falha ao criar minerador de teste")
                return
        
        # Mostrar mineradores encontrados
        print("\n⛏️ Mineradores encontrados:")
        for i, miner in enumerate(miners):
            nick = miner.get('nick', 'Unknown')
            level = miner.get('nivel_mineradora', 1)
            money = miner.get('dinheiro', 0)
            print(f"   {i+1}. {nick} - Nível {level} - ${money}")
        
        # Executar função de mineração
        print("\n⚡ EXECUTANDO FUNÇÃO DE MINERAÇÃO...")
        print("-" * 40)
        
        resultado = models.processar_mineracao_automatica_dinheiro()
        
        print("-" * 40)
        print(f"\n📊 RESULTADO:")
        print(f"   Sucesso: {resultado.get('sucesso', False)}")
        print(f"   Jogadores processados: {resultado.get('jogadores_processados', 0)}")
        print(f"   Total gerado: ${resultado.get('total_dinheiro_gerado', 0)}")
        print(f"   Mensagem: {resultado.get('mensagem', 'N/A')}")
        
        # Verificar se houve mudanças
        if resultado.get('sucesso') and resultado.get('jogadores_processados', 0) > 0:
            print("\n✅ FUNÇÃO EXECUTOU COM SUCESSO!")
            
            # Verificar saldos atualizados
            print("\n💰 Verificando saldos atualizados...")
            for miner in miners:
                uid = miner.get('uid')
                nick = miner.get('nick', 'Unknown')
                old_money = miner.get('dinheiro', 0)
                
                # Buscar saldo atualizado
                updated_result = supabase_client.client.table('usuarios').select('dinheiro').eq('uid', uid).execute()
                if updated_result.data:
                    new_money = updated_result.data[0].get('dinheiro', 0)
                    difference = new_money - old_money
                    
                    if difference > 0:
                        print(f"   {nick}: ${old_money} → ${new_money} (+${difference}) ✅")
                    else:
                        print(f"   {nick}: ${new_money} (sem mudança)")
                else:
                    print(f"   {nick}: Erro ao buscar saldo atualizado")
            
            print("\n🎉 MINERAÇÃO AUTOMÁTICA FUNCIONANDO!")
        else:
            print("\n❌ FUNÇÃO FALHOU OU NÃO PROCESSOU NENHUM JOGADOR")
            
            if not resultado.get('sucesso'):
                print(f"   Erro: {resultado.get('mensagem', 'Erro desconhecido')}")
            
            if resultado.get('jogadores_processados', 0) == 0:
                print("   Possíveis causas:")
                print("   - Nenhum jogador com mineradora ativa")
                print("   - Problemas na busca de usuários")
                print("   - Erro na lógica de processamento")
        
    except Exception as e:
        print(f"❌ ERRO GERAL: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
