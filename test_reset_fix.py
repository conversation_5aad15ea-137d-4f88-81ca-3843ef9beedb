#!/usr/bin/env python3
"""
Test the fixed tournament reset functionality
"""

import sys
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_reset_function():
    print("🧪 TESTING FIXED TOURNAMENT RESET")
    print("=" * 45)
    
    try:
        # Import modules
        print("📦 Importing modules...")
        from database.supabase_client import supabase_client
        print("✅ Supabase client imported")
        
        # Check connection
        if not supabase_client.is_connected():
            print("❌ Supabase not connected!")
            return False
        print("✅ Database connected")
        
        # Test the reset function directly
        print("\n🏆 Testing reset_deface_tournaments function...")
        
        # Import the function using the corrected module
        from mineracao_corrigida import mineracao_automatica_corrigida
        print("✅ Mineracao function imported successfully")
        
        # Try to import the reset function
        try:
            # Import new_models but avoid the problematic initialization
            import importlib.util
            spec = importlib.util.spec_from_file_location("new_models", "game/new_models.py")
            new_models = importlib.util.module_from_spec(spec)
            
            # Manually set the supabase_client to avoid initialization issues
            new_models.supabase_client = supabase_client
            
            # Execute the module
            spec.loader.exec_module(new_models)
            
            print("✅ new_models imported successfully")
            
            # Test if the reset function exists
            if hasattr(new_models, 'reset_deface_tournaments'):
                print("✅ reset_deface_tournaments function found")
                
                # Test the function (dry run)
                print("\n🔄 Testing reset function...")
                resultado = new_models.reset_deface_tournaments()
                
                print(f"\n📊 RESULT:")
                print(f"   Success: {resultado.get('sucesso', False)}")
                print(f"   Message: {resultado.get('mensagem', 'N/A')}")
                
                if resultado.get('resultados'):
                    stats = resultado['resultados']
                    print(f"   Groups rewarded: {len(stats.get('grupos_premiados', []))}")
                    print(f"   Players rewarded: {len(stats.get('jogadores_premiados', []))}")
                    print(f"   Groups reset: {stats.get('grupos_resetados', 0)}")
                    print(f"   Players reset: {stats.get('jogadores_resetados', 0)}")
                
                if resultado.get('sucesso'):
                    print("\n🎉 RESET FUNCTION WORKS!")
                    return True
                else:
                    print(f"\n❌ Reset function failed: {resultado.get('mensagem')}")
                    return False
            else:
                print("❌ reset_deface_tournaments function not found")
                return False
                
        except Exception as import_error:
            print(f"❌ Error importing new_models: {import_error}")
            return False
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    success = test_reset_function()
    
    if success:
        print("\n🎯 TOURNAMENT RESET FIX STATUS: ✅ WORKING")
        print("\n📋 WHAT WAS FIXED:")
        print("• Fixed supabase_client.table() → supabase_client.client.table()")
        print("• Fixed table names: 'users' → 'usuarios', 'groups' → 'grupos'")
        print("• Fixed column names: 'shacks' → 'shack', 'deface_points' → 'deface_points_individual'")
        print("• Improved error handling and logging")
        print("• Enhanced reward distribution logic")
        
        print("\n🚀 THE MANUAL RESET BUTTON SHOULD NOW WORK!")
    else:
        print("\n❌ TOURNAMENT RESET FIX STATUS: FAILED")
        print("Additional debugging may be needed.")

if __name__ == "__main__":
    main()
