# SHACK 3.0 - Routes Module
# Importações principais
from flask import (render_template, request, Blueprint, jsonify, current_app, session, g)
from functools import wraps
import datetime
import time

# Importa os modelos SUPABASE (migração completa)
from . import new_models as models

# NOVO SISTEMA TTL - Importação global
try:
    from .tournament_ttl import torneio_manager
    ttl_system_available = True

except ImportError as e:
    ttl_system_available = False

main = Blueprint('main', __name__)

# Sistema migrado para Supabase

# Variável temporária para compatibilidade (algumas funções antigas ainda não migradas)
db = None

# === IMPORTAÇÃO DOS SISTEMAS DE SEGURANÇA ===
# Tenta importar os sistemas de segurança se estiverem disponíveis
try:
    from flask import current_app
    security_available = True
    
    def get_security_systems():
        """Função auxiliar para obter os sistemas de segurança do app"""
        try:
            return {
                'csrf_protection': getattr(current_app, 'csrf_protection', None),
                'game_security': getattr(current_app, 'game_security', None),
                'advanced_game_security': getattr(current_app, 'advanced_game_security', None),
                'firebase_security': getattr(current_app, 'firebase_security', None),
                'activity_monitor': getattr(current_app, 'activity_monitor', None)
            }
        except:
            return {}
            
except ImportError:
    security_available = False
    def get_security_systems():
        return {}

# --- DECORATOR DE AUTENTICAÇÃO MIGRADO PARA SESSÕES ---
def token_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # Debug da sessão
        pass  # print de debug removido
        
        # Verifica se o usuário está autenticado via sessão
        uid = session.get('user_uid')  # Usar user_uid conforme auth_routes.py
        if not uid:

            return jsonify({"sucesso": False, "mensagem": "Usuário não autenticado."}), 401

        # Verifica se o usuário ainda existe no banco
        try:
            jogador = models.get_jogador(uid)
            if not jogador:
                # Remove sessão inválida
                session.clear()

                return jsonify({"sucesso": False, "mensagem": "Sessão inválida."}), 401
            
            pass  # print de debug removido
            
            # Armazena informações do usuário no contexto da requisição
            g.user = {
                'uid': uid,
                'email': jogador.get('email', ''),
                'nick': jogador.get('nick', ''),
                'jogador': jogador
            }
            
        except Exception as e:

            # Log tentativa de sessão inválida
            security_systems = get_security_systems()
            if security_systems.get('activity_monitor'):
                security_systems['activity_monitor'].log_failed_login(
                    "session_validation", request.remote_addr
                )
            return jsonify({"sucesso": False, "mensagem": f"Erro de autenticação: {str(e)}"}), 401

        return f(uid, *args, **kwargs)

    return decorated_function

# Decorator adicional para ações que requerem proteção extra
def secure_game_action(action_type, cooldown_check=True, csrf_required=True):
    """
    Decorator combinado para ações críticas do jogo
    
    Usage: @secure_game_action('attack', cooldown_check=True, csrf_required=True)
    """
    def decorator(f):
        @wraps(f)
        @token_required
        def decorated_function(uid, *args, **kwargs):
            security_systems = get_security_systems()
            
            # Verificação CSRF se requerida - Versão melhorada
            if csrf_required and security_systems.get('csrf_protection') and not current_app.config.get('CSRF_DISABLED', False):
                csrf_protection = security_systems['csrf_protection']
                csrf_token = csrf_protection._extract_token_from_request()
                
                print(f"🔒 Token CSRF recebido: {csrf_token[:20] if csrf_token else 'None'}...")
                
                # Validação mais flexível
                if not csrf_protection.validate_csrf_token(csrf_token):
                    # Tentar gerar um novo token se não há um válido
                    if not csrf_token or csrf_token == 'dummy_token':

                        new_token = csrf_protection.generate_csrf_token()
                        return jsonify({
                            "sucesso": False, 
                            "mensagem": "Token CSRF necessário",
                            "csrf_token": new_token,
                            "codigo": "CSRF_TOKEN_REQUIRED"
                        }), 403
                    else:

                        return jsonify({"sucesso": False, "mensagem": "Token CSRF inválido"}), 403
                else:

            elif current_app.config.get('CSRF_DISABLED', False):
                print("ℹ️ CSRF protection desabilitado para desenvolvimento")
            else:
                print("ℹ️ CSRF protection não ativo ou não requerido")
            
            # Verificação de segurança avançada do jogo
            if security_systems.get('advanced_game_security'):
                ags = security_systems['advanced_game_security']
                
                # Verificar cooldown
                if cooldown_check and not ags._check_cooldown(uid, action_type):
                    cooldown_time = ags.user_cooldowns.get(uid, {}).get(action_type, 0) - time.time()
                    return jsonify({
                        "sucesso": False, 
                        "mensagem": f"Ação em cooldown. Aguarde {int(cooldown_time)} segundos."
                    }), 429
                
                # Verificar limites de ação
                if not ags._check_action_limit(uid, action_type):
                    return jsonify({
                        "sucesso": False, 
                        "mensagem": f"Limite de ações '{action_type}' excedido. Tente mais tarde."
                    }), 429
                
                # Registrar a ação
                ags._record_action(uid, action_type)
                
                # Definir cooldown
                if cooldown_check:
                    ags._set_cooldown(uid, action_type)
                
                # Analisar comportamento
                ags._analyze_user_behavior(uid)
            
            return f(uid, *args, **kwargs)
        return decorated_function
    return decorator


# --- ROTAS DE PÁGINA (Renderizam o "esqueleto" HTML) ---

@main.route("/")
@main.route('/<path:path>') # Rota "Catch-All"
def index(path=None):
    return render_template("index.html")

# As rotas abaixo podem ser removidas se você não pretende ter URLs diretas para cada seção.
# Mantive-as por consistência com seu arquivo original.
@main.route("/loja")
def page_appstore():
    return render_template("index.html")

@main.route("/mercado-negro")
def page_mercado():
    return render_template("index.html")

@main.route("/grupo")
def page_group():
    return render_template("index.html")

@main.route("/ranking")
def page_ranking():
    return render_template("index.html")

@main.route("/scanear")
def page_scan():
    return render_template("index.html")


# --- ROTAS DE API (Lidam com a lógica e os dados) ---

@main.route("/api/csrf-token", methods=["GET"])
def api_get_csrf_token():
    """Endpoint para obter token CSRF"""
    try:
        security_systems = get_security_systems()
        csrf_protection = security_systems.get('csrf_protection')
        
        if csrf_protection:
            token = csrf_protection.generate_csrf_token()
            print(f"🔒 Token CSRF gerado: {token[:10]}...")
            return jsonify({
                "sucesso": True,
                "csrf_token": token
            })
        else:
            # Se CSRF protection não estiver ativo, retornar token dummy

            return jsonify({
                "sucesso": True,
                "csrf_token": "dummy_token"
            })
    except Exception as e:

        return jsonify({
            "sucesso": False,
            "mensagem": f"Erro ao gerar token CSRF: {str(e)}"
        }), 500

@main.route("/api/test-csrf", methods=["POST"])
@secure_game_action('test', cooldown_check=False, csrf_required=False)  # CSRF desabilitado para teste
def api_test_csrf(uid):
    """Endpoint de teste - sem CSRF por enquanto"""
    return jsonify({
        "sucesso": True,
        "mensagem": "Sistema funcionando sem CSRF!",
        "user_id": uid,
        "csrf_status": "disabled_for_stability"
    })

@main.route("/api/cadastro", methods=["POST"])
def api_cadastro():
    data = request.get_json()
    if not data or not all(k in data for k in ["uid", "nick", "email"]):
        return jsonify({"sucesso": False, "mensagem": "Dados incompletos."}), 400

    # Chama a nova função do models para Supabase
    resultado = models.criar_jogador(data["uid"], data["nick"], data["email"])
    return jsonify(resultado)

@main.route("/api/jogador")
@token_required
def api_get_jogador_data(uid):
    """API para buscar todos os dados do jogador logado."""
    try:
        jogador = models.get_jogador(uid)
        if not jogador:
            return jsonify({"sucesso": False, "mensagem": "Jogador não encontrado no banco de dados."}), 404
        
        jogador["xp_necessario"] = models.calcular_xp_necessario(jogador.get("nivel", 1))
        return jsonify({"sucesso": True, "jogador": jogador})
    except Exception as e:
        # Em produção, um log detalhado do erro seria o ideal aqui.
        return jsonify({"sucesso": False, "mensagem": f"Erro interno: {str(e)}"}), 500

@main.route("/api/appstore")
@token_required
def api_get_appstore_items(uid):
    """API para listar os itens e custos da loja de apps."""
    jogador = models.get_jogador(uid)
    if not jogador:
        return jsonify({"sucesso": False, "mensagem": "Jogador não encontrado."}), 404
    
    itens_loja = ["cpu", "firewall", "antivirus", "malware_kit", "bruteforce", "bankguard", "proxyvpn"]
    custos = {item: models.calcular_custo_upgrade(item, jogador.get(item, 1)) for item in itens_loja}
    return jsonify({"sucesso": True, "custos": custos})

@main.route("/api/appstore/comprar", methods=["POST"])
@secure_game_action('purchase', cooldown_check=False, csrf_required=True)
def api_buy_appstore_item(uid):
    """API para comprar/fazer upgrade de um item. Agora com validações de segurança."""
    data = request.get_json()
    item_comprado = data.get("item")
    quantidade = data.get("quantidade", 1)  # Default para 1 upgrade
    
    if not item_comprado:
        return jsonify({"sucesso": False, "mensagem": "Item não especificado."}), 400
    
    # Validação rigorosa de quantidade
    if not isinstance(quantidade, int):
        return jsonify({"sucesso": False, "mensagem": "Quantidade deve ser um número inteiro."}), 400
    
    if quantidade < 1 or quantidade > 50:  # Limite máximo de 50 upgrades por vez
        return jsonify({"sucesso": False, "mensagem": "Quantidade inválida (1-50)."}), 400
    
    # Lista de itens válidos (prevenção contra manipulação)
    items_validos = ["cpu", "firewall", "antivirus", "malware_kit", "bruteforce", "bankguard", "proxyvpn"]
    if item_comprado not in items_validos:
        return jsonify({"sucesso": False, "mensagem": "Item inválido."}), 400

    # A lógica de compra com múltiplos upgrades
    resultado = models.comprar_upgrade(uid, item_comprado, quantidade)
    return jsonify(resultado)

@main.route("/api/scan")
@token_required
def api_scan_ips(uid):
    """
    API OTIMIZADA que retorna uma lista de JOGADORES REAIS como alvos,
    com base no nível do jogador que está escaneando.
    """
    try:
        # Chama a nova função do models.py, passando o UID do atacante
        alvos_encontrados = models.encontrar_alvos_para_jogador(uid)
        
        # A lógica para pegar os detalhes já foi feita no models.py
        return jsonify({"sucesso": True, "alvos": alvos_encontrados})
    except Exception as e:
        # Em produção, um log detalhado do erro seria o ideal aqui.
        return jsonify({"sucesso": False, "mensagem": f"Erro interno: {str(e)}"}), 500


@main.route("/api/scan/ip/<ip>")
@token_required
def api_scan_specific_ip(uid, ip):
    """
    API para scan avançado - busca por IP específico
    """
    try:
        # Validar formato do IP
        import re
        ip_pattern = r'^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$'
        if not re.match(ip_pattern, ip):
            return jsonify({"sucesso": False, "mensagem": "Formato de IP inválido"}), 400
        
        # Buscar jogador específico pelo IP
        alvo = models.buscar_jogador_por_ip(ip)
        
        if alvo:
            return jsonify({"sucesso": True, "alvo": alvo})
        else:
            return jsonify({"sucesso": False, "mensagem": "IP não encontrado na rede"})
    except Exception as e:
        return jsonify({"sucesso": False, "mensagem": f"Erro interno: {str(e)}"}), 500


@main.route("/api/alvo/<ip>/exploit", methods=["POST"])
@secure_game_action('attack', cooldown_check=True, csrf_required=True)
def api_action_exploit(uid, ip):
    """API para a primeira etapa: tentar o exploit. Agora com proteções de segurança."""
    # Validação adicional de entrada
    import re
    if not re.match(r'^(\d{1,3}\.){3}\d{1,3}$', ip):
        return jsonify({"sucesso": False, "mensagem": "Formato de IP inválido"}), 400
    
    # Verificar se não está atacando a si mesmo
    atacante = models.get_jogador(uid)
    if atacante and atacante.get('ip') == ip:
        return jsonify({"sucesso": False, "mensagem": "Não é possível atacar a si mesmo"}), 400
    
    resultado = models.realizar_exploit(uid, ip)
    return jsonify(resultado)

@main.route("/api/alvo/transferir", methods=["POST"])
@secure_game_action('transfer', cooldown_check=True, csrf_required=True)
def api_action_transferir(uid):
    """API para a segunda etapa: transferir o dinheiro. Agora com validações de segurança."""
    try:
        data = request.get_json()
        if not data:
            return jsonify({"sucesso": False, "mensagem": "Dados não fornecidos."}), 400
            
        alvo_uid = data.get("alvo_uid")
        porcentagem = data.get("porcentagem")

        print(f"DEBUG: Transferência - Atacante: {uid}, Alvo: {alvo_uid}, Porcentagem: {porcentagem}")

        if not all([alvo_uid, porcentagem]):
            return jsonify({"sucesso": False, "mensagem": "Dados incompletos."}), 400

        # Validação rigorosa de tipos e intervalos
        if not isinstance(porcentagem, (int, float)):
            return jsonify({"sucesso": False, "mensagem": "Porcentagem deve ser um número."}), 400
            
        if porcentagem <= 0 or porcentagem > 100:
            return jsonify({"sucesso": False, "mensagem": "Porcentagem deve estar entre 1 e 100."}), 400
        
        # Verificar se não está transferindo para si mesmo
        if uid == alvo_uid:
            return jsonify({"sucesso": False, "mensagem": "Não é possível transferir para si mesmo."}), 400

        resultado = models.transferir_dinheiro_alvo(uid, alvo_uid, porcentagem)
        return jsonify(resultado)
        
    except Exception as e:
        print(f"ERRO na transferência: {str(e)}")
        return jsonify({"sucesso": False, "mensagem": f"Erro interno: {str(e)}"}), 500

# A rota de DEFACE precisa ser ajustada para receber o UID do alvo
@main.route("/api/alvo/deface", methods=["POST"])
@secure_game_action('deface', cooldown_check=True, csrf_required=True)
def api_action_deface(uid):
    """API para realizar deface. Agora com proteções anti-cheat."""
    try:
        data = request.get_json()
        if not data:
            return jsonify({"sucesso": False, "mensagem": "Dados não fornecidos."}), 400
            
        alvo_uid = data.get("alvo_uid")
        if not alvo_uid:
            return jsonify({"sucesso": False, "mensagem": "UID do alvo não fornecido."}), 400
        
        # Verificar se não está fazendo deface em si mesmo
        if uid == alvo_uid:
            return jsonify({"sucesso": False, "mensagem": "Não é possível fazer deface em si mesmo."}), 400
            
        print(f"DEBUG: Realizando deface - Atacante: {uid}, Alvo: {alvo_uid}")  # Debug
        
        resultado = models.realizar_deface(uid, alvo_uid)
        return jsonify(resultado)
        
    except Exception as e:
        print(f"ERRO na rota de deface: {str(e)}")  # Debug
        return jsonify({"sucesso": False, "mensagem": f"Erro interno: {str(e)}"}), 500

# --- ROTAS DE GRUPO E TORNEIO ---

@main.route("/api/grupo")
@token_required
def api_get_grupo_data(uid):
    # A função no models.py deve buscar o jogador, ver seu ID de grupo e buscar os dados do grupo.
    resultado = models.get_dados_completos_grupo(uid) 
    return jsonify(resultado)

@main.route("/api/torneio/status")
@token_required
def api_torneio_status(uid):
    """Obter status atual do torneio de upgrade diário - NOVO SISTEMA TTL"""
    try:
        # Verificar se o sistema TTL está disponível
        if not ttl_system_available:
            return jsonify({"sucesso": False, "mensagem": "Sistema TTL não disponível"})
        
        # Obter status atual (sem necessidade de verificação de reset)
        status = torneio_manager.get_status_torneio('upgrade')
        ranking = torneio_manager.get_ranking_upgrade()
        
        # Obter app do dia
        app_do_dia = torneio_manager.get_app_do_dia()
        
        # Obter pontos individuais do jogador
        pontos_jogador = torneio_manager.get_pontos_jogador(uid, 'upgrade')
        pontos_individuais = pontos_jogador.get('pontos_individuais', 0) if pontos_jogador['sucesso'] else 0
        
        return jsonify({
            "sucesso": True,
            "status": status,
            "ranking": ranking,
            "app_do_dia": app_do_dia,
            "pontos_individuais": pontos_individuais,
            "tipo_torneio": "upgrade"
        })
    except Exception as e:
        return jsonify({"sucesso": False, "mensagem": f"Erro: {str(e)}"})

@main.route("/api/torneio/reset", methods=["POST"])
@token_required
def api_reset_torneio_manual(uid):
    """Reset manual do torneio de upgrade (apenas para testes ou administradores)"""
    try:
        # Verificar se o usuário é admin (opcional)
        jogador = models.get_jogador(uid)
        if not jogador:
            return jsonify({"sucesso": False, "mensagem": "Jogador não encontrado"})
        
        # Por segurança, só permite reset se já passou das 00:00
        if not models.should_reset_tournament():
            return jsonify({"sucesso": False, "mensagem": "Ainda não é hora do reset diário"})
        
        resultado = models.reset_daily_tournament()
        return jsonify(resultado)
    except Exception as e:
        return jsonify({"sucesso": False, "mensagem": f"Erro: {str(e)}"})

@main.route("/api/torneio/app-do-dia")
@token_required
def api_app_do_dia(uid):
    """Obter o app específico do torneio de hoje - NOVO SISTEMA TTL"""
    try:
        # Verificar se o sistema TTL está disponível
        if not ttl_system_available:
            return jsonify({"sucesso": False, "mensagem": "Sistema TTL não disponível"})
        
        app_do_dia = torneio_manager.get_app_do_dia()
        
        # Nomes amigáveis para os apps
        nomes_apps = {
            'cpu': 'CPU',
            'ram': 'RAM', 
            'firewall': 'Firewall',
            'antivirus': 'Antivírus',
            'malware_kit': 'Malware Kit',
            'bruteforce': 'Bruteforce',
            'bankguard': 'BankGuard',
            'proxyvpn': 'ProxyVPN'
        }
        
        return jsonify({
            "sucesso": True,
            "app_do_dia": app_do_dia,
            "nome_amigavel": nomes_apps.get(app_do_dia, app_do_dia.title()),
            "descricao": f"Hoje o torneio é focado em upgrades de {nomes_apps.get(app_do_dia, app_do_dia)}. Cada upgrade neste app dá 1 ponto para você e seu grupo!"
        })
    except Exception as e:
        return jsonify({"sucesso": False, "mensagem": f"Erro: {str(e)}"})

@main.route("/api/torneio/ranking-jogadores")
@token_required  
def api_ranking_jogadores_deface(uid):
    """API para ranking individual de jogadores por pontos de deface - NOVO SISTEMA TTL"""
    try:
        # Verificar se o sistema TTL está disponível
        if not ttl_system_available:
            return jsonify({"sucesso": False, "mensagem": "Sistema TTL não disponível"})
        
        # Obter ranking individual de deface (sem necessidade de verificação de reset)
        ranking_result = torneio_manager.get_ranking_deface()
        
        if not ranking_result['sucesso']:
            return jsonify(ranking_result)
        
        # Obter tempo restante do torneio
        status_result = torneio_manager.get_status_torneio('deface')
        tempo_restante = status_result.get('tempo_restante', {}) if status_result['sucesso'] else {}
        
        # Definir recompensas para jogadores individuais
        recompensas = {
            "primeiro": {"dinheiro": 50000, "shacks": 500},
            "segundo": {"dinheiro": 30000, "shacks": 300}, 
            "terceiro": {"dinheiro": 20000, "shacks": 200},
            "top5": {"dinheiro": 15000, "shacks": 150},
            "top10": {"dinheiro": 10000, "shacks": 100}
        }
        
        return jsonify({
            "sucesso": True,
            "data": {
                "jogadores": ranking_result.get('ranking', []),
                "tempo_restante": tempo_restante,
                "recompensas": recompensas
            }
        })
    except Exception as e:

        return jsonify({"sucesso": False, "mensagem": f"Erro: {str(e)}"})

@main.route("/api/grupo/criar", methods=["POST"])
@token_required
def api_criar_grupo(uid):
    try:
        data = request.get_json()
        print(f"DEBUG: Dados recebidos para criar grupo: {data}")
        nome_grupo = data.get("nome") or data.get("group_name")  # Aceita ambos os formatos
        id_grupo_personalizado = data.get("id_grupo") or data.get("group_id")  # ID personalizado OBRIGATÓRIO
        print(f"DEBUG: Nome do grupo extraído: '{nome_grupo}'")
        print(f"DEBUG: ID personalizado: '{id_grupo_personalizado}'")
        
        if not nome_grupo:
            return jsonify({"sucesso": False, "mensagem": "Nome do grupo é obrigatório."}), 400
            
        if not id_grupo_personalizado:
            return jsonify({"sucesso": False, "mensagem": "ID personalizado do grupo é obrigatório."}), 400
            
        # Converte ID para maiúsculo e remove espaços
        id_grupo_personalizado = id_grupo_personalizado.upper().strip()
            
        resultado = models.criar_grupo(uid, nome_grupo, id_grupo_personalizado)
        return jsonify(resultado)
    except Exception as e:
        print(f"ERRO ao criar grupo: {str(e)}")
        return jsonify({"sucesso": False, "mensagem": f"Erro interno: {str(e)}"}), 500
    except Exception as e:
        print(f"ERRO ao criar grupo: {str(e)}")
        return jsonify({"sucesso": False, "mensagem": f"Erro interno: {str(e)}"}), 500

@main.route("/api/grupo/entrar", methods=["POST"])
@token_required
def api_entrar_grupo(uid):
    try:
        data = request.get_json()
        print(f"DEBUG: Dados recebidos para entrar em grupo: {data}")
        id_grupo = data.get("id_grupo") or data.get("group_id")  # Aceita ambos os formatos
        print(f"DEBUG: ID do grupo extraído: '{id_grupo}'")
        
        if not id_grupo:
            return jsonify({"sucesso": False, "mensagem": "ID do grupo não fornecido."}), 400
            
        resultado = models.entrar_em_grupo(uid, id_grupo.upper())
        return jsonify(resultado)
    except Exception as e:
        print(f"ERRO ao entrar em grupo: {str(e)}")
        return jsonify({"sucesso": False, "mensagem": f"Erro interno: {str(e)}"}), 500

@main.route("/api/grupo/sair", methods=["POST"])
@token_required
def api_sair_grupo(uid):
    try:
        print(f"DEBUG: Jogador {uid} tentando sair do grupo")
        resultado = models.sair_do_grupo(uid)
        print(f"DEBUG: Resultado sair do grupo: {resultado}")
        return jsonify(resultado)
    except Exception as e:
        print(f"ERRO ao sair do grupo: {str(e)}")
        return jsonify({"sucesso": False, "mensagem": f"Erro interno: {str(e)}"}), 500

# --- ROTA DE RANKING ---

@main.route("/api/ranking/deface")
@token_required
def api_ranking_deface(uid):
    """API para buscar os dados do ranking de grupos e individual."""
    try:
        # Buscar ranking individual de deface
        ranking_individual_result = models.get_individual_deface_ranking()
        ranking_jogadores_formatado = []

        if ranking_individual_result.get('sucesso'):
            ranking_jogadores_formatado = [{
                "nick": j.get("nick", ""),
                "pontos": j.get("deface_points", 0),
                "grupo_nome": j.get("grupo_nome", "Sem grupo")
            } for j in ranking_individual_result.get('ranking_jogadores', [])]

        # Buscar ranking de grupos
        ranking_grupos_result = models.get_groups_deface_ranking()
        ranking_grupos = []

        if ranking_grupos_result and ranking_grupos_result.get('sucesso'):
            grupos_data = ranking_grupos_result.get('data', {}).get('grupos', [])
            ranking_grupos = [{
                "nome": g.get("nome", ""),
                "pontos": g.get("pontos", 0),
                "membros_count": len(g.get("membros", []))
            } for g in grupos_data[:10]]

        return jsonify({
            "sucesso": True,
            "ranking_grupos": ranking_grupos,
            "ranking_jogadores": ranking_jogadores_formatado
        })
    except Exception as e:
         return jsonify({"sucesso": False, "mensagem": f"Erro interno: {str(e)}"}), 500

@main.route("/api/mercado-negro/items")
@token_required
def api_get_mercado_negro_items(uid):
    """
    Retorna os itens do Mercado Negro com o preço do upgrade da mineradora
    calculado dinamicamente para o jogador atual.
    """
    try:
        from .new_models import MERCADO_NEGRO_ITEMS, get_jogador, calcular_custo_upgrade_mineradora
        import copy
        
        # Faz uma cópia para não alterar o dicionário original
        items_para_enviar = copy.deepcopy(MERCADO_NEGRO_ITEMS)

        # Para o upgrade da mineradora, calcula o preço dinamicamente
        jogador = get_jogador(uid)
        if jogador and "upgrade_miner_1" in items_para_enviar:
            nivel_atual = jogador.get("nivel_mineradora", 1)
            preco_dinamico = calcular_custo_upgrade_mineradora(nivel_atual)
            items_para_enviar["upgrade_miner_1"]["preco_shack"] = preco_dinamico

        return jsonify({"sucesso": True, "items": items_para_enviar})

    except Exception as e:
        return jsonify({"sucesso": False, "mensagem": f"Erro interno: {str(e)}"}), 500

@main.route("/api/mercado-negro/comprar", methods=["POST"])
@token_required
def api_buy_mercado_negro_item(uid):
    """
    Processa a compra de um item do Mercado Negro.
    Recebe o ID do item do frontend e chama a função de lógica no models.
    """
    item_id = request.get_json().get("item_id")
    if not item_id:
        return jsonify({"sucesso": False, "mensagem": "ID do item não fornecido."}), 400

    resultado = models.comprar_item_mercado_negro(uid, item_id)
    return jsonify(resultado)

@main.route("/api/chat/enviar", methods=["POST"])
@token_required
def api_enviar_mensagem(uid):
    """Recebe uma mensagem de chat do frontend e a salva com sistema anti-spam."""
    try:
        data = request.get_json()
        if not data:
            return jsonify({"sucesso": False, "mensagem": "Dados não fornecidos"}), 400

        texto = data.get("texto") or data.get("mensagem")  # Aceita ambos os formatos
        if not texto:
            return jsonify({"sucesso": False, "mensagem": "Texto da mensagem não fornecido"}), 400

        resultado = models.enviar_mensagem_chat(uid, texto)
        return jsonify(resultado)
    except Exception as e:
        return jsonify({"sucesso": False, "mensagem": f"Erro interno: {str(e)}"}), 500

@main.route("/api/chat/mensagens", methods=["GET"])
@token_required
def api_buscar_mensagens_chat(uid):
    """Busca mensagens recentes do chat."""
    try:
        limite = request.args.get('limite', 50, type=int)
        if limite > 100:
            limite = 100  # Máximo de 100 mensagens

        resultado = models.buscar_mensagens_chat(limite)
        return jsonify(resultado)
    except Exception as e:
        return jsonify({"sucesso": False, "mensagem": f"Erro interno: {str(e)}", "mensagens": []}), 500

@main.route("/api/alvo/<alvo_uid>/log")
@token_required
def api_get_alvo_log(uid, alvo_uid):
    """Retorna os logs de um jogador alvo, com timestamps corrigidos."""
    alvo_doc = db.collection('usuarios').document(alvo_uid).get()
    if not alvo_doc.exists:
        return jsonify({"sucesso": False, "mensagem": "Alvo não encontrado."}), 404
    
    logs_brutos = alvo_doc.to_dict().get('log', [])
    
    # Ordena e limita
    logs_ordenados = sorted(logs_brutos, key=lambda x: x.get('timestamp'), reverse=True)[:50]
    
    # Converte o objeto datetime para um número (timestamp) antes de enviar
    logs_formatados = []
    for log in logs_ordenados:
        log_copia = log.copy()
        timestamp = log_copia.get('timestamp')
        
        # Converte diferentes tipos de timestamp para float
        if hasattr(timestamp, 'timestamp'):  # DatetimeWithNanoseconds do Firestore
            log_copia['timestamp'] = timestamp.timestamp()
        elif isinstance(timestamp, datetime.datetime):  # datetime padrão
            log_copia['timestamp'] = timestamp.timestamp()
        elif not isinstance(timestamp, (int, float)):  # Caso não seja número
            log_copia['timestamp'] = 0  # Timestamp padrão para casos inválidos
            
        logs_formatados.append(log_copia)
        
    return jsonify({"sucesso": True, "logs": logs_formatados})

@main.route("/api/log")
@token_required
def api_get_meu_log(uid):
    """Retorna os logs do jogador autenticado, com timestamps corrigidos."""
    try:
        resultado = models.get_meu_log(uid)
        return jsonify(resultado)
    except Exception as e:
        return jsonify({"sucesso": False, "mensagem": f"Erro interno: {str(e)}"}), 500

@main.route("/api/log/limpar", methods=["POST"])
@token_required
def api_limpar_meu_log(uid):
    """Limpa todos os logs do jogador autenticado."""
    try:
        resultado = models.limpar_meu_log(uid)
        return jsonify(resultado)
    except Exception as e:
        return jsonify({"sucesso": False, "mensagem": f"Erro interno: {str(e)}"}), 500

# ROTA 1: Para o ranking de jogadores por NÍVEL
@main.route("/api/ranking/level")
@token_required
def api_ranking_level(uid):
    """API que busca e retorna APENAS o ranking de jogadores por nível."""
    try:
        # Usar função do Supabase
        jogadores = models.get_ranking_jogadores()
        
        # Ordenar por nível e formatar
        ranking_jogadores = []
        for j in sorted(jogadores, key=lambda x: x.get('nivel', 1), reverse=True)[:50]:
            jogador_info = {
                "nick": j.get("nick"), 
                "nivel": j.get("nivel", 1),
                "grupo_nome": j.get("grupo_nome"),
                "grupo": j.get("grupo"),
                "grupo_id": j.get("grupo_id")
            }
            ranking_jogadores.append(jogador_info)
        
        return jsonify({"sucesso": True, "ranking": ranking_jogadores})
    except Exception as e:
         return jsonify({"sucesso": False, "mensagem": f"Erro interno: {str(e)}"}), 500


# ROTA 2: Para o ranking de GRUPOS por DEFACE
@main.route("/api/ranking/groups")
@token_required
def api_ranking_groups(uid):
    """API que busca e retorna APENAS o ranking de grupos por pontos de deface."""
    try:
        # Usar função do Supabase para buscar ranking de grupos
        ranking_result = models.get_groups_deface_ranking()

        if not ranking_result or not ranking_result.get('sucesso'):
            return jsonify({"sucesso": True, "ranking_grupos": []})

        # Formatar dados para o frontend
        grupos_data = ranking_result.get('data', {}).get('grupos', [])
        ranking_grupos = []
        for i, grupo in enumerate(grupos_data[:20]):  # Top 20 grupos
            ranking_grupos.append({
                "posicao": i + 1,
                "nome": grupo.get("nome", "Grupo Desconhecido"),
                "pontos": grupo.get("pontos", 0),
                "membros_count": len(grupo.get("membros", [])),
                "lider": grupo.get("lider", "Desconhecido")
            })

        return jsonify({"sucesso": True, "ranking_grupos": ranking_grupos})
    except Exception as e:
        return jsonify({"sucesso": False, "mensagem": f"Erro interno: {str(e)}"}), 500

# ROTA 3: Para o ranking INDIVIDUAL de DEFACE
@main.route("/api/ranking/deface-individual")
@token_required
def api_ranking_deface_individual(uid):
    """API que busca e retorna o ranking individual de jogadores por pontos de deface."""
    try:
        # Usar função corrigida do Supabase
        ranking_result = models.get_individual_deface_ranking()

        if not ranking_result.get('sucesso'):
            return jsonify(ranking_result)

        # Formatar dados para o frontend
        ranking_jogadores = []
        for jogador in ranking_result.get('ranking_jogadores', []):
            # Buscar nível do jogador se não estiver disponível
            nivel = jogador.get("nivel", 1)
            if nivel == 1 and jogador.get("uid"):
                try:
                    usuario_data = models.supabase_client.client.table('usuarios').select('nivel').eq('uid', jogador.get("uid")).execute()
                    if usuario_data.data:
                        nivel = usuario_data.data[0].get('nivel', 1)
                except:
                    nivel = 1

            jogador_info = {
                "nick": jogador.get("nick", "Jogador"),
                "nivel": nivel,
                "deface_points": jogador.get("deface_points", 0),
                "grupo_nome": jogador.get("grupo_nome", "Sem grupo"),
                "grupo_id": jogador.get("grupo_id"),
                "position": jogador.get("position", 0)
            }
            ranking_jogadores.append(jogador_info)

        # Definir recompensas escalonadas para mostrar no frontend
        recompensas = {
            "1": {"dinheiro": 50000, "shacks": 500, "descricao": "1º Lugar"},
            "2": {"dinheiro": 30000, "shacks": 300, "descricao": "2º Lugar"},
            "3": {"dinheiro": 20000, "shacks": 200, "descricao": "3º Lugar"},
            "4-5": {"dinheiro": 15000, "shacks": 150, "descricao": "4º-5º Lugar"},
            "6-10": {"dinheiro": 10000, "shacks": 100, "descricao": "6º-10º Lugar"}
        }

        return jsonify({
            "sucesso": True,
            "ranking": ranking_jogadores,
            "total_jogadores": len(ranking_jogadores),
            "recompensas": recompensas,
            "reset_diario": "00:00 UTC",
            "tempo_restante": models.get_tournament_time_remaining() if hasattr(models, 'get_tournament_time_remaining') else None
        })
    except Exception as e:
         return jsonify({"sucesso": False, "mensagem": f"Erro interno: {str(e)}"}), 500

@main.route("/api/admin/test-deface-points", methods=["POST"])
@token_required
def api_test_deface_points(uid):
    """API de teste para adicionar pontos de deface a alguns jogadores"""
    try:
        # Verificar se é admin (opcional)
        jogador = models.get_jogador(uid)
        if not jogador:
            return jsonify({"sucesso": False, "mensagem": "Jogador não encontrado"}), 404

        # Buscar alguns jogadores para adicionar pontos de teste
        usuarios = models.supabase_client.client.table('usuarios').select('uid, nick').limit(10).execute()

        if not usuarios.data:
            return jsonify({"sucesso": False, "mensagem": "Nenhum usuário encontrado"})

        import random
        resultados = []

        for i, usuario in enumerate(usuarios.data[:5]):  # Apenas 5 jogadores
            pontos_teste = random.randint(10, 100)  # Pontos aleatórios entre 10-100

            resultado = models.atualizar_jogador(usuario['uid'], {
                'deface_points_individual': pontos_teste
            })

            if resultado.get('sucesso', True):
                resultados.append({
                    'nick': usuario['nick'],
                    'pontos': pontos_teste
                })

        return jsonify({
            "sucesso": True,
            "mensagem": f"Pontos de teste adicionados para {len(resultados)} jogadores",
            "jogadores": resultados
        })

    except Exception as e:
        return jsonify({"sucesso": False, "mensagem": f"Erro interno: {str(e)}"}), 500

@main.route("/api/debug/test-deface", methods=["POST"])
@token_required
def api_debug_test_deface(uid):
    """API de debug para testar deface com dados específicos"""
    try:
        data = request.get_json()
        alvo_uid = data.get('alvo_uid')

        if not alvo_uid:
            return jsonify({"sucesso": False, "mensagem": "alvo_uid é obrigatório"})

        # Testar busca do atacante
        atacante = models.get_jogador(uid)
        if not atacante:
            return jsonify({"sucesso": False, "mensagem": f"Atacante não encontrado: {uid}"})

        # Testar busca do alvo
        alvo = models.get_jogador(alvo_uid)
        if not alvo:
            return jsonify({"sucesso": False, "mensagem": f"Alvo não encontrado: {alvo_uid}"})

        # Informações de debug
        debug_info = {
            "atacante": {
                "uid": uid,
                "nick": atacante.get('nick'),
                "grupo_id": atacante.get('grupo_id')
            },
            "alvo": {
                "uid": alvo_uid,
                "nick": alvo.get('nick'),
                "ip": alvo.get('ip')
            }
        }

        # Tentar deface real
        resultado = models.realizar_deface(uid, alvo_uid)

        return jsonify({
            "sucesso": True,
            "debug_info": debug_info,
            "resultado_deface": resultado
        })

    except Exception as e:
        return jsonify({"sucesso": False, "mensagem": f"Erro interno: {str(e)}"}), 500

@main.route("/api/banco")
@token_required
def api_get_banco_data(uid):
    """API para buscar dados do banco do jogador."""
    try:
        # Busca dados do jogador
        jogador = models.get_jogador(uid)
        if not jogador:
            return jsonify({"sucesso": False, "mensagem": "Jogador não encontrado."}), 404
        
        # Busca o extrato bancário (transações financeiras)
        extrato = models.get_extrato_bancario(uid)
        
        # Calcula estatísticas
        total_entradas = sum(log.get('valor', 0) for log in extrato if log.get('valor', 0) > 0)
        total_saidas = abs(sum(log.get('valor', 0) for log in extrato if log.get('valor', 0) < 0))
        
        return jsonify({
            "sucesso": True,
            "saldo_atual": jogador.get('dinheiro', 0),
            "total_entradas": total_entradas,
            "total_saidas": total_saidas,
            "transacoes": extrato[:20]  # Últimas 20 transações
        })
        
    except Exception as e:
        print(f"Erro no banco: {str(e)}")
        return jsonify({"sucesso": False, "mensagem": "Erro interno do servidor."}), 500

@main.route("/api/transferencia", methods=["POST"])
@token_required
def api_realizar_transferencia(uid):
    """API para realizar transferência entre jogadores"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({"sucesso": False, "mensagem": "Dados não fornecidos."}), 400
        
        destinatario_nick = data.get("destinatario_nick")
        valor = data.get("valor")
        descricao = data.get("descricao", "")
        
        # Validações
        if not destinatario_nick:
            return jsonify({"sucesso": False, "mensagem": "Nick do destinatário é obrigatório."}), 400
        
        if not valor or valor <= 0:
            return jsonify({"sucesso": False, "mensagem": "Valor deve ser maior que zero."}), 400
        
        # Buscar destinatário pelo nick
        try:
            destinatario_query = models.supabase_client.client.table('usuarios').select('uid').eq('nick', destinatario_nick).execute()
            
            if not destinatario_query.data:
                return jsonify({"sucesso": False, "mensagem": f"Jogador '{destinatario_nick}' não encontrado."}), 404
            
            destinatario_uid = destinatario_query.data[0]['uid']
            
        except Exception as e:
            return jsonify({"sucesso": False, "mensagem": "Erro ao buscar destinatário."}), 500
        
        # Realizar transferência
        resultado = models.realizar_transferencia(uid, destinatario_uid, valor, descricao)
        
        if resultado['sucesso']:
            return jsonify(resultado)
        else:
            return jsonify(resultado), 400
            
    except Exception as e:
        print(f"Erro na API de transferência: {str(e)}")
        return jsonify({"sucesso": False, "mensagem": "Erro interno do servidor."}), 500

@main.route("/api/appstore/custos", methods=["POST"])
@token_required
def api_calcular_custos_upgrade(uid):
    """API para calcular custos de múltiplos upgrades."""
    data = request.get_json()
    item = data.get("item")
    quantidade = data.get("quantidade", 1)
    
    if not item:
        return jsonify({"sucesso": False, "mensagem": "Item não especificado."}), 400
    
    # Busca jogador para saber o nível atual
    jogador = models.get_jogador(uid)
    if not jogador:
        return jsonify({"sucesso": False, "mensagem": "Jogador não encontrado."}), 404
    
    nivel_atual = jogador.get(item, 1)
    custos = models.calcular_custo_upgrade_multiplo(item, nivel_atual, quantidade)
    
    return jsonify({
        "sucesso": True,
        "custo_dinheiro": custos["custo_dinheiro"],
        "custo_shacks": custos["custo_shacks"],
        "nivel_atual": nivel_atual,
        "novo_nivel": nivel_atual + quantidade
    })

# --- ROTAS PARA MINERAÇÃO OFFLINE ---

@main.route("/api/admin/processar-mineracao-offline", methods=["POST"])
@token_required
def api_processar_mineracao_offline(uid):
    """
    API administrativa para processar mineração de todos os jogadores offline.
    Deve ser usada com cuidado e idealmente chamada por um cron job.
    """
    try:
        # Verificar se é um admin (opcional - você pode implementar verificação de admin)
        resultado = models.processar_mineracao_offline_todos_jogadores()
        return jsonify(resultado)
        
    except Exception as e:
        return jsonify({
            "sucesso": False,
            "mensagem": f"Erro no processamento offline: {str(e)}"
        }), 500

@main.route("/api/mineracao/processar/<target_uid>", methods=["POST"])
@token_required
def api_processar_mineracao_jogador(uid, target_uid):
    """
    API para processar mineração de um jogador específico.
    Útil para testes ou processamento manual.
    """
    try:
        resultado = models.processar_mineracao_jogador_especifico(target_uid)
        return jsonify(resultado)
        
    except Exception as e:
        return jsonify({
            "sucesso": False,
            "mensagem": f"Erro ao processar jogador: {str(e)}"
        }), 500

@main.route("/api/mineracao/status", methods=["GET"])
@token_required
def api_status_mineracao(uid):
    """
    API para verificar o status do sistema de mineração offline.
    """
    try:
        # Conta quantos jogadores existem
        usuarios_ref = db.collection('usuarios')
        total_jogadores = len(list(usuarios_ref.stream()))
        
        # Verifica estatísticas básicas
        agora = datetime.datetime.now(datetime.timezone.utc).timestamp()
        
        return jsonify({
            "sucesso": True,
            "sistema_ativo": True,
            "total_jogadores": total_jogadores,
            "ultima_verificacao": agora,
            "intervalo_processamento": "1 hora",
            "mensagem": "Sistema de mineração offline está ativo"
        })
        
    except Exception as e:
        return jsonify({
            "sucesso": False,
            "mensagem": f"Erro ao verificar status: {str(e)}"
        }), 500

@main.route("/api/mineracao/shacks-disponiveis", methods=["GET"])
@token_required
def api_shacks_disponiveis(uid):
    """
    API para verificar quantos Shacks estão disponíveis para coleta
    """
    try:
        resultado = models.calcular_shacks_disponiveis_para_coleta(uid)
        
        if resultado['sucesso']:
            return jsonify(resultado)
        else:
            return jsonify(resultado), 400
            
    except Exception as e:
        return jsonify({
            "sucesso": False,
            "mensagem": f"Erro ao verificar Shacks disponíveis: {str(e)}"
        }), 500

@main.route("/api/mineracao/coletar-shacks", methods=["POST"])
@token_required
def api_coletar_shacks(uid):
    """
    API para coletar Shacks acumulados da mineração
    """
    try:
        resultado = models.coletar_shacks_mineracao(uid)
        
        if resultado['sucesso']:
            return jsonify(resultado)
        else:
            return jsonify(resultado), 400
            
    except Exception as e:
        return jsonify({
            "sucesso": False,
            "mensagem": f"Erro ao coletar Shacks: {str(e)}"
        }), 500

@main.route("/api/admin/processar-dinheiro-automatico", methods=["POST"])
@token_required
def api_processar_dinheiro_automatico(uid):
    """
    API administrativa para processar mineração automática de dinheiro
    """
    try:
        # Verificar se é admin (implementar verificação se necessário)

        resultado = models.processar_mineracao_automatica_dinheiro()

        if resultado['sucesso']:
            return jsonify(resultado)
        else:
            return jsonify(resultado), 400

    except Exception as e:
        return jsonify({
            "sucesso": False,
            "mensagem": f"Erro ao processar mineração automática: {str(e)}"
        }), 500

@main.route("/api/mineracao/coletar-offline", methods=["POST"])
@token_required
def api_coletar_mineracao_offline(uid):
    """
    API para coletar dinheiro da mineração offline quando o jogador faz login
    """
    try:
        resultado = models.processar_mineracao_offline_jogador(uid)
        return jsonify(resultado)

    except Exception as e:
        return jsonify({
            "sucesso": False,
            "mensagem": f"Erro ao coletar mineração offline: {str(e)}"
        }), 500

# === ROTAS DE SEGURANÇA ===

@main.route("/api/security/status", methods=["GET"])
@token_required
def api_security_status(uid):
    """
    Retorna status geral de segurança do jogador
    """
    try:
        resultado = models.obter_status_seguranca(uid)
        return jsonify(resultado)

    except Exception as e:
        return jsonify({
            "sucesso": False,
            "mensagem": f"Erro ao obter status de segurança: {str(e)}"
        }), 500

@main.route("/api/security/invasions", methods=["GET"])
@token_required
def api_security_invasions(uid):
    """
    Retorna invasões detectadas contra o jogador
    """
    try:
        resultado = models.obter_invasoes_detectadas(uid)
        return jsonify(resultado)

    except Exception as e:
        return jsonify({
            "sucesso": False,
            "mensagem": f"Erro ao obter invasões: {str(e)}"
        }), 500

@main.route("/api/security/block-invasion", methods=["POST"])
@token_required
def api_security_block_invasion(uid):
    """
    Bloqueia uma invasão específica
    """
    try:
        data = request.get_json()
        atacante_uid = data.get('atacante_uid')

        if not atacante_uid:
            return jsonify({
                "sucesso": False,
                "mensagem": "UID do atacante não fornecido"
            }), 400

        resultado = models.bloquear_invasao(uid, atacante_uid)
        return jsonify(resultado)

    except Exception as e:
        return jsonify({
            "sucesso": False,
            "mensagem": f"Erro ao bloquear invasão: {str(e)}"
        }), 500

@main.route("/api/security/block-all-invasions", methods=["POST"])
@token_required
def api_security_block_all_invasions(uid):
    """
    Bloqueia todas as invasões ativas contra o jogador
    """
    try:
        resultado = models.bloquear_todas_invasoes(uid)
        return jsonify(resultado)

    except Exception as e:
        return jsonify({
            "sucesso": False,
            "mensagem": f"Erro ao bloquear todas as invasões: {str(e)}"
        }), 500

@main.route("/api/security/close-all-connections", methods=["POST"])
@token_required
def api_security_close_all_connections(uid):
    """
    Fecha todas as conexões ativas do jogador (ação defensiva)
    """
    try:
        resultado = models.fechar_todas_conexoes(uid)
        return jsonify(resultado)

    except Exception as e:
        return jsonify({
            "sucesso": False,
            "mensagem": f"Erro ao fechar conexões: {str(e)}"
        }), 500

@main.route("/api/security/cleanup-orphan-bruteforces", methods=["POST"])
@token_required
def api_security_cleanup_orphan_bruteforces(uid):
    """
    Limpa bruteforces órfãos (sem conexão ativa) para resolver problemas de constraint
    """
    try:
        orfaos_limpos = models.limpar_bruteforces_orfaos(uid)

        if orfaos_limpos > 0:
            return jsonify({
                "sucesso": True,
                "mensagem": f"✅ {orfaos_limpos} bruteforce{'s' if orfaos_limpos > 1 else ''} órfão{'s' if orfaos_limpos > 1 else ''} limpo{'s' if orfaos_limpos > 1 else ''}",
                "orfaos_limpos": orfaos_limpos
            })
        else:
            return jsonify({
                "sucesso": True,
                "mensagem": "✅ Nenhum bruteforce órfão encontrado",
                "orfaos_limpos": 0
            })

    except Exception as e:
        return jsonify({
            "sucesso": False,
            "mensagem": f"Erro ao limpar bruteforces órfãos: {str(e)}"
        }), 500

@main.route("/api/security/change-ip", methods=["POST"])
@token_required
def api_security_change_ip(uid):
    """
    Troca o IP do jogador por um novo IP aleatório (custa 100 shacks)
    """
    try:
        resultado = models.trocar_ip_jogador(uid)
        return jsonify(resultado)

    except Exception as e:
        return jsonify({
            "sucesso": False,
            "mensagem": f"Erro ao trocar IP: {str(e)}"
        }), 500

# === ROTAS DE LOGS MELHORADOS ===

@main.route("/api/logs/atividade", methods=["GET"])
@token_required
def api_logs_atividade_melhorados(uid):
    """
    Retorna logs de atividade com deface ativo no topo
    """
    try:
        limite = request.args.get('limite', 50, type=int)
        resultado = models.obter_logs_atividade_melhorados(uid, limite)
        return jsonify(resultado)

    except Exception as e:
        return jsonify({
            "sucesso": False,
            "mensagem": f"Erro ao obter logs: {str(e)}"
        }), 500

@main.route("/api/logs/test", methods=["POST"])
@token_required
def api_logs_test(uid):
    """
    Cria logs de teste para verificar funcionamento
    """
    try:
        # Criar log de exploit inicial
        models.supabase_client.log_atividade(
            uid,
            'security_alert',
            {
                'tipo': 'exploit_inicial',
                'atacante_ip': '*************',
                'atacante_nick': 'TestHacker',
                'poder_ataque': 5,
                'defesa_alvo': 3,
                'mensagem': 'Security alert! sistema invadido por *************'
            }
        )

        # Criar log de transferência
        models.supabase_client.log_atividade(
            uid,
            'security_alert',
            {
                'tipo': 'exploit_transferencia',
                'atacante_ip': '*************',
                'atacante_nick': 'TestHacker',
                'quantia_roubada': 1000,
                'mensagem': 'Security alert! transferencia $1000 por *************'
            }
        )

        # Criar log de deface
        models.supabase_client.log_atividade(
            uid,
            'security_alert',
            {
                'tipo': 'exploit_deface',
                'atacante_ip': '*************',
                'atacante_nick': 'TestHacker',
                'grupo_nome': 'Test Group',
                'mensagem': 'Security alert! deface aplicado por ************* (Test Group)'
            }
        )

        return jsonify({
            "sucesso": True,
            "mensagem": "Logs de teste criados com sucesso (exploit, transferência, deface)"
        })

    except Exception as e:
        return jsonify({
            "sucesso": False,
            "mensagem": f"Erro ao criar logs de teste: {str(e)}"
        }), 500

@main.route("/api/logs/alvo/<ip>", methods=["GET"])
@token_required
def api_logs_alvo(uid, ip):
    """
    Retorna logs de atividade do alvo exploitado (para botão "Ver Log" no exploit)
    """
    try:
        # Buscar o alvo pelo IP
        alvo = models.buscar_jogador_por_ip(ip)
        if not alvo:
            return jsonify({
                "sucesso": False,
                "mensagem": "Alvo não encontrado"
            }), 404

        alvo_uid = alvo.get('uid')
        if not alvo_uid:
            return jsonify({
                "sucesso": False,
                "mensagem": "UID do alvo não encontrado"
            }), 404

        # Obter logs do alvo
        limite = request.args.get('limite', 20, type=int)
        resultado = models.obter_logs_atividade_melhorados(alvo_uid, limite)

        if resultado.get('sucesso'):
            # Adicionar informações do alvo
            resultado['alvo_info'] = {
                'nick': alvo.get('nick', 'Desconhecido'),
                'ip': alvo.get('ip', ip),
                'nivel': alvo.get('nivel', 1)
            }

        return jsonify(resultado)

    except Exception as e:
        return jsonify({
            "sucesso": False,
            "mensagem": f"Erro ao obter logs do alvo: {str(e)}"
        }), 500

@main.route("/api/logs/limpar", methods=["POST"])
@token_required
def api_limpar_logs_usuario(uid):
    """
    Limpa todos os logs de security_alert do usuário
    """
    try:
        resultado = models.limpar_logs_usuario(uid)
        return jsonify(resultado)

    except Exception as e:
        return jsonify({
            "sucesso": False,
            "mensagem": f"Erro ao limpar logs: {str(e)}"
        }), 500

@main.route("/api/logs/limpar-alvo/<ip>", methods=["POST"])
@token_required
def api_limpar_logs_alvo(uid, ip):
    """
    Limpa todos os logs de security_alert do alvo (para atacantes)
    """
    try:
        resultado = models.limpar_logs_alvo(ip)
        return jsonify(resultado)

    except Exception as e:
        return jsonify({
            "sucesso": False,
            "mensagem": f"Erro ao limpar logs do alvo: {str(e)}"
        }), 500


# --- ROTAS DA API DE NOTÍCIAS ---

@main.route('/api/news', methods=['GET'])
@token_required
def get_news(uid):
    """Obter todas as notícias"""
    try:
        noticias = models.obter_noticias()
        return jsonify({
            "sucesso": True,
            "noticias": noticias
        })
        
    except Exception as e:
        print(f"Erro ao obter notícias: {str(e)}")
        return jsonify({
            "sucesso": False,
            "mensagem": f"Erro ao obter notícias: {str(e)}"
        }), 500


@main.route('/api/news', methods=['POST'])
@token_required
def create_news(uid):
    """Criar nova notícia (apenas administradores)"""
    try:
        # Verificar se o usuário é administrador
        usuario = models.get_jogador(uid)
        if not usuario or not usuario.get('is_admin', False):
            return jsonify({
                "sucesso": False,
                "mensagem": "Acesso negado. Apenas administradores podem criar notícias."
            }), 403
        
        data = request.get_json()
        title = data.get('title')
        content = data.get('content')
        priority = data.get('priority', False)
        
        if not title or not content:
            return jsonify({
                "sucesso": False,
                "mensagem": "Título e conteúdo são obrigatórios."
            }), 400
        
        noticia_id = models.criar_noticia(uid, title, content, priority)
        
        return jsonify({
            "sucesso": True,
            "mensagem": "Notícia criada com sucesso!",
            "noticia_id": noticia_id
        })
        
    except Exception as e:
        print(f"Erro ao criar notícia: {str(e)}")
        return jsonify({
            "sucesso": False,
            "mensagem": f"Erro ao criar notícia: {str(e)}"
        }), 500


@main.route('/api/news/<news_id>', methods=['DELETE'])
@token_required
def delete_news(uid, news_id):
    """Excluir notícia (apenas administradores)"""
    try:
        # Verificar se o usuário é administrador
        usuario = models.get_jogador(uid)
        if not usuario or not usuario.get('is_admin', False):
            return jsonify({
                "sucesso": False,
                "mensagem": "Acesso negado. Apenas administradores podem excluir notícias."
            }), 403
        
        success = models.excluir_noticia(news_id)
        
        if success:
            return jsonify({
                "sucesso": True,
                "mensagem": "Notícia excluída com sucesso!"
            })
        else:
            return jsonify({
                "sucesso": False,
                "mensagem": "Notícia não encontrada."
            }), 404
        
    except Exception as e:
        print(f"Erro ao excluir notícia: {str(e)}")
        return jsonify({
            "sucesso": False,
            "mensagem": f"Erro ao excluir notícia: {str(e)}"
        }), 500

# === ROTAS DO TORNEIO DE DEFACE ===

@main.route("/api/torneio/ranking")
@token_required
def api_torneio_ranking(uid):
    """API para obter o ranking do torneio atual (upgrade) - NOVO SISTEMA TTL"""
    try:
        # Verificar se o sistema TTL está disponível
        if not ttl_system_available:
            return jsonify({"sucesso": False, "mensagem": "Sistema TTL não disponível"})
        
        ranking_result = torneio_manager.get_ranking_upgrade()
        return jsonify(ranking_result)
    except Exception as e:
        print(f"Erro ao buscar ranking do torneio: {str(e)}")
        return jsonify({
            "sucesso": False,
            "mensagem": f"Erro ao buscar ranking: {str(e)}"
        }), 500

@main.route("/api/torneio/ranking-deface")
@token_required
def api_torneio_ranking_deface(uid):
    """API para obter o ranking de grupos por pontos de deface - NOVO SISTEMA TTL"""
    try:
        # Verificar se o sistema TTL está disponível
        if not ttl_system_available:
            return jsonify({"sucesso": False, "mensagem": "Sistema TTL não disponível"})
        
        # Obter ranking de deface (sem necessidade de verificação de reset)
        ranking_result = torneio_manager.get_ranking_deface()
        
        return jsonify(ranking_result)
    except Exception as e:
        print(f"Erro ao buscar ranking de deface: {str(e)}")
        return jsonify({
            "sucesso": False,
            "mensagem": f"Erro ao buscar ranking de deface: {str(e)}"
        }), 500

@main.route("/api/torneio/reset-deface", methods=["POST"])
@token_required
def api_reset_torneios_deface(uid):
    """API para resetar manualmente os torneios de deface - NOVO SISTEMA TTL"""
    try:
        # Verificar se o usuário é administrador
        if not models.is_user_admin(uid):
            return jsonify({
                "sucesso": False,
                "mensagem": "Acesso negado. Apenas administradores podem resetar torneios manualmente."
            }), 403
        
        # Verificar se o sistema TTL está disponível
        if not ttl_system_available:
            return jsonify({"sucesso": False, "mensagem": "Sistema TTL não disponível"})
        
        # No novo sistema, criar um novo torneio força o reset
        resultado = torneio_manager.criar_torneio_manual('deface')
        
        # Adicionar log de quem fez o reset
        if resultado.get('sucesso'):
            usuario = models.get_jogador(uid)
            resultado['reset_por'] = usuario.get('nick', 'Admin') if usuario else 'Admin'
            resultado['timestamp_reset'] = datetime.datetime.now(datetime.timezone.utc).isoformat()
            resultado['mensagem'] = 'Torneio de deface resetado manualmente com sucesso!'
        
        return jsonify(resultado)
    except Exception as e:
        print(f"Erro ao resetar torneios de deface: {str(e)}")
        return jsonify({
            "sucesso": False,
            "mensagem": f"Erro ao resetar torneios: {str(e)}"
        }), 500

@main.route("/api/admin/torneio/info", methods=["GET"])
@token_required
def api_admin_torneio_info(uid):
    """API para obter informações administrativas do torneio - NOVO SISTEMA TTL"""
    try:
        # Verificar se o usuário é administrador
        if not models.is_user_admin(uid):
            return jsonify({
                "sucesso": False,
                "mensagem": "Acesso negado. Apenas administradores."
            }), 403
        
        # Verificar se o sistema TTL está disponível
        if not ttl_system_available:
            return jsonify({"sucesso": False, "mensagem": "Sistema TTL não disponível"})
        
        # Obter estatísticas de ambos os torneios
        status_deface = torneio_manager.get_status_torneio('deface')
        status_upgrade = torneio_manager.get_status_torneio('upgrade')
        
        # Obter histórico recente
        historico = torneio_manager.get_historico_recompensas(limite=20)
        
        stats = {
            "sucesso": True,
            "is_admin": True,
            "torneio_deface": status_deface,
            "torneio_upgrade": status_upgrade,
            "historico_recompensas": historico.get('historico', []) if historico['sucesso'] else [],
            "sistema": "TTL Automático",
            "observacoes": [
                "Sistema com TTL automático ativo",
                "Reset e recompensas são automáticos",
                "Sem necessidade de verificações manuais",
                "Performance otimizada (95% menos requisições)"
            ]
        }
        
        return jsonify(stats)
        
    except Exception as e:
        print(f"Erro ao obter info admin do torneio: {str(e)}")
        return jsonify({
            "sucesso": False,
            "mensagem": f"Erro: {str(e)}"
        }), 500

@main.route("/api/torneio/reset", methods=["POST"])
def api_torneio_reset():
    """API para resetar o torneio (chamada automática)"""
    try:
        resultado = models.reset_daily_tournament()
        return jsonify(resultado)
    except Exception as e:
        print(f"Erro ao resetar torneio: {str(e)}")
        return jsonify({
            "sucesso": False,
            "mensagem": f"Erro ao resetar torneio: {str(e)}"
        }), 500

@main.route("/api/jogador/<nick>")
@token_required
def api_get_jogador_by_nick(uid, nick):
    """API para buscar dados públicos de um jogador por nick."""
    try:
        # Busca o jogador pelo nick usando Supabase
        jogador_data = models.get_jogador_por_nick(nick)

        if not jogador_data:
            return jsonify({"sucesso": False, "mensagem": "Jogador não encontrado."}), 404

        # Se o jogador tem grupo_id, buscar o nome real do grupo
        grupo_nome = jogador_data.get("grupo_nome", "Sem Grupo")
        grupo_id = jogador_data.get("grupo_id")

        if grupo_id and grupo_id != 'sem_grupo' and grupo_id.strip():
            try:
                grupo_info = models.get_grupo_by_id(grupo_id)
                if grupo_info:
                    grupo_nome = grupo_info.get("nome", grupo_nome)
            except Exception as e:
                print(f"Erro ao buscar nome do grupo {grupo_id}: {e}")

        # Retorna apenas dados públicos
        jogador_publico = {
            "nick": jogador_data.get("nick"),
            "nivel": jogador_data.get("nivel", 1),
            "grupo_nome": grupo_nome,
            "grupo": grupo_nome,
            "grupo_id": grupo_id,
            "tournament_points": jogador_data.get("tournament_points", 0),
            "deface_total": jogador_data.get("deface_total", 0)
        }

        return jsonify({"sucesso": True, "jogador": jogador_publico})
    except Exception as e:
        return jsonify({"sucesso": False, "mensagem": f"Erro interno: {str(e)}"}), 500

@main.route("/api/admin/corrigir-grupos")
def api_corrigir_grupos():
    """API temporária para corrigir dados de grupo dos usuários"""
    try:
        resultado = models.corrigir_dados_grupo_usuarios()
        return jsonify(resultado)
    except Exception as e:
        return jsonify({"sucesso": False, "mensagem": f"Erro: {str(e)}"}), 500

# --- ROTAS DO SISTEMA DE HABILIDADES NFT ---

@main.route("/api/habilidades/disponiveis", methods=["GET"])
@token_required
def listar_habilidades_disponiveis(uid):
    """Endpoint para listar todas as habilidades NFT disponíveis com seu estoque"""
    try:
        resultado = models.get_habilidades_nft_disponiveis()
        return jsonify(resultado)
    except Exception as e:
        return jsonify({"sucesso": False, "mensagem": f"Erro: {str(e)}"}), 500

@main.route("/api/habilidades/comprar", methods=["POST"])
@token_required
def comprar_habilidade(uid):
    """Endpoint para comprar uma habilidade NFT"""
    try:
        data = request.get_json()
        habilidade_id = data.get('habilidade_id')
        
        if not habilidade_id:
            return jsonify({"sucesso": False, "mensagem": "ID da habilidade não especificado"}), 400
        
        resultado = models.comprar_habilidade_nft(uid, habilidade_id)
        return jsonify(resultado)
    except Exception as e:
        return jsonify({"sucesso": False, "mensagem": f"Erro: {str(e)}"}), 500

@main.route("/api/habilidades/upgrade", methods=["POST"])
@token_required
def upgrade_habilidade(uid):
    """Endpoint para realizar upgrade de habilidade (sistema antigo)"""
    try:
        # Esta rota é mantida para compatibilidade com o frontend atual
        data = request.get_json()
        tipo_habilidade = data.get('tipo_habilidade')
        
        if not tipo_habilidade:
            return jsonify({"sucesso": False, "mensagem": "Tipo de habilidade não especificado"}), 400
        
        # Retorna uma mensagem de erro informando que o sistema de habilidades foi alterado
        return jsonify({
            "sucesso": False,
            "mensagem": "O sistema de habilidades foi alterado para um sistema baseado em NFTs. Acesse a nova aba para adquirir habilidades exclusivas."
        })
    except Exception as e:
        return jsonify({"sucesso": False, "mensagem": f"Erro: {str(e)}"}), 500

# --- ROTAS DE ADMINISTRAÇÃO DE HABILIDADES NFT ---

@main.route("/api/admin/habilidades/criar", methods=["POST"])
@token_required
def admin_criar_habilidade(uid):
    """Endpoint para administradores criarem novas habilidades NFT"""
    try:
        # Verifica se o usuário é administrador (implemente sua lógica de verificação aqui)
        jogador = models.get_jogador(uid)
        if not jogador or jogador.get("role") != "admin":
            return jsonify({"sucesso": False, "mensagem": "Acesso negado. Apenas administradores podem usar esta função."}), 403
        
        data = request.get_json()
        nome = data.get('nome')
        descricao = data.get('descricao')
        efeito = data.get('efeito')
        preco_shack = data.get('preco_shack')
        estoque_maximo = data.get('estoque_maximo')
        habilidade_id = data.get('habilidade_id')  # Opcional
        
        # Validação dos campos obrigatórios
        campos_obrigatorios = {'nome': nome, 'descricao': descricao, 'efeito': efeito, 
                              'preco_shack': preco_shack, 'estoque_maximo': estoque_maximo}
        
        for campo, valor in campos_obrigatorios.items():
            if valor is None:
                return jsonify({"sucesso": False, "mensagem": f"Campo '{campo}' é obrigatório"}), 400
        
        resultado = models.admin_criar_habilidade_nft(
            nome, descricao, efeito, preco_shack, estoque_maximo, habilidade_id
        )
        
        return jsonify(resultado)
    except Exception as e:
        return jsonify({"sucesso": False, "mensagem": f"Erro: {str(e)}"}), 500

@main.route("/api/admin/habilidades/atualizar", methods=["PUT"])
@token_required
def admin_atualizar_habilidade(uid):
    """Endpoint para administradores atualizarem habilidades NFT existentes"""
    try:
        # Verifica se o usuário é administrador
        jogador = models.get_jogador(uid)
        if not jogador or jogador.get("role") != "admin":
            return jsonify({"sucesso": False, "mensagem": "Acesso negado. Apenas administradores podem usar esta função."}), 403
        
        data = request.get_json()
        habilidade_id = data.get('habilidade_id')
        
        if not habilidade_id:
            return jsonify({"sucesso": False, "mensagem": "ID da habilidade não especificado"}), 400
        
        # Remove o ID do dicionário de campos para atualização
        campos_atualizacao = {k: v for k, v in data.items() if k != 'habilidade_id'}
        
        if not campos_atualizacao:
            return jsonify({"sucesso": False, "mensagem": "Nenhum campo para atualização"}), 400
        
        resultado = models.admin_atualizar_habilidade_nft(habilidade_id, campos_atualizacao)
        return jsonify(resultado)
    except Exception as e:
        return jsonify({"sucesso": False, "mensagem": f"Erro: {str(e)}"}), 500

@main.route("/api/admin/habilidades/excluir", methods=["DELETE"])
@token_required
def admin_excluir_habilidade(uid):
    """Endpoint para administradores excluírem habilidades NFT"""
    try:
        # Verifica se o usuário é administrador
        jogador = models.get_jogador(uid)
        if not jogador or jogador.get("role") != "admin":
            return jsonify({"sucesso": False, "mensagem": "Acesso negado. Apenas administradores podem usar esta função."}), 403
        
        data = request.get_json()
        habilidade_id = data.get('habilidade_id')
        
        if not habilidade_id:
            return jsonify({"sucesso": False, "mensagem": "ID da habilidade não especificado"}), 400
        
        resultado = models.admin_excluir_habilidade_nft(habilidade_id)
        return jsonify(resultado)
    except Exception as e:
        return jsonify({"sucesso": False, "mensagem": f"Erro: {str(e)}"}), 500

@main.route("/api/admin/habilidades/migrar", methods=["POST"])
@token_required
def admin_migrar_jogadores(uid):
    """Endpoint para migrar todos os jogadores para o novo sistema de habilidades NFT"""
    try:
        # Verifica se o usuário é administrador
        jogador = models.get_jogador(uid)
        if not jogador or jogador.get("role") != "admin":
            return jsonify({"sucesso": False, "mensagem": "Acesso negado. Apenas administradores podem usar esta função."}), 403
        
        resultado = models.migrar_jogadores_para_novo_sistema_habilidades()
        return jsonify(resultado)
    except Exception as e:
        return jsonify({"sucesso": False, "mensagem": f"Erro: {str(e)}"}), 500

# === ROTAS ADMINISTRATIVAS DE SEGURANÇA ===

@main.route("/api/admin/security/stats", methods=["GET"])
@token_required
def api_security_stats(uid):
    """Endpoint para administradores verificarem estatísticas de segurança"""
    try:
        # Verificar se o usuário é administrador
        jogador = models.get_jogador(uid)
        if not jogador or jogador.get("role") != "admin":
            return jsonify({"sucesso": False, "mensagem": "Acesso negado. Apenas administradores."}), 403
        
        security_systems = get_security_systems()
        stats = {}
        
        # Estatísticas do sistema avançado de segurança do jogo
        if security_systems.get('advanced_game_security'):
            ags = security_systems['advanced_game_security']
            current_time = time.time()
            
            stats['advanced_game_security'] = {
                'suspicious_users': len(ags.suspicious_users),
                'active_users': len(ags.user_actions),
                'total_actions_last_hour': sum(
                    len([a for a in actions if current_time - a['time'] < 3600])
                    for actions in ags.user_actions.values()
                ),
                'users_in_cooldown': sum(
                    1 for cooldowns in ags.user_cooldowns.values()
                    if any(end_time > current_time for end_time in cooldowns.values())
                )
            }
        
        # Estatísticas do sistema de rate limiting
        if security_systems.get('rate_limiter'):
            rl = security_systems['rate_limiter']
            stats['rate_limiter'] = {
                'tracked_ips': len(rl.request_history),
                'total_requests_tracked': sum(len(reqs) for reqs in rl.request_history.values())
            }
        
        return jsonify({"sucesso": True, "stats": stats})
        
    except Exception as e:
        return jsonify({"sucesso": False, "mensagem": f"Erro: {str(e)}"}), 500

@main.route("/api/admin/security/suspicious-users", methods=["GET"])
@token_required
def api_get_suspicious_users(uid):
    """Listar usuários marcados como suspeitos"""
    try:
        # Verificar se o usuário é administrador
        jogador = models.get_jogador(uid)
        if not jogador or jogador.get("role") != "admin":
            return jsonify({"sucesso": False, "mensagem": "Acesso negado. Apenas administradores."}), 403
        
        security_systems = get_security_systems()
        
        if not security_systems.get('advanced_game_security'):
            return jsonify({"sucesso": False, "mensagem": "Sistema de segurança não disponível"}), 503
        
        ags = security_systems['advanced_game_security']
        suspicious_data = []
        
        for user_id in ags.suspicious_users:
            user_stats = ags.get_user_stats(user_id)
            user_info = models.get_jogador(user_id)
            
            suspicious_data.append({
                'uid': user_id,
                'nick': user_info.get('nick', 'Desconhecido') if user_info else 'Não encontrado',
                'stats': user_stats
            })
        
        return jsonify({"sucesso": True, "suspicious_users": suspicious_data})
        
    except Exception as e:
        return jsonify({"sucesso": False, "mensagem": f"Erro: {str(e)}"}), 500

@main.route("/api/admin/security/clear-suspicion", methods=["POST"])
@token_required
def api_clear_suspicion(uid):
    """Remover flag de suspeito de um usuário"""
    try:
        # Verificar se o usuário é administrador
        jogador = models.get_jogador(uid)
        if not jogador or jogador.get("role") != "admin":
            return jsonify({"sucesso": False, "mensagem": "Acesso negado. Apenas administradores."}), 403
        
        data = request.get_json()
        target_uid = data.get('target_uid')
        
        if not target_uid:
            return jsonify({"sucesso": False, "mensagem": "UID do usuário não fornecido"}), 400
        
        security_systems = get_security_systems()
        
        if security_systems.get('advanced_game_security'):
            security_systems['advanced_game_security'].reset_user_suspicion(target_uid)
            return jsonify({"sucesso": True, "mensagem": f"Flag de suspeito removida para usuário {target_uid}"})
        else:
            return jsonify({"sucesso": False, "mensagem": "Sistema de segurança não disponível"}), 503
        
    except Exception as e:
        return jsonify({"sucesso": False, "mensagem": f"Erro: {str(e)}"}), 500

# === ROTAS ADMINISTRATIVAS DE CACHE ===

@main.route("/api/admin/cache/stats", methods=["GET"])
@token_required
def api_cache_stats(uid):
    """Endpoint para administradores verificarem estatísticas do cache"""
    try:
        # Verificar se o usuário é administrador
        jogador = models.get_jogador(uid)
        if not jogador or jogador.get("role") != "admin":
            return jsonify({"sucesso": False, "mensagem": "Acesso negado. Apenas administradores."}), 403
        
        security_systems = get_security_systems()
        
        if not security_systems.get('cache_security'):
            return jsonify({"sucesso": False, "mensagem": "Sistema de cache não disponível"}), 503
        
        cache_stats = security_systems['cache_security'].get_cache_stats()
        
        return jsonify({"sucesso": True, "cache_stats": cache_stats})
        
    except Exception as e:
        return jsonify({"sucesso": False, "mensagem": f"Erro: {str(e)}"}), 500

@main.route("/api/admin/cache/clear", methods=["POST"])
@token_required
def api_clear_cache(uid):
    """Limpar todo o cache (apenas administradores)"""
    try:
        # Verificar se o usuário é administrador
        jogador = models.get_jogador(uid)
        if not jogador or jogador.get("role") != "admin":
            return jsonify({"sucesso": False, "mensagem": "Acesso negado. Apenas administradores."}), 403
        
        data = request.get_json() or {}
        reason = data.get('reason', f'admin_clear_by_{uid}')
        
        security_systems = get_security_systems()
        
        if not security_systems.get('cache_security'):
            return jsonify({"sucesso": False, "mensagem": "Sistema de cache não disponível"}), 503
        
        cleared_count = security_systems['cache_security'].clear_all_cache(reason)
        
        return jsonify({
            "sucesso": True, 
            "mensagem": f"Cache limpo com sucesso. {cleared_count} itens removidos.",
            "cleared_count": cleared_count
        })
        
    except Exception as e:
        return jsonify({"sucesso": False, "mensagem": f"Erro: {str(e)}"}), 500

@main.route("/api/admin/cache/invalidate", methods=["POST"])
@token_required
def api_invalidate_cache_pattern(uid):
    """Invalidar cache por padrão (apenas administradores)"""
    try:
        # Verificar se o usuário é administrador
        jogador = models.get_jogador(uid)
        if not jogador or jogador.get("role") != "admin":
            return jsonify({"sucesso": False, "mensagem": "Acesso negado. Apenas administradores."}), 403
        
        data = request.get_json()
        pattern = data.get('pattern')
        
        if not pattern:
            return jsonify({"sucesso": False, "mensagem": "Padrão de cache não fornecido"}), 400
        
        # Importar helper de cache
        from security.cache_helpers import SecureGameCache
        invalidated_count = SecureGameCache.batch_invalidate(pattern)
        
        return jsonify({
            "sucesso": True,
            "mensagem": f"Cache invalidado com sucesso. {invalidated_count} itens removidos.",
            "invalidated_count": invalidated_count,
            "pattern": pattern
        })
        
    except Exception as e:
        return jsonify({"sucesso": False, "mensagem": f"Erro: {str(e)}"}), 500

@main.route("/api/cache/player/invalidate", methods=["POST"])
@token_required
def api_invalidate_player_cache(uid):
    """Permitir que jogadores invalidem seu próprio cache"""
    try:
        # Importar helper de cache
        from security.cache_helpers import SecureGameCache
        
        success = SecureGameCache.invalidate_player_cache(uid)
        
        if success:
            return jsonify({
                "sucesso": True,
                "mensagem": "Cache do jogador invalidado com sucesso."
            })
        else:
            return jsonify({
                "sucesso": False,
                "mensagem": "Sistema de cache não disponível."
            }), 503
        
    except Exception as e:
        return jsonify({"sucesso": False, "mensagem": f"Erro: {str(e)}"}), 500

# --- ROTAS DO TERMINAL E BRUTEFORCE ---

@main.route("/terminal")
@token_required
def page_terminal(uid):
    """Página do Terminal para execução de bruteforce"""
    return render_template('terminal.html')

# === SUPPORTER SHOP APIs ===

@main.route("/api/shop/items")
@token_required
def api_get_shop_items(uid):
    """API para buscar itens disponíveis no shop para supporters"""
    try:
        # Verificar se o jogador é supporter
        jogador = models.get_jogador(uid)
        if not jogador:
            return jsonify({"sucesso": False, "mensagem": "Jogador não encontrado."}), 404

        print(f"[DEBUG SHOP] Dados do jogador: {jogador}")
        print(f"[DEBUG SHOP] Spoints do jogador: {jogador.get('spoints', 'CAMPO NÃO ENCONTRADO')}")

        is_supporter = jogador.get('is_supporter', False)
        spoints_disponiveis = jogador.get('spoints', 0)
        print(f"[DEBUG SHOP] Is supporter: {is_supporter}")
        print(f"[DEBUG SHOP] Spoints disponíveis: {spoints_disponiveis}")

        # Itens do shop para supporters
        shop_items = [
            # === PACKS DE DINHEIRO ===
            {
                "id": "money_pack_small",
                "nome": "Pack Dinheiro Pequeno",
                "descricao": "Receba $500.000 instantaneamente",
                "preco": 50,
                "moeda": "spoints",
                "categoria": "dinheiro",
                "icone": "💵",
                "disponivel": is_supporter,
                "requisito": "Supporter ativo"
            },
            {
                "id": "money_pack_medium",
                "nome": "Pack Dinheiro Médio",
                "descricao": "Receba $2.000.000 instantaneamente",
                "preco": 150,
                "moeda": "spoints",
                "categoria": "dinheiro",
                "icone": "💰",
                "disponivel": is_supporter,
                "requisito": "Supporter ativo"
            },
            {
                "id": "money_pack_large",
                "nome": "Pack Dinheiro Grande",
                "descricao": "Receba $5.000.000 instantaneamente",
                "preco": 300,
                "moeda": "spoints",
                "categoria": "dinheiro",
                "icone": "💎",
                "disponivel": is_supporter,
                "requisito": "Supporter ativo"
            },

            # === HABILIDADES EXCLUSIVAS ===
            {
                "id": "ghost_mode",
                "nome": "Modo Fantasma",
                "descricao": "Torna-se invisível nos logs de outros jogadores por 24h",
                "preco": 400,
                "moeda": "spoints",
                "categoria": "habilidade",
                "icone": "👻",
                "disponivel": is_supporter,
                "requisito": "Supporter ativo"
            },
            {
                "id": "super_bruteforce",
                "nome": "Super Bruteforce",
                "descricao": "Bruteforce 3x mais rápido por 12 horas",
                "preco": 200,
                "moeda": "spoints",
                "categoria": "habilidade",
                "icone": "🚀",
                "disponivel": is_supporter,
                "requisito": "Supporter ativo"
            },
            {
                "id": "shield_protection",
                "nome": "Escudo de Proteção",
                "descricao": "Imune a ataques por 6 horas",
                "preco": 600,
                "moeda": "spoints",
                "categoria": "habilidade",
                "icone": "🛡️",
                "disponivel": is_supporter,
                "requisito": "Supporter ativo"
            },

            # === BOOSTS MELHORADOS ===
            {
                "id": "xp_boost_24h",
                "nome": "XP Boost 24h",
                "descricao": "Dobra o ganho de XP por 24 horas",
                "preco": 100,
                "moeda": "spoints",
                "categoria": "boost",
                "icone": "⚡",
                "disponivel": is_supporter,
                "requisito": "Supporter ativo"
            },
            {
                "id": "money_boost_12h",
                "nome": "Money Boost 12h",
                "descricao": "Aumenta ganho de dinheiro em 100% por 12 horas",
                "preco": 150,
                "moeda": "spoints",
                "categoria": "boost",
                "icone": "�",
                "disponivel": is_supporter,
                "requisito": "Supporter ativo"
            },
            {
                "id": "mining_boost_24h",
                "nome": "Mining Boost 24h",
                "descricao": "Mineração 5x mais rápida por 24 horas",
                "preco": 250,
                "moeda": "spoints",
                "categoria": "boost",
                "icone": "⛏️",
                "disponivel": is_supporter,
                "requisito": "Supporter ativo"
            },

            # === VISUAL E SOCIAL ===
            {
                "id": "premium_skin_1",
                "nome": "Skin Premium Cyber",
                "descricao": "Interface exclusiva com tema cyberpunk avançado",
                "preco": 200,
                "moeda": "spoints",
                "categoria": "visual",
                "icone": "🎨",
                "disponivel": is_supporter,
                "requisito": "Supporter ativo"
            },
            {
                "id": "exclusive_title",
                "nome": "Título [SUPPORTER]",
                "descricao": "Título especial '[SUPPORTER]' no perfil",
                "preco": 50,
                "moeda": "spoints",
                "categoria": "social",
                "icone": "👑",
                "disponivel": is_supporter,
                "requisito": "Supporter ativo"
            },
            {
                "id": "custom_ip_color",
                "nome": "IP Colorido",
                "descricao": "Seu IP aparece em dourado para outros jogadores",
                "preco": 100,
                "moeda": "spoints",
                "categoria": "visual",
                "icone": "🌟",
                "disponivel": is_supporter,
                "requisito": "Supporter ativo"
            },

            # === HABILIDADE NFT EXCLUSIVA ===
            {
                "id": "nft_firstsupp",
                "nome": "Firstsupp NFT",
                "descricao": "Habilidade NFT exclusiva: +100% geração de dinheiro e +2 slots de conexão",
                "preco": 1000,
                "moeda": "spoints",
                "categoria": "nft",
                "icone": "💎",
                "disponivel": is_supporter,
                "requisito": "Supporter ativo",
                "estoque_limitado": True,
                "estoque_atual": models.get_nft_estoque("firstsupp"),
                "estoque_total": 10
            },

            # === SERVIÇOS GRATUITOS ===
            {
                "id": "priority_support",
                "nome": "Suporte Prioritário",
                "descricao": "Acesso a canal de suporte exclusivo no Discord",
                "preco": 0,
                "moeda": "gratis",
                "categoria": "servico",
                "icone": "🎧",
                "disponivel": is_supporter,
                "requisito": "Supporter ativo"
            },
            {
                "id": "beta_access",
                "nome": "Acesso Beta",
                "descricao": "Teste novas funcionalidades antes do lançamento",
                "preco": 0,
                "moeda": "gratis",
                "categoria": "servico",
                "icone": "🧪",
                "disponivel": is_supporter,
                "requisito": "Supporter ativo"
            }
        ]

        return jsonify({
            "sucesso": True,
            "items": shop_items,
            "is_supporter": is_supporter,
            "spoints_disponiveis": spoints_disponiveis
        })

    except Exception as e:
        return jsonify({"sucesso": False, "mensagem": f"Erro interno: {str(e)}"}), 500

@main.route("/api/shop/comprar", methods=["POST"])
@token_required
def api_comprar_item_shop(uid):
    """API para comprar item do shop"""
    try:
        data = request.get_json()
        item_id = data.get('item_id')

        if not item_id:
            return jsonify({"sucesso": False, "mensagem": "ID do item é obrigatório."}), 400

        # Verificar se o jogador é supporter
        jogador = models.get_jogador(uid)
        if not jogador:
            return jsonify({"sucesso": False, "mensagem": "Jogador não encontrado."}), 404

        if not jogador.get('is_supporter', False):
            return jsonify({"sucesso": False, "mensagem": "Apenas supporters podem acessar o shop."}), 403

        # Processar compra baseado no item
        resultado = models.processar_compra_shop(uid, item_id)
        return jsonify(resultado)

    except Exception as e:
        return jsonify({"sucesso": False, "mensagem": f"Erro interno: {str(e)}"}), 500

@main.route("/api/admin/setup-shop-columns", methods=["POST"])
@token_required
def api_setup_shop_columns(uid):
    """API para verificar e orientar sobre as colunas necessárias para o shop"""
    try:
        # Lista de colunas necessárias para o shop funcionar
        colunas_necessarias = [
            "is_supporter BOOLEAN DEFAULT FALSE",
            "spoints INTEGER DEFAULT 0",
            "nft_firstsupp BOOLEAN DEFAULT FALSE",
            "money_multiplier DECIMAL DEFAULT 1.0",
            "connection_slots_bonus INTEGER DEFAULT 0",
            "habilidades_nft JSONB DEFAULT '{}'"
        ]

        # Verificar quais colunas existem
        colunas_existentes = []
        colunas_faltantes = []

        for coluna_def in colunas_necessarias:
            coluna_nome = coluna_def.split()[0]
            try:
                # Tentar fazer uma query na coluna
                models.supabase_client.client.table('usuarios').select(coluna_nome).limit(1).execute()
                colunas_existentes.append(coluna_nome)
            except Exception:
                colunas_faltantes.append(coluna_def)

        if not colunas_faltantes:
            return jsonify({
                "sucesso": True,
                "mensagem": "Todas as colunas necessárias já existem!",
                "colunas_existentes": colunas_existentes
            })
        else:
            sql_commands = []
            for coluna in colunas_faltantes:
                sql_commands.append(f"ALTER TABLE usuarios ADD COLUMN {coluna};")

            return jsonify({
                "sucesso": False,
                "mensagem": "Algumas colunas estão faltando. Execute os comandos SQL abaixo no Supabase:",
                "colunas_existentes": colunas_existentes,
                "colunas_faltantes": [c.split()[0] for c in colunas_faltantes],
                "sql_commands": sql_commands
            })

    except Exception as e:
        return jsonify({"sucesso": False, "mensagem": f"Erro interno: {str(e)}"}), 500

@main.route("/api/admin/tornar-supporter", methods=["POST"])
@token_required
def api_tornar_supporter(uid):
    """API administrativa para tornar um jogador supporter (para testes)"""
    try:
        data = request.get_json()
        target_uid = data.get('target_uid', uid)  # Se não especificar, usa o próprio UID

        # Atualizar jogador para supporter
        resultado = models.atualizar_jogador(target_uid, {
            'is_supporter': True
        })

        if resultado.get('sucesso', True):
            return jsonify({
                "sucesso": True,
                "mensagem": f"Jogador {target_uid} agora é supporter!"
            })
        else:
            return jsonify({
                "sucesso": False,
                "mensagem": "Erro ao atualizar jogador"
            })

    except Exception as e:
        return jsonify({"sucesso": False, "mensagem": f"Erro interno: {str(e)}"}), 500

@main.route("/api/habilidades/nft")
@token_required
def api_get_habilidades_nft(uid):
    """API para buscar habilidades NFT do jogador"""
    try:
        jogador = models.get_jogador(uid)
        if not jogador:
            return jsonify({"sucesso": False, "mensagem": "Jogador não encontrado."}), 404

        habilidades_nft = jogador.get('habilidades_nft', {})
        if isinstance(habilidades_nft, str):
            import json
            try:
                habilidades_nft = json.loads(habilidades_nft)
            except:
                habilidades_nft = {}

        return jsonify({
            "sucesso": True,
            "habilidades": habilidades_nft,
            "total": len(habilidades_nft)
        })

    except Exception as e:
        return jsonify({"sucesso": False, "mensagem": f"Erro interno: {str(e)}"}), 500

@main.route("/api/admin/dar-spoints", methods=["POST"])
@token_required
def api_dar_spoints(uid):
    """API administrativa para dar spoints a um jogador (para testes)"""
    try:
        data = request.get_json()
        target_uid = data.get('target_uid', uid)  # Se não especificar, usa o próprio UID
        quantidade = data.get('quantidade', 1000)  # Padrão: 1000 spoints

        # Buscar jogador atual
        jogador = models.get_jogador(target_uid)
        if not jogador:
            return jsonify({
                "sucesso": False,
                "mensagem": "Jogador não encontrado"
            })

        # Adicionar spoints
        spoints_atuais = jogador.get('spoints', 0)
        novos_spoints = spoints_atuais + quantidade

        resultado = models.atualizar_jogador(target_uid, {
            'spoints': novos_spoints
        })

        if resultado.get('sucesso', True):
            return jsonify({
                "sucesso": True,
                "mensagem": f"{quantidade} spoints adicionados! Total: {novos_spoints}",
                "spoints_anteriores": spoints_atuais,
                "spoints_atuais": novos_spoints
            })
        else:
            return jsonify({
                "sucesso": False,
                "mensagem": "Erro ao atualizar spoints"
            })

    except Exception as e:
        return jsonify({"sucesso": False, "mensagem": f"Erro interno: {str(e)}"}), 500

@main.route("/api/admin/setup-firstsupp-nft", methods=["POST"])
@token_required
def api_setup_firstsupp_nft(uid):
    """API para adicionar a habilidade Firstsupp NFT na tabela habilidades_nft"""
    try:
        # Verificar se a habilidade já existe
        existing_check = models.supabase_client.client.table('habilidades_nft').select('*').eq('skill_id', 'firstsupp').execute()

        if existing_check.data:
            return jsonify({
                "sucesso": True,
                "mensagem": "Habilidade Firstsupp já existe na tabela habilidades_nft",
                "habilidade_existente": existing_check.data[0]
            })

        # Inserir a nova habilidade NFT
        nova_habilidade = {
            "id": "nft-firstsupp-001",
            "skill_id": "firstsupp",
            "nome": "Firstsupp",
            "descricao": "Habilidade NFT exclusiva: +100% geração de dinheiro e +2 slots de conexão no terminal",
            "efeito_numero": 2.0,
            "preco_shacks": 1000,  # Mantemos por compatibilidade, mas shop usa spoints
            "estoque_maximo": 10,
            "estoque_atual": 10
        }

        resultado = models.supabase_client.client.table('habilidades_nft').insert(nova_habilidade).execute()

        if resultado.data:
            return jsonify({
                "sucesso": True,
                "mensagem": "Habilidade Firstsupp NFT adicionada com sucesso à tabela habilidades_nft!",
                "habilidade_criada": resultado.data[0]
            })
        else:
            return jsonify({
                "sucesso": False,
                "mensagem": "Erro ao inserir habilidade na tabela"
            })

    except Exception as e:
        return jsonify({
            "sucesso": False,
            "mensagem": f"Erro interno: {str(e)}",
            "sql_manual": "INSERT INTO habilidades_nft (id, skill_id, nome, descricao, efeito_numero, preco_shacks, estoque_maximo, estoque_atual) VALUES ('nft-firstsupp-001', 'firstsupp', 'Firstsupp', 'Habilidade NFT exclusiva: +100% geração de dinheiro e +2 slots de conexão no terminal', 2.0, 1000, 10, 10);"
        }), 500

@main.route("/api/admin/verificar-precos-shop", methods=["GET"])
@token_required
def api_verificar_precos_shop(uid):
    """API para verificar se os preços estão corretos entre shop e tabela habilidades_nft"""
    try:
        # Verificar preço da NFT Firstsupp na tabela
        habilidade_response = models.supabase_client.client.table('habilidades_nft').select('preco_shacks').eq('skill_id', 'firstsupp').execute()

        preco_tabela = None
        if habilidade_response.data:
            preco_tabela = habilidade_response.data[0].get('preco_shacks')

        # Preço usado no shop (hardcoded em spoints)
        preco_shop_spoints = 1000

        return jsonify({
            "sucesso": True,
            "preco_tabela_habilidades": preco_tabela,
            "preco_shop_spoints": preco_shop_spoints,
            "observacao": "O shop sempre usa spoints (1000), independente do valor na tabela habilidades_nft",
            "compatibilidade": "✅ Sistema funciona corretamente - shop usa spoints, tabela é apenas referência"
        })

    except Exception as e:
        return jsonify({"sucesso": False, "mensagem": f"Erro interno: {str(e)}"}), 500

@main.route("/api/nft/efeitos-ativos")
@token_required
def api_verificar_efeitos_nft(uid):
    """API para verificar quais efeitos NFT estão ativos para o jogador"""
    try:
        jogador = models.get_jogador(uid)
        if not jogador:
            return jsonify({"sucesso": False, "mensagem": "Jogador não encontrado."}), 404

        # Verificar NFT Firstsupp
        tem_nft_firstsupp = jogador.get('nft_firstsupp', False)
        money_multiplier = jogador.get('money_multiplier', 1.0)
        connection_slots_bonus = jogador.get('connection_slots_bonus', 0)

        # Verificar habilidades NFT
        habilidades_nft = jogador.get('habilidades_nft', {})
        if isinstance(habilidades_nft, str):
            import json
            try:
                habilidades_nft = json.loads(habilidades_nft)
            except:
                habilidades_nft = {}

        tem_firstsupp_habilidade = 'firstsupp' in habilidades_nft

        # Calcular efeitos ativos
        efeitos_ativos = {
            "nft_firstsupp": {
                "ativo": tem_nft_firstsupp or tem_firstsupp_habilidade,
                "money_multiplier": 2.0 if (tem_nft_firstsupp or tem_firstsupp_habilidade or money_multiplier > 1.0) else 1.0,
                "connection_slots_bonus": 2 if (tem_nft_firstsupp or tem_firstsupp_habilidade or connection_slots_bonus > 0) else 0,
                "limite_conexoes_total": 5 + (2 if (tem_nft_firstsupp or tem_firstsupp_habilidade or connection_slots_bonus > 0) else 0)
            }
        }

        # Verificar status atual das conexões
        status_conexoes = models.obter_status_conexoes_jogador(uid)

        return jsonify({
            "sucesso": True,
            "efeitos_ativos": efeitos_ativos,
            "status_conexoes": status_conexoes,
            "resumo": {
                "tem_nft_firstsupp": tem_nft_firstsupp or tem_firstsupp_habilidade,
                "money_boost_ativo": money_multiplier > 1.0 or tem_nft_firstsupp or tem_firstsupp_habilidade,
                "slots_bonus_ativo": connection_slots_bonus > 0 or tem_nft_firstsupp or tem_firstsupp_habilidade
            }
        })

    except Exception as e:
        return jsonify({"sucesso": False, "mensagem": f"Erro interno: {str(e)}"}), 500

@main.route("/api/terminal/connections")
@token_required
def api_get_terminal_connections(uid):
    """API para listar conexões ativas no terminal usando nova tabela"""
    try:
        # Usar a nova função para obter conexões ativas
        conexoes_ativas = models.obter_conexoes_ativas(uid)
        
        # Buscar nível de bruteforce do jogador
        jogador = models.get_jogador(uid)
        bruteforce_nivel = jogador.get('bruteforce', 1) if jogador else 1
        
        # Converter dados para formato esperado pelo frontend
        conexoes_formatadas = []
        for conexao in conexoes_ativas:
            # Calcular tempo restante usando nossa função corrigida
            try:
                expires_at = models.parse_iso_datetime(conexao['expires_at'])
                tempo_restante = int((expires_at - datetime.datetime.now(datetime.timezone.utc)).total_seconds())
            except Exception as e:
                print(f"[API ERROR] Erro ao calcular tempo: {e}")
                # Se falhar, definir 30 minutos como padrão
                tempo_restante = 1800  # 30 minutos
            
            # Verificar status do bruteforce para esta conexão
            status_bruteforce = models.verificar_status_bruteforce_conexao(uid, conexao['alvo_ip'])
            
            conexoes_formatadas.append({
                "ip": conexao['alvo_ip'],
                "nick": conexao['alvo_nick'],
                "bankguard": conexao['alvo_bankguard'],
                "dinheiro": conexao['alvo_dinheiro'],
                "status": conexao['status'],
                "tempo_restante": max(0, tempo_restante),  # Não pode ser negativo
                "expires_at": conexao['expires_at'],
                "bruteforce_status": status_bruteforce.get('status', 'disponivel'),
                "banco_liberado": status_bruteforce.get('acesso_banco_liberado', False),
                "alvo_dados": status_bruteforce.get('alvo_dados', None)
            })
        
        return jsonify({
            "sucesso": True, 
            "conexoes": conexoes_formatadas,
            "bruteforce_nivel": bruteforce_nivel,
            "total_conexoes": len(conexoes_formatadas)
        })
        
    except Exception as e:
        return jsonify({"sucesso": False, "mensagem": f"Erro: {str(e)}"}), 500

@main.route("/api/terminal/bruteforce", methods=["POST"])
@secure_game_action('bruteforce', cooldown_check=True, csrf_required=True)
def api_execute_bruteforce(uid):
    """API para executar bruteforce em uma conexão"""
    try:
        data = request.get_json()
        alvo_ip = data.get("alvo_ip")
        
        if not alvo_ip:
            return jsonify({"sucesso": False, "mensagem": "IP do alvo não especificado"}), 400
        
        # Executar bruteforce
        resultado = models.executar_bruteforce(uid, alvo_ip)
        return jsonify(resultado)
        
    except Exception as e:
        return jsonify({"sucesso": False, "mensagem": f"Erro: {str(e)}"}), 500

@main.route("/api/terminal/status/<alvo_ip>")
@token_required
def api_get_bruteforce_status(uid, alvo_ip):
    """API para verificar status do bruteforce em andamento"""
    try:
        status = models.verificar_status_bruteforce(uid, alvo_ip)
        return jsonify(status)
        
    except Exception as e:
        return jsonify({"sucesso": False, "mensagem": f"Erro: {str(e)}"}), 500

@main.route("/api/terminal/check/<alvo_ip>")
@token_required  
def api_check_bruteforce_progress(uid, alvo_ip):
    """API para verificar progresso do bruteforce em tempo real"""
    try:
        resultado = models.verificar_e_finalizar_bruteforce(uid, alvo_ip)
        return jsonify(resultado)
        
    except Exception as e:
        return jsonify({"sucesso": False, "mensagem": f"Erro: {str(e)}"}), 500

@main.route("/api/terminal/history")
@token_required
def api_get_terminal_history(uid):
    """API para obter histórico de bruteforce do usuário"""
    try:
        historico = models.obter_historico_bruteforce(uid)
        return jsonify({"sucesso": True, "historico": historico})
        
    except Exception as e:
        return jsonify({"sucesso": False, "mensagem": f"Erro ao carregar histórico: {str(e)}"}), 500
def api_get_terminal_history(uid):
    """API para obter histórico de bruteforce do usuário"""
    try:
        historico = models.obter_historico_bruteforce(uid)
        return jsonify({"sucesso": True, "historico": historico})
        
    except Exception as e:
        return jsonify({"sucesso": False, "mensagem": f"Erro ao carregar histórico: {str(e)}"}), 500
