#!/usr/bin/env python3
"""
Teste para verificar quais funções específicas existem no módulo
"""

print("🔍 TESTE DE FUNÇÕES ESPECÍFICAS")
print("=" * 40)

try:
    from dotenv import load_dotenv
    load_dotenv()
    
    from database.supabase_client import supabase_client
    from game import new_models
    
    print("✅ Módulos importados")
    
    # Listar todas as funções que começam com 'processar_mineracao'
    print("\n🔍 Funções de mineração encontradas:")
    funcoes_mineracao = [attr for attr in dir(new_models) if attr.startswith('processar_mineracao')]
    
    if funcoes_mineracao:
        for i, func in enumerate(funcoes_mineracao, 1):
            print(f"   {i}. {func}")
    else:
        print("   ❌ Nenhuma função de mineração encontrada!")
    
    # Testar funções específicas
    print("\n🧪 Testando funções específicas:")
    
    # Teste 1: teste_funcao_simples
    if hasattr(new_models, 'teste_funcao_simples'):
        print("   ✅ teste_funcao_simples encontrada")
        try:
            resultado = new_models.teste_funcao_simples()
            print(f"      Resultado: {resultado}")
        except Exception as e:
            print(f"      ❌ Erro ao executar: {e}")
    else:
        print("   ❌ teste_funcao_simples NÃO encontrada")
    
    # Teste 2: processar_mineracao_nova_versao
    if hasattr(new_models, 'processar_mineracao_nova_versao'):
        print("   ✅ processar_mineracao_nova_versao encontrada")
        try:
            resultado = new_models.processar_mineracao_nova_versao()
            print(f"      Resultado: {resultado}")
        except Exception as e:
            print(f"      ❌ Erro ao executar: {e}")
    else:
        print("   ❌ processar_mineracao_nova_versao NÃO encontrada")
    
    # Teste 3: processar_mineracao_automatica_dinheiro (original)
    if hasattr(new_models, 'processar_mineracao_automatica_dinheiro'):
        print("   ✅ processar_mineracao_automatica_dinheiro encontrada")
        try:
            resultado = new_models.processar_mineracao_automatica_dinheiro()
            print(f"      Resultado: {resultado}")
        except Exception as e:
            print(f"      ❌ Erro ao executar: {e}")
    else:
        print("   ❌ processar_mineracao_automatica_dinheiro NÃO encontrada")
    
    # Listar todas as funções disponíveis (primeiras 20)
    print("\n📋 Primeiras 20 funções disponíveis no módulo:")
    todas_funcoes = [attr for attr in dir(new_models) if callable(getattr(new_models, attr)) and not attr.startswith('_')]
    for i, func in enumerate(todas_funcoes[:20], 1):
        print(f"   {i:2d}. {func}")
    
    if len(todas_funcoes) > 20:
        print(f"   ... e mais {len(todas_funcoes) - 20} funções")
    
except Exception as e:
    print(f"❌ ERRO: {e}")
    import traceback
    traceback.print_exc()
