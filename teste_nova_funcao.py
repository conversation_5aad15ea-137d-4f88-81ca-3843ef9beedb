#!/usr/bin/env python3
"""
Teste da nova função de mineração
"""

import sys
import os
from dotenv import load_dotenv

# Carrega variáveis de ambiente
load_dotenv()

def main():
    print("🧪 TESTE DA NOVA FUNÇÃO DE MINERAÇÃO")
    print("=" * 45)
    
    try:
        # Importar módulos
        print("📦 Importando módulos...")
        from database.supabase_client import supabase_client
        from game import new_models as models
        print("✅ Módulos importados")
        
        # Verificar conexão
        if not supabase_client.is_connected():
            print("❌ Supabase não conectado")
            return
        print("✅ Supabase conectado")
        
        # Verificar se a nova função existe
        print("\n🔍 Verificando nova função...")
        if hasattr(models, 'processar_mineracao_nova_versao'):
            print("✅ Nova função encontrada!")
        else:
            print("❌ Nova função NÃO encontrada")
            return
        
        # Executar nova função
        print("\n⚡ EXECUTANDO NOVA FUNÇÃO...")
        print("-" * 50)
        
        resultado = models.processar_mineracao_nova_versao()
        
        print("-" * 50)
        print(f"\n📊 RESULTADO:")
        print(f"   Sucesso: {resultado.get('sucesso', False)}")
        print(f"   Processados: {resultado.get('jogadores_processados', 0)}")
        print(f"   Total gerado: ${resultado.get('total_dinheiro_gerado', 0)}")
        print(f"   Mensagem: {resultado.get('mensagem', 'N/A')}")
        
        # Verificar se funcionou
        if resultado.get('sucesso') and resultado.get('jogadores_processados', 0) > 0:
            print("\n🎉 NOVA FUNÇÃO FUNCIONOU!")
        else:
            print("\n❌ Nova função falhou ou não processou jogadores")
        
    except Exception as e:
        print(f"❌ ERRO: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
