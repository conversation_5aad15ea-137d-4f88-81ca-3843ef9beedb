#!/usr/bin/env python3
"""
Teste simples da mineração corrigida
"""

import os
from dotenv import load_dotenv

# Carrega variáveis de ambiente
load_dotenv()

print("🔍 Iniciando teste...")

try:
    from database.supabase_client import supabase_client
    print("✅ Supabase importado")
except Exception as e:
    print(f"❌ Erro ao importar supabase: {e}")
    exit(1)

try:
    from game import new_models as models
    print("✅ Models importado")
except Exception as e:
    print(f"❌ Erro ao importar models: {e}")
    exit(1)

# Verificar conexão
if not supabase_client.is_connected():
    print("❌ Supabase não conectado")
    exit(1)

print("✅ Supabase conectado")

# Buscar usuários
print("\n👥 Buscando usuários...")
usuarios = supabase_client.get_ranking_usuarios(3)
if not usuarios:
    print("❌ Nenhum usuário encontrado")
    exit(1)

print(f"📊 {len(usuarios)} usuários encontrados")

# Mostrar dados antes
print("\n💰 Saldos ANTES:")
saldos_antes = {}
for usuario in usuarios:
    uid = usuario['uid']
    nick = usuario.get('nick', 'Unknown')
    dinheiro = usuario.get('dinheiro', 0)
    nivel = usuario.get('nivel_mineradora', 1)
    saldos_antes[uid] = dinheiro
    
    # Nova fórmula: 15 + (nivel-1) * 20 + (nivel-1) * 5
    dinheiro_esperado = 15 + (nivel - 1) * 20 + (nivel - 1) * 5
    print(f"   {nick} (Nível {nivel}): ${dinheiro} (deveria gerar ${dinheiro_esperado}/min)")

# Limpar cache (se existir)
print("\n🧹 Limpando cache...")
try:
    models.MINERACAO_AUTOMATICA_CACHE.clear()
    print("✅ Cache limpo")
except AttributeError:
    print("⚠️ Cache não encontrado, continuando...")

# Executar mineração
print("\n⚡ Executando mineração...")
try:
    resultado = models.processar_mineracao_automatica_dinheiro()
    print(f"\n📊 Resultado: {resultado}")
except Exception as e:
    print(f"❌ Erro na mineração: {e}")
    exit(1)

# Verificar resultados
print("\n🔍 Verificando resultados...")
usuarios_depois = supabase_client.get_ranking_usuarios(3)

print("\n💰 Saldos DEPOIS:")
mudancas = 0
total_diferenca = 0

for usuario in usuarios_depois:
    uid = usuario['uid']
    nick = usuario.get('nick', 'Unknown')
    dinheiro_depois = usuario.get('dinheiro', 0)
    dinheiro_antes = saldos_antes.get(uid, 0)
    diferenca = dinheiro_depois - dinheiro_antes
    
    if diferenca > 0:
        print(f"   {nick}: ${dinheiro_antes} → ${dinheiro_depois} (+${diferenca}) ✅")
        mudancas += 1
        total_diferenca += diferenca
    else:
        print(f"   {nick}: ${dinheiro_depois} (sem mudança)")

print(f"\n📈 RESUMO:")
print(f"   Usuários com mudança: {mudancas}")
print(f"   Total incrementado: ${total_diferenca}")

if mudancas > 0:
    print("\n🎉 SUCESSO! A mineração está funcionando!")
else:
    print("\n❌ FALHA! A mineração não funcionou!")
