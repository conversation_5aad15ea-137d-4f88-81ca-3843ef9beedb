<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste Direto do Ranking</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            background: #1a1a1a; 
            color: white; 
        }
        .container { 
            max-width: 1000px; 
            margin: 0 auto; 
        }
        .ranking-container {
            background: #2a2a2a;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        .ranking-item { 
            background: #333; 
            margin: 10px 0; 
            padding: 15px; 
            border-radius: 8px; 
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .position { 
            font-weight: bold; 
            color: #4CAF50; 
            font-size: 24px;
            width: 50px;
        }
        .player-info {
            flex: 1;
            margin-left: 15px;
        }
        .nick { 
            font-size: 18px; 
            font-weight: bold; 
            color: #fff;
        }
        .group { 
            color: #9C27B0; 
            font-size: 14px;
        }
        .points { 
            color: #2196F3; 
            font-size: 24px; 
            font-weight: bold; 
            text-align: right;
        }
        .error { 
            color: #f44336; 
            background: #4a1a1a;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
        }
        .success { 
            color: #4CAF50; 
            background: #1a4a1a;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
        }
        button { 
            background: #2196F3; 
            color: white; 
            border: none; 
            padding: 12px 24px; 
            border-radius: 5px; 
            cursor: pointer; 
            margin: 10px 5px; 
            font-size: 16px;
        }
        button:hover { 
            background: #1976D2; 
        }
        .log {
            background: #1a1a1a;
            border: 1px solid #444;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏆 Teste Direto do Ranking Individual de Deface</h1>
        
        <div>
            <button onclick="testCompleteFlow()">🚀 Teste Completo</button>
            <button onclick="clearLogs()">🧹 Limpar Logs</button>
        </div>
        
        <div id="status"></div>
        <div id="logs" class="log"></div>
        <div id="ranking-container" class="ranking-container"></div>
    </div>

    <script>
        function log(message) {
            const logs = document.getElementById('logs');
            const timestamp = new Date().toLocaleTimeString();
            logs.innerHTML += `[${timestamp}] ${message}\n`;
            logs.scrollTop = logs.scrollHeight;
            console.log(message);
        }
        
        function clearLogs() {
            document.getElementById('logs').innerHTML = '';
            document.getElementById('status').innerHTML = '';
            document.getElementById('ranking-container').innerHTML = '';
        }
        
        async function testCompleteFlow() {
            clearLogs();
            log('🚀 Iniciando teste completo...');
            
            try {
                // 1. Login
                log('1️⃣ Fazendo login...');
                const loginResponse = await fetch('/api/auth/auto-login');
                const loginData = await loginResponse.json();
                
                if (loginResponse.ok) {
                    log('✅ Login realizado com sucesso');
                    document.getElementById('status').innerHTML = '<div class="success">✅ Login OK</div>';
                } else {
                    throw new Error(`Login falhou: ${loginData.mensagem}`);
                }
                
                // 2. Buscar ranking
                log('2️⃣ Buscando ranking...');
                const timestamp = Date.now();
                const rankingResponse = await fetch(`/api/ranking/deface-individual?t=${timestamp}`);
                const rankingData = await rankingResponse.json();
                
                log(`📊 Status da resposta: ${rankingResponse.status}`);
                log(`📊 Dados recebidos: ${JSON.stringify(rankingData, null, 2)}`);
                
                if (rankingResponse.ok && rankingData.sucesso) {
                    log(`✅ API funcionando! ${rankingData.total_jogadores || 0} jogadores`);
                    
                    // 3. Renderizar ranking
                    log('3️⃣ Renderizando ranking...');
                    renderRanking(rankingData);
                    
                } else {
                    throw new Error(`API falhou: ${rankingData.mensagem || 'Erro desconhecido'}`);
                }
                
            } catch (error) {
                log(`❌ ERRO: ${error.message}`);
                document.getElementById('status').innerHTML = `<div class="error">❌ ${error.message}</div>`;
            }
        }
        
        function renderRanking(data) {
            const container = document.getElementById('ranking-container');
            
            if (!data.ranking || data.ranking.length === 0) {
                container.innerHTML = `
                    <div class="error">
                        <h3>❌ Nenhum jogador encontrado</h3>
                        <p>Dados recebidos: ${JSON.stringify(data, null, 2)}</p>
                    </div>
                `;
                log('❌ Nenhum jogador no ranking');
                return;
            }
            
            log(`🎨 Renderizando ${data.ranking.length} jogadores`);
            
            container.innerHTML = `
                <h3>🏆 Ranking Individual de Deface (${data.ranking.length} jogadores)</h3>
                ${data.ranking.map((jogador, index) => {
                    log(`👤 Jogador ${index + 1}: ${jogador.nick} - ${jogador.deface_points} pontos`);
                    return `
                        <div class="ranking-item">
                            <div class="position">#${index + 1}</div>
                            <div class="player-info">
                                <div class="nick">${jogador.nick || 'Sem nick'}</div>
                                <div class="group">Grupo: ${jogador.grupo_nome || 'Sem grupo'}</div>
                            </div>
                            <div class="points">${jogador.deface_points || 0} pts</div>
                        </div>
                    `;
                }).join('')}
            `;
            
            document.getElementById('status').innerHTML = '<div class="success">✅ Ranking carregado com sucesso!</div>';
            log('✅ Ranking renderizado com sucesso!');
        }
        
        // Auto-executar ao carregar
        window.onload = function() {
            log('🌐 Página carregada, aguardando comando...');
        };
    </script>
</body>
</html>
