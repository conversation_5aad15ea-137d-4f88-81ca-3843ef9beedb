#!/usr/bin/env python3
"""
Debug passo a passo da função de mineração
"""

import os
from dotenv import load_dotenv
from datetime import datetime, timezone

# Carrega variáveis de ambiente
load_dotenv()

from database.supabase_client import supabase_client

print("🔍 DEBUG PASSO A PASSO DA MINERAÇÃO")
print("=" * 40)

# Simular exatamente o que a função faz
print("📊 Passo 1: Verificando conexão...")
if not supabase_client.is_connected():
    print("❌ Database não disponível")
    exit(1)
print("✅ Database disponível")

print("\n📊 Passo 2: Buscando usuários...")
try:
    usuarios = supabase_client.get_ranking_usuarios(1000)
    print(f"✅ Busca realizada: {len(usuarios) if usuarios else 0} usuários")
except Exception as e:
    print(f"❌ Erro na busca: {e}")
    exit(1)

if not usuarios:
    print("❌ Nenhum usuário encontrado")
    exit(1)

print(f"👥 {len(usuarios)} usuários encontrados")

print("\n📊 Passo 3: Preparando processamento...")
processados = 0
total_dinheiro_gerado = 0
agora = datetime.now(timezone.utc)
timestamp_atual = agora.timestamp()

print(f"⏰ Timestamp atual: {timestamp_atual}")

print("\n📊 Passo 4: Iniciando loop...")
for i, usuario in enumerate(usuarios):
    print(f"\n👤 Processando usuário {i+1}/{len(usuarios)}")
    
    try:
        uid = usuario.get('uid')
        nick = usuario.get('nick', 'Unknown')
        
        print(f"   UID: {uid}")
        print(f"   Nick: {nick}")
        
        if not uid:
            print(f"   ❌ UID inválido, pulando")
            continue
        
        # Verificar nível da mineradora
        nivel_mineradora = usuario.get('nivel_mineradora', 1)
        print(f"   Nível mineradora: {nivel_mineradora}")
        
        if nivel_mineradora < 1:
            print(f"   ❌ Mineradora inativa (nível {nivel_mineradora}), pulando")
            continue
        
        print(f"   ✅ Mineradora ativa (nível {nivel_mineradora})")
        
        # Verificar cache (simulado)
        print(f"   🔍 Verificando cache...")
        # Simular que não há cache
        print(f"   ✅ Cache OK, pode processar")
        
        # Calcular dinheiro
        dinheiro_base = 15 + (nivel_mineradora - 1) * 20 + (nivel_mineradora - 1) * 5
        print(f"   💰 Dinheiro base: ${dinheiro_base}/min")
        
        # Aplicar NFT (simplificado)
        dinheiro_por_minuto = dinheiro_base
        print(f"   💎 Dinheiro final: ${dinheiro_por_minuto}/min")
        
        dinheiro_gerado = int(dinheiro_por_minuto)
        print(f"   📊 Dinheiro a gerar: ${dinheiro_gerado}")
        
        if dinheiro_gerado <= 0:
            print(f"   ❌ Dinheiro gerado = 0, pulando")
            continue
        
        # Buscar saldo atual
        dinheiro_atual = usuario.get('dinheiro', 0)
        novo_dinheiro = dinheiro_atual + dinheiro_gerado
        print(f"   🏦 ${dinheiro_atual} + ${dinheiro_gerado} = ${novo_dinheiro}")
        
        # Simular atualização
        print(f"   📝 Simulando atualização no banco...")
        resultado = supabase_client.atualizar_usuario(uid, {
            'dinheiro': novo_dinheiro
        })
        
        print(f"   📊 Resultado: {resultado}")
        
        if resultado.get('sucesso'):
            processados += 1
            total_dinheiro_gerado += dinheiro_gerado
            print(f"   ✅ Usuário processado com sucesso!")
        else:
            print(f"   ❌ Falha na atualização: {resultado.get('erro', 'Erro desconhecido')}")
        
        # Processar apenas os primeiros 3 para debug
        if i >= 2:
            print(f"\n🔄 Processando apenas os primeiros 3 usuários para debug...")
            break
            
    except Exception as e:
        print(f"   ❌ Erro ao processar usuário: {e}")
        continue

print(f"\n📈 RESULTADO FINAL:")
print(f"   Usuários processados: {processados}")
print(f"   Total dinheiro gerado: ${total_dinheiro_gerado}")

if processados > 0:
    print("\n🎉 SUCESSO!")
    print("✅ O processamento funcionaria corretamente!")
    print("✅ O problema deve estar na função original")
else:
    print("\n❌ FALHA!")
    print("❌ Nenhum usuário foi processado mesmo no debug")

print(f"\n🔧 ANÁLISE:")
print(f"   Loop executou: {'✅' if processados >= 0 else '❌'}")
print(f"   Usuários válidos: {'✅' if processados > 0 else '❌'}")
print(f"   Atualizações funcionam: {'✅' if processados > 0 else '❌'}")

print(f"\n📋 CONCLUSÃO:")
if processados > 0:
    print("O problema está na função original, não na lógica")
    print("Vou corrigir a função original para funcionar como este debug")
else:
    print("O problema é mais profundo, precisa investigar mais")
