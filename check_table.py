#!/usr/bin/env python3
"""
Script simples para verificar a tabela torneio_pontuacoes
"""

import sys
sys.path.append('.')

try:
    from game import new_models
    
    def check_table():
        # print de debug removido
        
        try:
            # Usar a função que já existe no models
            ranking_result = new_models.get_individual_deface_ranking()
            
            # print de debug removido}")
            
            if ranking_result.get('sucesso'):
                jogadores = ranking_result.get('ranking_jogadores', [])
                # print de debug removido}")
                
                if jogadores:
                    print("\n🏆 Jogadores encontrados:")
                    for i, jogador in enumerate(jogadores[:5]):
                        nick = jogador.get('nick', 'Sem nick')
                        pontos = jogador.get('deface_points', 0)
                        grupo = jogador.get('grupo_nome', 'Sem grupo')
                        uid = jogador.get('uid', 'N/A')
                        print(f"  {i+1}. {nick} - {pontos} pontos - Grupo: {grupo} - UID: {uid[:8] if uid != 'N/A' else 'N/A'}...")
                else:
                    # print de debug removido
                    
                    # Tentar verificar se a tabela existe mas está vazia
                    try:
                        response = new_models.supabase_client.client.table('torneio_pontuacoes').select('*').limit(1).execute()
                        if response.data is not None:
                            # print de debug removido
                            print("💡 Sugestão: Execute alguns defaces para gerar dados")
                        else:
                            # print de debug removido
                    except Exception as e:
                        # print de debug removido
            else:
                # print de debug removido}")
                
        except Exception as e:
            # print de debug removido
            import traceback
            print(traceback.format_exc())
    
    def test_api_directly():
        print("\n🌐 Testando API diretamente...")
        
        import requests
        
        try:
            # Criar sessão
            session = requests.Session()
            
            # Login
            login_response = session.get("http://localhost:5000/api/auth/auto-login")
            if login_response.status_code == 200:
                # print de debug removido
                
                # Testar API
                api_response = session.get("http://localhost:5000/api/ranking/deface-individual")
                if api_response.status_code == 200:
                    data = api_response.json()
                    # print de debug removido}")
                    
                    if data.get('sucesso'):
                        ranking = data.get('ranking', [])
                        # print de debug removido} jogadores")
                        
                        if ranking:
                            print("🏆 Top 3 da API:")
                            for i, jogador in enumerate(ranking[:3]):
                                nick = jogador.get('nick', 'Sem nick')
                                pontos = jogador.get('deface_points', 0)
                                grupo = jogador.get('grupo_nome', 'Sem grupo')
                                print(f"  {i+1}. {nick} - {pontos} pontos - {grupo}")
                        else:
                            # print de debug removido
                    else:
                        # print de debug removido}")
                else:
                    # print de debug removido
            else:
                # print de debug removido
                
        except Exception as e:
            # print de debug removido
    
    if __name__ == "__main__":
        check_table()
        test_api_directly()
        
        print("\n💡 Próximos passos:")
        print("   1. Se não há dados, execute alguns defaces no jogo")
        print("   2. Verifique se o frontend está carregando corretamente")
        print("   3. Limpe o cache do navegador")

except ImportError as e:
    # print de debug removido
except Exception as e:
    # print de debug removido
