#!/usr/bin/env python3
"""
Teste de importação simples para identificar o problema
"""

print("1. Iniciando teste...")

try:
    print("2. Importando os...")
    import os
    print("   ✅ os importado")
    
    print("3. Importando sys...")
    import sys
    print("   ✅ sys importado")
    
    print("4. Importando datetime...")
    from datetime import datetime, timezone, timedelta
    print("   ✅ datetime importado")
    
    print("5. Importando typing...")
    from typing import Dict, List, Any, Optional
    print("   ✅ typing importado")
    
    print("6. Importando dotenv...")
    from dotenv import load_dotenv
    print("   ✅ dotenv importado")
    
    print("7. Carregando .env...")
    load_dotenv()
    print("   ✅ .env carregado")
    
    print("8. Importando supabase_client...")
    from database.supabase_client import supabase_client
    print("   ✅ supabase_client importado")
    
    print("9. <PERSON>ando conexão...")
    if supabase_client.is_connected():
        print("   ✅ Supabase conectado")
    else:
        print("   ⚠️ Supabase não conectado")
    
    print("10. Importando new_models...")
    from game import new_models
    print("   ✅ new_models importado!")

    print("11. Forçando reload do módulo...")
    import importlib
    importlib.reload(new_models)
    print("   ✅ Módulo recarregado!")

    print("12. Verificando função...")
    if hasattr(new_models, 'processar_mineracao_nova_versao'):
        print("   ✅ Nova função encontrada!")

        print("13. Testando nova função...")
        resultado = new_models.processar_mineracao_nova_versao()
        print(f"   📊 Resultado: {resultado}")

        if resultado.get('sucesso') and resultado.get('jogadores_processados', 0) > 0:
            print("   🎉 NOVA FUNÇÃO FUNCIONOU!")
        else:
            print("   ⚠️ Nova função executou mas não processou jogadores")
    else:
        print("   ❌ Nova função não encontrada")
    
    print("\n🎉 TESTE CONCLUÍDO COM SUCESSO!")
    
except Exception as e:
    print(f"\n❌ ERRO: {e}")
    import traceback
    traceback.print_exc()
