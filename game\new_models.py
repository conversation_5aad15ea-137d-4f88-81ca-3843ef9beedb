"""
Novo models.py adaptado para Supabase
Substitui completamente o Firebase
"""

import os
import sys
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Any, Optional
import hashlib
import random
import time
import json

# Importa o cliente Supabase
from database.supabase_client import supabase_client

# Cache para controlar cooldowns (definido no topo para ser acessível)
SHACKS_COLETA_CACHE = {}
MINERACAO_AUTOMATICA_CACHE = {}

def parse_iso_datetime(iso_string: str) -> datetime:
    """
    Converte string ISO para datetime de forma compatível com Python < 3.7
    Lida com diferentes precisões de microssegundos
    """
    try:
        # Primeiro, normalizar a string para fromisoformat
        normalized = iso_string.replace('Z', '+00:00')
        
        # Corrigir problema de microssegundos: garantir que tenha exatamente 6 dígitos
        import re
        # Encontrar parte dos microssegundos
        match = re.search(r'\.(\d+)', normalized)
        if match:
            microsecs = match.group(1)
            # Ajustar para exatamente 6 dígitos
            if len(microsecs) < 6:
                microsecs = microsecs.ljust(6, '0')  # Adicionar zeros à direita
            elif len(microsecs) > 6:
                microsecs = microsecs[:6]  # Truncar para 6 dígitos
            # Substituir na string original
            normalized = re.sub(r'\.\d+', f'.{microsecs}', normalized)
        
        # Tentar usar fromisoformat se disponível (Python 3.7+)
        return datetime.fromisoformat(normalized)
    except (AttributeError, ValueError):
        # Fallback para versões mais antigas ou formatos problemáticos
        try:
            from dateutil.parser import parse
            return parse(iso_string)
        except ImportError:
            # Último recurso: parsing manual
            import re
            # Remove timezone info e converte manualmente
            clean_string = re.sub(r'[+\-]\d{2}:\d{2}$|Z$', '', iso_string)
            clean_string = clean_string.replace('T', ' ')
            
            # Tenta diferentes formatos
            formats = [
                '%Y-%m-%d %H:%M:%S.%f',
                '%Y-%m-%d %H:%M:%S',
                '%Y-%m-%d'
            ]
            
            for fmt in formats:
                try:
                    dt = datetime.strptime(clean_string, fmt)
                    return dt.replace(tzinfo=timezone.utc)
                except ValueError:
                    continue
            
            # Se nada funcionou, retorna datetime atual
            print(f"[WARNING] Não conseguiu converter datetime: {iso_string}")
            return datetime.now(timezone.utc)

# Sistema de cooldown em memória (fallback quando campo não existe no banco)
COOLDOWN_CACHE = {}

def verificar_cooldown_memoria(player_uid: str, ip_alvo: str) -> str:
    """Verifica cooldown usando cache em memória como fallback"""
    if player_uid not in COOLDOWN_CACHE:
        return None
    
    cooldowns = COOLDOWN_CACHE[player_uid]
    if ip_alvo not in cooldowns:
        return None
    
    timestamp_atual = datetime.now(timezone.utc).timestamp()
    timestamp_expira = cooldowns[ip_alvo]
    
    if timestamp_atual < timestamp_expira:
        tempo_restante = int((timestamp_expira - timestamp_atual) // 60)
        return f"Cooldown ativo para este IP. Aguarde {tempo_restante + 1} minutos."
    else:
        # Cooldown expirado, remove do cache
        del cooldowns[ip_alvo]
        if not cooldowns:
            del COOLDOWN_CACHE[player_uid]
    
    return None

def adicionar_cooldown_memoria(player_uid: str, ip_alvo: str):
    """Adiciona cooldown usando cache em memória como fallback"""
    if player_uid not in COOLDOWN_CACHE:
        COOLDOWN_CACHE[player_uid] = {}
    
    # Buscar nível do ProxyVPN do jogador para aplicar redução de cooldown
    jogador = get_jogador(player_uid)
    proxyvpn_nivel = jogador.get('proxyvpn', 1) if jogador else 1
    reducao_cooldown = 1 - (proxyvpn_nivel - 1) * 0.1  # 10% menos cooldown por nível acima de 1
    
    # Aplicar efeito da habilidade Speed Hacker (redução adicional de 10%)
    cooldown_segundos = int(3600 * reducao_cooldown)  # Base: 60 minutos
    if jogador:
        cooldown_segundos = aplicar_efeito_habilidade_cooldown(cooldown_segundos, jogador)
    
    timestamp_atual = datetime.now(timezone.utc).timestamp()
    COOLDOWN_CACHE[player_uid][ip_alvo] = timestamp_atual + cooldown_segundos
    
    print(f"[COOLDOWN MEMORIA] Adicionado: {player_uid} -> {ip_alvo} ({cooldown_segundos}s)")

# === CONSTANTES DO JOGO ===
MERCADO_NEGRO_ITEMS = {
    "upgrade_miner_1": {
        "nome": "Otimização de Algoritmo",
        "descricao": "Aumenta o nível da sua Mineradora de Shack em 1, acelerando a geração de recursos.",
        "preco_shack": 500, # Preço base em Shack (calculado dinamicamente)
        "tipo": "upgrade_mineradora"
    },
    "virus_v1": {
        "nome": "Vírus 'Corruptor' v1",
        "descricao": "Aumenta temporariamente o poder do seu Malware Kit para o próximo Deface.",
        "preco_shack": 1200,
        "tipo": "boost_deface"
    }
}

# Sistema de Habilidades NFT com estoque limitado (carregado do Supabase)
HABILIDADES_NFT = {
    "the_killer": {
        "nome": "The Killer",
        "descricao": "Aumenta o poder de ataque no deface em 5% ",
        "efeito": 0.05,  # 5% de aumento do firewall
        "preco_shack": 2500,
        "estoque_maximo": 5,
        "estoque_atual": 5,
        "tipo": "habilidade_nft"
    },
    "firewall_reverso": {
        "nome": "Firewall Reverso",
        "descricao": "Ignora 5% do firewall adversário em ataques.",
        "efeito": 0.05,  # 5% de redução do firewall adversário
        "preco_shack": 6000,
        "estoque_maximo": 5,
        "estoque_atual": 5,
        "tipo": "habilidade_nft"
    },
    "speed_hacker": {
        "nome": "Speed Hacker",
        "descricao": "Reduz tempo de cooldown entre ataques em 10%",
        "efeito": 0.1,  # 10% de redução de cooldown
        "preco_shack": 15000,
        "estoque_maximo": 3,
        "estoque_atual": 3,
        "tipo": "habilidade_nft"
    },
    "crypto_miner": {
        "nome": "Crypto Miner",
        "descricao": "Aumenta geração de Shack da mineradora em 15%",
        "efeito": 0.15,  # 15% de aumento na mineração
        "preco_shack": 4000,
        "estoque_maximo": 10,
        "estoque_atual": 10,
        "tipo": "habilidade_nft"
    }
}

def calcular_custo_upgrade_mineradora(nivel_atual):
    """Calcula o custo em Shacks para o próximo upgrade da mineradora."""
    # Fórmula: CustoBase * (Multiplicador ^ (NívelAtual - 1))
    # Progressão mais suave: base 300, multiplicador 1.25 (25% de aumento)
    custo_base = 300  # Reduzido de 500 para 300
    multiplicador = 1.25  # Reduzido de 1.5 para 1.25 (25% em vez de 50%)
    
    return int(custo_base * (multiplicador ** (nivel_atual - 1)))

def calcular_xp_necessario(nivel):
    """Calcula a quantidade de XP necessária para subir para o próximo nível"""
    if nivel <= 1: 
        return 500
    return int(500 + ((nivel - 1) ** 2) * 150)

def calcular_xp_upgrade(nivel_atual):
    """Calcula quanto XP o jogador ganha ao fazer um upgrade baseado no nível do item"""
    return int(50 + (nivel_atual * 25))

def ganhar_xp(jogador_data, xp_ganho):
    """Adiciona XP ao jogador e verifica se subiu de nível"""
    jogador_data["xp"] = jogador_data.get("xp", 0) + int(xp_ganho)
    xp_necessario_atual = calcular_xp_necessario(jogador_data.get("nivel", 1))
    
    nivel_anterior = jogador_data.get("nivel", 1)
    
    while jogador_data["xp"] >= xp_necessario_atual:
        jogador_data["xp"] -= xp_necessario_atual
        jogador_data["nivel"] = jogador_data.get("nivel", 1) + 1
        
        # Adicionar ao histórico
        if "historico" not in jogador_data:
            jogador_data["historico"] = []
        jogador_data["historico"].append(f"🏆 Subiu para o Nível {jogador_data['nivel']}!")
        
        xp_necessario_atual = calcular_xp_necessario(jogador_data["nivel"])
    
    return jogador_data

# Carrega variáveis de ambiente
from dotenv import load_dotenv
load_dotenv()

# Variáveis globais
HABILIDADES_NFT = {}

def init_supabase():
    """Inicializa conexão com Supabase"""
    global HABILIDADES_NFT
    try:
        if supabase_client.is_connected():
            print("✅ Supabase inicializado com sucesso")
            HABILIDADES_NFT = carregar_habilidades_nft_do_supabase()
            print(f"✅ {len(HABILIDADES_NFT)} habilidades NFT carregadas")
            return True
        else:
            print("⚠️ Supabase não conectado - verifique .env")
            return False
    except Exception as e:
        print(f"❌ Erro ao inicializar Supabase: {e}")
        return False

# --- FUNÇÕES DE USUÁRIOS ---
def criar_perfil_firestore(uid: str, nick: str, email: str) -> Dict[str, Any]:
    """Cria perfil do jogador (mantém nome para compatibilidade)"""
    return supabase_client.criar_usuario(uid, nick, email)

def get_jogador(uid: str) -> Optional[Dict[str, Any]]:
    """Busca jogador por UID"""
    return supabase_client.get_user_by_uid(uid)

def get_jogador_por_nick(nick: str) -> Optional[Dict[str, Any]]:
    """Busca jogador por nickname"""
    return supabase_client.get_user_by_nick(nick)

def get_jogador_por_email(email: str) -> Optional[Dict[str, Any]]:
    """Busca jogador por email"""
    try:
        if not supabase_client.is_connected():
            return None
        
        return supabase_client.get_user_by_email(email)
    except Exception as e:
        print(f"Erro ao buscar jogador por email: {e}")
        return None

def criar_jogador(uid_ou_data, nick=None, email=None) -> Dict[str, Any]:
    """Cria novo jogador - compatibilidade com routes.py e auth_routes.py"""
    if isinstance(uid_ou_data, dict):
        # Se é um dict, usa create_user direto
        return supabase_client.create_user(uid_ou_data)
    else:
        # Se são parâmetros separados, usa criar_usuario
        return supabase_client.criar_usuario(uid_ou_data, nick, email)

def atualizar_jogador(uid: str, updates: Dict[str, Any]) -> Dict[str, Any]:
    """Atualiza dados do jogador"""
    return supabase_client.atualizar_usuario(uid, updates)

def get_ranking_jogadores() -> List[Dict[str, Any]]:
    """Retorna ranking de jogadores"""
    try:
        ranking = supabase_client.get_ranking_usuarios(50)
        
        # Processa dados para compatibilidade
        resultado = []
        for jogador in ranking:
            # Buscar nome real do grupo se o jogador tem grupo_id
            grupo_nome = None
            grupo_id = jogador.get('grupo_id')

            if grupo_id and grupo_id != 'sem_grupo' and grupo_id.strip():
                try:
                    grupo_info = get_grupo_by_id(grupo_id)
                    if grupo_info:
                        grupo_nome = grupo_info.get("nome", f"Grupo {grupo_id[:8]}")
                        print(f"[DEBUG RANKING] Nome do grupo encontrado: {grupo_nome} para ID {grupo_id}")
                    else:
                        print(f"[DEBUG RANKING] Grupo não encontrado para ID: {grupo_id}")
                except Exception as e:
                    print(f"[DEBUG RANKING] Erro ao buscar grupo {grupo_id}: {e}")
                    grupo_nome = f"Grupo {grupo_id[:8]}"

            jogador_data = {
                'uid': jogador['uid'],
                'nick': jogador['nick'],
                'nivel': jogador['nivel'],
                'deface_points_individual': jogador.get('deface_points_individual', 0),
                'grupo': grupo_nome,
                'grupo_nome': grupo_nome,
                'grupo_id': grupo_id
            }

            resultado.append(jogador_data)
        
        return resultado
    
    except Exception as e:
        print(f"Erro ao buscar ranking: {e}")
        return []

# --- FUNÇÕES DE HABILIDADES NFT ---
def carregar_habilidades_nft_do_supabase() -> Dict[str, Any]:
    """Carrega habilidades NFT do Supabase"""
    try:
        skills = supabase_client.get_todas_habilidades_nft()
        habilidades = {}
        
        for skill in skills:
            habilidades[skill['skill_id']] = {
                'nome': skill['nome'],
                'descricao': skill['descricao'],
                'efeito': float(skill['efeito']),
                'preco_shack': skill['preco_shack'],
                'estoque_maximo': skill['estoque_maximo'],
                'estoque_atual': skill['estoque_atual']
            }
        
        return habilidades
    
    except Exception as e:
        print(f"Erro ao carregar habilidades NFT: {e}")
        return {}

def carregar_habilidades_nft_do_firestore():
    """Função de compatibilidade - usa Supabase"""
    return carregar_habilidades_nft_do_supabase()

def comprar_habilidade_nft(uid: str, skill_id: str) -> Dict[str, Any]:
    """Compra uma habilidade NFT"""
    try:
        if not supabase_client.is_connected():
            return {"sucesso": False, "mensagem": "Database não disponível"}
        
        # Verifica se habilidade existe
        if skill_id not in HABILIDADES_NFT:
            return {"sucesso": False, "mensagem": "Habilidade não encontrada"}
        
        habilidade = HABILIDADES_NFT[skill_id]
        
        # Verifica estoque
        if habilidade['estoque_atual'] <= 0:
            return {"sucesso": False, "mensagem": "Habilidade esgotada"}
        
        # Busca usuário
        usuario = get_jogador(uid)
        if not usuario:
            return {"sucesso": False, "mensagem": "Usuário não encontrado"}
        
        # Verifica se já possui a habilidade
        habilidades_adquiridas = usuario.get('habilidades_adquiridas', {})
        if skill_id in habilidades_adquiridas:
            return {"sucesso": False, "mensagem": "Você já possui esta habilidade"}
        
        # Verifica se tem Shack suficiente
        if usuario['shack'] < habilidade['preco_shack']:
            return {"sucesso": False, "mensagem": "Shack insuficiente"}
        
        # Executa transação
        novo_shack = usuario['shack'] - habilidade['preco_shack']
        habilidades_adquiridas[skill_id] = {
            'adquirida_em': datetime.now(timezone.utc).isoformat(),
            'preco_pago': habilidade['preco_shack']
        }
        
        # Atualiza usuário
        user_result = atualizar_jogador(uid, {
            'shack': novo_shack,
            'habilidades_adquiridas': habilidades_adquiridas
        })
        
        if not user_result['sucesso']:
            return {"sucesso": False, "mensagem": "Erro ao atualizar usuário"}
        
        # Atualiza estoque da habilidade
        skill_result = supabase_client.atualizar_habilidade_nft(skill_id, {
            'estoque_atual': habilidade['estoque_atual'] - 1
        })
        
        if skill_result['sucesso']:
            # Atualiza cache global
            HABILIDADES_NFT[skill_id]['estoque_atual'] -= 1
            
            # Log da atividade
            supabase_client.log_atividade(
                uid, 
                'compra_habilidade_nft',
                {'skill_id': skill_id, 'preco': habilidade['preco_shack']}
            )
            
            return {
                "sucesso": True, 
                "mensagem": f"Habilidade '{habilidade['nome']}' adquirida com sucesso!",
                "novo_shack": novo_shack
            }
        else:
            return {"sucesso": False, "mensagem": "Erro ao atualizar estoque"}
    
    except Exception as e:
        print(f"Erro ao comprar habilidade NFT: {e}")
        return {"sucesso": False, "mensagem": "Erro interno do servidor"}

def get_habilidades_nft_disponiveis() -> Dict[str, Any]:
    """Retorna todas as habilidades NFT disponíveis com seus estoques atualizados"""
    try:
        # Recarregar habilidades do Supabase para ter estoque atualizado
        habilidades_atualizadas = carregar_habilidades_nft_do_supabase()
        
        # Se conseguiu carregar do Supabase, atualiza o cache global
        if habilidades_atualizadas:
            global HABILIDADES_NFT
            HABILIDADES_NFT.update(habilidades_atualizadas)
        
        return {
            "sucesso": True,
            "habilidades": HABILIDADES_NFT
        }
    except Exception as e:
        print(f"Erro ao buscar habilidades NFT disponíveis: {e}")
        return {
            "sucesso": False,
            "mensagem": f"Erro ao carregar habilidades: {str(e)}",
            "habilidades": {}
        }

def verificar_efeitos_habilidades_jogador(uid: str) -> Dict[str, Any]:
    """Verifica quais habilidades NFT um jogador possui e seus efeitos"""
    try:
        jogador = get_jogador(uid)
        if not jogador:
            return {"sucesso": False, "mensagem": "Jogador não encontrado"}
        
        habilidades_adquiridas = jogador.get('habilidades_adquiridas', {})
        efeitos_ativos = {}
        
        for skill_id, dados_compra in habilidades_adquiridas.items():
            if skill_id in HABILIDADES_NFT:
                habilidade_info = HABILIDADES_NFT[skill_id]
                efeitos_ativos[skill_id] = {
                    'nome': habilidade_info['nome'],
                    'descricao': habilidade_info['descricao'],
                    'efeito': habilidade_info['efeito'],
                    'adquirida_em': dados_compra.get('adquirida_em'),
                    'preco_pago': dados_compra.get('preco_pago', 0)
                }
        
        return {
            "sucesso": True,
            "habilidades_ativas": efeitos_ativos,
            "total_habilidades": len(efeitos_ativos)
        }
        
    except Exception as e:
        print(f"Erro ao verificar efeitos de habilidades: {e}")
        return {"sucesso": False, "mensagem": f"Erro interno: {str(e)}"}

def aplicar_efeito_habilidade_firewall(jogador_data: Dict[str, Any]) -> int:
    """Aplica efeito da habilidade 'the_killer' no firewall do jogador"""
    firewall_base = jogador_data.get('firewall', 1)
    habilidades = jogador_data.get('habilidades_adquiridas', {})
    
    if 'the_killer' in habilidades:
        # Aumenta firewall em 5%
        bonus_firewall = int(firewall_base * HABILIDADES_NFT['the_killer']['efeito'])
        return firewall_base + max(1, bonus_firewall)  # Mínimo +1
    
    return firewall_base

def aplicar_efeito_habilidade_cooldown(cooldown_base_segundos: int, jogador_data: Dict[str, Any]) -> int:
    """Aplica efeito da habilidade 'speed_hacker' no cooldown do jogador"""
    habilidades = jogador_data.get('habilidades_adquiridas', {})
    
    if 'speed_hacker' in habilidades:
        # Reduz cooldown em 10%
        reducao = HABILIDADES_NFT['speed_hacker']['efeito']
        cooldown_reduzido = int(cooldown_base_segundos * (1 - reducao))
        return max(300, cooldown_reduzido)  # Mínimo 5 minutos
    
    return cooldown_base_segundos

def aplicar_efeito_habilidade_mineracao(shack_base: float, jogador_data: Dict[str, Any]) -> float:
    """Aplica efeito da habilidade 'crypto_miner' na mineração do jogador"""
    habilidades = jogador_data.get('habilidades_adquiridas', {})

    if 'crypto_miner' in habilidades:
        # Aumenta mineração em 15%
        bonus = HABILIDADES_NFT['crypto_miner']['efeito']
        return shack_base * (1 + bonus)

    return shack_base

def aplicar_efeito_nft_firstsupp_dinheiro(dinheiro_base: float, jogador_data: Dict[str, Any]) -> float:
    """Aplica efeito da NFT Firstsupp na geração de dinheiro (+100%)"""
    # Verificar se o jogador possui a NFT Firstsupp
    tem_nft_firstsupp = jogador_data.get('nft_firstsupp', False)
    money_multiplier = jogador_data.get('money_multiplier', 1.0)

    # Verificar também nas habilidades NFT
    habilidades_nft = jogador_data.get('habilidades_nft', {})
    if isinstance(habilidades_nft, str):
        import json
        try:
            habilidades_nft = json.loads(habilidades_nft)
        except:
            habilidades_nft = {}

    tem_firstsupp_habilidade = 'firstsupp' in habilidades_nft

    if tem_nft_firstsupp or tem_firstsupp_habilidade or money_multiplier > 1.0:
        # Aplica multiplicador x2 (100% de aumento)
        dinheiro_multiplicado = dinheiro_base * 2.0
        print(f"[NFT FIRSTSUPP] Dinheiro multiplicado: ${dinheiro_base} → ${dinheiro_multiplicado} (x2)")
        return dinheiro_multiplicado

    return dinheiro_base

def aplicar_efeito_nft_firstsupp_slots(limite_base: int, jogador_data: Dict[str, Any]) -> int:
    """Aplica efeito da NFT Firstsupp nos slots de conexão (+2 slots)"""
    # Verificar se o jogador possui a NFT Firstsupp
    tem_nft_firstsupp = jogador_data.get('nft_firstsupp', False)
    connection_slots_bonus = jogador_data.get('connection_slots_bonus', 0)

    # Verificar também nas habilidades NFT
    habilidades_nft = jogador_data.get('habilidades_nft', {})
    if isinstance(habilidades_nft, str):
        import json
        try:
            habilidades_nft = json.loads(habilidades_nft)
        except:
            habilidades_nft = {}

    tem_firstsupp_habilidade = 'firstsupp' in habilidades_nft

    if tem_nft_firstsupp or tem_firstsupp_habilidade or connection_slots_bonus > 0:
        # Adiciona +2 slots de conexão
        slots_aumentados = limite_base + 2
        print(f"[NFT FIRSTSUPP] Slots de conexão aumentados: {limite_base} → {slots_aumentados} (+2)")
        return slots_aumentados

    return limite_base

def aplicar_todos_efeitos_habilidades(jogador_data: Dict[str, Any], acao: str, valor_base: Any = None) -> Any:
    """
    Função centralizada para aplicar todos os efeitos de habilidades NFT
    
    Args:
        jogador_data: Dados do jogador
        acao: Tipo de ação ('firewall', 'cooldown', 'mineracao', 'ataque')
        valor_base: Valor base a ser modificado (opcional)
    
    Returns:
        Valor modificado pelos efeitos das habilidades
    """
    habilidades = jogador_data.get('habilidades_adquiridas', {})
    
    if acao == 'firewall':
        # The Killer: +5% no firewall
        firewall_base = jogador_data.get('firewall', 1)
        if 'the_killer' in habilidades:
            bonus = int(firewall_base * HABILIDADES_NFT['the_killer']['efeito'])
            return firewall_base + max(1, bonus)
        return firewall_base
    
    elif acao == 'cooldown' and valor_base is not None:
        # Speed Hacker: -10% no cooldown
        if 'speed_hacker' in habilidades:
            reducao = HABILIDADES_NFT['speed_hacker']['efeito']
            return max(300, int(valor_base * (1 - reducao)))  # Mínimo 5 minutos
        return valor_base
    
    elif acao == 'mineracao' and valor_base is not None:
        # Crypto Miner: +15% na mineração
        if 'crypto_miner' in habilidades:
            bonus = HABILIDADES_NFT['crypto_miner']['efeito']
            return valor_base * (1 + bonus)
        return valor_base
    
    elif acao == 'ataque' and valor_base is not None:
        # The Killer: +5% no poder de ataque
        if 'the_killer' in habilidades:
            bonus = int(valor_base * HABILIDADES_NFT['the_killer']['efeito'])
            return valor_base + max(1, bonus)
        return valor_base
    
    elif acao == 'defesa_ignorada' and valor_base is not None:
        # Firewall Reverso: -5% na defesa do alvo
        if 'firewall_reverso' in habilidades:
            return int(valor_base * 0.95)
        return valor_base
    
    return valor_base

def reestocar_habilidade_nft(skill_id: str) -> Dict[str, Any]:
    """Reestoca uma habilidade NFT específica (função administrativa)"""
    try:
        if not supabase_client.is_connected():
            return {"sucesso": False, "mensagem": "Database não disponível"}
        
        if skill_id not in HABILIDADES_NFT:
            return {"sucesso": False, "mensagem": "Habilidade não encontrada"}
        
        habilidade = HABILIDADES_NFT[skill_id]
        estoque_maximo = habilidade['estoque_maximo']
        
        # Atualiza estoque no Supabase
        result = supabase_client.atualizar_habilidade_nft(skill_id, {
            'estoque_atual': estoque_maximo
        })
        
        if result['sucesso']:
            # Atualiza cache global
            HABILIDADES_NFT[skill_id]['estoque_atual'] = estoque_maximo
            
            return {
                "sucesso": True,
                "mensagem": f"Habilidade '{habilidade['nome']}' reestocada para {estoque_maximo}/{estoque_maximo}",
                "estoque_novo": estoque_maximo
            }
        else:
            return {"sucesso": False, "mensagem": "Erro ao atualizar estoque no banco"}
        
    except Exception as e:
        print(f"Erro ao reestocar habilidade NFT: {e}")
        return {"sucesso": False, "mensagem": f"Erro interno: {str(e)}"}

def get_estatisticas_habilidades_nft() -> Dict[str, Any]:
    """Retorna estatísticas das habilidades NFT"""
    try:
        # Recarregar dados do Supabase para ter informações atualizadas
        habilidades_atualizadas = carregar_habilidades_nft_do_supabase()
        
        if habilidades_atualizadas:
            global HABILIDADES_NFT
            HABILIDADES_NFT.update(habilidades_atualizadas)
        
        estatisticas = {
            "total_habilidades": len(HABILIDADES_NFT),
            "habilidades_disponivel": 0,
            "habilidades_esgotadas": 0,
            "valor_total_mercado": 0,
            "detalhes": []
        }
        
        for skill_id, habilidade in HABILIDADES_NFT.items():
            estoque_atual = habilidade['estoque_atual']
            estoque_maximo = habilidade['estoque_maximo']
            preco = habilidade['preco_shack']
            
            if estoque_atual > 0:
                estatisticas["habilidades_disponivel"] += 1
            else:
                estatisticas["habilidades_esgotadas"] += 1
            
            estatisticas["valor_total_mercado"] += (preco * estoque_atual)
            
            estatisticas["detalhes"].append({
                "id": skill_id,
                "nome": habilidade['nome'],
                "estoque": f"{estoque_atual}/{estoque_maximo}",
                "preco": preco,
                "disponivel": estoque_atual > 0,
                "valor_estoque": preco * estoque_atual
            })
        
        return {
            "sucesso": True,
            "estatisticas": estatisticas
        }
        
    except Exception as e:
        print(f"Erro ao gerar estatísticas de habilidades NFT: {e}")
        return {"sucesso": False, "mensagem": f"Erro interno: {str(e)}"}

# --- FUNÇÕES DE COMBATE ---
def encontrar_alvos_para_jogador(uid: str) -> List[Dict[str, Any]]:
    """
    Encontra exatamente 5 jogadores REAIS aleatórios como alvos para scan.
    Se não encontrar jogadores reais suficientes, usa alvos de emergência.
    """
    try:
        print(f"🔍 Buscando alvos para jogador {uid}")
        
        # Primeiro tenta buscar jogadores reais
        todos_jogadores = []
        
        if supabase_client.is_connected():
            try:
                # Query mais permissiva - busca apenas colunas que existem
                response = supabase_client.client.table('usuarios').select(
                    'uid, nick, ip, nivel, dinheiro, firewall, antivirus, cpu, malware_kit'
                ).neq('uid', uid).limit(100).execute()
                
                print(f"📊 Query retornou {len(response.data)} registros do banco")
                
                for jogador_data in response.data:
                    # Valida se tem dados básicos
                    nick = jogador_data.get('nick')
                    
                    if not nick:
                        continue  # Pula jogadores sem nick
                    
                    # Se não tem IP, gera um aleatório
                    ip = jogador_data.get('ip')
                    if not ip:
                        import random
                        ip = f"192.168.{random.randint(1,254)}.{random.randint(1,254)}"
                    
                    # Converte para o formato esperado
                    jogador_formatado = {
                        "uid": jogador_data.get('uid'),
                        "nick": nick,
                        "ip": ip,
                        "nivel": jogador_data.get('nivel', 1),
                        "dinheiro": jogador_data.get('dinheiro', 1000),
                        "firewall": jogador_data.get('firewall', 1),
                        "antivirus": jogador_data.get('antivirus', 1),
                        "cpu": jogador_data.get('cpu', 1),
                        "malware_kit": jogador_data.get('malware_kit', 1),
                        "bruteforce": jogador_data.get('bruteforce', 1),
                        "bankguard": jogador_data.get('bankguard', 1),
                        "proxyvpn": jogador_data.get('proxyvpn', 1)
                    }
                    todos_jogadores.append(jogador_formatado)
                
                print(f"✅ Processados {len(todos_jogadores)} jogadores reais válidos")
                
            except Exception as e:
                print(f"❌ Erro ao buscar no Supabase: {e}")
        else:
            print("❌ Banco Supabase não conectado")
        
        # Se encontrou jogadores reais suficientes, usa eles
        if len(todos_jogadores) >= 5:
            import random
            alvos_sorteados = random.sample(todos_jogadores, 5)
            print(f"🎯 Retornando {len(alvos_sorteados)} jogadores REAIS")
            for alvo in alvos_sorteados:
                print(f"  ✅ {alvo['nick']} (Nível {alvo['nivel']}) - IP: {alvo['ip']}")
            return alvos_sorteados
        
        elif len(todos_jogadores) > 0:
            # Se tem alguns jogadores, retorna todos + completa com fictícios
            print(f"🔄 Poucos jogadores reais ({len(todos_jogadores)}), completando com fictícios")
            alvos_ficticios = gerar_alvos_ficticios_emergencia()
            todos_alvos = todos_jogadores + alvos_ficticios[:5-len(todos_jogadores)]
            return todos_alvos[:5]
        
        else:
            # Não encontrou nenhum jogador real, usa fictícios
            print("🚨 Nenhum jogador real encontrado, usando alvos fictícios")
            return gerar_alvos_ficticios_emergencia()
        
    except Exception as e:
        print(f"❌ Erro geral no scan: {e}")
        print("🚨 Fallback para alvos fictícios por erro")
        return gerar_alvos_ficticios_emergencia()

def gerar_alvos_ficticios_emergencia() -> List[Dict[str, Any]]:
    """Gera alvos fictícios em caso de emergência quando o banco não responde"""
    import random
    
    nicks_ficticios = [
        "CyberGhost", "HackerPro", "CodeBreaker", "NetNinja", "ByteHunter",
        "PixelWarrior", "DataMiner", "CyberPunk", "TechSavage", "QuantumHack"
    ]
    
    alvos_ficticios = []
    for i in range(5):
        nick = random.choice(nicks_ficticios) + str(random.randint(100, 999))
        alvos_ficticios.append({
            "uid": f"emergency_bot_{i}",
            "nick": nick,
            "ip": f"192.168.{random.randint(1,254)}.{random.randint(1,254)}",
            "nivel": random.randint(1, 10),
            "dinheiro": random.randint(500, 5000),
            "firewall": random.randint(1, 5),
            "antivirus": random.randint(1, 5),
            "cpu": random.randint(1, 5),
            "malware_kit": random.randint(1, 5),
            "bruteforce": random.randint(1, 5),
            "bankguard": random.randint(1, 5),
            "proxyvpn": random.randint(1, 5)
        })
    
    print("🚨 Usando alvos fictícios de emergência")
    return alvos_ficticios

def buscar_jogador_por_ip(ip: str) -> Dict[str, Any]:
    """
    Busca APENAS um jogador REAL (usuário registrado) pelo IP para scan avançado.
    NÃO retorna bots ou dados fictícios.
    """
    try:
        print(f"🔍 Buscando jogador REAL com IP: {ip}")
        
        if not supabase_client.is_connected():
            print("❌ Banco não conectado - sem jogadores disponíveis")
            return None
        
        # Buscar APENAS jogador real no Supabase pelo IP
        response = supabase_client.client.table('usuarios').select(
            'uid, nick, ip, nivel, dinheiro, firewall, antivirus, cpu, malware_kit, bruteforce, bankguard, proxyvpn'
        ).eq('ip', ip).limit(1).execute()
        
        if response.data and len(response.data) > 0:
            jogador_data = response.data[0]
            
            # Validar se tem dados mínimos
            if not jogador_data.get('nick') or not jogador_data.get('ip'):
                print(f"❌ Jogador com IP {ip} tem dados incompletos")
                return None
            
            resultado = {
                "uid": jogador_data.get('uid'),
                "nick": jogador_data.get('nick', 'Jogador'),
                "ip": jogador_data.get('ip', ip),
                "nivel": jogador_data.get('nivel', 1),
                "dinheiro": jogador_data.get('dinheiro', 1000),
                "firewall": jogador_data.get('firewall', 1),
                "antivirus": jogador_data.get('antivirus', 1),
                "cpu": jogador_data.get('cpu', 1),
                "malware_kit": jogador_data.get('malware_kit', 1),
                "bruteforce": jogador_data.get('bruteforce', 1),
                "bankguard": jogador_data.get('bankguard', 1),
                "proxyvpn": jogador_data.get('proxyvpn', 1)
            }
            
            print(f"✅ Jogador REAL encontrado: {resultado['nick']} (IP: {resultado['ip']})")
            return resultado
        
        print(f"❌ Nenhum jogador REAL encontrado com IP: {ip}")
        return None
        
    except Exception as e:
        print(f"❌ Erro ao buscar jogador por IP {ip}: {e}")
        return None

def realizar_exploit(uid: str, ip_alvo: str) -> Dict[str, Any]:
    """
    Realiza exploit contra um alvo (jogador real ou vítima estática).
    Lógica: CPU do atacante vs FIREWALL do alvo.
    """
    print(f"[DEBUG EXPLOIT] 🎯 Iniciando exploit: atacante={uid}, alvo_ip={ip_alvo}")
    try:
        if not supabase_client.is_connected():
            return {"sucesso": False, "mensagem": "Database não disponível"}
        
        # Busca atacante
        atacante = get_jogador(uid)
        if not atacante:
            return {"sucesso": False, "mensagem": "Atacante não encontrado"}
        
        # Primeiro tenta buscar jogador real por IP
        alvo = None
        alvo_uid = None
        is_jogador_real = False
        
        try:
            # Busca jogador real no Supabase
            print(f"[DEBUG EXPLOIT] 🔍 Buscando jogador real com IP: {ip_alvo}")
            response = supabase_client.client.table('usuarios').select(
                'uid, nick, ip, nivel, dinheiro, firewall, antivirus, cpu, malware_kit, xp, log'
            ).eq('ip', ip_alvo).limit(1).execute()

            print(f"[DEBUG EXPLOIT] 📊 Resposta da busca: {len(response.data) if response.data else 0} jogadores encontrados")

            if response.data:
                alvo_data = response.data[0]
                alvo_uid = alvo_data['uid']  # Corrigido: usar 'uid' ao invés de 'id'
                print(f"[DEBUG EXPLOIT] 🎯 Jogador real encontrado: {alvo_data.get('nick')} (UID: {alvo_uid})")

                # Não pode atacar a si mesmo
                if uid == alvo_uid:
                    return {"sucesso": False, "mensagem": "Você não pode atacar a si mesmo"}
                
                alvo = {
                    'uid': alvo_uid,  # UID correto do jogador real
                    'nick': alvo_data.get('nick', 'Desconhecido'),
                    'ip': alvo_data.get('ip', ip_alvo),
                    'nivel': alvo_data.get('nivel', 1),
                    'dinheiro': alvo_data.get('dinheiro', 0),
                    'firewall': alvo_data.get('firewall', 1),
                    'antivirus': alvo_data.get('antivirus', 1),
                    'cpu': alvo_data.get('cpu', 1),
                    'malware_kit': alvo_data.get('malware_kit', 1),
                    'xp': alvo_data.get('xp', 0),
                    'log': alvo_data.get('log', [])
                }
                is_jogador_real = True
                print(f"🎯 Atacando jogador real: {alvo['nick']}")
            
        except Exception as e:
            print(f"[DEBUG EXPLOIT] ❌ Erro ao buscar jogador real: {e}")

        # Se não encontrou jogador real, retornar erro (não há bots no jogo)
        if not alvo:
            print(f"[DEBUG EXPLOIT] ❌ Nenhum jogador encontrado para IP: {ip_alvo}")
            return {"sucesso": False, "mensagem": "Nenhum jogador encontrado com este IP"}

        print(f"[DEBUG EXPLOIT] ✅ Jogador real encontrado: {alvo['nick']}")
        
        # Lógica de combate: CPU vs FIREWALL (mais balanceada)
        poder_ataque = atacante.get('cpu', 1)
        defesa_alvo = alvo.get('firewall', 1)
        
        # Aplica efeitos de habilidades NFT do atacante
        habilidades_atacante = atacante.get('habilidades_adquiridas', {})
        if 'firewall_reverso' in habilidades_atacante:
            defesa_alvo = int(defesa_alvo * 0.95)  # Reduz 5% da defesa
        
        # Lógica de sucesso mais balanceada
        if poder_ataque >= defesa_alvo:
            sucesso = True  # Sucesso garantido se CPU >= Firewall
        elif poder_ataque == (defesa_alvo - 1):
            # 50% de chance se CPU é só 1 ponto menor que Firewall
            import random
            sucesso = random.random() < 0.5
        else:
            sucesso = False  # Falha se a diferença for maior que 1
        
        timestamp_atual = datetime.now(timezone.utc).timestamp()
        
        if sucesso:
            print(f"[DEBUG EXPLOIT] Exploit bem-sucedido! is_jogador_real: {is_jogador_real}, alvo_uid: {alvo_uid}")

            # Log para o ATACANTE (invasão realizada)
            log_invasao_atacante = {
                "mensagem": f"🚀 Exploitou com sucesso {alvo['nick']} (IP: {alvo['ip']})",
                "timestamp": timestamp_atual,
                "tipo": "invasao_realizada"
            }
            
            # Adiciona log ao atacante
            logs_atacante = atacante.get('log', [])
            logs_atacante.append(log_invasao_atacante)
            
            # Mantém apenas os últimos 100 logs
            if len(logs_atacante) > 100:
                logs_atacante = logs_atacante[-100:]
            
            # Atualiza atacante com XP (só se for jogador real)
            if is_jogador_real:
                # Adiciona 25 XP por exploit bem-sucedido
                atacante_com_xp = ganhar_xp(atacante.copy(), 25)
                
                # Atualiza atacante no banco
                supabase_client.atualizar_usuario(uid, {
                    'xp': atacante_com_xp.get('xp', 0),
                    'nivel': atacante_com_xp.get('nivel', 1),
                    'log': logs_atacante,
                    'historico': atacante_com_xp.get('historico', [])
                })
                
                # Log para o ALVO (invasão recebida) - só se for jogador real
                log_invasao_alvo = {
                    "mensagem": f"🚨 Sistema invadido por {atacante['nick']} (IP: {atacante['ip']})",
                    "timestamp": timestamp_atual,
                    "tipo": "invasao_recebida"
                }

                # Adiciona log ao alvo
                logs_alvo = alvo.get('log', [])
                logs_alvo.append(log_invasao_alvo)

                if len(logs_alvo) > 100:
                    logs_alvo = logs_alvo[-100:]

                # Atualiza alvo no banco
                supabase_client.atualizar_usuario(alvo_uid, {
                    'log': logs_alvo
                })

                # Registrar security alert APENAS para a VÍTIMA (alvo_uid)
                # Log de exploit inicial - quando sistema é invadido
                try:
                    # Verificar ProxyVPN vs Firewall para anonimização
                    atacante_proxy_vpn = atacante.get('proxyvpn', 0)
                    alvo_firewall = alvo.get('firewall', 0)

                    # Se ProxyVPN >= Firewall, informações ficam anônimas
                    if atacante_proxy_vpn >= alvo_firewall:
                        atacante_ip_log = "unknown"
                        atacante_nick_log = "unknown"
                        print(f"[PROXY VPN] Atacante anônimo: ProxyVPN {atacante_proxy_vpn} >= Firewall {alvo_firewall}")
                    else:
                        atacante_ip_log = atacante.get('ip', 'IP desconhecido')
                        atacante_nick_log = atacante.get('nick', 'Desconhecido')
                        print(f"[PROXY VPN] Atacante identificado: ProxyVPN {atacante_proxy_vpn} < Firewall {alvo_firewall}")

                    supabase_client.log_atividade(
                        alvo_uid,  # ← VÍTIMA recebe o log
                        'security_alert',
                        {
                            'tipo': 'exploit_inicial',
                            'atacante_ip': atacante_ip_log,
                            'atacante_nick': atacante_nick_log,
                            'poder_ataque': poder_ataque,
                            'defesa_alvo': defesa_alvo,
                            'proxy_vpn_usado': atacante_proxy_vpn >= alvo_firewall,
                            'mensagem': f"Security alert! sistema invadido por {atacante_ip_log}"
                        }
                    )
                    print(f"[SECURITY LOG] Log de exploit inicial registrado para VÍTIMA: {alvo_uid}")
                except Exception as log_error:
                    print(f"[SECURITY LOG] Erro ao registrar log de exploit: {log_error}")


            # Log duplicado removido - mantendo apenas a versão com lógica ProxyVPN vs Firewall acima

            # Ganho de XP e level up
            if is_jogador_real:
                xp_ganho_msg = f" +25 XP!"
                if atacante_com_xp.get('nivel', 1) > atacante.get('nivel', 1):
                    xp_ganho_msg += f" 🏆 Level up! Nível {atacante_com_xp.get('nivel', 1)}!"
            else:
                # Só atualiza o log para vítimas estáticas
                supabase_client.atualizar_usuario(uid, {'log': logs_atacante})
                xp_ganho_msg = ""
            
            # VERIFICAR LIMITE DE CONEXÕES ANTES DE CRIAR NOVA CONEXÃO
            status_conexoes = obter_status_conexoes_jogador(uid)

            if status_conexoes['no_limite']:
                # Exploit teve sucesso mas não pode criar conexão devido ao limite
                return {
                    "sucesso": False,  # Falha devido ao limite de conexões
                    "mensagem": f"🚫 Exploit bem-sucedido contra {alvo['nick']}, mas você já atingiu o limite de conexões! {status_conexoes['resumo']}. Feche algumas conexões ou aguarde expirarem para estabelecer novas conexões.{xp_ganho_msg}",
                    "alvo_explorado": {
                        "uid": alvo_uid,  # Sempre jogador real
                        "nick": alvo['nick'],
                        "ip": alvo['ip'],
                        "nivel": alvo.get('nivel', 1),
                        "dinheiro": alvo.get('dinheiro', 0)
                    },
                    "is_jogador_real": is_jogador_real,
                    "conexao_criada": False,
                    "limite_conexoes_atingido": True,
                    "status_conexoes": status_conexoes
                }
            
            # CRIAR CONEXÃO ATIVA PARA O TERMINAL
            resultado_conexao = criar_conexao_ativa(uid, alvo['ip'], alvo)
            if resultado_conexao['sucesso']:
                conexao_msg = f" Conexão estabelecida - acesse o Terminal para executar bruteforce! ({resultado_conexao['conexoes_ativas']}/5 conexões ativas)"
            else:
                conexao_msg = f" ⚠️ Erro ao estabelecer conexão: {resultado_conexao.get('mensagem', 'Erro desconhecido')}"
                
            return {
                "sucesso": True,
                "mensagem": f"Exploit bem-sucedido! Você invadiu o sistema de {alvo['nick']}.{xp_ganho_msg}{conexao_msg}",
                "alvo_explorado": {
                    "uid": alvo_uid,  # Sempre jogador real
                    "nick": alvo['nick'],
                    "ip": alvo['ip'],
                    "nivel": alvo.get('nivel', 1),
                    "dinheiro": alvo.get('dinheiro', 0)
                },
                "is_jogador_real": is_jogador_real,
                "conexao_criada": resultado_conexao['sucesso'],
                "status_conexoes": status_conexoes if resultado_conexao['sucesso'] else None
            }
        else:
            # Log para o atacante (falha na invasão)
            log_falha_atacante = {
                "mensagem": f"❌ Falha ao exploitar {alvo['nick']} (IP: {alvo['ip']}) - Firewall muito forte",
                "timestamp": timestamp_atual,
                "tipo": "invasao_falhada"
            }
            
            # Atualiza log do atacante
            logs_atacante = atacante.get('log', [])
            logs_atacante.append(log_falha_atacante)
            
            if len(logs_atacante) > 100:
                logs_atacante = logs_atacante[-100:]
            
            supabase_client.atualizar_usuario(uid, {'log': logs_atacante})
            
            return {
                "sucesso": False,
                "mensagem": f"Exploit falhou! O firewall de {alvo['nick']} (Nível {defesa_alvo}) é mais forte que seu CPU (Nível {poder_ataque}).",
                "poder_ataque": poder_ataque,
                "defesa_alvo": defesa_alvo
            }
    
    except Exception as e:
        print(f"Erro no exploit: {e}")
        return {"sucesso": False, "mensagem": "Erro interno do servidor"}

def calcular_custo_upgrade(item: str, nivel_atual: int) -> int:
    """Calcula custo de upgrade de um item"""
    base_costs = {
        'cpu': 1,            # Reduzido de 10 para 1
        'ram': 1,            # Reduzido de 10 para 1
        'firewall': 1,       # Reduzido de 10 para 1
        'antivirus': 1,      # Reduzido de 10 para 1
        'malware_kit': 1,    # Reduzido de 10 para 1
        'bruteforce': 1,     # Reduzido de 10 para 1
        'bankguard': 1,      # Reduzido de 10 para 1
        'proxyvpn': 1,       # Reduzido de 10 para 1
        'nivel_mineradora': 150  # Mantido mais alto para mineradora
    }
    
    base_cost = base_costs.get(item, 1)
    # Mudança: multiplicador reduzido de 1.2 para 1.15 (15% de aumento em vez de 20%)
    return int(base_cost * (1.15 ** (nivel_atual - 1)))

def comprar_upgrade(uid: str, item: str, quantidade: int = 1) -> Dict[str, Any]:
    """Compra upgrade de equipamento com sistema de XP, Shacks e pontos de torneio"""
    try:
        if not supabase_client.is_connected():
            return {"sucesso": False, "mensagem": "Database não disponível"}
        
        jogador = get_jogador(uid)
        if not jogador:
            return {"sucesso": False, "mensagem": "Jogador não encontrado"}
        
        nivel_atual = jogador.get(item, 1)
        
        # Calcula custos usando a nova função que inclui Shacks
        custos = calcular_custo_upgrade_multiplo(item, nivel_atual, quantidade)
        custo_dinheiro = custos.get("custo_dinheiro", 0)
        custo_shacks = custos.get("custo_shacks", 0)
        
        # Verifica se tem dinheiro suficiente
        if jogador['dinheiro'] < custo_dinheiro:
            return {"sucesso": False, "mensagem": "Dinheiro insuficiente"}
        
        # Verifica se tem Shacks suficientes (se necessário)
        if custo_shacks > 0 and jogador.get('shack', 0) < custo_shacks:
            return {"sucesso": False, "mensagem": f"Shacks insuficientes. Necessário: {custo_shacks} Shacks"}
        
        # Calcula XP ganho baseado nos upgrades
        xp_ganho = sum([calcular_xp_upgrade(nivel_atual + i + 1) for i in range(quantidade)])
        
        # Executa compra
        novo_nivel = nivel_atual + quantidade
        novo_dinheiro = jogador['dinheiro'] - custo_dinheiro
        novo_shacks = jogador.get('shack', 0) - custo_shacks
        
        # Aplica XP e verifica level up
        jogador_atualizado = ganhar_xp(jogador.copy(), xp_ganho)
        
        updates = {
            item: novo_nivel,
            'dinheiro': novo_dinheiro,
            'xp': jogador_atualizado.get('xp', 0),
            'nivel': jogador_atualizado.get('nivel', 1),
            'historico': jogador_atualizado.get('historico', [])
        }
        
        # Apenas atualiza Shacks se foram consumidos
        if custo_shacks > 0:
            updates['shack'] = novo_shacks
        
        result = atualizar_jogador(uid, updates)
        
        if result['sucesso']:
            # NOVO: Adicionar pontos de torneio se for o app do dia
            pontos_torneio_adicionados = adicionar_pontos_torneio_upgrade(uid, item, quantidade)
            
            supabase_client.log_atividade(
                uid,
                'compra_upgrade',
                {
                    'item': item,
                    'nivel_antigo': nivel_atual,
                    'nivel_novo': novo_nivel,
                    'custo_dinheiro': custo_dinheiro,
                    'custo_shacks': custo_shacks,
                    'xp_ganho': xp_ganho,
                    'nivel_jogador': jogador_atualizado.get('nivel', 1),
                    'pontos_torneio': pontos_torneio_adicionados
                }
            )
            
            nivel_final = jogador_atualizado.get('nivel', 1)
            level_up_msg = ""
            if nivel_final > jogador.get('nivel', 1):
                level_up_msg = f" 🏆 Parabéns! Você subiu para o nível {nivel_final}!"
            
            # Mensagem adicional se ganhou pontos de torneio
            torneio_msg = ""
            if pontos_torneio_adicionados:
                app_do_dia = get_app_do_torneio_hoje()
                torneio_msg = f" 🏆 +{quantidade} ponto(s) de torneio! (App do dia: {app_do_dia})"
            
            # Monta mensagem incluindo consumo de Shacks se aplicável
            mensagem_custo = f"${custo_dinheiro:,}"
            if custo_shacks > 0:
                mensagem_custo += f" + {custo_shacks} Shacks"
            
            return {
                "sucesso": True,
                "mensagem": f"Upgrade concluído! {item} nível {novo_nivel}. Custo: {mensagem_custo}. +{xp_ganho} XP!{level_up_msg}{torneio_msg}",
                "nivel_novo": novo_nivel,
                "custo_dinheiro": custo_dinheiro,
                "custo_shacks": custo_shacks,
                "xp_ganho": xp_ganho,
                "nivel_jogador": nivel_final,
                "level_up": nivel_final > jogador.get('nivel', 1),
                "pontos_torneio_ganhos": quantidade if pontos_torneio_adicionados else 0,
                "app_do_dia": get_app_do_torneio_hoje() if pontos_torneio_adicionados else None
            }
        else:
            return {"sucesso": False, "mensagem": "Erro ao executar upgrade"}
    
    except Exception as e:
        print(f"Erro ao comprar upgrade: {e}")
        return {"sucesso": False, "mensagem": "Erro interno do servidor"}

def comprar_upgrade(uid: str, item: str, quantidade: int = 1) -> Dict[str, Any]:
    """Compra upgrade de equipamento com sistema de XP e Shacks"""
    try:
        if not supabase_client.is_connected():
            return {"sucesso": False, "mensagem": "Database não disponível"}
        
        jogador = get_jogador(uid)
        if not jogador:
            return {"sucesso": False, "mensagem": "Jogador não encontrado"}
        
        nivel_atual = jogador.get(item, 1)
        
        # Calcula custos usando a nova função que inclui Shacks
        custos = calcular_custo_upgrade_multiplo(item, nivel_atual, quantidade)
        custo_dinheiro = custos.get("custo_dinheiro", 0)
        custo_shacks = custos.get("custo_shacks", 0)
        
        # Verifica se tem dinheiro suficiente
        if jogador['dinheiro'] < custo_dinheiro:
            return {"sucesso": False, "mensagem": "Dinheiro insuficiente"}
        
        # Verifica se tem Shacks suficientes (se necessário)
        if custo_shacks > 0 and jogador.get('shack', 0) < custo_shacks:
            return {"sucesso": False, "mensagem": f"Shacks insuficientes. Necessário: {custo_shacks} Shacks"}
        
        # Calcula XP ganho baseado nos upgrades
        xp_ganho = sum([calcular_xp_upgrade(nivel_atual + i + 1) for i in range(quantidade)])
        
        # Executa compra
        novo_nivel = nivel_atual + quantidade
        novo_dinheiro = jogador['dinheiro'] - custo_dinheiro
        novo_shacks = jogador.get('shack', 0) - custo_shacks
        
        # Aplica XP e verifica level up
        jogador_atualizado = ganhar_xp(jogador.copy(), xp_ganho)
        
        updates = {
            item: novo_nivel,
            'dinheiro': novo_dinheiro,
            'xp': jogador_atualizado.get('xp', 0),
            'nivel': jogador_atualizado.get('nivel', 1),
            'historico': jogador_atualizado.get('historico', [])
        }
        
        # Apenas atualiza Shacks se foram consumidos
        if custo_shacks > 0:
            updates['shack'] = novo_shacks
        
        result = atualizar_jogador(uid, updates)
        
        if result['sucesso']:
            supabase_client.log_atividade(
                uid,
                'compra_upgrade',
                {
                    'item': item,
                    'nivel_antigo': nivel_atual,
                    'nivel_novo': novo_nivel,
                    'custo_dinheiro': custo_dinheiro,
                    'custo_shacks': custo_shacks,
                    'xp_ganho': xp_ganho,
                    'nivel_jogador': jogador_atualizado.get('nivel', 1)
                }
            )
            
            nivel_final = jogador_atualizado.get('nivel', 1)
            level_up_msg = ""
            if nivel_final > jogador.get('nivel', 1):
                level_up_msg = f" 🏆 Parabéns! Você subiu para o nível {nivel_final}!"
            
            # Monta mensagem incluindo consumo de Shacks se aplicável
            mensagem_custo = f"${custo_dinheiro:,}"
            if custo_shacks > 0:
                mensagem_custo += f" + {custo_shacks} Shacks"
            
            return {
                "sucesso": True,
                "mensagem": f"Upgrade concluído! {item} nível {novo_nivel}. Custo: {mensagem_custo}. +{xp_ganho} XP!{level_up_msg}",
                "nivel_novo": novo_nivel,
                "custo_dinheiro": custo_dinheiro,
                "custo_shacks": custo_shacks,
                "xp_ganho": xp_ganho,
                "nivel_jogador": nivel_final,
                "level_up": nivel_final > jogador.get('nivel', 1)
            }
        else:
            return {"sucesso": False, "mensagem": "Erro ao executar upgrade"}
    
    except Exception as e:
        print(f"Erro ao comprar upgrade: {e}")
        return {"sucesso": False, "mensagem": "Erro interno do servidor"}

# --- FUNÇÕES DE NOTÍCIAS ---
def get_noticias() -> List[Dict[str, Any]]:
    """Retorna todas as notícias"""
    return supabase_client.get_todas_noticias()

def obter_noticias() -> List[Dict[str, Any]]:
    """Alias para get_noticias - compatibilidade com routes.py"""
    return get_noticias()

def criar_noticia(autor_uid: str, titulo: str, conteudo: str, prioridade: bool = False) -> str:
    """Cria uma nova notícia"""
    try:
        if not supabase_client.is_connected():
            raise Exception("Conexão com Supabase não disponível")

        # Buscar dados do autor
        autor = get_jogador(autor_uid)
        if not autor:
            raise Exception("Autor não encontrado")

        # Dados da notícia (usando nomes de colunas em inglês da tabela existente)
        noticia_data = {
            'title': titulo,
            'content': conteudo,
            'priority': prioridade,
            'is_active': True
        }

        # Inserir no Supabase
        result = supabase_client.client.table('noticias').insert(noticia_data).execute()

        if result.data and len(result.data) > 0:
            return result.data[0]['id']
        else:
            raise Exception("Erro ao inserir notícia na database")

    except Exception as e:
        print(f"Erro ao criar notícia: {e}")
        raise e

def excluir_noticia(noticia_id: str) -> bool:
    """Exclui uma notícia"""
    try:
        if not supabase_client.is_connected():
            return False

        # Excluir do Supabase
        result = supabase_client.client.table('noticias').delete().eq('id', noticia_id).execute()

        # Verificar se alguma linha foi afetada
        return result.data is not None and len(result.data) > 0

    except Exception as e:
        print(f"Erro ao excluir notícia: {e}")
        return False

# --- FUNÇÕES DE MINERAÇÃO ---
def processar_mineracao_automatica_dinheiro() -> Dict[str, Any]:
    """
    Processa mineração automática de dinheiro para todos os jogadores
    Sistema corrigido com valores mais atrativos e debugging melhorado
    """
    print("🚀🚀🚀 [MINERACAO NOVA VERSAO] FUNÇÃO ATUALIZADA SENDO EXECUTADA!")
    print("🔥 [DEBUG] Esta é a versão com correções para nível 1 e jogadores online!")
    print("🚀 [MINERACAO] Iniciando processamento automático de mineração...")
    try:
        if not supabase_client.is_connected():
            print("❌ [MINERACAO] Database não disponível")
            return {"sucesso": False, "mensagem": "Database não disponível"}

        # Busca todos os usuários
        usuarios = supabase_client.get_ranking_usuarios(1000)
        if not usuarios:
            print("⚠️ [MINERACAO] Nenhum usuário encontrado")
            return {"sucesso": True, "jogadores_processados": 0, "total_dinheiro_gerado": 0, "mensagem": "Nenhum usuário encontrado"}

        print(f"👥 [MINERACAO] Encontrados {len(usuarios)} usuários")

        processados = 0
        total_dinheiro_gerado = 0
        agora = datetime.now(timezone.utc)
        timestamp_atual = agora.timestamp()
        timestamp_atual_iso = agora.isoformat()

        print(f"⏰ [MINERACAO] Timestamp atual: {timestamp_atual}")

        # DEBUG: Verificar estado do cache
        print(f"🔍 [MINERACAO] Cache atual: {len(MINERACAO_AUTOMATICA_CACHE)} entradas")
        if MINERACAO_AUTOMATICA_CACHE:
            print(f"🔍 [MINERACAO] Primeiras entradas do cache:")
            for i, (uid, ts) in enumerate(list(MINERACAO_AUTOMATICA_CACHE.items())[:3]):
                tempo_desde = timestamp_atual - ts
                print(f"   {i+1}. {uid}: {tempo_desde:.1f}s atrás")

        print(f"🔄 [MINERACAO] Iniciando loop de processamento...")

        for i, usuario in enumerate(usuarios):
            print(f"\n👤 [MINERACAO] Processando usuário {i+1}/{len(usuarios)}")
            try:
                uid = usuario.get('uid')
                nick = usuario.get('nick', 'Unknown')

                # DEBUG: Verificar status online do jogador
                is_online = usuario.get('is_online', False)
                last_activity = usuario.get('last_activity', 'N/A')
                print(f"🌐 [MINERACAO DEBUG] {nick}: Online = {is_online}, Last Activity = {last_activity}")

                if not uid:
                    print(f"⚠️ [MINERACAO] {nick}: UID inválido")
                    continue

                # Verifica nível da mineradora (GARANTINDO que nível 1 funcione)
                nivel_mineradora = usuario.get('nivel_mineradora', 1)
                print(f"🔍 [MINERACAO DEBUG] {nick}: Nível mineradora = {nivel_mineradora} (tipo: {type(nivel_mineradora)})")

                # DEBUG: Verificar se há alguma condição que pula jogadores online
                if is_online:
                    print(f"🟢 [MINERACAO DEBUG] {nick}: Jogador está ONLINE - verificando se será processado...")
                else:
                    print(f"🔴 [MINERACAO DEBUG] {nick}: Jogador está OFFLINE")

                if nivel_mineradora < 1:
                    print(f"⚠️ [MINERACAO] {nick}: Mineradora inativa (nível {nivel_mineradora})")
                    continue

                # DEBUG ESPECÍFICO PARA NÍVEL 1
                if nivel_mineradora == 1:
                    print(f"🎯 [MINERACAO DEBUG LEVEL 1] {nick}: Processando jogador nível 1 - DEVE GERAR $600!")

                # Verifica cache para evitar processamento muito frequente
                try:
                    if uid in MINERACAO_AUTOMATICA_CACHE:
                        ultimo_processamento = MINERACAO_AUTOMATICA_CACHE[uid]
                        tempo_desde_ultimo = timestamp_atual - ultimo_processamento

                        # CORRIGIDO: Cooldown de 30 segundos (menor que o timer de 60s)
                        if tempo_desde_ultimo < 30:  # 30 segundos de cooldown
                            print(f"⏱️ [MINERACAO] {nick}: Cooldown ativo ({tempo_desde_ultimo:.1f}s)")
                            continue
                        else:
                            print(f"✅ [MINERACAO] {nick}: Cooldown expirado ({tempo_desde_ultimo:.1f}s)")
                    else:
                        print(f"✅ [MINERACAO] {nick}: Primeira execução ou cache limpo")
                except (NameError, AttributeError):
                    # Cache não existe, continuar sem verificação de cooldown
                    print(f"⚠️ [MINERACAO] {nick}: Cache não disponível, processando sem cooldown")

                # FÓRMULA CORRIGIDA: Valores conforme especificação
                # Nível 1: $600/min (base)
                # Nível 2: $600/min (mesmo valor por enquanto)
                # Nível 3+: Progressão futura
                if nivel_mineradora == 1:
                    dinheiro_base = 600  # Valor fixo para nível 1
                    print(f"🎯 [MINERACAO DEBUG LEVEL 1] {nick}: Aplicando valor fixo $600 para nível 1")
                elif nivel_mineradora == 2:
                    dinheiro_base = 600  # Mesmo valor para nível 2
                    print(f"🎯 [MINERACAO DEBUG LEVEL 2] {nick}: Aplicando valor fixo $600 para nível 2")
                else:
                    # Fórmula progressiva para níveis superiores
                    dinheiro_base = 600 + (nivel_mineradora - 2) * 100
                    print(f"🎯 [MINERACAO DEBUG LEVEL {nivel_mineradora}] {nick}: Aplicando fórmula progressiva = $600 + {(nivel_mineradora - 2) * 100}")

                print(f"💰 [MINERACAO DEBUG] {nick}: Dinheiro base calculado = ${dinheiro_base}/min")

                # Aplicar efeito da NFT Firstsupp (+100% geração de dinheiro)
                dinheiro_por_minuto = aplicar_efeito_nft_firstsupp_dinheiro(dinheiro_base, usuario)

                print(f"💎 [MINERACAO DEBUG] {nick}: Dinheiro após NFT = ${dinheiro_por_minuto}/min")

                # Calcula dinheiro gerado (1 minuto de processamento)
                dinheiro_gerado = int(dinheiro_por_minuto)

                print(f"🔢 [MINERACAO DEBUG] {nick}: Dinheiro gerado final = ${dinheiro_gerado}")

                if dinheiro_gerado <= 0:
                    print(f"❌ [MINERACAO ERROR] {nick}: Dinheiro gerado = 0, PULANDO! (Base: ${dinheiro_base}, NFT: ${dinheiro_por_minuto})")
                    if nivel_mineradora == 1:
                        print(f"🚨 [CRITICAL ERROR] {nick}: NÍVEL 1 GERANDO $0 - ISSO NÃO DEVERIA ACONTECER!")
                    continue

                # DEBUG: Verificar se jogador online será processado até aqui
                if is_online:
                    print(f"✅ [MINERACAO DEBUG ONLINE] {nick}: Jogador online chegou até o processamento de dinheiro!")

                # Busca saldo atual
                dinheiro_atual = usuario.get('dinheiro', 0)
                novo_dinheiro = dinheiro_atual + dinheiro_gerado

                print(f"🏦 [MINERACAO DEBUG] {nick}: ${dinheiro_atual} + ${dinheiro_gerado} = ${novo_dinheiro}")

                # DEBUG: Verificar se jogador online chega até a atualização
                if is_online:
                    print(f"🎯 [MINERACAO DEBUG ONLINE] {nick}: Jogador online chegou até a ATUALIZAÇÃO DO BANCO!")

                if nivel_mineradora == 1:
                    print(f"🎯 [MINERACAO DEBUG LEVEL 1] {nick}: Nível 1 chegou até a ATUALIZAÇÃO DO BANCO!")

                # Atualiza saldo e timestamp no banco
                try:
                    print(f"📝 [MINERACAO DEBUG] {nick}: Iniciando atualização no banco...")
                    result = supabase_client.atualizar_usuario(uid, {
                        'dinheiro': novo_dinheiro,
                        'ultimo_dinheiro_mineracao_timestamp': timestamp_atual_iso
                    })

                    print(f"📊 [MINERACAO DEBUG] {nick}: Resultado da atualização = {result}")

                    if result.get('sucesso'):
                        processados += 1
                        total_dinheiro_gerado += dinheiro_gerado

                        # Atualiza cache (se disponível)
                        try:
                            MINERACAO_AUTOMATICA_CACHE[uid] = timestamp_atual
                        except (NameError, AttributeError):
                            # Cache não disponível, continuar sem atualizar
                            pass

                        print(f"✅ [MINERACAO SUCCESS] {nick}: +${dinheiro_gerado} (Total: ${novo_dinheiro})")

                        # DEBUG ESPECÍFICO PARA CASOS IMPORTANTES
                        if is_online:
                            print(f"🟢 [MINERACAO SUCCESS ONLINE] {nick}: Jogador ONLINE processado com sucesso!")
                        if nivel_mineradora == 1:
                            print(f"🎯 [MINERACAO SUCCESS LEVEL 1] {nick}: Nível 1 processado com sucesso!")

                        # Log da atividade (sem bloquear se falhar)
                        try:
                            supabase_client.log_atividade(
                                uid,
                                'mineracao_automatica',
                                {
                                    'dinheiro_gerado': dinheiro_gerado,
                                    'nivel_mineradora': nivel_mineradora,
                                    'saldo_anterior': dinheiro_atual,
                                    'novo_saldo': novo_dinheiro
                                }
                            )
                        except Exception as log_error:
                            print(f"⚠️ [MINERACAO] {nick}: Erro no log (não crítico) - {log_error}")

                    else:
                        erro_msg = result.get('erro', result.get('mensagem', 'Erro desconhecido'))
                        print(f"❌ [MINERACAO ERROR] {nick}: Falha na atualização - {erro_msg}")
                        if is_online:
                            print(f"🔴 [MINERACAO ERROR ONLINE] {nick}: Jogador ONLINE falhou na atualização!")
                        if nivel_mineradora == 1:
                            print(f"🚨 [MINERACAO ERROR LEVEL 1] {nick}: Nível 1 falhou na atualização!")

                except Exception as update_error:
                    print(f"❌ [MINERACAO EXCEPTION] {nick}: Exceção na atualização - {update_error}")
                    if is_online:
                        print(f"🔴 [MINERACAO EXCEPTION ONLINE] {nick}: Jogador ONLINE teve exceção!")
                    if nivel_mineradora == 1:
                        print(f"🚨 [MINERACAO EXCEPTION LEVEL 1] {nick}: Nível 1 teve exceção!")
                    continue

            except Exception as e:
                print(f"❌ [MINERACAO] Erro ao processar {usuario.get('nick', 'Unknown')}: {e}")
                continue

        print(f"📊 [MINERACAO] CONCLUÍDO: {processados}/{len(usuarios)} jogadores processados, ${total_dinheiro_gerado} gerado")

        return {
            "sucesso": True,
            "jogadores_processados": processados,
            "total_dinheiro_gerado": total_dinheiro_gerado,
            "mensagem": f"Mineração processada: {processados} jogadores, ${total_dinheiro_gerado} gerado"
        }

    except Exception as e:
        print(f"❌ [MINERACAO] ERRO GERAL: {e}")
        return {"sucesso": False, "mensagem": f"Erro: {str(e)}"}

# Cache já definido no topo do arquivo

def calcular_shacks_disponiveis_para_coleta(uid: str) -> Dict[str, Any]:
    """Calcula quantos Shacks estão disponíveis para coleta sem coletá-los"""
    try:
        if not supabase_client.is_connected():
            return {"sucesso": False, "mensagem": "Database não disponível"}
        
        usuario = get_jogador(uid)
        if not usuario:
            return {"sucesso": False, "mensagem": "Usuário não encontrado"}
        
        nivel_mineradora = usuario.get('nivel_mineradora', 1)
        if nivel_mineradora < 1:
            return {
                "sucesso": True,
                "shacks_disponiveis": 0,
                "tempo_proxima_coleta": 0,
                "mensagem": "Mineradora não ativa"
            }
        
        agora = datetime.now(timezone.utc)
        timestamp_atual = agora.timestamp()
        
        # Verifica cooldown em cache (evita coletas muito frequentes)
        if uid in SHACKS_COLETA_CACHE:
            ultima_coleta = SHACKS_COLETA_CACHE[uid]
            tempo_desde_ultima = timestamp_atual - ultima_coleta
            
            # Cooldown de 30 minutos entre coletas
            if tempo_desde_ultima < 1800:  # 30 minutos
                tempo_restante = int(1800 - tempo_desde_ultima)
                return {
                    "sucesso": True,
                    "shacks_disponiveis": 0,
                    "shack_por_minuto": nivel_mineradora * 0.6,
                    "minutos_acumulados": 0,
                    "tempo_proxima_coleta": tempo_restante,
                    "nivel_mineradora": nivel_mineradora,
                    "crypto_miner_ativo": 'crypto_miner' in usuario.get('habilidades_adquiridas', {})
                }
        
        # Calcula Shack por minuto - CORRIGIDO: nível 1 também deve produzir
        shack_por_minuto = max(0, nivel_mineradora * 0.6)
        
        # Aplica bônus de habilidades NFT
        habilidades = usuario.get('habilidades_adquiridas', {})
        if 'crypto_miner' in habilidades:
            shack_por_minuto = aplicar_efeito_habilidade_mineracao(shack_por_minuto, usuario)
        
        # Sistema baseado em nível: cada nível permite mais Shacks por coleta
        # Nível 1: 18 Shacks (30 min * 0.6), Nível 2: 36 Shacks (30 min * 1.2), etc.
        minutos_de_producao = 30  # 30 minutos de produção por coleta
        shacks_por_coleta = int(shack_por_minuto * minutos_de_producao)
        
        # Garante mínimo de 1 Shack se a mineradora está ativa
        shacks_disponiveis = max(1, shacks_por_coleta) if shack_por_minuto > 0 else 0
        
        return {
            "sucesso": True,
            "shacks_disponiveis": shacks_disponiveis,
            "shack_por_minuto": round(shack_por_minuto, 2),
            "minutos_acumulados": minutos_de_producao,
            "tempo_proxima_coleta": 0,  # Disponível agora
            "nivel_mineradora": nivel_mineradora,
            "crypto_miner_ativo": 'crypto_miner' in habilidades
        }
        
    except Exception as e:
        print(f"Erro ao calcular Shacks disponíveis: {e}")
        return {"sucesso": False, "mensagem": f"Erro interno: {str(e)}"}

def coletar_shacks_mineracao(uid: str) -> Dict[str, Any]:
    """Coleta os Shacks acumulados da mineração"""
    try:
        if not supabase_client.is_connected():
            return {"sucesso": False, "mensagem": "Database não disponível"}
        
        # Primeiro calcula quantos Shacks estão disponíveis
        calculo = calcular_shacks_disponiveis_para_coleta(uid)
        if not calculo['sucesso']:
            return calculo
        
        shacks_disponiveis = calculo['shacks_disponiveis']
        
        if shacks_disponiveis <= 0:
            return {
                "sucesso": False,
                "mensagem": "Nenhum Shack disponível para coleta",
                "shacks_coletados": 0
            }
        
        # Busca usuário novamente para ter dados atualizados
        usuario = get_jogador(uid)
        if not usuario:
            return {"sucesso": False, "mensagem": "Usuário não encontrado"}
        
        # Adiciona Shacks coletados ao saldo
        novo_shack = usuario.get('shack', 0) + shacks_disponiveis
        
        # Atualiza usuário - usa apenas o campo que sabemos que existe
        result = atualizar_jogador(uid, {'shack': novo_shack})
        
        if result.get('sucesso', False):
            # Registra timestamp da coleta no cache para evitar spam
            agora = datetime.now(timezone.utc)
            SHACKS_COLETA_CACHE[uid] = agora.timestamp()
            
            # Log da atividade
            supabase_client.log_atividade(
                uid,
                'coleta_shacks_mineracao',
                {
                    'shacks_coletados': shacks_disponiveis,
                    'novo_saldo_shack': novo_shack,
                    'nivel_mineradora': calculo['nivel_mineradora']
                }
            )
            
            return {
                "sucesso": True,
                "mensagem": f"Coletados {shacks_disponiveis} Shacks da mineração!",
                "shacks_coletados": shacks_disponiveis,
                "novo_saldo_shack": novo_shack,
                "detalhes": {
                    "shack_por_minuto": calculo['shack_por_minuto'],
                    "minutos_acumulados": calculo['minutos_acumulados'],
                    "crypto_miner_ativo": calculo['crypto_miner_ativo']
                }
            }
        else:
            error_msg = result.get('erro', 'Erro desconhecido ao atualizar dados')
            print(f"Erro ao atualizar saldo do usuário {uid}: {error_msg}")
            return {"sucesso": False, "mensagem": f"Erro ao atualizar saldo: {error_msg}"}
        
    except Exception as e:
        print(f"Erro ao coletar Shacks: {e}")
        return {"sucesso": False, "mensagem": f"Erro interno: {str(e)}"}

def processar_mineracao_offline_jogador(uid: str) -> Dict[str, Any]:
    """
    Processa mineração offline para um jogador específico quando ele faz login
    Coleta automaticamente o dinheiro acumulado durante o tempo offline
    """
    try:
        if not supabase_client.is_connected():
            return {"sucesso": False, "mensagem": "Database não disponível"}

        usuario = get_jogador(uid)
        if not usuario:
            return {"sucesso": False, "mensagem": "Usuário não encontrado"}

        nivel_mineradora = usuario.get('nivel_mineradora', 1)
        if nivel_mineradora < 1:
            return {
                "sucesso": True,
                "dinheiro_coletado": 0,
                "mensagem": "Mineradora não ativa"
            }

        # Calcula dinheiro por minuto baseado no nível da mineradora
        # Nível 1: 3 dinheiro/min, Nível 2: 6 dinheiro/min, etc.
        dinheiro_por_minuto = nivel_mineradora * 3

        # Aplicar efeito da NFT Firstsupp (+100% geração de dinheiro)
        dinheiro_por_minuto = aplicar_efeito_nft_firstsupp_dinheiro(dinheiro_por_minuto, usuario)

        if dinheiro_por_minuto <= 0:
            return {
                "sucesso": True,
                "dinheiro_coletado": 0,
                "mensagem": "Mineradora não ativa"
            }

        agora = datetime.now(timezone.utc)
        ultimo_dinheiro = usuario.get('ultimo_dinheiro_mineracao_timestamp')
        minutos_offline = 0

        if ultimo_dinheiro:
            try:
                ultimo_dinheiro_dt = parse_iso_datetime(ultimo_dinheiro)
                segundos_offline = (agora - ultimo_dinheiro_dt).total_seconds()
                minutos_offline = segundos_offline / 60

                # Se passou menos de 1 minuto, não há dinheiro para coletar
                if segundos_offline < 60:  # 1 minuto
                    return {
                        "sucesso": True,
                        "dinheiro_coletado": 0,
                        "mensagem": "Aguarde mais tempo para coletar dinheiro da mineração"
                    }

                # Limita a 1440 minutos máximo de acúmulo offline (24 horas)
                minutos_offline = min(minutos_offline, 1440)

            except Exception as e:
                print(f"Erro ao parsear timestamp para {uid}: {e}")
                # Se erro ao parsear, assume que pode coletar 1 minuto
                minutos_offline = 1
        else:
            # Primeira vez - pode coletar 1 minuto
            minutos_offline = 1

        # Calcula dinheiro gerado
        dinheiro_gerado = int(dinheiro_por_minuto * minutos_offline)

        if dinheiro_gerado > 0:
            novo_dinheiro = usuario.get('dinheiro', 0) + dinheiro_gerado

            # Atualiza usuário
            result = supabase_client.atualizar_usuario(uid, {
                'dinheiro': novo_dinheiro,
                'ultimo_dinheiro_mineracao_timestamp': agora.isoformat()
            })

            if result.get('sucesso'):
                # Log da atividade
                supabase_client.log_atividade(
                    uid,
                    'coleta_mineracao_offline',
                    {
                        'dinheiro_coletado': dinheiro_gerado,
                        'minutos_offline': round(minutos_offline, 2),
                        'nivel_mineradora': nivel_mineradora,
                        'novo_saldo': novo_dinheiro
                    }
                )

                return {
                    "sucesso": True,
                    "dinheiro_coletado": dinheiro_gerado,
                    "minutos_offline": round(minutos_offline, 2),
                    "novo_saldo": novo_dinheiro,
                    "mensagem": f"Coletados ${dinheiro_gerado} da mineração offline! ({minutos_offline:.1f}min)"
                }
            else:
                return {"sucesso": False, "mensagem": "Erro ao atualizar saldo"}
        else:
            return {
                "sucesso": True,
                "dinheiro_coletado": 0,
                "mensagem": "Nenhum dinheiro para coletar"
            }

    except Exception as e:
        print(f"Erro ao processar mineração offline para {uid}: {e}")
        return {"sucesso": False, "mensagem": f"Erro interno: {str(e)}"}

def processar_mineracao_offline_todos_jogadores() -> Dict[str, Any]:
    """
    Função legada - agora apenas chama o processamento automático de dinheiro
    Os Shacks agora devem ser coletados manualmente
    """
    return processar_mineracao_automatica_dinheiro()

def calcular_custo_upgrade_multiplo(item: str, nivel_atual: int, quantidade: int) -> Dict[str, Any]:
    """Calcula o custo total para múltiplos upgrades com Shacks a partir do nível 10"""
    custo_total_dinheiro = 0
    custo_total_shacks = 0
    
    for i in range(quantidade):
        nivel_upgrade = nivel_atual + i + 1  # Nível que será alcançado após o upgrade
        custo_nivel = calcular_custo_upgrade(item, nivel_atual + i)
        custo_total_dinheiro += custo_nivel
        
        # A partir do nível 10, precisa de Shacks adicionalmente
        if nivel_upgrade >= 10:
            # Determinar quantos Shacks são necessários baseado no nível
            if nivel_upgrade >= 10 and nivel_upgrade < 20:
                # Nível 10-19: 2 Shacks por upgrade
                custo_total_shacks += 2
            elif nivel_upgrade >= 20 and nivel_upgrade < 30:
                # Nível 20-29: 4 Shacks por upgrade
                custo_total_shacks += 4
            elif nivel_upgrade >= 30 and nivel_upgrade < 40:
                # Nível 30-39: 6 Shacks por upgrade
                custo_total_shacks += 6
            elif nivel_upgrade >= 40 and nivel_upgrade < 50:
                # Nível 40-49: 8 Shacks por upgrade
                custo_total_shacks += 8
            else:
                # Nível 50+: incrementa 2 Shacks a cada 10 níveis
                # Fórmula: ((nível - 10) // 10 + 1) * 2
                shacks_base = 2 + ((nivel_upgrade - 10) // 10) * 2
                custo_total_shacks += shacks_base
    
    return {
        "custo_dinheiro": custo_total_dinheiro,
        "custo_shacks": custo_total_shacks
    }

def get_extrato_bancario(uid: str) -> List[Dict[str, Any]]:
    """Retorna apenas transferências entre jogadores do extrato bancário"""
    try:
        # Buscar transferências onde o usuário é remetente ou destinatário
        transferencias_query = supabase_client.client.table('transferencias').select('''
            id, valor, descricao, tipo, status, created_at,
            remetente_uid, remetente_nick, remetente_ip,
            destinatario_uid, destinatario_nick, destinatario_ip
        ''').or_(f'remetente_uid.eq.{uid},destinatario_uid.eq.{uid}').order('created_at', desc=True).limit(50).execute()
        
        transferencias = []
        
        for transferencia in transferencias_query.data:
            # Determinar se é entrada ou saída
            is_remetente = transferencia['remetente_uid'] == uid
            
            if is_remetente:
                # Saída de dinheiro (enviou para outro jogador)
                tipo_transacao = transferencia['destinatario_ip']  # IP do destinatário
                valor = -abs(transferencia['valor'])  # Valor negativo
                outra_parte = {
                    'nick': transferencia['destinatario_nick'],
                    'ip': transferencia['destinatario_ip'],
                    'uid': transferencia['destinatario_uid']
                }
                # Remover descrição - apenas IP
                descricao = ""
            else:
                # Entrada de dinheiro (recebeu de outro jogador)
                tipo_transacao = transferencia['remetente_ip']  # IP do remetente
                valor = abs(transferencia['valor'])  # Valor positivo
                outra_parte = {
                    'nick': transferencia['remetente_nick'],
                    'ip': transferencia['remetente_ip'],
                    'uid': transferencia['remetente_uid']
                }
                # Remover descrição - apenas IP
                descricao = ""
            
            # Converter timestamp
            created_at = transferencia['created_at']
            if isinstance(created_at, str):
                # Tratar diferentes formatos de data do Supabase
                try:
                    # Remover o 'Z' e adicionar '+00:00' se necessário
                    if created_at.endswith('Z'):
                        created_at = created_at[:-1] + '+00:00'
                    elif '+' not in created_at and not created_at.endswith('+00:00'):
                        created_at += '+00:00'
                    
                    # Lidar com microsegundos longos (truncar para 6 dígitos)
                    if '.' in created_at:
                        date_part, time_part = created_at.split('.')
                        microseconds_and_tz = time_part
                        if '+' in microseconds_and_tz:
                            microseconds, tz = microseconds_and_tz.split('+')
                            microseconds = microseconds[:6]  # Truncar para 6 dígitos
                            created_at = f"{date_part}.{microseconds}+{tz}"
                    
                    timestamp = parse_iso_datetime(created_at).timestamp()
                except ValueError as e:
                    print(f"[DEBUG] Erro ao converter data '{created_at}': {e}")
                    timestamp = 0
            else:
                timestamp = created_at.timestamp() if hasattr(created_at, 'timestamp') else 0
            
            transferencia_formatada = {
                'id': transferencia['id'],
                'tipo': tipo_transacao,  # Agora contém o IP
                'valor': valor,
                'timestamp': timestamp,
                'mensagem': descricao,
                'acao': tipo_transacao,  # IP copiável no campo acao
                'status': transferencia['status'],
                'outra_parte': outra_parte,
                'descricao_original': transferencia.get('descricao', ''),
                'created_at': created_at,
                'ip_copiavel': tipo_transacao  # Campo extra para facilitar cópia do IP
            }
            
            transferencias.append(transferencia_formatada)
        
        return transferencias
        
    except Exception as e:
        print(f"Erro ao buscar extrato bancário: {e}")
        return []

def registrar_transferencia(remetente_uid: str, destinatario_uid: str, valor: int, descricao: str = "", tipo: str = "transferencia") -> Dict[str, Any]:
    """Registra uma transferência entre jogadores na tabela de transferências"""
    try:
        # Buscar dados do remetente
        remetente = get_jogador(remetente_uid)
        if not remetente:
            return {"sucesso": False, "mensagem": "Remetente não encontrado"}
        
        # Buscar dados do destinatário
        destinatario = get_jogador(destinatario_uid)
        if not destinatario:
            return {"sucesso": False, "mensagem": "Destinatário não encontrado"}
        
        # Dados da transferência
        transferencia_data = {
            'remetente_uid': remetente_uid,
            'remetente_nick': remetente['nick'],
            'remetente_ip': remetente['ip'],
            'destinatario_uid': destinatario_uid,
            'destinatario_nick': destinatario['nick'],
            'destinatario_ip': destinatario['ip'],
            'valor': valor,
            'descricao': descricao,
            'tipo': tipo,
            'status': 'concluida'
        }
        
        # Inserir na tabela de transferências
        result = supabase_client.client.table('transferencias').insert(transferencia_data).execute()
        
        if result.data:
            print(f"[DEBUG TRANSFERENCIA] Registrada: {remetente['nick']} -> {destinatario['nick']} | Valor: ${valor}")
            return {
                "sucesso": True, 
                "mensagem": "Transferência registrada com sucesso",
                "transferencia_id": result.data[0]['id']
            }
        else:
            return {"sucesso": False, "mensagem": "Erro ao registrar transferência"}
            
    except Exception as e:
        print(f"Erro ao registrar transferência: {e}")
        return {"sucesso": False, "mensagem": f"Erro interno: {str(e)}"}

def realizar_transferencia(remetente_uid: str, destinatario_uid: str, valor: int, descricao: str = "") -> Dict[str, Any]:
    """Realiza uma transferência completa entre jogadores (atualiza saldos + registra histórico)"""
    try:
        # Verificar se não é transferência para si mesmo
        if remetente_uid == destinatario_uid:
            return {"sucesso": False, "mensagem": "Não é possível transferir para si mesmo"}
        
        # Buscar dados dos jogadores
        remetente = get_jogador(remetente_uid)
        destinatario = get_jogador(destinatario_uid)
        
        if not remetente:
            return {"sucesso": False, "mensagem": "Remetente não encontrado"}
        if not destinatario:
            return {"sucesso": False, "mensagem": "Destinatário não encontrado"}
        
        # Verificar se remetente tem dinheiro suficiente
        if remetente.get('dinheiro', 0) < valor:
            return {"sucesso": False, "mensagem": "Saldo insuficiente"}
        
        # Calcular novos saldos
        novo_saldo_remetente = remetente['dinheiro'] - valor
        novo_saldo_destinatario = destinatario['dinheiro'] + valor
        
        # Atualizar saldo do remetente
        result_remetente = atualizar_jogador(remetente_uid, {
            'dinheiro': novo_saldo_remetente
        })
        
        if not result_remetente['sucesso']:
            return {"sucesso": False, "mensagem": "Erro ao debitar valor do remetente"}
        
        # Atualizar saldo do destinatário
        result_destinatario = atualizar_jogador(destinatario_uid, {
            'dinheiro': novo_saldo_destinatario
        })
        
        if not result_destinatario['sucesso']:
            # Reverter operação do remetente
            atualizar_jogador(remetente_uid, {'dinheiro': remetente['dinheiro']})
            return {"sucesso": False, "mensagem": "Erro ao creditar valor para destinatário"}
        
        # Registrar transferência no histórico
        registro = registrar_transferencia(remetente_uid, destinatario_uid, valor, descricao)
        
        return {
            "sucesso": True,
            "mensagem": f"Transferência de ${valor} realizada com sucesso para {destinatario['nick']}",
            "novo_saldo": novo_saldo_remetente,
            "transferencia_id": registro.get('transferencia_id')
        }
        
    except Exception as e:
        print(f"Erro ao realizar transferência: {e}")
        return {"sucesso": False, "mensagem": f"Erro interno: {str(e)}"}
    except Exception as e:
        print(f"Erro ao buscar extrato bancário: {e}")
        return []

def get_meu_log(uid: str) -> Dict[str, Any]:
    """Busca apenas invasões recebidas e transferências (roubos) de um jogador"""
    try:
        jogador = get_jogador(uid)
        if not jogador:
            return {"sucesso": False, "mensagem": "Jogador não encontrado."}

        logs_consolidados = []
        
        # 1. Buscar transferências onde o jogador foi roubado (remetente_uid = atacante, destinatario_uid = vítima)
        transferencias_recebidas = supabase_client.client.table('transferencias').select('''
            id, valor, tipo, created_at,
            remetente_uid, remetente_nick, remetente_ip,
            destinatario_uid, destinatario_nick, destinatario_ip
        ''').eq('destinatario_uid', uid).order('created_at', desc=True).limit(25).execute()
        
        for transferencia in transferencias_recebidas.data:
            # Converter timestamp
            created_at = transferencia['created_at']
            if isinstance(created_at, str):
                try:
                    if created_at.endswith('Z'):
                        created_at = created_at[:-1] + '+00:00'
                    elif '+' not in created_at and not created_at.endswith('+00:00'):
                        created_at += '+00:00'
                    
                    if '.' in created_at:
                        date_part, time_part = created_at.split('.')
                        microseconds_and_tz = time_part
                        if '+' in microseconds_and_tz:
                            microseconds, tz = microseconds_and_tz.split('+')
                            microseconds = microseconds[:6]
                            created_at = f"{date_part}.{microseconds}+{tz}"
                    
                    timestamp = parse_iso_datetime(created_at).timestamp()
                except ValueError:
                    timestamp = 0
            else:
                timestamp = created_at.timestamp() if hasattr(created_at, 'timestamp') else 0
            
            # Determinar tipo de log baseado no tipo da transferência
            if transferencia.get('tipo') == 'roubo':
                tipo_log = 'roubo_recebido'
                mensagem = f"Security alert! transferencia ${transferencia['valor']} por {transferencia['remetente_ip']}"
                emoji = "�"
            else:
                tipo_log = 'transferencia_recebida'
                mensagem = f"Security alert! transferencia ${transferencia['valor']} por {transferencia['remetente_ip']}"
                emoji = "�"
            
            log_entry = {
                'tipo': tipo_log,
                'mensagem': mensagem,
                'timestamp': timestamp,
                'valor': transferencia['valor'],
                'ip_atacante': transferencia['remetente_ip'],
                'atacante_nick': transferencia['remetente_nick'],
                'emoji': emoji,
                'id': transferencia['id']
            }
            
            logs_consolidados.append(log_entry)
        
        # 2. Buscar logs do campo 'log' do jogador apenas para invasões recebidas
        logs_jogador = jogador.get('log', [])
        for log in logs_jogador:
            # Filtrar apenas invasões recebidas (quando foi exploitado)
            if log.get('tipo') == 'invasao_recebida' or (
                'mensagem' in log and any(palavra in log['mensagem'].lower() for palavra in ['exploitado', 'invadido', 'atacado'])
            ):
                # Converter timestamp
                timestamp = log.get('timestamp')
                if hasattr(timestamp, 'timestamp'):
                    timestamp = timestamp.timestamp()
                elif isinstance(timestamp, datetime):
                    timestamp = timestamp.timestamp()
                elif not isinstance(timestamp, (int, float)):
                    timestamp = 0
                
                # Extrair IP da mensagem se possível
                ip_atacante = "IP não identificado"
                mensagem = log.get('mensagem', '')
                
                # Tentar extrair IP da mensagem (formato comum: "IP: ***********")
                import re
                ip_match = re.search(r'(?:IP:?\s*)?(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})', mensagem)
                if ip_match:
                    ip_atacante = ip_match.group(1)
                
                # Extrair nick do atacante
                atacante_nick = "Atacante desconhecido"
                nick_match = re.search(r'por\s+(\w+)', mensagem)
                if nick_match:
                    atacante_nick = nick_match.group(1)
                
                log_entry = {
                    'tipo': 'invasao_recebida',
                    'mensagem': f"Security alert! Sistema invadido por {ip_atacante}",
                    'timestamp': timestamp,
                    'valor': 0,  # Invasões não têm valor específico
                    'ip_atacante': ip_atacante,
                    'atacante_nick': atacante_nick,
                    'emoji': "🚨",
                    'id': f"log_{timestamp}"
                }
                
                logs_consolidados.append(log_entry)
        
        # Ordenar por timestamp (mais recente primeiro) e limitar a 50
        logs_ordenados = sorted(logs_consolidados, key=lambda x: x.get('timestamp', 0), reverse=True)[:50]

        # Adicionar data formatada para cada log
        for log in logs_ordenados:
            timestamp = log.get('timestamp', 0)
            if timestamp and timestamp > 0:
                try:
                    if timestamp < 1000000000000:  # Se está em segundos, converter para milissegundos
                        timestamp = timestamp * 1000
                    date_obj = datetime.fromtimestamp(timestamp / 1000)
                    log['data_formatada'] = date_obj.strftime('%d/%m/%Y %H:%M:%S')
                except:
                    log['data_formatada'] = 'Data não disponível'
            else:
                log['data_formatada'] = 'Data não disponível'

        return {"sucesso": True, "logs": logs_ordenados}
    except Exception as e:
        print(f"Erro ao buscar meu log: {e}")
        return {"sucesso": False, "mensagem": f"Erro interno: {str(e)}"}

def limpar_meu_log(uid: str) -> Dict[str, Any]:
    """Limpa todos os logs do jogador"""
    try:
        if not uid:
            return {"sucesso": False, "mensagem": "UID não fornecido."}
        
        # Atualiza o jogador removendo todos os logs
        result = atualizar_jogador(uid, {"log": []})
        
        if result['sucesso']:
            return {"sucesso": True, "mensagem": "Log limpo com sucesso!"}
        else:
            return {"sucesso": False, "mensagem": "Erro ao limpar log"}
            
    except Exception as e:
        return {"sucesso": False, "mensagem": f"Erro ao limpar log: {str(e)}"}

def get_dados_completos_grupo(uid: str) -> Dict[str, Any]:
    """Busca os dados completos do grupo ao qual o jogador pertence"""
    try:
        jogador = get_jogador(uid)
        if not jogador:
            return {"sucesso": False, "mensagem": "Jogador não encontrado."}
        
        grupo_id = jogador.get("grupo_id")
        if not grupo_id:
            return {"sucesso": True, "jogador": jogador, "grupo": None, "membros": [], "ranking_diario": [], "ranking_deface": []}
        
        # Buscar dados do grupo no Supabase
        if not supabase_client.is_connected():
            return {"sucesso": False, "mensagem": "Database não disponível"}
        
        try:
            # Buscar dados do grupo
            response = supabase_client.client.table("grupos").select("*").eq("id", grupo_id).execute()
            if not response.data:
                return {"sucesso": False, "mensagem": "Grupo não encontrado"}
            
            grupo = response.data[0]
            
            # Buscar membros do grupo
            membros_response = supabase_client.client.table("usuarios").select("nick, deface_points_individual, dinheiro").eq("grupo_id", grupo_id).execute()
            membros = membros_response.data if membros_response.data else []
            
            # Buscar dados do líder
            lider_uid = grupo["lider_uid"]
            lider_response = supabase_client.client.table("usuarios").select("nick").eq("uid", lider_uid).execute()
            lider_nick = lider_response.data[0]["nick"] if lider_response.data else "Desconhecido"
            
            # Buscar ranking diário do torneio
            ranking_tournament = get_tournament_ranking()
            ranking_diario = ranking_tournament.get('ranking_grupos', [])[:10] if ranking_tournament.get('sucesso') else []
            
            # Buscar ranking de deface dos membros
            ranking_deface = sorted(membros, key=lambda x: x.get("deface_points_individual", 0), reverse=True)
            
            resultado = {
                "sucesso": True,
                "jogador": jogador,
                "grupo": {
                    "id": grupo["id"],
                    "nome": grupo["nome"],
                    "lider_uid": grupo["lider_uid"],
                    "lider": lider_nick,  # Adicionar nick do líder para compatibilidade com JS
                    "membros": [m["nick"] for m in membros],  # Lista de nicks para compatibilidade com JS
                    "membros_count": len(membros),
                    "max_membros": grupo.get("max_membros", 5),
                    "deface_points": grupo.get("deface_points", 0),
                    "tournament_points": grupo.get("tournament_points", 0)
                },
                "membros": membros,
                "ranking_diario": ranking_diario,
                "ranking_deface": ranking_deface
            }
            
            return resultado
            
        except Exception as db_error:
            print(f"Erro ao buscar dados do grupo: {db_error}")
            return {"sucesso": False, "mensagem": f"Erro ao buscar dados do grupo: {str(db_error)}"}
        
    except Exception as e:
        print(f"Erro em get_dados_completos_grupo: {e}")
        return {"sucesso": False, "mensagem": f"Erro interno: {str(e)}"}

def gerar_id_grupo_personalizado() -> str:
    """Gera um ID de grupo no formato C184C2 (6 caracteres alfanuméricos)"""
    import random
    import string
    # Gera 6 caracteres: letras maiúsculas e números
    caracteres = string.ascii_uppercase + string.digits
    return ''.join(random.choices(caracteres, k=6))

def validar_id_grupo_personalizado(id_grupo: str) -> bool:
    """Valida se o ID do grupo está no formato correto"""
    import re
    # Deve ter exatamente 6 caracteres alfanuméricos maiúsculos
    return bool(re.match(r'^[A-Z0-9]{6}$', id_grupo))

def id_grupo_existe(id_grupo: str) -> bool:
    """Verifica se já existe um grupo com esse ID personalizado (sistema híbrido)"""
    try:
        if not supabase_client.is_connected():
            return True  # Em caso de erro, assume que existe para evitar conflitos
        
        # No sistema atual (UUID), verificamos se existe um grupo com o ID no nome
        response = supabase_client.client.table('grupos').select('nome').execute()
        
        for grupo in response.data if response.data else []:
            nome = grupo.get('nome', '')
            # Verificar se o nome contém o ID personalizado no formato "Nome (ID123)"
            if f"({id_grupo})" in nome:
                return True
        
        return False
    except Exception:
        return True  # Em caso de erro, assume que existe

def criar_grupo(uid: str, nome_grupo: str, id_grupo_personalizado: str) -> Dict[str, Any]:
    """Cria um novo grupo com ID personalizado OBRIGATÓRIO - versão compatível com UUID"""
    try:
        if not supabase_client.is_connected():
            return {"sucesso": False, "mensagem": "Database não disponível"}
        
        # Validações obrigatórias
        if not nome_grupo or not nome_grupo.strip():
            return {"sucesso": False, "mensagem": "Nome do grupo é obrigatório"}
        
        if not id_grupo_personalizado or not id_grupo_personalizado.strip():
            return {"sucesso": False, "mensagem": "ID personalizado do grupo é obrigatório"}
        
        jogador = get_jogador(uid)
        if not jogador:
            return {"sucesso": False, "mensagem": "Jogador não encontrado"}
        
        if jogador.get("grupo_id"):  # Corrigido para grupo_id
            return {"sucesso": False, "mensagem": "Você já está em um grupo"}
        
        custo_criacao = 10000
        if jogador.get("dinheiro", 0) < custo_criacao:
            return {"sucesso": False, "mensagem": f"Dinheiro insuficiente. Custa ${custo_criacao} para criar um grupo"}
        
        # Validar formato do ID personalizado (OBRIGATÓRIO)
        if not validar_id_grupo_personalizado(id_grupo_personalizado):
            return {"sucesso": False, "mensagem": "ID do grupo deve ter 6 caracteres alfanuméricos (exemplo: C184C2)"}
        
        # VERIFICAÇÃO ESPECIAL: Verificar se já existe grupo com esse nome personalizado
        # Como ainda estamos usando UUID, vamos verificar se já existe um grupo com esse nome + ID na descrição
        grupos_existentes = supabase_client.client.table('grupos').select('*').execute()
        
        for grupo_existente in grupos_existentes.data if grupos_existentes.data else []:
            # Verificar se existe grupo com o mesmo nome personalizado (será usado depois da migração)
            if grupo_existente.get('nome') == nome_grupo:
                return {"sucesso": False, "mensagem": f"Já existe um grupo com o nome '{nome_grupo}'"}
        
        # Gerar UUID para o sistema atual, mas salvar o ID personalizado no nome/descrição
        import uuid
        id_grupo_uuid = str(uuid.uuid4())
        
        # Criar nome que inclui o ID personalizado para futuras consultas
        nome_completo = f"{nome_grupo} ({id_grupo_personalizado})"
        
        # Cria o grupo na tabela grupos (usando UUID temporariamente até migração)
        grupo_data = {
            "id": id_grupo_uuid,  # Usar UUID temporariamente
            "nome": nome_completo,  # Nome inclui ID personalizado
            "lider_uid": uid,  # Corrigido: usar lider_uid ao invés de lider
            "membros": [jogador["nick"]],
            "max_membros": 5,
            "deface_points": 0,
            "tournament_points": 0
        }
        
        # Insere o grupo no Supabase
        grupo_result = supabase_client.client.table('grupos').insert(grupo_data).execute()
        
        if grupo_result.data:
            # Pega o ID do grupo criado
            grupo_criado = grupo_result.data[0]
            id_grupo_criado = grupo_criado['id']
            
            # Atualiza o jogador para estar no grupo
            novo_dinheiro = jogador["dinheiro"] - custo_criacao
            
            jogador_result = supabase_client.client.table('usuarios').update({
                "dinheiro": novo_dinheiro,
                "grupo_id": id_grupo_criado  # Usar apenas grupo_id
            }).eq('uid', uid).execute()
            
            if jogador_result.data:
                return {
                    "sucesso": True, 
                    "mensagem": f"Grupo '{nome_grupo}' criado com sucesso! ID: {id_grupo_personalizado}",
                    "id_grupo": id_grupo_personalizado,  # Retorna o ID personalizado
                    "grupo_id": id_grupo_criado  # ID interno do sistema
                }
            else:
                # Se falhou ao atualizar jogador, remove o grupo
                supabase_client.client.table('grupos').delete().eq('id', id_grupo_criado).execute()
                return {"sucesso": False, "mensagem": "Erro ao atualizar dados do jogador"}
        else:
            return {"sucesso": False, "mensagem": "Erro ao criar grupo no banco de dados"}
        
    except Exception as e:
        print(f"Erro em criar_grupo: {e}")
        return {"sucesso": False, "mensagem": f"Erro interno: {str(e)}"}

def entrar_em_grupo(uid: str, id_grupo: str) -> Dict[str, Any]:
    """Entra em um grupo existente"""
    try:
        if not supabase_client.is_connected():
            return {"sucesso": False, "mensagem": "Database não disponível"}
        
        jogador = get_jogador(uid)
        if not jogador:
            return {"sucesso": False, "mensagem": "Jogador não encontrado"}
        
        if jogador.get("grupo_id"):
            return {"sucesso": False, "mensagem": "Você já está em um grupo"}
        
        # Busca o grupo no banco de dados
        grupo_result = supabase_client.client.table('grupos').select('*').eq('id', id_grupo).execute()
        
        if not grupo_result.data:
            return {"sucesso": False, "mensagem": "Grupo não encontrado"}
        
        grupo = grupo_result.data[0]
        membros_atuais = grupo.get("membros", [])
        max_membros = grupo.get("max_membros", 5)
        
        if len(membros_atuais) >= max_membros:
            return {"sucesso": False, "mensagem": "Este grupo está cheio"}
        
        # Adiciona o jogador à lista de membros
        novos_membros = membros_atuais + [jogador["nick"]]
        
        # Atualiza o grupo
        grupo_update = supabase_client.client.table('grupos').update({
            "membros": novos_membros
        }).eq('id', id_grupo).execute()
        
        if grupo_update.data:
            # Atualiza o jogador
            jogador_update = supabase_client.client.table('usuarios').update({
                "grupo_id": id_grupo  # Usar apenas grupo_id
            }).eq('uid', uid).execute()
            
            if jogador_update.data:
                return {"sucesso": True, "mensagem": f"Você entrou no grupo '{grupo['nome']}'!"}
            else:
                # Reverte a atualização do grupo
                supabase_client.client.table('grupos').update({
                    "membros": membros_atuais
                }).eq('id', id_grupo).execute()
                return {"sucesso": False, "mensagem": "Erro ao atualizar dados do jogador"}
        else:
            return {"sucesso": False, "mensagem": "Erro ao atualizar grupo"}
        
    except Exception as e:
        print(f"❌ Erro ao entrar em grupo: {str(e)}")
        return {"sucesso": False, "mensagem": f"Erro interno: {str(e)}"}

def sair_do_grupo(uid: str) -> Dict[str, Any]:
    """Sai do grupo atual - versão corrigida para evitar violação de foreign key"""
    try:
        if not supabase_client.is_connected():
            return {"sucesso": False, "mensagem": "Database não disponível"}
        
        jogador = get_jogador(uid)
        if not jogador:
            return {"sucesso": False, "mensagem": "Jogador não encontrado"}
        
        id_grupo = jogador.get("grupo_id")
        if not id_grupo:
            return {"sucesso": False, "mensagem": "Você não está em nenhum grupo"}
        
        # Primeiro, sempre remove a referência do usuário (para evitar violação de FK)
        supabase_client.client.table('usuarios').update({
            "grupo_id": None
        }).eq('uid', uid).execute()
        
        # Busca o grupo atual
        grupo_result = supabase_client.client.table('grupos').select('*').eq('id', id_grupo).execute()
        
        if not grupo_result.data:
            # Grupo não existe mais, referência já foi limpa
            return {"sucesso": True, "mensagem": "Você saiu do grupo (o grupo não existia mais)"}
        
        grupo = grupo_result.data[0]
        membros_atuais = grupo.get("membros", [])
        nick_jogador = jogador["nick"]
        
        # Remove o jogador da lista de membros
        novos_membros = [membro for membro in membros_atuais if membro != nick_jogador]
        
        # Verifica quantos usuários ainda referenciam esse grupo
        usuarios_no_grupo = supabase_client.client.table('usuarios').select('uid').eq('grupo_id', id_grupo).execute()
        usuarios_restantes = len(usuarios_no_grupo.data) if usuarios_no_grupo.data else 0
        
        # Se não sobrar ninguém referenciando o grupo, deleta o grupo
        if usuarios_restantes == 0 or not novos_membros:
            supabase_client.client.table('grupos').delete().eq('id', id_grupo).execute()
            return {"sucesso": True, "mensagem": "Você saiu do grupo e o grupo foi dissolvido (último membro)"}
        
        # Se o jogador era o líder, transfere a liderança
        if grupo.get("lider_uid") == uid:
            # Buscar o UID do novo líder pelo nick
            novo_lider_nick = novos_membros[0]
            novo_lider_data = supabase_client.client.table('usuarios').select('uid').eq('nick', novo_lider_nick).limit(1).execute()
            
            if novo_lider_data.data:
                novo_lider_uid = novo_lider_data.data[0]['uid']
                
                supabase_client.client.table('grupos').update({
                    "lider_uid": novo_lider_uid,
                    "membros": novos_membros
                }).eq('id', id_grupo).execute()
                
                return {"sucesso": True, "mensagem": f"Você saiu do grupo e {novo_lider_nick} se tornou o novo líder"}
            else:
                # Se não encontrar o novo líder, dissolve o grupo
                supabase_client.client.table('grupos').delete().eq('id', id_grupo).execute()
                return {"sucesso": True, "mensagem": "Você saiu do grupo e o grupo foi dissolvido (erro na transferência de liderança)"}
        else:
            # Membro comum saindo - apenas atualiza a lista
            supabase_client.client.table('grupos').update({
                "membros": novos_membros
            }).eq('id', id_grupo).execute()
            
            return {"sucesso": True, "mensagem": "Você saiu do grupo com sucesso!"}
        
    except Exception as e:
        print(f"❌ Erro ao sair do grupo: {str(e)}")
        # Em caso de erro, tenta garantir que o usuário não fica "preso" no grupo
        try:
            supabase_client.client.table('usuarios').update({
                "grupo_id": None
            }).eq('uid', uid).execute()
        except:
            pass  # Se falhar, pelo menos tentamos
        return {"sucesso": False, "mensagem": f"Erro interno: {str(e)}"}

def comprar_item_mercado_negro(uid: str, item_id: str) -> Dict[str, Any]:
    """Compra um item do mercado negro"""
    try:
        if item_id not in MERCADO_NEGRO_ITEMS:
            return {"sucesso": False, "mensagem": "Item não encontrado no Mercado Negro"}
        
        item_info = MERCADO_NEGRO_ITEMS[item_id]
        jogador = get_jogador(uid)
        
        if not jogador:
            return {"sucesso": False, "mensagem": "Jogador não encontrado"}
        
        # Lógica de preço dinâmico para upgrade da mineradora
        preco_item = item_info.get("preco_shack", 0)
        if item_info["tipo"] == "upgrade_mineradora":
            nivel_atual = jogador.get("nivel_mineradora", 1)
            preco_item = calcular_custo_upgrade_mineradora(nivel_atual)
        
        if jogador.get("shack", 0) < preco_item:
            return {"sucesso": False, "mensagem": "Shacks insuficientes"}
        
        # Atualiza jogador
        updates = {'shack': jogador['shack'] - preco_item}
        
        if item_info["tipo"] == "upgrade_mineradora":
            updates['nivel_mineradora'] = jogador.get('nivel_mineradora', 1) + 1
        elif item_info["tipo"] == "boost_deface":
            # Adiciona boost temporário
            updates['boost_deface'] = jogador.get('boost_deface', 0) + 1
        
        result = atualizar_jogador(uid, updates)
        
        if result['sucesso']:
            return {"sucesso": True, "mensagem": f"Você comprou '{item_info['nome']}'!"}
        else:
            return {"sucesso": False, "mensagem": "Erro ao processar compra"}
        
    except Exception as e:
        return {"sucesso": False, "mensagem": f"Erro interno: {str(e)}"}

def transferir_dinheiro_alvo(atacante_uid: str, alvo_uid: str, porcentagem: float) -> Dict[str, Any]:
    """Transfere dinheiro de um alvo para o atacante"""
    try:
        print(f"[DEBUG TRANSFER] Iniciando transferência")
        print(f"[DEBUG TRANSFER] atacante_uid: '{atacante_uid}' (tipo: {type(atacante_uid)})")
        print(f"[DEBUG TRANSFER] alvo_uid: '{alvo_uid}' (tipo: {type(alvo_uid)})")
        print(f"[DEBUG TRANSFER] porcentagem: {porcentagem}")
        
        # Buscar atacante (sempre deve ser um jogador real)
        atacante = get_jogador(atacante_uid)
        print(f"[DEBUG TRANSFER] atacante encontrado: {atacante is not None}")
        
        if not atacante:
            print(f"[DEBUG TRANSFER] Atacante não encontrado para UID: '{atacante_uid}'")
            return {"sucesso": False, "mensagem": f"Atacante não encontrado (UID: {atacante_uid})"}
        
        # Buscar jogador real (não há bots no jogo)
        print(f"[DEBUG TRANSFER] Buscando jogador real: {alvo_uid}")
        alvo = get_jogador(alvo_uid)
        alvo_ip = None

        if alvo:
            alvo_ip = alvo.get('ip')
        else:
            print(f"[DEBUG TRANSFER] Jogador não encontrado: {alvo_uid}")
            return {"sucesso": False, "mensagem": f"Jogador não encontrado (UID: {alvo_uid})"}
        
        print(f"[DEBUG TRANSFER] alvo encontrado: {alvo is not None}")
        
        if not alvo:
            print(f"[DEBUG TRANSFER] Alvo não encontrado para UID: '{alvo_uid}'")
            return {"sucesso": False, "mensagem": f"Alvo não encontrado (UID: {alvo_uid})"}
        
        # VERIFICAR COOLDOWN DE ROUBO (tenta banco primeiro, depois memória)
        if alvo_ip:
            cooldown_message = None
            
            # Tenta verificar no banco de dados primeiro
            try:
                cooldowns_roubo = atacante.get('cooldown_roubo_ips', {})
                if cooldowns_roubo:
                    timestamp_atual = datetime.now(timezone.utc).timestamp()
                    cooldown_ip = cooldowns_roubo.get(alvo_ip, 0)
                    
                    if timestamp_atual < cooldown_ip:
                        tempo_restante = int((cooldown_ip - timestamp_atual) // 60)
                        cooldown_message = f"Cooldown ativo para este IP. Aguarde {tempo_restante + 1} minutos."
            except Exception as e:
                print(f"[DEBUG] Campo cooldown_roubo_ips não existe no banco, usando memória: {e}")
                # Se falhar (campo não existe), usa sistema em memória
                cooldown_message = verificar_cooldown_memoria(atacante_uid, alvo_ip)
            
            # Se não conseguiu verificar no banco, usa memória como fallback
            if cooldown_message is None and atacante.get('cooldown_roubo_ips') is None:
                cooldown_message = verificar_cooldown_memoria(atacante_uid, alvo_ip)
            
            if cooldown_message:
                return {"sucesso": False, "mensagem": cooldown_message}
        
        saldo_alvo = alvo.get('dinheiro', 0)
        quantia_transferir = int(saldo_alvo * (porcentagem / 100))
        
        # BANKGUARD: Reduz a quantidade perdida pelo alvo baseado no nível do app (apenas jogadores reais)
        bankguard_nivel = alvo.get('bankguard', 1)
        reducao_bankguard = 1 - (bankguard_nivel - 1) * 0.03  # 3% menos perda por nível acima de 1
        quantia_transferir = int(quantia_transferir * reducao_bankguard)
        
        # BRUTEFORCE: Aumenta a quantidade roubada baseado no nível do app do atacante
        bruteforce_nivel = atacante.get('bruteforce', 1)
        bonus_bruteforce = 1 + (bruteforce_nivel - 1) * 0.05  # 5% extra por nível acima de 1
        quantia_transferir = int(quantia_transferir * bonus_bruteforce)
        
        if quantia_transferir <= 0:
            return {"sucesso": False, "mensagem": "Não há dinheiro suficiente para transferir"}
        
        # Calcula novo saldo do atacante
        novo_saldo_atacante = atacante.get('dinheiro', 0) + quantia_transferir
        
        # Atualiza apenas o atacante (bots não precisam ser atualizados no banco)
        result = atualizar_jogador(atacante_uid, {'dinheiro': novo_saldo_atacante})
        
        if result['sucesso']:
                # ADICIONAR COOLDOWN DE ROUBO após transferência bem-sucedida (sistema híbrido)
            if alvo_ip:
                # PROXYVPN: Reduz o cooldown baseado no nível do app
                proxyvpn_nivel = atacante.get('proxyvpn', 1)
                reducao_cooldown = 1 - (proxyvpn_nivel - 1) * 0.1  # 10% menos cooldown por nível acima de 1
                cooldown_segundos = int(3600 * reducao_cooldown)  # Base: 60 minutos
                
                # Tenta salvar no banco primeiro
                try:
                    cooldowns_roubo = atacante.get('cooldown_roubo_ips', {})
                    timestamp_atual = datetime.now(timezone.utc).timestamp()
                    cooldowns_roubo[alvo_ip] = timestamp_atual + cooldown_segundos
                    
                    # Atualizar no banco
                    update_result = atualizar_jogador(atacante_uid, {'cooldown_roubo_ips': cooldowns_roubo})
                    if update_result.get('sucesso'):
                        print(f"[DEBUG TRANSFER] Cooldown salvo no banco: {atacante_uid} -> {alvo_ip} ({cooldown_segundos}s)")
                    else:
                        raise Exception("Falha ao salvar no banco")
                        
                except Exception as e:
                    print(f"[DEBUG] Campo cooldown_roubo_ips não existe, usando memória: {e}")
                    # Se falhar, usa sistema em memória com cooldown reduzido
                    if atacante_uid not in COOLDOWN_CACHE:
                        COOLDOWN_CACHE[atacante_uid] = {}
                    
                    timestamp_atual = datetime.now(timezone.utc).timestamp()
                    COOLDOWN_CACHE[atacante_uid][alvo_ip] = timestamp_atual + cooldown_segundos
                    
                    print(f"[COOLDOWN MEMORIA] Adicionado: {atacante_uid} -> {alvo_ip} ({cooldown_segundos}s)")
            
            # Para jogadores reais, atualizar também o saldo do alvo
                novo_saldo_alvo = saldo_alvo - quantia_transferir
                result2 = atualizar_jogador(alvo_uid, {'dinheiro': novo_saldo_alvo})
                
                if result2['sucesso']:
                    # Registrar transferência na tabela de transferências
                    registro_transferencia = registrar_transferencia(
                        alvo_uid,  # Remetente (quem "perdeu" o dinheiro)
                        atacante_uid,  # Destinatário (quem "ganhou" o dinheiro)
                        quantia_transferir,
                        f"Roubo realizado por {atacante.get('nick', 'Atacante')} - {porcentagem}% do saldo",
                        "roubo"  # Tipo especial para roubo
                    )
                    
                    if registro_transferencia['sucesso']:
                        print(f"[DEBUG TRANSFER] Transferência registrada no extrato: ID {registro_transferencia.get('transferencia_id')}")
                    else:
                        print(f"[DEBUG TRANSFER] Erro ao registrar transferência: {registro_transferencia['mensagem']}")

                    # Registrar log de segurança APENAS para a VÍTIMA (alvo_uid)
                    # O atacante NÃO recebe log - apenas a vítima vê que foi exploitada
                    try:
                        # Verificar ProxyVPN vs Firewall para anonimização
                        atacante_proxy_vpn = atacante.get('proxyvpn', 0)
                        alvo_firewall = alvo.get('firewall', 0)

                        # Se ProxyVPN >= Firewall, informações ficam anônimas
                        if atacante_proxy_vpn >= alvo_firewall:
                            atacante_ip_log = "unknown"
                            atacante_nick_log = "unknown"
                            print(f"[PROXY VPN] Transferência anônima: ProxyVPN {atacante_proxy_vpn} >= Firewall {alvo_firewall}")
                        else:
                            atacante_ip_log = atacante.get('ip', 'IP desconhecido')
                            atacante_nick_log = atacante.get('nick', 'Desconhecido')
                            print(f"[PROXY VPN] Transferência identificada: ProxyVPN {atacante_proxy_vpn} < Firewall {alvo_firewall}")

                        supabase_client.log_atividade(
                            alvo_uid,  # ← VÍTIMA recebe o log
                            'security_alert',
                            {
                                'tipo': 'exploit_transferencia',
                                'atacante_ip': atacante_ip_log,
                                'atacante_nick': atacante_nick_log,
                                'quantia_roubada': quantia_transferir,
                                'porcentagem': porcentagem,
                                'saldo_anterior': saldo_alvo,
                                'saldo_atual': novo_saldo_alvo,
                                'proxy_vpn_usado': atacante_proxy_vpn >= alvo_firewall,
                                'mensagem': f"Security alert! transferencia ${quantia_transferir} por {atacante_ip_log}"
                            }
                        )
                        print(f"[SECURITY LOG] Log de transferência registrado para VÍTIMA: {alvo_uid}")
                    except Exception as log_error:
                        print(f"[SECURITY LOG] Erro ao registrar log: {log_error}")

                    return {
                        "sucesso": True,
                        "mensagem": f"Transferência concluída! Você roubou ${quantia_transferir} ({porcentagem}%)",
                        "saldo_restante_alvo": novo_saldo_alvo,
                        "quantia_transferida": quantia_transferir
                    }
                else:
                    # Rollback: reverter dinheiro do atacante
                    atualizar_jogador(atacante_uid, {'dinheiro': atacante.get('dinheiro', 0)})
                    return {"sucesso": False, "mensagem": "Erro ao atualizar saldo do alvo"}
        else:
            return {"sucesso": False, "mensagem": "Erro ao processar transferência"}
        
    except Exception as e:
        print(f"[DEBUG TRANSFER] Erro na transferência: {str(e)}")
        return {"sucesso": False, "mensagem": f"Erro interno: {str(e)}"}

def realizar_deface_antigo(atacante_uid: str, alvo_uid: str) -> Dict[str, Any]:
    """Realiza deface em um alvo"""
    try:
        print(f"[DEBUG DEFACE] 🎯 Iniciando deface: atacante={atacante_uid}, alvo={alvo_uid}")

        atacante = get_jogador(atacante_uid)

        if not atacante:
            print(f"[DEBUG DEFACE] ❌ Atacante não encontrado: {atacante_uid}")
            return {"sucesso": False, "mensagem": "Atacante não encontrado"}

        print(f"[DEBUG DEFACE] ✅ Atacante encontrado: {atacante.get('nick', 'Sem nick')}")

        # Buscar jogador real
        print(f"[DEBUG DEFACE] 🔍 Buscando jogador real com UID: {alvo_uid}")
        alvo = get_jogador(alvo_uid)

        if not alvo:
            print(f"[DEBUG DEFACE] ❌ Jogador não encontrado para UID: {alvo_uid}")
            return {"sucesso": False, "mensagem": "Alvo não encontrado"}

        print(f"[DEBUG DEFACE] ✅ Jogador encontrado: {alvo.get('nick', 'Sem nick')}")

        # Verifica se o atacante tem um grupo
        grupo_id = atacante.get('grupo_id')  # Corrigido: usar grupo_id ao invés de group
        if not grupo_id:
            return {"sucesso": False, "mensagem": "Você precisa estar em um grupo para realizar deface"}

        # NOVA FUNCIONALIDADE: Limpar defaces expirados antes de verificar
        print(f"[DEBUG DEFACE] 🧹 Limpando defaces expirados...")
        defaces_removidos = limpar_defaces_expirados_automatico()
        if defaces_removidos > 0:
            print(f"[DEBUG DEFACE] ✅ Removidos {defaces_removidos} defaces expirados")

        # NOVA FUNCIONALIDADE: Verificar se o alvo já tem deface ativo
        print(f"[DEBUG DEFACE] 🔍 Verificando deface ativo no alvo: {alvo_uid}")
        deface_status = supabase_client.verificar_deface_ativo(alvo_uid)

        if deface_status.get("tem_deface", False):
            deface_info = deface_status["deface"]
            minutos_restantes = deface_status["minutos_restantes"]
            atacante_anterior = deface_info.get("atacante_uid", "Desconhecido")
            grupo_anterior = deface_info.get("grupo_id", "Desconhecido")

            print(f"[DEBUG DEFACE] ❌ Alvo já tem deface ativo por {minutos_restantes} minutos")
            return {
                "sucesso": False,
                "mensagem": f"Este alvo já foi defaceado! Tempo restante: {minutos_restantes} minutos. Atacante anterior: {atacante_anterior} (Grupo: {grupo_anterior})"
            }
        
        # Verifica cooldown de deface (30 minutos) - sistema compatível com o anterior
        timestamp_atual = datetime.now(timezone.utc).timestamp()
        last_deface_timestamp = atacante.get('last_deface_timestamp', 0)
        cooldown_deface = 30 * 60  # 30 minutos em segundos
        
        # Aplicar efeito da habilidade Speed Hacker (redução de cooldown em 10%)
        cooldown_deface = aplicar_efeito_habilidade_cooldown(cooldown_deface, atacante)
        
        if timestamp_atual - last_deface_timestamp < cooldown_deface:
            tempo_restante = cooldown_deface - (timestamp_atual - last_deface_timestamp)
            minutos_restantes = int(tempo_restante // 60)
            segundos_restantes = int(tempo_restante % 60)
            return {"sucesso": False, "mensagem": f"Cooldown ativo! Aguarde {minutos_restantes}m {segundos_restantes}s para fazer outro deface."}
        
        # Lógica de combate: CPU + Malware Kit vs Antivirus
        cpu_atacante = atacante.get('cpu', 1)
        malware_kit_atacante = atacante.get('malware_kit', 1)
        poder_atacante = cpu_atacante + malware_kit_atacante
        
        # Aplicar efeito da habilidade The Killer (aumenta poder de ataque em 5%)
        habilidades_atacante = atacante.get('habilidades_adquiridas', {})
        if 'the_killer' in habilidades_atacante:
            bonus_the_killer = int(poder_atacante * HABILIDADES_NFT['the_killer']['efeito'])
            poder_atacante += max(1, bonus_the_killer)  # Mínimo +1 de bônus
            print(f"[DEBUG HABILIDADE] The Killer ativo! Bônus: +{max(1, bonus_the_killer)} poder")
        
        # Para jogadores reais, usar antivirus do database
        antivirus_alvo = alvo.get('antivirus', 1)
        alvo_nick = alvo.get('nick', 'Jogador')
        
        print(f"[DEBUG DEFACE] Atacante {atacante['nick']}: CPU {cpu_atacante} + Malware Kit {malware_kit_atacante} = {poder_atacante}")
        print(f"[DEBUG DEFACE] Alvo {alvo_nick}: Antivirus {antivirus_alvo}")
        
        # Nova lógica: Poder atacante precisa ser pelo menos o dobro do antivirus do alvo
        poder_minimo_necessario = antivirus_alvo * 2
        
        print(f"[DEBUG DEFACE] Poder necessário para sucesso garantido: {poder_minimo_necessario}")
        
        import random
        if poder_atacante >= poder_minimo_necessario:
            # Sucesso garantido quando poder é pelo menos o dobro do antivirus
            sucesso_deface = True
            chance_final = 100
            print(f"[DEBUG DEFACE] Sucesso garantido! Poder {poder_atacante} >= {poder_minimo_necessario}")
        else:
            # Chance baseada na proporção do poder vs antivirus
            # Quanto mais próximo do dobro, maior a chance
            proporcao = poder_atacante / antivirus_alvo
            
            if proporcao >= 1.5:
                chance_final = 80  # 80% se for 1.5x o antivirus
            elif proporcao >= 1.2:
                chance_final = 60  # 60% se for 1.2x o antivirus
            elif proporcao >= 1.0:
                chance_final = 40  # 40% se for igual ao antivirus
            else:
                chance_final = 20  # 20% se for menor que o antivirus
            
            sucesso_deface = random.randint(1, 100) <= chance_final
            print(f"[DEBUG DEFACE] Proporção: {proporcao:.1f}x | Chance: {chance_final}% | Sucesso: {sucesso_deface}")
        
        if not sucesso_deface:
            # Deface falhou - apenas atualiza cooldown
            result = atualizar_jogador(atacante_uid, {
                'last_deface_timestamp': int(timestamp_atual)  # Converter para inteiro
            })
            return {
                "sucesso": False, 
                "mensagem": f"Deface falhou! Seu poder de ataque (CPU {cpu_atacante} + Malware Kit {malware_kit_atacante} = {poder_atacante}) não foi suficiente contra o antivirus de {alvo_nick} (nível {antivirus_alvo}). Para sucesso garantido, você precisa de poder {poder_minimo_necessario}. Chance era {chance_final}%"
            }
        
        # Deface bem-sucedido - 30 pontos para jogador e grupo
        pontos_ganhos = 30
        
        # Atualizar pontos individuais do atacante
        pontos_individuais = atacante.get('deface_points_individual', 0) + pontos_ganhos
        pontos_torneio_individuais = atacante.get('tournament_points_individual', 0) + pontos_ganhos
        
        # Atualizar jogador
        print(f"[DEBUG DEFACE] Atualizando jogador {atacante_uid} com dados:")
        print(f"  - deface_points_individual: {pontos_individuais}")
        print(f"  - tournament_points_individual: {pontos_torneio_individuais}")
        print(f"  - last_deface_timestamp: {int(timestamp_atual)}")
        
        result_jogador = atualizar_jogador(atacante_uid, {
            'deface_points_individual': pontos_individuais,
            'tournament_points_individual': pontos_torneio_individuais,
            'last_deface_timestamp': int(timestamp_atual)  # Converter para inteiro
        })
        
        print(f"[DEBUG DEFACE] Resultado atualização jogador: {result_jogador}")
        
        if not result_jogador['sucesso']:
            print(f"[DEBUG DEFACE] Erro detalhado: {result_jogador.get('erro', 'N/A')}")
            return {"sucesso": False, "mensagem": f"Erro ao atualizar pontos do jogador: {result_jogador.get('erro', 'N/A')}"}
        
        # Atualizar pontos do grupo
        try:
            grupo_response = supabase_client.client.table('grupos').select('*').eq('id', grupo_id).execute()
            
            if grupo_response.data:
                grupo = grupo_response.data[0]
                pontos_grupo_atuais = grupo.get('deface_points', 0)
                pontos_torneio_grupo = grupo.get('tournament_points', 0)
                
                novos_pontos_grupo = pontos_grupo_atuais + pontos_ganhos
                novos_pontos_torneio_grupo = pontos_torneio_grupo + pontos_ganhos
                
                result_grupo = supabase_client.client.table('grupos').update({
                    'deface_points': novos_pontos_grupo,
                    'tournament_points': novos_pontos_torneio_grupo
                }).eq('id', grupo_id).execute()
                
                if result_grupo.data:
                    print(f"[DEBUG DEFACE] Pontos do grupo atualizados: {pontos_grupo_atuais} -> {novos_pontos_grupo}")
                else:
                    print(f"[DEBUG DEFACE] Erro ao atualizar pontos do grupo")
            
        except Exception as e:
            print(f"[DEBUG DEFACE] Erro ao atualizar grupo: {e}")
            # Não falha o deface se não conseguir atualizar o grupo
        
        # INTEGRAÇÃO COM SISTEMA TTL - Adicionar pontos ao torneio de deface
        try:
            from .tournament_ttl import torneio_manager
            resultado_ttl = torneio_manager.adicionar_pontos_deface(atacante_uid, pontos_ganhos, grupo_id)
            print(f"[DEBUG DEFACE TTL] Pontos adicionados ao sistema TTL: {resultado_ttl}")
        except ImportError:
            print("[DEBUG DEFACE TTL] Sistema TTL não disponível")
        except Exception as e:
            print(f"[DEBUG DEFACE TTL] Erro ao adicionar pontos TTL: {e}")



        # Registrar log de segurança APENAS para a VÍTIMA (alvo_uid)
        # O atacante NÃO recebe log - apenas a vítima vê que foi defaceada
        try:
            # Verificar ProxyVPN vs Firewall para anonimização
            atacante_proxy_vpn = atacante.get('proxyvpn', 0)
            alvo_firewall = alvo.get('firewall', 0)

            # Se ProxyVPN >= Firewall, informações ficam anônimas
            if atacante_proxy_vpn >= alvo_firewall:
                atacante_ip_log = "unknown"
                atacante_nick_log = "unknown"
                grupo_nome_log = "unknown"
                print(f"[PROXY VPN] Deface anônimo: ProxyVPN {atacante_proxy_vpn} >= Firewall {alvo_firewall}")
            else:
                atacante_ip_log = atacante.get('ip', 'IP desconhecido')
                atacante_nick_log = atacante.get('nick', 'Desconhecido')
                # Buscar nome do grupo usando grupo_id
                try:
                    grupo_response = supabase_client.client.table('grupos').select('nome').eq('id', grupo_id).execute()
                    if grupo_response.data:
                        grupo_nome_log = grupo_response.data[0].get('nome', 'Grupo Desconhecido')
                    else:
                        grupo_nome_log = 'Grupo Desconhecido'
                except:
                    grupo_nome_log = 'Grupo Desconhecido'
                print(f"[PROXY VPN] Deface identificado: ProxyVPN {atacante_proxy_vpn} < Firewall {alvo_firewall}")

            supabase_client.log_atividade(
                alvo_uid,  # ← VÍTIMA recebe o log
                'security_alert',
                {
                    'tipo': 'exploit_deface',
                    'atacante_ip': atacante_ip_log,
                    'atacante_nick': atacante_nick_log,
                    'grupo_nome': grupo_nome_log,
                    'pontos_ganhos': pontos_ganhos,
                    'poder_atacante': poder_atacante,
                    'antivirus_alvo': antivirus_alvo,
                    'proxy_vpn_usado': atacante_proxy_vpn >= alvo_firewall,
                    'mensagem': f"Security alert! deface aplicado por {atacante_ip_log} ({grupo_nome_log})"
                }
            )
            print(f"[SECURITY LOG] Log de deface registrado para VÍTIMA: {alvo_uid}")
        except Exception as log_error:
            print(f"[SECURITY LOG] Erro ao registrar log de deface: {log_error}")

        # NOVA FUNCIONALIDADE: Registrar deface ativo por 30 minutos
        try:
            print(f"[DEBUG DEFACE] 📝 Registrando deface ativo para alvo: {alvo_uid}")
            resultado_deface_ativo = supabase_client.registrar_deface_ativo(
                alvo_uid=alvo_uid,
                atacante_uid=atacante_uid,
                grupo_id=grupo_id,
                duracao_minutos=30
            )

            if resultado_deface_ativo.get("sucesso"):
                print(f"[DEBUG DEFACE] ✅ Deface ativo registrado com sucesso por 30 minutos")
            else:
                print(f"[DEBUG DEFACE] ⚠️ Erro ao registrar deface ativo: {resultado_deface_ativo.get('erro')}")

        except Exception as deface_ativo_error:
            print(f"[DEBUG DEFACE] ❌ Erro ao registrar deface ativo: {deface_ativo_error}")

        return {
            "sucesso": True,
            "mensagem": f"Deface realizado com sucesso em {alvo_nick}! Seu poder de ataque (CPU {cpu_atacante} + Malware Kit {malware_kit_atacante} = {poder_atacante}) superou o antivirus {antivirus_alvo}. +{pontos_ganhos} pontos para você e seu grupo. (Chance: {chance_final}%) - Alvo protegido por 30 minutos!",
            "pontos_ganhos": pontos_ganhos,
            "chance_sucesso": chance_final,
            "tempo_protecao_minutos": 30
        }
        
    except Exception as e:
        return {"sucesso": False, "mensagem": f"Erro interno: {str(e)}"}

def enviar_mensagem_chat(uid: str, texto_mensagem: str) -> Dict[str, Any]:
    """Salva uma nova mensagem de chat com sistema anti-spam"""
    try:
        if not supabase_client.is_connected():
            return {"sucesso": False, "mensagem": "Sistema de chat indisponível"}

        if not texto_mensagem or not texto_mensagem.strip():
            return {"sucesso": False, "mensagem": "A mensagem não pode estar vazia"}

        # Buscar dados do jogador
        jogador = get_jogador(uid)
        if not jogador:
            return {"sucesso": False, "mensagem": "Usuário não encontrado"}

        nick = jogador.get('nick', 'Jogador')
        user_ip = jogador.get('ip', 'unknown')

        # Usar função SQL para enviar mensagem com anti-spam
        try:
            response = supabase_client.client.rpc('send_chat_message', {
                'p_user_uid': uid,
                'p_nick': nick,
                'p_message': texto_mensagem.strip(),
                'p_user_ip': user_ip
            }).execute()

            if response.data and len(response.data) > 0:
                result = response.data[0]

                if result.get('success'):
                    return {
                        "sucesso": True,
                        "mensagem": "Mensagem enviada com sucesso",
                        "message_id": result.get('message_id')
                    }
                else:
                    # Mensagem bloqueada pelo anti-spam
                    error_msg = result.get('error_message', 'Mensagem bloqueada')
                    blocked_reason = result.get('blocked_reason')

                    if blocked_reason == 'DUPLICATE':
                        return {"sucesso": False, "mensagem": "🚫 MENSAGEM BLOQUEADA pelo antispam - mensagem idêntica"}
                    elif blocked_reason == 'COOLDOWN':
                        return {"sucesso": False, "mensagem": "⏱️ Aguarde antes de enviar outra mensagem"}
                    elif blocked_reason == 'SPAM_CHARS':
                        return {"sucesso": False, "mensagem": "🚫 MENSAGEM BLOQUEADA pelo antispam - spam detectado"}
                    else:
                        return {"sucesso": False, "mensagem": f"🚫 {error_msg}"}
            else:
                return {"sucesso": False, "mensagem": "Erro ao processar mensagem"}

        except Exception as rpc_error:
            print(f"[CHAT] Erro na função SQL: {rpc_error}")
            return {"sucesso": False, "mensagem": "Erro no sistema de chat"}

    except Exception as e:
        print(f"[CHAT] Erro geral: {e}")
        return {"sucesso": False, "mensagem": f"Erro interno: {str(e)}"}

def buscar_mensagens_chat(limite: int = 50) -> Dict[str, Any]:
    """Busca mensagens recentes do chat"""
    try:
        if not supabase_client.is_connected():
            return {"sucesso": False, "mensagem": "Sistema de chat indisponível", "mensagens": []}

        # Usar função SQL para buscar mensagens
        try:
            response = supabase_client.client.rpc('get_recent_chat_messages', {
                'limite': limite
            }).execute()

            if response.data:
                mensagens = []
                for msg in response.data:
                    mensagens.append({
                        'id': msg.get('id'),
                        'user_uid': msg.get('user_uid'),
                        'nick': msg.get('nick'),
                        'message': msg.get('message'),
                        'created_at': msg.get('created_at'),
                        'time_ago': msg.get('time_ago'),
                        'is_blocked': msg.get('is_blocked', False)
                    })

                return {
                    "sucesso": True,
                    "mensagens": mensagens,
                    "total": len(mensagens)
                }
            else:
                return {"sucesso": True, "mensagens": [], "total": 0}

        except Exception as rpc_error:
            print(f"[CHAT] Erro na função SQL de busca: {rpc_error}")
            # Fallback: busca direta na tabela
            try:
                response = supabase_client.client.table('chat_messages').select(
                    'id, user_uid, nick, message, created_at, is_blocked'
                ).eq('is_blocked', False).order('created_at', desc=True).limit(limite).execute()

                if response.data:
                    mensagens = []
                    for msg in response.data:
                        mensagens.append({
                            'id': msg.get('id'),
                            'user_uid': msg.get('user_uid'),
                            'nick': msg.get('nick'),
                            'message': msg.get('message'),
                            'created_at': msg.get('created_at'),
                            'time_ago': 'agora',
                            'is_blocked': msg.get('is_blocked', False)
                        })

                    return {
                        "sucesso": True,
                        "mensagens": mensagens,
                        "total": len(mensagens)
                    }
                else:
                    return {"sucesso": True, "mensagens": [], "total": 0}

            except Exception as fallback_error:
                print(f"[CHAT] Erro no fallback: {fallback_error}")
                return {"sucesso": False, "mensagem": "Erro ao buscar mensagens", "mensagens": []}

    except Exception as e:
        print(f"[CHAT] Erro geral na busca: {e}")
        return {"sucesso": False, "mensagem": f"Erro interno: {str(e)}", "mensagens": []}

def upgrade_jogador(uid: str, tipo: str, quantidade: int = 1) -> Dict[str, Any]:
    """
    Realiza upgrade de um jogador (CPU, RAM ou Firewall) com sistema de XP e Shacks
    
    Args:
        uid: ID do usuário
        tipo: Tipo de upgrade ('cpu', 'ram', 'firewall')
        quantidade: Quantidade de upgrades a fazer
    
    Returns:
        Dict com resultado da operação
    """
    try:
        jogador = get_jogador(uid)
        if not jogador:
            return {"sucesso": False, "mensagem": "Jogador não encontrado"}
        
        # Calcular custo total incluindo Shacks
        custo_resultado = calcular_custo_upgrade_multiplo(tipo, jogador.get(tipo, 1), quantidade)
        custo_dinheiro = custo_resultado.get("custo_dinheiro", 0)
        custo_shacks = custo_resultado.get("custo_shacks", 0)
        
        # Verificar se tem dinheiro suficiente
        if jogador.get("dinheiro", 0) < custo_dinheiro:
            return {"sucesso": False, "mensagem": "Dinheiro insuficiente"}
        
        # Verificar se tem Shacks suficientes (se necessário)
        if custo_shacks > 0 and jogador.get("shack", 0) < custo_shacks:
            return {"sucesso": False, "mensagem": f"Shacks insuficientes. Necessário: {custo_shacks} Shacks"}
        
        # Calcular XP ganho
        nivel_atual_item = jogador.get(tipo, 1)
        xp_ganho = sum([calcular_xp_upgrade(nivel_atual_item + i + 1) for i in range(quantidade)])
        
        # Realizar upgrade
        novo_valor = jogador.get(tipo, 1) + quantidade
        novo_dinheiro = jogador.get("dinheiro", 0) - custo_dinheiro
        novo_shacks = jogador.get("shack", 0) - custo_shacks
        
        # Aplicar XP e verificar level up
        jogador_atualizado = ganhar_xp(jogador.copy(), xp_ganho)
        
        # Preparar updates
        updates = {
            tipo: novo_valor,
            "dinheiro": novo_dinheiro,
            "xp": jogador_atualizado.get("xp", 0),
            "nivel": jogador_atualizado.get("nivel", 1),
            "historico": jogador_atualizado.get("historico", [])
        }
        
        # Apenas atualiza Shacks se foram consumidos
        if custo_shacks > 0:
            updates["shack"] = novo_shacks
        
        # Atualizar no banco
        resultado = supabase_client.atualizar_usuario(uid, updates)
        
        if resultado.get("sucesso"):
            nivel_final = jogador_atualizado.get("nivel", 1)
            level_up_msg = ""
            if nivel_final > jogador.get("nivel", 1):
                level_up_msg = f" 🏆 Level up! Nível {nivel_final}!"
            
            # Monta mensagem incluindo consumo de Shacks se aplicável
            mensagem_custo = f"${custo_dinheiro:,}"
            if custo_shacks > 0:
                mensagem_custo += f" + {custo_shacks} Shacks"
            
            return {
                "sucesso": True,
                "mensagem": f"Upgrade de {tipo} realizado com sucesso! Custo: {mensagem_custo}. +{xp_ganho} XP!{level_up_msg}",
                "novo_valor": novo_valor,
                "custo_dinheiro": custo_dinheiro,
                "custo_shacks": custo_shacks,
                "dinheiro_restante": novo_dinheiro,
                "shacks_restantes": novo_shacks,
                "xp_ganho": xp_ganho,
                "nivel_jogador": nivel_final,
                "level_up": nivel_final > jogador.get("nivel", 1)
            }
        else:
            return {"sucesso": False, "mensagem": "Erro ao atualizar dados"}
            
    except Exception as e:
        return {"sucesso": False, "mensagem": f"Erro interno: {str(e)}"}

# === SISTEMA DE TORNEIO DIÁRIO DE DEFACE ===

def get_tournament_status():
    """
    Obter status do torneio atual (data início, fim, etc.)
    """
    try:
        now = datetime.now(timezone.utc)
        today_start = now.replace(hour=0, minute=0, second=0, microsecond=0)
        tomorrow_start = today_start + timedelta(days=1)
        
        time_left = tomorrow_start - now
        
        return {
            "sucesso": True,
            "torneio_ativo": True,
            "inicio": today_start.isoformat(),
            "fim": tomorrow_start.isoformat(),
            "tempo_restante_segundos": int(time_left.total_seconds()),
            "tempo_restante_formatado": {
                "horas": int(time_left.total_seconds() // 3600),
                "minutos": int((time_left.total_seconds() % 3600) // 60),
                "segundos": int(time_left.total_seconds() % 60)
            }
        }
    except Exception as e:
        print(f"❌ Erro ao obter status do torneio: {str(e)}")
        return {"sucesso": False, "mensagem": f"Erro: {str(e)}"}

def get_tournament_status():
    """
    Obter status do torneio de upgrade diário
    """
    try:
        # Buscar último reset
        sistema_data = supabase_client.client.table('sistema').select('*').eq('id', 'torneio_upgrade').limit(1).execute()
        
        if sistema_data.data:
            ultimo_reset = sistema_data.data[0].get('ultimo_reset')
            app_do_dia = sistema_data.data[0].get('app_do_dia', 'cpu')
            
            # Calcular próximo reset
            if ultimo_reset:
                ultimo_reset_dt = parse_iso_datetime(ultimo_reset)
                proximo_reset_dt = ultimo_reset_dt + timedelta(days=1)
                proximo_reset_dt = proximo_reset_dt.replace(hour=0, minute=0, second=0, microsecond=0)
            else:
                # Se não há reset anterior, próximo é amanhã às 00:00
                agora = datetime.now(timezone.utc)
                proximo_reset_dt = (agora + timedelta(days=1)).replace(hour=0, minute=0, second=0, microsecond=0)
            
            return {
                "sucesso": True,
                "ultimo_reset": ultimo_reset,
                "proximo_reset": proximo_reset_dt.isoformat(),
                "app_do_dia": app_do_dia,
                "tipo_torneio": "upgrade"
            }
        else:
            # Primeira vez
            agora = datetime.now(timezone.utc)
            proximo_reset = (agora + timedelta(days=1)).replace(hour=0, minute=0, second=0, microsecond=0)
            
            return {
                "sucesso": True,
                "ultimo_reset": None,
                "proximo_reset": proximo_reset.isoformat(),
                "app_do_dia": "cpu",  # Default
                "tipo_torneio": "upgrade"
            }
            
    except Exception as e:
        print(f"❌ Erro ao obter status do torneio: {str(e)}")
        return {"sucesso": False, "mensagem": f"Erro: {str(e)}"}

def sortear_app_diario():
    """
    Sorteia um app específico para o torneio de upgrade diário
    """
    apps_disponiveis = [
        'cpu', 'ram', 'firewall', 'antivirus', 
        'malware_kit', 'bruteforce', 'bankguard', 'proxyvpn'
    ]
    
    import random
    app_sorteado = random.choice(apps_disponiveis)
    return app_sorteado

def get_app_do_torneio_hoje():
    """
    Obter o app sorteado para o torneio de hoje
    """
    try:
        # Buscar app do dia atual
        sistema_data = supabase_client.client.table('sistema').select('*').eq('id', 'torneio_upgrade').limit(1).execute()
        
        if sistema_data.data:
            app_hoje = sistema_data.data[0].get('app_do_dia')
            ultimo_sorteio = sistema_data.data[0].get('ultimo_sorteio')
            
            # Verificar se já passou de hoje
            now = datetime.now(timezone.utc)
            hoje_00h = now.replace(hour=0, minute=0, second=0, microsecond=0)
            
            if ultimo_sorteio:
                ultimo_sorteio_dt = parse_iso_datetime(ultimo_sorteio)
                if ultimo_sorteio_dt >= hoje_00h and app_hoje:
                    return app_hoje
            
            # Se não tem app para hoje ou passou do dia, sorteia um novo
            novo_app = sortear_app_diario()
            
            # Salvar o novo app do dia
            supabase_client.client.table('sistema').upsert({
                'id': 'torneio_upgrade',
                'app_do_dia': novo_app,
                'ultimo_sorteio': now.isoformat()
            }).execute()
            
            return novo_app
        else:
            # Primeira vez, sorteia e salva
            novo_app = sortear_app_diario()
            supabase_client.client.table('sistema').insert({
                'id': 'torneio_upgrade',
                'app_do_dia': novo_app,
                'ultimo_sorteio': datetime.now(timezone.utc).isoformat()
            }).execute()
            
            return novo_app
            
    except Exception as e:
        print(f"❌ Erro ao obter app do torneio: {str(e)}")
        # Fallback para um app padrão
        return 'cpu'

def adicionar_pontos_torneio_upgrade(uid: str, app_upgradado: str, quantidade: int = 1):
    """
    Adiciona pontos ao jogador e grupo quando faz upgrade no app específico do dia
    1 upgrade = 1 ponto
    """
    try:
        app_do_dia = get_app_do_torneio_hoje()
        
        # Só conta pontos se o upgrade foi no app específico do dia
        if app_upgradado != app_do_dia:
            return False
        
        jogador = get_jogador(uid)
        if not jogador:
            return False
        
        # Adicionar pontos individuais
        pontos_atuais = jogador.get('tournament_points_individual', 0)
        novos_pontos = pontos_atuais + quantidade
        
        supabase_client.client.table('usuarios').update({
            'tournament_points_individual': novos_pontos
        }).eq('uid', uid).execute()
        
        # Se o jogador tem grupo, adicionar pontos ao grupo
        grupo_id = jogador.get('grupo_id')
        if grupo_id:
            grupo_data = supabase_client.client.table('grupos').select('*').eq('id', grupo_id).limit(1).execute()
            if grupo_data.data:
                grupo = grupo_data.data[0]
                pontos_grupo_atuais = grupo.get('tournament_points', 0)
                novos_pontos_grupo = pontos_grupo_atuais + quantidade
                
                supabase_client.client.table('grupos').update({
                    'tournament_points': novos_pontos_grupo
                }).eq('id', grupo_id).execute()
        
        print(f"🏆 Pontos de torneio adicionados: {jogador['nick']} +{quantidade} pontos por upgrade em {app_upgradado}")
        
        # INTEGRAÇÃO COM SISTEMA TTL - Adicionar pontos ao torneio de upgrade
        try:
            from .tournament_ttl import torneio_manager
            resultado_ttl = torneio_manager.adicionar_pontos_upgrade(uid, quantidade, grupo_id)
            print(f"[DEBUG UPGRADE TTL] Pontos adicionados ao sistema TTL: {resultado_ttl}")
        except ImportError:
            print("[DEBUG UPGRADE TTL] Sistema TTL não disponível")
        except Exception as e:
            print(f"[DEBUG UPGRADE TTL] Erro ao adicionar pontos TTL: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro ao adicionar pontos de torneio: {str(e)}")
        return False

def get_tournament_ranking():
    """
    Obter ranking do torneio de upgrade diário atual
    """
    try:
        # Buscar grupos ordenados pelos pontos do torneio
        ranking_grupos = []
        grupos_data = supabase_client.client.table('grupos').select('*').order('tournament_points', desc=True).limit(50).execute()
        
        if grupos_data.data:
            for grupo in grupos_data.data:
                ranking_grupos.append({
                    "id": grupo.get("id", ""),
                    "nome": grupo.get("nome", ""),
                    "tournament_points": grupo.get("tournament_points", 0),
                    "deface_points": grupo.get("deface_points", 0),
                    "membros": grupo.get("membros", []),
                    "lider": grupo.get("lider", "")
                })
        
        # Buscar jogadores individuais por pontos do torneio
        ranking_jogadores = []
        usuarios_data = supabase_client.client.table('usuarios').select('*').order('tournament_points_individual', desc=True).limit(50).execute()
        
        if usuarios_data.data:
            for usuario in usuarios_data.data:
                ranking_jogadores.append({
                    "uid": usuario.get("uid", ""),
                    "nick": usuario.get("nick", ""),
                    "tournament_points": usuario.get("tournament_points_individual", 0),
                    "deface_points": usuario.get("deface_points_individual", 0),
                    "grupo": usuario.get("grupo_id", None),
                    "grupo_nome": usuario.get("grupo_nome", None),
                    "grupo_id": usuario.get("grupo_id", None),
                    "nivel": usuario.get("nivel", 1)
                })
        
        # Obter app do dia
        app_do_dia = get_app_do_torneio_hoje()
        
        return {
            "sucesso": True,
            "ranking_grupos": ranking_grupos,
            "ranking_jogadores": ranking_jogadores,
            "status": get_tournament_status(),
            "app_do_dia": app_do_dia,
            "tipo_torneio": "upgrade"
        }
        
    except Exception as e:
        print(f"❌ Erro ao obter ranking do torneio: {str(e)}")
        return {"sucesso": False, "mensagem": f"Erro: {str(e)}"}

def adicionar_log_usuario(uid: str, mensagem: str) -> bool:
    """
    Adiciona uma entrada no log do usuário
    """
    try:
        jogador = get_jogador(uid)
        if not jogador:
            return False
        
        now = datetime.now(timezone.utc)
        log_entry = {
            "timestamp": now.isoformat(),
            "tipo": "sistema",
            "mensagem": mensagem,
            "hora": now.strftime("%H:%M:%S"),
            "data": now.strftime("%d/%m/%Y")
        }
        
        logs_jogador = jogador.get('log', [])
        logs_jogador.append(log_entry)
        
        # Mantém apenas os últimos 100 logs
        if len(logs_jogador) > 100:
            logs_jogador = logs_jogador[-100:]
        
        supabase_client.atualizar_usuario(uid, {'log': logs_jogador})
        return True
        
    except Exception as e:
        print(f"❌ Erro ao adicionar log para {uid}: {e}")
        return False

def reset_daily_tournament():
    """
    Resetar o torneio de upgrade diário e dar recompensas às 00:00 server time
    """
    try:
        print("🏆 Iniciando reset do torneio de upgrade diário...")
        
        # Buscar top 10 grupos para dar recompensas escalonadas
        grupos_data = supabase_client.client.table('grupos').select('*').order('tournament_points', desc=True).limit(10).execute()
        
        # Sistema de recompensas escalonadas (reduzidas por ser torneio de upgrade)
        recompensas_grupos = [
            {"dinheiro": 50000, "shacks": 500, "bonus_xp": 2500},   # 1º lugar
            {"dinheiro": 35000, "shacks": 350, "bonus_xp": 2000},   # 2º lugar  
            {"dinheiro": 25000, "shacks": 250, "bonus_xp": 1500},   # 3º lugar
            {"dinheiro": 20000, "shacks": 200, "bonus_xp": 1200},   # 4º lugar
            {"dinheiro": 15000, "shacks": 150, "bonus_xp": 1000},   # 5º lugar
            {"dinheiro": 12000, "shacks": 120, "bonus_xp": 800},    # 6º lugar
            {"dinheiro": 10000, "shacks": 100, "bonus_xp": 600},    # 7º lugar
            {"dinheiro": 8000, "shacks": 80, "bonus_xp": 500},      # 8º lugar
            {"dinheiro": 6000, "shacks": 60, "bonus_xp": 400},      # 9º lugar
            {"dinheiro": 5000, "shacks": 50, "bonus_xp": 300}       # 10º lugar
        ]
        
        grupos_premiados = []
        total_grupos_premiados = 0
        
        # Dar recompensas aos grupos vencedores
        if grupos_data.data:
            for i, grupo in enumerate(grupos_data.data):
                if i >= len(recompensas_grupos) or grupo.get("tournament_points", 0) <= 0:
                    break
                    
                recompensa = recompensas_grupos[i]
                membros_premiados = 0
                
                # Buscar membros do grupo pelos UIDs
                if grupo.get("membros"):
                    for membro_nick in grupo.get("membros", []):
                        try:
                            # Buscar o membro pelo nick
                            membro_data = supabase_client.client.table('usuarios').select('*').eq('nick', membro_nick).limit(1).execute()
                            
                            if membro_data.data:
                                membro = membro_data.data[0]
                                membro_uid = membro.get('uid')
                                
                                # Dar recompensa
                                novo_dinheiro = membro.get('dinheiro', 0) + recompensa["dinheiro"]
                                novo_shack = membro.get('shack', 0) + recompensa["shacks"]
                                novo_xp = membro.get('xp', 0) + recompensa["bonus_xp"]
                                
                                # Calcular se subiu de nível com o XP bonus
                                nivel_atual = membro.get('nivel', 1)
                                xp_necessario = calcular_xp_necessario(nivel_atual)
                                novo_nivel = nivel_atual
                                
                                while novo_xp >= xp_necessario and novo_nivel < 100:
                                    novo_xp -= xp_necessario
                                    novo_nivel += 1
                                    xp_necessario = calcular_xp_necessario(novo_nivel)
                                
                                supabase_client.client.table('usuarios').update({
                                    'dinheiro': novo_dinheiro,
                                    'shack': novo_shack,
                                    'xp': novo_xp,
                                    'nivel': novo_nivel,
                                    'xp_necessario': xp_necessario
                                }).eq('uid', membro_uid).execute()
                                
                                membros_premiados += 1
                                
                                # Registrar no log do jogador
                                adicionar_log_usuario(membro_uid, f"🏆 Torneio Upgrade #{i+1}: +${recompensa['dinheiro']:,}, +{recompensa['shacks']} Shacks, +{recompensa['bonus_xp']} XP")
                                
                        except Exception as membro_error:
                            print(f"❌ Erro ao premiar membro {membro_nick}: {membro_error}")
                            continue
                
                if membros_premiados > 0:
                    grupos_premiados.append({
                        "posicao": i + 1,
                        "grupo_id": grupo.get("id", ""),
                        "grupo_nome": grupo.get("nome", ""),
                        "pontos": grupo.get("tournament_points", 0),
                        "membros_premiados": membros_premiados,
                        "recompensa": recompensa
                    })
                    total_grupos_premiados += 1
        
        # Buscar top 10 jogadores individuais para recompensas pessoais
        usuarios_data = supabase_client.client.table('usuarios').select('*').order('tournament_points_individual', desc=True).limit(10).execute()
        
        recompensas_individuais = [
            {"dinheiro": 25000, "shacks": 250, "bonus_xp": 1250},   # 1º lugar individual
            {"dinheiro": 20000, "shacks": 200, "bonus_xp": 1000},   # 2º lugar individual
            {"dinheiro": 15000, "shacks": 150, "bonus_xp": 750},    # 3º lugar individual
            {"dinheiro": 12000, "shacks": 120, "bonus_xp": 600},    # 4º lugar individual
            {"dinheiro": 10000, "shacks": 100, "bonus_xp": 500},    # 5º lugar individual
            {"dinheiro": 8000, "shacks": 80, "bonus_xp": 400},      # 6º lugar individual
            {"dinheiro": 6000, "shacks": 60, "bonus_xp": 300},      # 7º lugar individual
            {"dinheiro": 5000, "shacks": 50, "bonus_xp": 250},      # 8º lugar individual
            {"dinheiro": 4000, "shacks": 40, "bonus_xp": 200},      # 9º lugar individual
            {"dinheiro": 3000, "shacks": 30, "bonus_xp": 150}       # 10º lugar individual
        ]
        
        jogadores_premiados = []
        
        if usuarios_data.data:
            for i, usuario in enumerate(usuarios_data.data):
                if i >= len(recompensas_individuais) or usuario.get("tournament_points_individual", 0) <= 0:
                    break
                
                recompensa = recompensas_individuais[i]
                
                try:
                    # Dar recompensa individual
                    novo_dinheiro = usuario.get('dinheiro', 0) + recompensa["dinheiro"]
                    novo_shack = usuario.get('shack', 0) + recompensa["shacks"]
                    novo_xp = usuario.get('xp', 0) + recompensa["bonus_xp"]
                    
                    # Calcular se subiu de nível
                    nivel_atual = usuario.get('nivel', 1)
                    xp_necessario = calcular_xp_necessario(nivel_atual)
                    novo_nivel = nivel_atual
                    
                    while novo_xp >= xp_necessario and novo_nivel < 100:
                        novo_xp -= xp_necessario
                        novo_nivel += 1
                        xp_necessario = calcular_xp_necessario(novo_nivel)
                    
                    supabase_client.client.table('usuarios').update({
                        'dinheiro': novo_dinheiro,
                        'shack': novo_shack,
                        'xp': novo_xp,
                        'nivel': novo_nivel,
                        'xp_necessario': xp_necessario
                    }).eq('uid', usuario.get('uid')).execute()
                    
                    # Registrar no log
                    adicionar_log_usuario(usuario.get('uid'), f"🥇 Ranking Upgrade Individual #{i+1}: +${recompensa['dinheiro']:,}, +{recompensa['shacks']} Shacks, +{recompensa['bonus_xp']} XP")
                    
                    jogadores_premiados.append({
                        "posicao": i + 1,
                        "nick": usuario.get("nick", ""),
                        "pontos": usuario.get("tournament_points_individual", 0),
                        "recompensa": recompensa
                    })
                    
                except Exception as usuario_error:
                    print(f"❌ Erro ao premiar jogador {usuario.get('nick', 'N/A')}: {usuario_error}")
                    continue
        
        # Resetar pontos do torneio de todos os grupos
        grupos_reset = supabase_client.client.table('grupos').update({'tournament_points': 0}).neq('id', 'dummy').execute()
        
        # Resetar pontos do torneio de todos os usuários
        usuarios_reset = supabase_client.client.table('usuarios').update({'tournament_points_individual': 0}).neq('uid', 'dummy').execute()
        
        # Sortear novo app para o próximo dia
        novo_app = sortear_app_diario()
        
        # Atualizar timestamp do último reset e novo app
        now_iso = datetime.now(timezone.utc).isoformat()
        supabase_client.client.table('sistema').upsert({
            'id': 'torneio_upgrade',
            'ultimo_reset': now_iso,
            'app_do_dia': novo_app,
            'ultimo_sorteio': now_iso,
            'grupos_premiados': grupos_premiados,
            'jogadores_premiados': jogadores_premiados,
            'total_grupos_participantes': len(grupos_data.data) if grupos_data.data else 0,
            'total_jogadores_participantes': len(usuarios_data.data) if usuarios_data.data else 0
        }).execute()
        
        resultado = {
            "sucesso": True,
            "mensagem": f"Torneio de Upgrade resetado! {total_grupos_premiados} grupos e {len(jogadores_premiados)} jogadores premiados. Novo app: {novo_app}",
            "grupos_premiados": grupos_premiados,
            "jogadores_premiados": jogadores_premiados,
            "reset_timestamp": now_iso,
            "novo_app_do_dia": novo_app,
            "tipo_torneio": "upgrade",
            "estatisticas": {
                "total_grupos_participantes": len(grupos_data.data) if grupos_data.data else 0,
                "total_jogadores_participantes": len(usuarios_data.data) if usuarios_data.data else 0,
                "grupos_premiados": total_grupos_premiados,
                "jogadores_premiados": len(jogadores_premiados)
            }
        }
        
        print(f"✅ Torneio de Upgrade resetado: {resultado['mensagem']}")
        return resultado
        
    except Exception as e:
        print(f"❌ Erro ao resetar torneio de upgrade: {str(e)}")
        return {"sucesso": False, "mensagem": f"Erro: {str(e)}"}

def should_reset_tournament():
    """
    Verificar se o torneio de upgrade deve ser resetado (às 00:00 server time)
    """
    try:
        now = datetime.now(timezone.utc)
        
        # Buscar último reset do sistema
        sistema_data = supabase_client.client.table('sistema').select('*').eq('id', 'torneio_upgrade').limit(1).execute()
        
        if sistema_data.data:
            ultimo_reset_str = sistema_data.data[0].get('ultimo_reset')
            if ultimo_reset_str:
                ultimo_reset_dt = parse_iso_datetime(ultimo_reset_str.replace('Z', '+00:00'))
                
                # Calcular próximo reset (00:00 do dia seguinte após o último reset)
                ultimo_reset_date = ultimo_reset_dt.date()
                proximo_reset_date = ultimo_reset_date + timedelta(days=1)
                proximo_reset_dt = datetime.combine(proximo_reset_date, datetime.min.time()).replace(tzinfo=timezone.utc)
                
                # Se já passou das 00:00 de hoje e ainda não resetou hoje
                hoje_00h = now.replace(hour=0, minute=0, second=0, microsecond=0)
                
                # Resetar se:
                # 1. Já passou das 00:00 de hoje E
                # 2. O último reset foi antes das 00:00 de hoje
                if now >= hoje_00h and ultimo_reset_dt < hoje_00h:
                    return True
        else:
            # Primeira vez, deve resetar
            return True
            
        return False
        
    except Exception as e:
        print(f"❌ Erro ao verificar reset do torneio de upgrade: {str(e)}")
        return False

def auto_reset_tournament_if_needed():
    """
    Função para ser chamada periodicamente para resetar o torneio automaticamente
    """
    try:
        if should_reset_tournament():
            resultado = reset_daily_tournament()
            print(f"🏆 Torneio resetado automaticamente: {resultado}")
            return resultado
        
        return {"sucesso": True, "mensagem": "Reset não necessário"}
        
    except Exception as e:
        print(f"❌ Erro no reset automático do torneio: {str(e)}")
        return {"sucesso": False, "mensagem": f"Erro: {str(e)}"}

# === FUNÇÕES PARA RANKING INDIVIDUAL DE DEFACE ===

def get_groups_deface_ranking():
    """Obtém o ranking dos grupos por pontos de deface"""
    try:
        # Verificar se precisa resetar automaticamente
        auto_reset_deface_tournaments_if_needed()
        
        # Buscar todos os grupos com seus membros
        groups_result = supabase_client.table('groups').select('*').execute()
        
        if not groups_result.data:
            return {
                'sucesso': True,
                'data': {
                    'grupos': [],
                    'status': 'sem_grupos'
                }
            }
        
        groups_ranking = []
        
        for group in groups_result.data:
            # Buscar membros do grupo
            members_result = supabase_client.table('users').select('deface_points').eq('group_id', group['id']).execute()
            
            # Calcular total de pontos de deface do grupo
            total_deface_points = sum(member.get('deface_points', 0) for member in members_result.data)
            member_count = len(members_result.data)
            
            groups_ranking.append({
                'id': group['id'],
                'name': group['name'],
                'description': group.get('description', ''),
                'total_deface_points': total_deface_points,
                'member_count': member_count,
                'avatar': group.get('avatar_url', '')
            })
        
        # Ordenar por pontos de deface (maior primeiro)
        groups_ranking.sort(key=lambda x: x['total_deface_points'], reverse=True)
        
        # Adicionar posições
        for i, group in enumerate(groups_ranking):
            group['position'] = i + 1
        
        return {
            'sucesso': True,
            'data': {
                'grupos': groups_ranking,
                'status': 'ativo',
                'total_grupos': len(groups_ranking),
                'data_atualizacao': datetime.now().isoformat(),
                'tempo_restante': 86400,  # 24 horas em segundos
                'recompensas': {
                    'primeiro': {'dinheiro': 100000, 'shacks': 1000, 'xp': 5000},
                    'segundo': {'dinheiro': 50000, 'shacks': 500, 'xp': 2500},
                    'terceiro': {'dinheiro': 25000, 'shacks': 250, 'xp': 1250}
                }
            }
        }
        
    except Exception as e:
        print(f"Erro ao buscar ranking de grupos de deface: {str(e)}")
        return {
            'sucesso': False,
            'mensagem': f"Erro ao buscar ranking de grupos de deface: {str(e)}"
        }

def get_individual_deface_ranking():
    """
    Obtém ranking individual de jogadores por pontos de deface da tabela torneio_pontuacoes
    """
    try:
        if not supabase_client.is_connected():
            return {
                'sucesso': False,
                'mensagem': 'Database não disponível',
                'ranking_jogadores': []
            }

        # Tentar usar função SQL primeiro
        try:
            response = supabase_client.client.rpc('get_ranking_individual_deface', {'limite': 50}).execute()
            if response.data:
                ranking_jogadores = []
                for i, jogador in enumerate(response.data):
                    ranking_jogadores.append({
                        'position': jogador.get('posicao', i + 1),
                        'uid': jogador.get('jogador_uid'),
                        'nick': jogador.get('nick', 'Jogador'),
                        'deface_points': jogador.get('pontos_individuais', 0),
                        'grupo_nome': jogador.get('grupo_nome', 'Sem grupo'),
                        'grupo_id': jogador.get('grupo_id')
                    })

                return {
                    'sucesso': True,
                    'ranking_jogadores': ranking_jogadores
                }
        except Exception as rpc_error:
            print(f"[DEFACE RANKING] Função SQL não disponível, usando consulta direta: {rpc_error}")

        # Fallback: Buscar ranking da tabela torneio_pontuacoes diretamente
        response = supabase_client.client.table('torneio_pontuacoes').select(
            'jogador_uid, nick, pontos_individuais, grupo_id, grupo_nome'
        ).eq('tipo_torneio', 'deface').order('pontos_individuais', desc=True).limit(50).execute()

        if not response.data:
            return {
                'sucesso': True,
                'ranking_jogadores': [],
                'mensagem': 'Nenhum jogador encontrado'
            }

        ranking_jogadores = []
        posicao_atual = 1

        for jogador in response.data:
            pontos_deface = jogador.get('pontos_individuais', 0)
            jogador_uid = jogador.get('jogador_uid')
            nick = jogador.get('nick')
            grupo_nome = jogador.get('grupo_nome')
            grupo_id = jogador.get('grupo_id')

            # Pular jogadores sem pontos
            if pontos_deface <= 0:
                continue

            # Se não temos nick, buscar da tabela usuarios
            if not nick and jogador_uid:
                try:
                    usuario_data = supabase_client.client.table('usuarios').select('nick, grupo_id').eq('uid', jogador_uid).execute()
                    if usuario_data.data:
                        usuario = usuario_data.data[0]
                        nick = usuario.get('nick', f'Jogador {jogador_uid[:8]}')
                        if not grupo_id:
                            grupo_id = usuario.get('grupo_id')
                except Exception as e:
                    print(f"[DEBUG RANKING] Erro ao buscar dados do jogador {jogador_uid}: {e}")
                    nick = f'Jogador {jogador_uid[:8]}'

            # Se não temos grupo_nome mas temos grupo_id, buscar da tabela grupos
            if not grupo_nome and grupo_id:
                try:
                    grupo_data = supabase_client.client.table('grupos').select('nome').eq('id', grupo_id).execute()
                    if grupo_data.data:
                        grupo_nome = grupo_data.data[0].get('nome', f'Grupo {grupo_id[:8]}')
                except Exception as e:
                    print(f"[DEBUG RANKING] Erro ao buscar dados do grupo {grupo_id}: {e}")
                    grupo_nome = f'Grupo {grupo_id[:8]}'

            ranking_jogadores.append({
                'position': posicao_atual,
                'uid': jogador_uid,
                'nick': nick or f'Jogador {jogador_uid[:8]}',
                'deface_points': pontos_deface,
                'grupo_nome': grupo_nome or 'Sem grupo',
                'grupo_id': grupo_id
            })

            posicao_atual += 1

        return {
            'sucesso': True,
            'ranking_jogadores': ranking_jogadores
        }

    except Exception as e:
        print(f"❌ Erro ao buscar ranking individual de deface: {str(e)}")
        return {
            'sucesso': False,
            'mensagem': f'Erro ao buscar ranking: {str(e)}',
            'ranking_jogadores': []
        }

def get_groups_deface_ranking():
    """
    Obtém ranking de grupos por pontos de deface da tabela torneio_pontuacoes
    """
    try:
        if not supabase_client.is_connected():
            return {"sucesso": False, "mensagem": "Database não disponível"}

        # Tentar usar função SQL primeiro
        try:
            response = supabase_client.client.rpc('get_ranking_grupos_deface', {'limite': 50}).execute()
            if response.data:
                ranking = []
                for grupo in response.data:
                    ranking.append({
                        'id': grupo.get('grupo_id'),
                        'nome': grupo.get('grupo_nome', 'Grupo Desconhecido'),
                        'pontos': grupo.get('total_pontos', 0),
                        'membros_count': grupo.get('membros_count', 0)
                    })

                return {
                    "sucesso": True,
                    "data": {
                        "grupos": ranking,
                        "tempo_restante": get_tournament_time_remaining(),
                        "recompensas": {
                            "primeiro": {"dinheiro": 100000, "shacks": 1000, "xp": 5000},
                            "segundo": {"dinheiro": 50000, "shacks": 500, "xp": 2500},
                            "terceiro": {"dinheiro": 25000, "shacks": 250, "xp": 1250}
                        }
                    }
                }
        except Exception as rpc_error:
            print(f"[DEFACE RANKING] Função SQL de grupos não disponível, usando consulta direta: {rpc_error}")

        # Fallback: Buscar grupos com pontos de deface da tabela torneio_pontuacoes
        # Agregar pontos por grupo_id
        print(f"[DEBUG RANKING GRUPOS] Buscando dados da tabela torneio_pontuacoes...")
        response = supabase_client.client.table('torneio_pontuacoes').select(
            'grupo_id, grupo_nome, group_points'
        ).eq('tipo_torneio', 'deface').execute()

        print(f"[DEBUG RANKING GRUPOS] Resposta da consulta: {response}")
        print(f"[DEBUG RANKING GRUPOS] Dados encontrados: {len(response.data) if response.data else 0}")

        if response.data:
            for i, item in enumerate(response.data[:3]):  # Mostrar apenas os 3 primeiros para debug
                print(f"[DEBUG RANKING GRUPOS] Item {i+1}: {item}")

        if not response.data:
            return {
                "sucesso": True,
                "data": {"grupos": []},
                "mensagem": "Nenhum grupo encontrado"
            }

        # Agregar pontos por grupo
        grupos_pontos = {}
        for registro in response.data:
            grupo_id = registro.get('grupo_id')
            grupo_nome = registro.get('grupo_nome')
            pontos = registro.get('group_points', 0)

            if grupo_id and pontos > 0:
                if grupo_id not in grupos_pontos:
                    grupos_pontos[grupo_id] = {
                        'id': grupo_id,
                        'nome': grupo_nome or f'Grupo {grupo_id[:8]}',
                        'pontos': 0,
                        'membros_count': 0
                    }
                grupos_pontos[grupo_id]['pontos'] += pontos
                grupos_pontos[grupo_id]['membros_count'] += 1

        # Converter para lista e ordenar
        ranking = list(grupos_pontos.values())
        ranking.sort(key=lambda x: x['pontos'], reverse=True)

        return {
            "sucesso": True,
            "data": {
                "grupos": ranking[:50],  # Top 50 grupos
                "tempo_restante": get_tournament_time_remaining(),
                "recompensas": {
                    "primeiro": {"dinheiro": 100000, "shacks": 1000, "xp": 5000},
                    "segundo": {"dinheiro": 50000, "shacks": 500, "xp": 2500},
                    "terceiro": {"dinheiro": 25000, "shacks": 250, "xp": 1250}
                }
            }
        }

    except Exception as e:
        print(f"❌ Erro ao buscar ranking de grupos de deface: {str(e)}")
        return {"sucesso": False, "mensagem": f"Erro: {str(e)}"}

def get_tournament_time_remaining():
    """
    Calcula tempo restante até próximo reset do torneio (00:00 UTC)
    """
    try:
        now = datetime.now(timezone.utc)
        
        # Próxima meia-noite UTC
        tomorrow = now.replace(hour=0, minute=0, second=0, microsecond=0) + timedelta(days=1)
        
        # Tempo restante em segundos
        time_remaining = int((tomorrow - now).total_seconds())
        
        return max(0, time_remaining)
        
    except Exception as e:
        print(f"❌ Erro ao calcular tempo restante: {str(e)}")
        return 0

def reset_deface_tournaments():
    """
    Reseta os torneios de deface (grupos e individual) e paga recompensas
    """
    try:
        print("🏆 Iniciando reset dos torneios de deface...")
        
        # 1. Obter rankings finais antes do reset
        ranking_grupos = get_groups_deface_ranking()
        ranking_individual = get_individual_deface_ranking()
        
        resultados = {
            'grupos_premiados': [],
            'jogadores_premiados': [],
            'grupos_resetados': 0,
            'jogadores_resetados': 0
        }
        
        # 2. Pagar recompensas para grupos
        if ranking_grupos['sucesso'] and ranking_grupos['data']['grupos']:
            grupos = ranking_grupos['data']['grupos'][:3]  # Top 3 grupos
            
            recompensas_grupos = {
                1: {'dinheiro': 100000, 'shacks': 1000, 'xp': 5000},
                2: {'dinheiro': 50000, 'shacks': 500, 'xp': 2500},
                3: {'dinheiro': 25000, 'shacks': 250, 'xp': 1250}
            }
            
            for i, grupo in enumerate(grupos):
                posicao = i + 1
                if posicao in recompensas_grupos:
                    recompensa = recompensas_grupos[posicao]
                    
                    # Distribuir recompensas para todos os membros do grupo
                    membros_result = supabase_client.table('users').select('id, username').eq('group_id', grupo['id']).execute()
                    
                    for membro in membros_result.data:
                        try:
                            # Atualizar dinheiro, shacks e XP do membro
                            supabase_client.table('users').update({
                                'dinheiro': supabase_client.rpc('increment_money', {'user_id': membro['id'], 'amount': recompensa['dinheiro']}),
                                'shack': supabase_client.rpc('increment_shacks', {'user_id': membro['id'], 'amount': recompensa['shacks']}),
                                'xp': supabase_client.rpc('increment_xp', {'user_id': membro['id'], 'amount': recompensa['xp']})
                            }).eq('id', membro['id']).execute()
                            
                            # Adicionar log para o jogador
                            log_entry = {
                                'tipo': 'recompensa_torneio_grupo',
                                'descricao': f'Recompensa do torneio de grupos - {posicao}º lugar',
                                'dinheiro_ganho': recompensa['dinheiro'],
                                'shacks_ganho': recompensa['shacks'],
                                'xp_ganho': recompensa['xp'],
                                'timestamp': datetime.now().isoformat()
                            }
                            
                            # Adicionar ao log do jogador (simplificado)
                            try:
                                user_result = supabase_client.table('users').select('log').eq('id', membro['id']).execute()
                                if user_result.data:
                                    current_log = user_result.data[0].get('log', [])
                                    current_log.append(log_entry)
                                    supabase_client.table('users').update({'log': current_log}).eq('id', membro['id']).execute()
                            except:
                                pass  # Log não crítico
                            
                        except Exception as e:
                            print(f"❌ Erro ao premiar membro {membro['username']}: {e}")
                    
                    resultados['grupos_premiados'].append({
                        'grupo': grupo['name'],
                        'posicao': posicao,
                        'membros': len(membros_result.data),
                        'recompensa': recompensa
                    })
        
        # 3. Pagar recompensas para jogadores individuais
        if ranking_individual.get('sucesso') and ranking_individual.get('ranking_jogadores'):
            jogadores_top = ranking_individual['ranking_jogadores'][:10]  # Top 10 jogadores

            recompensas_individuais = {
                1: {'dinheiro': 50000, 'shacks': 500},
                2: {'dinheiro': 30000, 'shacks': 300},
                3: {'dinheiro': 20000, 'shacks': 200},
                4: {'dinheiro': 15000, 'shacks': 150},
                5: {'dinheiro': 15000, 'shacks': 150},
                6: {'dinheiro': 10000, 'shacks': 100},
                7: {'dinheiro': 10000, 'shacks': 100},
                8: {'dinheiro': 10000, 'shacks': 100},
                9: {'dinheiro': 10000, 'shacks': 100},
                10: {'dinheiro': 10000, 'shacks': 100}
            }

            print(f"🏆 Premiando top {len(jogadores_top)} jogadores individuais...")

            for i, jogador in enumerate(jogadores_top):
                posicao = i + 1
                if posicao in recompensas_individuais:
                    recompensa = recompensas_individuais[posicao]

                    try:
                        # Buscar jogador pelo UID (tabela correta: usuarios)
                        uid = jogador.get('uid')
                        if not uid:
                            print(f"❌ UID não encontrado para jogador: {jogador}")
                            continue

                        user_result = supabase_client.client.table('usuarios').select('uid, dinheiro, shacks').eq('uid', uid).execute()

                        if user_result.data:
                            user_data = user_result.data[0]

                            # Atualizar dinheiro e shacks
                            novo_dinheiro = user_data.get('dinheiro', 0) + recompensa['dinheiro']
                            novo_shacks = user_data.get('shacks', 0) + recompensa['shacks']

                            supabase_client.client.table('usuarios').update({
                                'dinheiro': novo_dinheiro,
                                'shacks': novo_shacks
                            }).eq('uid', uid).execute()

                            print(f"✅ Premiado {jogador.get('nick')} (#{posicao}): ${recompensa['dinheiro']} + {recompensa['shacks']} shacks")

                            resultados['jogadores_premiados'].append({
                                'jogador': jogador.get('nick'),
                                'uid': uid,
                                'posicao': posicao,
                                'pontos_deface': jogador.get('deface_points', 0),
                                'recompensa': recompensa
                            })
                        else:
                            print(f"❌ Jogador não encontrado na base de dados: {uid}")

                    except Exception as e:
                        print(f"❌ Erro ao premiar jogador {jogador.get('nick', 'Desconhecido')}: {e}")
        
        # 4. Resetar pontos de deface de todos os grupos
        try:
            print("🔄 Resetando pontos de deface dos grupos...")
            grupos_result = supabase_client.client.table('grupos').select('id').execute()
            for grupo in grupos_result.data:
                supabase_client.client.table('grupos').update({
                    'deface_points': 0
                }).eq('id', grupo['id']).execute()
                resultados['grupos_resetados'] += 1
            print(f"✅ {resultados['grupos_resetados']} grupos resetados")
        except Exception as e:
            print(f"❌ Erro ao resetar grupos: {e}")

        # 5. Pagar recompensas individuais ANTES do reset
        try:
            print("💰 Pagando recompensas individuais...")
            for i, jogador in enumerate(ranking_individual.get('ranking_jogadores', [])[:10]):
                posicao = i + 1
                uid = jogador.get('uid')
                nick = jogador.get('nick', 'Desconhecido')
                pontos = jogador.get('deface_points', 0)

                # Definir recompensas escalonadas
                if posicao == 1:
                    dinheiro, shacks = 50000, 500
                elif posicao == 2:
                    dinheiro, shacks = 30000, 300
                elif posicao == 3:
                    dinheiro, shacks = 20000, 200
                elif posicao <= 5:
                    dinheiro, shacks = 15000, 150
                elif posicao <= 10:
                    dinheiro, shacks = 10000, 100
                else:
                    continue

                # Buscar dados atuais do jogador
                jogador_atual = get_jogador(uid)
                if jogador_atual:
                    novo_dinheiro = jogador_atual.get('dinheiro', 0) + dinheiro
                    novos_shacks = jogador_atual.get('shacks', 0) + shacks

                    # Atualizar jogador
                    resultado_pagamento = atualizar_jogador(uid, {
                        'dinheiro': novo_dinheiro,
                        'shacks': novos_shacks
                    })

                    if resultado_pagamento.get('sucesso', True):
                        resultados['jogadores_premiados'].append({
                            'posicao': posicao,
                            'nick': nick,
                            'pontos': pontos,
                            'dinheiro': dinheiro,
                            'shacks': shacks
                        })
                        print(f"✅ {posicao}º lugar: {nick} - ${dinheiro:,} + {shacks} shacks")
                    else:
                        print(f"❌ Erro ao pagar {nick}: {resultado_pagamento.get('erro', 'N/A')}")
                else:
                    print(f"❌ Jogador {nick} não encontrado para pagamento")

        except Exception as e:
            print(f"❌ Erro ao pagar recompensas individuais: {e}")

        # 6. Resetar pontos de deface individuais de todos os jogadores
        try:
            print("🔄 Resetando pontos de deface individuais dos jogadores...")
            # Usar tabela correta: usuarios, campo correto: deface_points_individual
            reset_result = supabase_client.client.table('usuarios').update({
                'deface_points_individual': 0
            }).neq('uid', 'dummy-uid').execute()  # Atualiza todos exceto um UID inexistente

            # Contar jogadores resetados
            users_count_result = supabase_client.client.table('usuarios').select('uid', count='exact').execute()
            resultados['jogadores_resetados'] = users_count_result.count if users_count_result.count else 0

            print(f"✅ {resultados['jogadores_resetados']} jogadores resetados")

        except Exception as e:
            print(f"❌ Erro ao resetar jogadores: {e}")
        
        print(f"✅ Reset completo dos torneios de deface realizado!")
        print(f"   - Grupos premiados: {len(resultados['grupos_premiados'])}")
        print(f"   - Jogadores premiados: {len(resultados['jogadores_premiados'])}")
        print(f"   - Grupos resetados: {resultados['grupos_resetados']}")
        print(f"   - Jogadores resetados: {resultados['jogadores_resetados']}")
        
        return {
            'sucesso': True,
            'mensagem': 'Torneios de deface resetados e recompensas pagas com sucesso!',
            'resultados': resultados
        }
        
    except Exception as e:
        print(f"❌ Erro no reset dos torneios de deface: {str(e)}")
        return {
            'sucesso': False,
            'mensagem': f"Erro no reset dos torneios: {str(e)}"
        }

def should_reset_deface_tournaments():
    """
    Verifica se já é hora de resetar os torneios de deface (meia-noite UTC)
    """
    try:
        time_remaining = get_tournament_time_remaining()
        return time_remaining <= 0
    except Exception as e:
        print(f"❌ Erro ao verificar reset: {str(e)}")
        return False

def auto_reset_deface_tournaments_if_needed():
    """
    Verifica automaticamente se precisa resetar os torneios e executa se necessário
    """
    try:
        if should_reset_deface_tournaments():
            return reset_deface_tournaments()
        else:
            return {
                'sucesso': True,
                'mensagem': 'Reset não necessário ainda',
                'tempo_restante': get_tournament_time_remaining()
            }
    except Exception as e:
        print(f"❌ Erro no auto-reset: {str(e)}")
        return {
            'sucesso': False,
            'mensagem': f"Erro no auto-reset: {str(e)}"
        }

# --- SISTEMA DE BRUTEFORCE E TERMINAL ---

def executar_bruteforce(atacante_uid: str, alvo_ip: str) -> Dict[str, Any]:
    """
    Executa bruteforce em uma conexão ativa usando nova tabela
    Permite ataques repetidos durante a mesma conexão
    """
    try:
        if not supabase_client.is_connected():
            return {"sucesso": False, "mensagem": "Database não disponível"}
        
        atacante = get_jogador(atacante_uid)
        if not atacante:
            return {"sucesso": False, "mensagem": "Atacante não encontrado"}
        
        # Buscar conexão ativa na nova tabela
        conexoes_result = supabase_client.client.table('conexoes_ativas').select('*').eq('atacante_uid', atacante_uid).eq('alvo_ip', alvo_ip).eq('status', 'ativa').execute()
        
        if not conexoes_result.data:
            return {"sucesso": False, "mensagem": "Não há conexão ativa com este alvo. Execute primeiro um exploit bem-sucedido."}
        
        conexao = conexoes_result.data[0]
        
        # Verificar se a conexão não expirou
        expires_at = parse_iso_datetime(conexao['expires_at'])
        if datetime.now(timezone.utc) > expires_at:
            # Remover conexão expirada
            remover_conexao_ativa(atacante_uid, alvo_ip)
            return {"sucesso": False, "mensagem": "Conexão expirou. Execute um novo exploit."}
        
        # Primeiro, limpar bruteforces órfãos (sem conexão ativa)
        orfaos_limpos = limpar_bruteforces_orfaos(atacante_uid)
        if orfaos_limpos > 0:
            print(f"[BRUTEFORCE] {orfaos_limpos} bruteforces órfãos limpos antes de iniciar novo ataque")

        # Verificar se já há um ataque bruteforce em andamento para este alvo
        ataques_ativos = supabase_client.client.table('bruteforce_ataques').select('*').eq('atacante_uid', atacante_uid).eq('alvo_ip', alvo_ip).eq('status', 'executando').execute()

        if ataques_ativos.data:
            ataque_ativo = ataques_ativos.data[0]
            fim_estimado = parse_iso_datetime(ataque_ativo['fim_estimado'])
            tempo_restante = int((fim_estimado - datetime.now(timezone.utc)).total_seconds())

            if tempo_restante > 0:
                return {
                    "sucesso": False,
                    "mensagem": f"⏳ Bruteforce já em andamento! Aguarde {tempo_restante // 60}m {tempo_restante % 60}s"
                }

        # Limpar registros antigos finalizados para este atacante e alvo (resolve constraint)
        supabase_client.client.table('bruteforce_ataques').delete().eq('atacante_uid', atacante_uid).eq('alvo_ip', alvo_ip).neq('status', 'executando').execute()
        
        # Buscar dados do alvo
        alvo = buscar_jogador_por_ip(alvo_ip)
        if not alvo:
            return {"sucesso": False, "mensagem": "Alvo não encontrado"}
        
        # Sistema Bruteforce vs BankGuard
        bruteforce_atacante = atacante.get('bruteforce', 1)
        bankguard_alvo = alvo.get('bankguard', 1)
        
        # Aplicar efeito da habilidade Firewall Reverso (reduz defesa do alvo)
        habilidades_atacante = atacante.get('habilidades_adquiridas', {})
        if 'firewall_reverso' in habilidades_atacante:
            bankguard_original = bankguard_alvo
            bankguard_alvo = int(bankguard_alvo * 0.95)  # Reduz 5% da defesa
            print(f"[HABILIDADE] Firewall Reverso ativo! BankGuard reduzido de {bankguard_original} para {bankguard_alvo}")
        
        print(f"[BRUTEFORCE] {atacante['nick']} (Bruteforce {bruteforce_atacante}) vs {alvo.get('nick', 'Alvo')} (BankGuard {bankguard_alvo})")
        
        # Calcular tempo de execução e chance de sucesso baseado na nova lógica RÍGIDA
        diferenca = bruteforce_atacante - bankguard_alvo
        
        if diferenca >= 3:
            # Bruteforce MUITO maior que bankguard: 2 minutos fixo
            tempo_execucao = 120  # 2 minutos
            chance_sucesso = 1.0  # 100% de chance
            tipo_ataque = "dominante"
        elif diferenca >= 1:
            # Bruteforce maior que bankguard: tempo base + diferença
            tempo_execucao = 120 + (abs(diferenca - 1) * 30)  # Base 2min + 30s por diferença
            chance_sucesso = max(0.90, 1.0 - (abs(diferenca - 1) * 0.05))  # 90-100% de chance
            tipo_ataque = "vantagem"
        elif diferenca == 0:
            # Bruteforce igual ao bankguard: tempo equilibrado
            tempo_execucao = 180  # 3 minutos
            chance_sucesso = 0.75  # 75% de chance
            tipo_ataque = "equilibrado"
        elif diferenca >= -2:
            # Bruteforce um pouco menor: tempo aumenta drasticamente
            tempo_execucao = 240 + (abs(diferenca) * 120)  # Base 4min + 2min por diferença
            chance_sucesso = max(0.40, 0.70 - (abs(diferenca) * 0.15))  # 40-70% de chance
            tipo_ataque = "desvantagem"
        else:
            # Bruteforce MUITO menor: tempo muito alto
            tempo_execucao = 600 + (abs(diferenca) * 180)  # Base 10min + 3min por diferença
            chance_sucesso = max(0.10, 0.40 - (abs(diferenca) * 0.05))  # 10-40% de chance
            tipo_ataque = "desvantagem_severa"
        
        print(f"[BRUTEFORCE] Tipo: {tipo_ataque} | Tempo: {tempo_execucao}s | Chance: {chance_sucesso:.0%}")
        
        # Aplicar efeito da habilidade Speed Hacker (redução de tempo em 10%)
        if 'speed_hacker' in habilidades_atacante:
            tempo_original = tempo_execucao
            tempo_execucao = aplicar_efeito_habilidade_cooldown(tempo_execucao, atacante)
            print(f"[HABILIDADE] Speed Hacker ativo! Tempo reduzido de {tempo_original}s para {tempo_execucao}s")
        
        # Criar ataque em andamento na tabela
        ataque_data = {
            'atacante_uid': atacante_uid,
            'alvo_ip': alvo_ip,
            'alvo_nick': conexao.get('alvo_nick', 'Desconhecido'),
            'alvo_uid': alvo.get('uid'),
            'tempo_total': tempo_execucao,
            'chance_sucesso': chance_sucesso,
            'inicio': datetime.now(timezone.utc).isoformat(),
            'fim_estimado': (datetime.now(timezone.utc) + timedelta(seconds=tempo_execucao)).isoformat(),
            'status': 'executando'
        }
        
        # Inserir ataque na tabela bruteforce_ataques
        resultado_insert = supabase_client.client.table('bruteforce_ataques').insert(ataque_data).execute()
        
        if not resultado_insert.data:
            return {"sucesso": False, "mensagem": "Erro ao iniciar bruteforce"}
        
        return {
            "sucesso": True,
            "mensagem": f"Bruteforce iniciado! Tempo estimado: {tempo_execucao // 60}m {tempo_execucao % 60}s",
            "tempo_execucao": tempo_execucao,
            "ataque_id": resultado_insert.data[0]['id'],
            "em_andamento": True
        }
        
    except Exception as e:
        print(f"[BRUTEFORCE ERROR] {str(e)}")
        return {"sucesso": False, "mensagem": f"Erro no bruteforce: {str(e)}"}

def verificar_status_bruteforce_conexao(user_id, target_ip):
    """
    Verifica o status do bruteforce para uma conexão específica
    Retorna informações sobre se o banco foi liberado
    IMPORTANTE: Verifica se a conexão ainda está ativa antes de permitir acesso ao banco
    """
    try:
        if not supabase_client.is_connected():
            return {
                'status': 'disponivel',
                'acesso_banco_liberado': False,
                'alvo_dados': None
            }

        # Primeiro verificar se a conexão ainda está ativa
        agora = datetime.now(timezone.utc)
        conexao_ativa = supabase_client.client.table('conexoes_ativas').select('*').eq(
            'atacante_uid', user_id
        ).eq('alvo_ip', target_ip).gt('expires_at', agora.isoformat()).execute()

        if not conexao_ativa.data:
            # Conexão expirada ou não existe - marcar bruteforce como expirado se existir
            supabase_client.client.table('bruteforce_ataques').update({
                'status': 'expirado',
                'sucesso_confirmado': False,
                'alvo_dados': None,
                'finalizado_em': agora.isoformat()
            }).eq('atacante_uid', user_id).eq('alvo_ip', target_ip).in_(
                'status', ['concluido_sucesso', 'executando']
            ).execute()

            print(f"[BRUTEFORCE] Conexão expirada - acesso bancário removido para {user_id} -> {target_ip}")

            return {
                'status': 'disponivel',
                'acesso_banco_liberado': False,
                'alvo_dados': None,
                'mensagem': 'Conexão expirada. Execute um novo bruteforce.'
            }

        # Verificar se o acesso ao banco foi liberado na conexão ativa
        conexao = conexao_ativa.data[0]
        acesso_banco_liberado = conexao.get('atacante_acess', False)

        # Buscar ataque de bruteforce para este usuário e alvo
        response = supabase_client.client.table('bruteforce_ataques').select(
            'id, status, chance_sucesso, sucesso_confirmado, alvo_dados, fim_estimado'
        ).eq('atacante_uid', user_id).eq('alvo_ip', target_ip).order('created_at', desc=True).limit(1).execute()

        # Se há acesso ao banco liberado na conexão, retornar dados
        if acesso_banco_liberado and response.data:
            ataque = response.data[0]
            print(f"🔍 [DEBUG] Acesso ao banco liberado (atacante_acess=TRUE na conexao) para alvo_dados: {ataque.get('alvo_dados')}")
            return {
                'status': 'concluido_sucesso',
                'acesso_banco_liberado': True,
                'alvo_dados': ataque.get('alvo_dados')
            }

        if not response.data:
            return {
                'status': 'disponivel',
                'acesso_banco_liberado': False,
                'alvo_dados': None
            }

        ataque = response.data[0]

        # Verificar se o ataque já terminou
        fim_estimado = parse_iso_datetime(ataque['fim_estimado'])
        agora = datetime.now(timezone.utc)

        if ataque['status'] == 'falha' or ataque['status'] == 'expirado':
            # Bruteforce terminou sem sucesso ou expirado
            return {
                'status': 'concluido_falha',
                'acesso_banco_liberado': False,
                'alvo_dados': None
            }
        elif ataque['status'] == 'executando' and agora < fim_estimado:
            # Bruteforce ainda está em andamento
            return {
                'status': 'executando',
                'acesso_banco_liberado': False,
                'alvo_dados': None,
                'tempo_restante': int((fim_estimado - agora).total_seconds())
            }
        else:
            # Status desconhecido ou disponível
            return {
                'status': 'disponivel',
                'acesso_banco_liberado': acesso_banco_liberado,
                'alvo_dados': ataque.get('alvo_dados') if acesso_banco_liberado else None
            }

    except Exception as e:
        print(f"[ERRO] Falha ao verificar status bruteforce: {e}")
        return {
            'status': 'erro',
            'acesso_banco_liberado': False,
            'alvo_dados': None
        }

def verificar_e_finalizar_bruteforce(atacante_uid: str, alvo_ip: str) -> Dict[str, Any]:
    """
    Verifica se um ataque bruteforce terminou e o finaliza se necessário
    """
    try:
        if not supabase_client.is_connected():
            return {"sucesso": False, "mensagem": "Database não disponível"}
        
        # Verificar e corrigir schema se necessário
        schema_ok = verificar_e_corrigir_schema_bruteforce()
        if not schema_ok:
            return {"sucesso": False, "mensagem": "⚠️ Schema da tabela bruteforce_ataques precisa ser atualizado. Consulte o administrador."}
        
        # Buscar ataque em andamento
        resultado = supabase_client.client.table('bruteforce_ataques').select('*').eq('atacante_uid', atacante_uid).eq('alvo_ip', alvo_ip).eq('status', 'executando').execute()
        
        if not resultado.data:
            return {"sucesso": False, "mensagem": "Nenhum ataque em andamento"}
        
        ataque = resultado.data[0]
        fim_estimado = parse_iso_datetime(ataque['fim_estimado'])
        
        # Verificar se o tempo acabou
        if datetime.now(timezone.utc) >= fim_estimado:
            # Finalizar ataque
            sucesso = random.random() < ataque['chance_sucesso']
            
            if sucesso:
                # Buscar dados atuais do alvo para acesso ao banco
                alvo = buscar_jogador_por_ip(alvo_ip)
                
                # Log para debug
                print(f"🔍 [DEBUG] Bruteforce concluído com sucesso:")
                print(f"  - Atacante UID: {atacante_uid}")
                print(f"  - Alvo IP: {alvo_ip}")
                print(f"  - Dados do alvo encontrado: {alvo}")
                
                if not alvo:
                    # Fallback: usar dados da conexão ativa se não encontrar jogador
                    conexao = supabase_client.client.table('conexoes_ativas').select('*').eq('atacante_uid', atacante_uid).eq('alvo_ip', alvo_ip).execute()
                    if conexao.data:
                        conn_data = conexao.data[0]
                        alvo = {
                            'uid': f"target_{alvo_ip}",  # UID fictício para o alvo
                            'nick': conn_data.get('alvo_nick', 'Alvo Desconhecido'),
                            'ip': alvo_ip,
                            'dinheiro': conn_data.get('alvo_dinheiro', 1000)
                        }
                        print(f"🔄 [DEBUG] Usando dados da conexão ativa: {alvo}")
                
                # Marcar como bem-sucedido na tabela bruteforce_ataques
                supabase_client.client.table('bruteforce_ataques').update({
                    'status': 'concluido_sucesso',
                    'sucesso_confirmado': True,
                    'alvo_dados': alvo,
                    'finalizado_em': datetime.now(timezone.utc).isoformat()
                }).eq('id', ataque['id']).execute()

                # IMPORTANTE: Liberar acesso ao banco na tabela conexoes_ativas
                supabase_client.client.table('conexoes_ativas').update({
                    'atacante_acess': True
                }).eq('atacante_uid', atacante_uid).eq('alvo_ip', alvo_ip).execute()

                print(f"✅ [DEBUG] Bruteforce bem-sucedido! atacante_acess=TRUE na conexao_ativa, alvo_dados: {alvo}")

                return {
                    "sucesso": True,
                    "finalizado": True,
                    "ataque_sucesso": True,
                    "mensagem": "🎉 Bruteforce concluído com sucesso! Acesso ao banco liberado.",
                    "pode_acessar_banco": True,
                    "alvo_dados": alvo
                }
            else:
                # Marcar como falha
                supabase_client.client.table('bruteforce_ataques').update({
                    'status': 'falha',
                    'sucesso_confirmado': False,
                    'finalizado_em': datetime.now(timezone.utc).isoformat()
                }).eq('id', ataque['id']).execute()

                # Garantir que não há acesso ao banco na conexão ativa
                supabase_client.client.table('conexoes_ativas').update({
                    'atacante_acess': False
                }).eq('atacante_uid', atacante_uid).eq('alvo_ip', alvo_ip).execute()

                return {
                    "sucesso": True,
                    "finalizado": True,
                    "ataque_sucesso": False,
                    "mensagem": "❌ Bruteforce falhou! BankGuard bloqueou o ataque",
                    "pode_tentar_novamente": True
                }
        else:
            # Ainda em andamento
            tempo_restante = int((fim_estimado - datetime.now(timezone.utc)).total_seconds())
            return {
                "sucesso": True,
                "finalizado": False,
                "tempo_restante": tempo_restante,
                "mensagem": f"⏳ Bruteforce em andamento... {tempo_restante // 60}m {tempo_restante % 60}s restantes"
            }
            
    except Exception as e:
        print(f"[BRUTEFORCE CHECK ERROR] {str(e)}")
        return {"sucesso": False, "mensagem": f"Erro ao verificar bruteforce: {str(e)}"}
        print(f"[BRUTEFORCE ERROR] {str(e)}")
        return {"sucesso": False, "mensagem": f"Erro interno: {str(e)}"}

def limpar_conexoes_expiradas() -> int:
    """
    Remove conexões expiradas da tabela e também limpa os acessos bancários associados
    """
    try:
        if not supabase_client.is_connected():
            return 0
        
        # Primeiro, buscar conexões que estão expirando para limpar seus bruteforces
        agora = datetime.now(timezone.utc)
        conexoes_expirando = supabase_client.client.table('conexoes_ativas').select(
            'atacante_uid, alvo_ip'
        ).lt('expires_at', agora.isoformat()).execute()
        
        # Limpar ataques de bruteforce associados às conexões expiradas
        if conexoes_expirando.data:
            print(f"[CONEXOES] Limpando acessos bancários de {len(conexoes_expirando.data)} conexões expiradas")
            
            for conexao in conexoes_expirando.data:
                # Remover ou marcar como expirado o acesso bancário
                supabase_client.client.table('bruteforce_ataques').update({
                    'status': 'expirado',
                    'sucesso_confirmado': False,
                    'alvo_dados': None,
                    'finalizado_em': agora.isoformat()
                }).eq('atacante_uid', conexao['atacante_uid']).eq('alvo_ip', conexao['alvo_ip']).in_(
                    'status', ['concluido_sucesso', 'executando']
                ).execute()

                print(f"[BRUTEFORCE] Acesso bancário removido para {conexao['atacante_uid']} -> {conexao['alvo_ip']}")
        
        # Usar a função SQL para limpeza automática das conexões
        resultado = supabase_client.client.rpc('limpar_conexoes_expiradas').execute()
        count = resultado.data if resultado.data else 0
        
        if count > 0:
            print(f"[CONEXOES] Removidas {count} conexões expiradas")
        
        return count
        
    except Exception as e:
        print(f"[CONEXOES ERROR] Erro ao limpar expiradas: {e}")
        return 0

def verificar_e_corrigir_schema_bruteforce():
    """
    Verifica e corrige o schema da tabela bruteforce_ataques se necessário
    """
    try:
        if not supabase_client.is_connected():
            print("[SCHEMA] Database não disponível")
            return False
        
        # Primeiro, tentar uma consulta simples para ver se as colunas existem
        try:
            result = supabase_client.client.table('bruteforce_ataques').select('id, alvo_dados, sucesso_confirmado').limit(1).execute()
            print("[SCHEMA] ✅ Colunas alvo_dados e sucesso_confirmado já existem na bruteforce_ataques")

            # Verificar se atacante_acess existe na conexoes_ativas
            result2 = supabase_client.client.table('conexoes_ativas').select('id, atacante_acess').limit(1).execute()
            print("[SCHEMA] ✅ Coluna atacante_acess existe na conexoes_ativas")
            return True
        except Exception as e:
            if "PGRST204" in str(e) or "alvo_dados" in str(e) or "sucesso_confirmado" in str(e):
                print("[SCHEMA] ⚠️ Colunas não encontradas na bruteforce_ataques:")
                print("[SCHEMA] ALTER TABLE bruteforce_ataques ADD COLUMN IF NOT EXISTS alvo_dados JSONB;")
                print("[SCHEMA] ALTER TABLE bruteforce_ataques ADD COLUMN IF NOT EXISTS sucesso_confirmado BOOLEAN DEFAULT FALSE;")
                return False
            elif "atacante_acess" in str(e):
                print("[SCHEMA] ⚠️ Coluna atacante_acess não encontrada na conexoes_ativas:")
                print("[SCHEMA] ALTER TABLE conexoes_ativas ADD COLUMN IF NOT EXISTS atacante_acess BOOLEAN DEFAULT FALSE;")
                return False
            else:
                print(f"[SCHEMA] ❌ Erro não relacionado a colunas: {str(e)}")
                return False
        
    except Exception as e:
        print(f"[SCHEMA] ❌ Erro geral: {str(e)}")
        return False

def obter_conexoes_ativas(atacante_uid: str) -> List[Dict[str, Any]]:
    """
    Obtém todas as conexões ativas de um jogador com informações detalhadas
    Inclui contador de conexões e limite
    """
    try:
        if not supabase_client.is_connected():
            return []
        
        # Limpar expiradas primeiro
        limpar_conexoes_expiradas()
        
        # Buscar conexões ativas do jogador
        resultado = supabase_client.client.table('conexoes_ativas').select('*').eq('atacante_uid', atacante_uid).eq('status', 'ativa').execute()
        
        conexoes_validas = []
        agora = datetime.now(timezone.utc)
        
        for conexao in resultado.data if resultado.data else []:
            expires_at = parse_iso_datetime(conexao['expires_at'])
            
            # Só incluir conexões que ainda não expiraram
            if agora < expires_at:
                # Adicionar tempo restante em minutos
                tempo_restante_seg = int((expires_at - agora).total_seconds())
                tempo_restante_min = tempo_restante_seg // 60
                
                conexao_info = conexao.copy()
                conexao_info['tempo_restante_segundos'] = tempo_restante_seg
                conexao_info['tempo_restante_minutos'] = tempo_restante_min
                conexao_info['expires_in_format'] = f"{tempo_restante_min}min"
                
                conexoes_validas.append(conexao_info)
        
        return conexoes_validas
        
    except Exception as e:
        print(f"[CONEXOES ERROR] Erro ao obter conexões: {e}")
        return []

def obter_status_conexoes_jogador(atacante_uid: str) -> Dict[str, Any]:
    """
    Retorna status completo das conexões de um jogador incluindo limite
    """
    try:
        conexoes_ativas = obter_conexoes_ativas(atacante_uid)

        # Buscar dados do jogador para verificar NFT Firstsupp
        jogador = get_jogador(atacante_uid)
        limite_conexoes = 5

        if jogador:
            # Aplicar efeito da NFT Firstsupp (+2 slots de conexão)
            limite_conexoes = aplicar_efeito_nft_firstsupp_slots(limite_conexoes, jogador)

        conexoes_disponiveis = limite_conexoes - len(conexoes_ativas)

        return {
            "sucesso": True,
            "conexoes_ativas": conexoes_ativas,
            "total_conexoes": len(conexoes_ativas),
            "limite_conexoes": limite_conexoes,
            "conexoes_disponiveis": conexoes_disponiveis,
            "no_limite": len(conexoes_ativas) >= limite_conexoes,
            "resumo": f"{len(conexoes_ativas)}/{limite_conexoes} conexões ativas"
        }
        
    except Exception as e:
        print(f"[STATUS CONEXOES] Erro: {e}")
        return {
            "sucesso": False,
            "mensagem": f"Erro ao obter status: {str(e)}",
            "conexoes_ativas": [],
            "total_conexoes": 0,
            "limite_conexoes": 5,
            "conexoes_disponiveis": 5,
            "no_limite": False
        }

def remover_conexao_ativa(atacante_uid: str, alvo_ip: str) -> bool:
    """
    Remove uma conexão específica, limpa o acesso bancário e encerra bruteforces ativos
    Isso resolve o problema de constraint única ao permitir novos bruteforces após bloqueio
    """
    try:
        if not supabase_client.is_connected():
            return False

        # Primeiro, verificar se a conexão tem acesso bancário para log
        conexao_info = supabase_client.client.table('conexoes_ativas').select('atacante_acess').eq('atacante_uid', atacante_uid).eq('alvo_ip', alvo_ip).execute()
        tinha_acesso = conexao_info.data and conexao_info.data[0].get('atacante_acess', False)

        # Encerrar todos os bruteforces ativos para esta conexão
        bruteforce_result = supabase_client.client.table('bruteforce_ataques').update({
            'status': 'conexao_encerrada',
            'finalizado_em': datetime.now(timezone.utc).isoformat(),
            'sucesso_confirmado': False,
            'alvo_dados': None
        }).eq('atacante_uid', atacante_uid).eq('alvo_ip', alvo_ip).in_('status', ['executando', 'concluido_sucesso']).execute()

        bruteforces_encerrados = len(bruteforce_result.data) if bruteforce_result.data else 0

        # Remover a conexão (isso automaticamente remove o acesso bancário)
        resultado = supabase_client.client.table('conexoes_ativas').delete().eq('atacante_uid', atacante_uid).eq('alvo_ip', alvo_ip).execute()

        if resultado.data:
            log_msg = f"[CONEXAO] Conexão removida para {atacante_uid} -> {alvo_ip}"
            if tinha_acesso:
                log_msg += " (acesso bancário revogado)"
            if bruteforces_encerrados > 0:
                log_msg += f" ({bruteforces_encerrados} bruteforce{'s' if bruteforces_encerrados > 1 else ''} encerrado{'s' if bruteforces_encerrados > 1 else ''})"
            print(log_msg)

        return bool(resultado.data)

    except Exception as e:
        print(f"[CONEXOES ERROR] Erro ao remover conexão: {e}")
        return False

def fechar_conexao_especifica(atacante_uid: str, alvo_ip: str) -> Dict[str, Any]:
    """
    Fecha uma conexão específica com feedback detalhado
    """
    try:
        if not supabase_client.is_connected():
            return {"sucesso": False, "mensagem": "Database não disponível"}
        
        # Verificar se a conexão existe
        conexao_existe = supabase_client.client.table('conexoes_ativas').select('*').eq('atacante_uid', atacante_uid).eq('alvo_ip', alvo_ip).eq('status', 'ativa').execute()
        
        if not conexao_existe.data:
            return {"sucesso": False, "mensagem": f"Nenhuma conexão ativa encontrada com IP {alvo_ip}"}
        
        conexao = conexao_existe.data[0]
        alvo_nick = conexao.get('alvo_nick', 'Desconhecido')
        
        # Verificar se tinha acesso bancário antes de remover
        tinha_acesso = conexao.get('atacante_acess', False)

        # Remover a conexão (isso automaticamente remove o acesso bancário)
        sucesso = remover_conexao_ativa(atacante_uid, alvo_ip)

        if sucesso:
            # Também limpar qualquer bruteforce associado
            supabase_client.client.table('bruteforce_ataques').update({
                'status': 'encerrado',
                'finalizado_em': datetime.now(timezone.utc).isoformat()
            }).eq('atacante_uid', atacante_uid).eq('alvo_ip', alvo_ip).eq('status', 'executando').execute()

            # Obter status atualizado
            status_conexoes = obter_status_conexoes_jogador(atacante_uid)

            mensagem_base = f"✅ Conexão com {alvo_nick} ({alvo_ip}) encerrada com sucesso!"
            if tinha_acesso:
                mensagem_base += " Acesso bancário revogado."

            return {
                "sucesso": True,
                "mensagem": mensagem_base,
                "status_conexoes": status_conexoes
            }
        else:
            return {"sucesso": False, "mensagem": f"Erro ao encerrar conexão com {alvo_nick} ({alvo_ip})"}
        
    except Exception as e:
        print(f"[FECHAR CONEXAO] Erro: {e}")
        return {"sucesso": False, "mensagem": f"Erro interno: {str(e)}"}

def fechar_todas_conexoes(atacante_uid: str) -> Dict[str, Any]:
    """
    Fecha todas as conexões ativas de um jogador
    """
    try:
        if not supabase_client.is_connected():
            return {"sucesso": False, "mensagem": "Database não disponível"}
        
        # Buscar todas as conexões ativas
        conexoes = supabase_client.client.table('conexoes_ativas').select('*').eq('atacante_uid', atacante_uid).eq('status', 'ativa').execute()
        
        if not conexoes.data:
            return {"sucesso": False, "mensagem": "Nenhuma conexão ativa encontrada"}
        
        conexoes_fechadas = []
        acessos_revogados = 0

        # Fechar cada conexão
        for conexao in conexoes.data:
            alvo_ip = conexao['alvo_ip']
            alvo_nick = conexao.get('alvo_nick', 'Desconhecido')
            tinha_acesso = conexao.get('atacante_acess', False)

            if remover_conexao_ativa(atacante_uid, alvo_ip):
                conexoes_fechadas.append(f"{alvo_nick} ({alvo_ip})")
                if tinha_acesso:
                    acessos_revogados += 1
        
        # Limpar todos os bruteforces em andamento
        supabase_client.client.table('bruteforce_ataques').update({
            'status': 'encerrado',
            'finalizado_em': datetime.now(timezone.utc).isoformat()
        }).eq('atacante_uid', atacante_uid).eq('status', 'executando').execute()
        
        # Obter status atualizado
        status_conexoes = obter_status_conexoes_jogador(atacante_uid)
        
        if conexoes_fechadas:
            mensagem_base = f"✅ {len(conexoes_fechadas)} conexões encerradas: {', '.join(conexoes_fechadas[:3])}{'...' if len(conexoes_fechadas) > 3 else ''}"
            if acessos_revogados > 0:
                mensagem_base += f" ({acessos_revogados} acesso{'s' if acessos_revogados > 1 else ''} bancário{'s' if acessos_revogados > 1 else ''} revogado{'s' if acessos_revogados > 1 else ''})"

            return {
                "sucesso": True,
                "mensagem": mensagem_base,
                "conexoes_fechadas": conexoes_fechadas,
                "total_fechadas": len(conexoes_fechadas),
                "acessos_revogados": acessos_revogados,
                "status_conexoes": status_conexoes
            }
        else:
            return {"sucesso": False, "mensagem": "Erro ao fechar conexões"}
        
    except Exception as e:
        print(f"[FECHAR TODAS CONEXOES] Erro: {e}")
        return {"sucesso": False, "mensagem": f"Erro interno: {str(e)}"}

# --- FUNÇÕES DE SEGURANÇA ---

def obter_invasoes_detectadas(alvo_uid: str) -> Dict[str, Any]:
    """
    Detecta invasões ativas contra um jogador específico
    Retorna conexões de outros jogadores direcionadas ao jogador atual
    """
    try:
        print(f"[SECURITY APP DEBUG] 🛡️ Iniciando detecção de invasões para alvo: {alvo_uid}")

        if not supabase_client.is_connected():
            return {"sucesso": False, "mensagem": "Database não disponível"}

        # Buscar dados do jogador alvo para obter seu IP
        alvo = get_jogador(alvo_uid)
        print(f"[SECURITY APP DEBUG] 🎯 Dados do alvo: {alvo}")

        if not alvo:
            return {"sucesso": False, "mensagem": "Jogador não encontrado"}

        alvo_ip = alvo.get('ip')
        if not alvo_ip:
            return {"sucesso": False, "mensagem": "IP do jogador não encontrado"}

        print(f"[SECURITY APP DEBUG] 🌐 IP do alvo: {alvo_ip}")
        print(f"[SECURITY APP DEBUG] 🔥 Firewall do alvo: {alvo.get('firewall', 0)}")

        # Buscar conexões ativas de outros jogadores direcionadas a este IP
        agora = datetime.now(timezone.utc)
        invasoes_result = supabase_client.client.table('conexoes_ativas').select(
            'atacante_uid, alvo_ip, alvo_nick, status, expires_at, created_at'
        ).eq('alvo_ip', alvo_ip).eq('status', 'ativa').execute()

        invasoes_ativas = []

        if invasoes_result.data:
            for invasao in invasoes_result.data:
                # Verificar se não expirou
                try:
                    expires_at = parse_iso_datetime(invasao['expires_at'])
                    if agora < expires_at:
                        # Buscar dados do atacante
                        atacante = get_jogador(invasao['atacante_uid'])
                        print(f"[SECURITY APP DEBUG] Atacante UID: {invasao['atacante_uid']}")
                        print(f"[SECURITY APP DEBUG] Dados do atacante: {atacante}")

                        # Verificar ProxyVPN vs Firewall para anonimização
                        if atacante:
                            atacante_proxy_vpn = atacante.get('proxyvpn', 0)
                            alvo_firewall = alvo.get('firewall', 0)

                            print(f"[SECURITY APP DEBUG] ProxyVPN do atacante: {atacante_proxy_vpn}")
                            print(f"[SECURITY APP DEBUG] Firewall do alvo: {alvo_firewall}")
                            print(f"[SECURITY APP DEBUG] Comparação: {atacante_proxy_vpn} >= {alvo_firewall} = {atacante_proxy_vpn >= alvo_firewall}")

                            # Se ProxyVPN >= Firewall, atacante fica anônimo
                            if atacante_proxy_vpn >= alvo_firewall:
                                atacante_nick = "unknown"
                                atacante_ip = "unknown"
                                is_anonimo = True
                                print(f"[SECURITY APP] ✅ Invasor anônimo: ProxyVPN {atacante_proxy_vpn} >= Firewall {alvo_firewall}")
                            else:
                                atacante_nick = atacante.get('nick', 'Desconhecido')
                                atacante_ip = atacante.get('ip', 'IP desconhecido')
                                is_anonimo = False
                                print(f"[SECURITY APP] ❌ Invasor identificado: ProxyVPN {atacante_proxy_vpn} < Firewall {alvo_firewall}")
                        else:
                            print(f"[SECURITY APP DEBUG] ❌ Atacante não encontrado para UID: {invasao['atacante_uid']}")
                            atacante_nick = 'Desconhecido'
                            atacante_ip = 'IP desconhecido'
                            is_anonimo = False

                        # Verificar se há bruteforce ativo
                        bruteforce_status = verificar_status_bruteforce_conexao(invasao['atacante_uid'], alvo_ip)

                        tempo_restante = int((expires_at - agora).total_seconds())

                        invasoes_ativas.append({
                            'atacante_uid': invasao['atacante_uid'],
                            'atacante_nick': atacante_nick,
                            'atacante_ip': atacante_ip,
                            'alvo_ip': alvo_ip,
                            'tempo_restante': tempo_restante,
                            'expires_at': invasao['expires_at'],
                            'created_at': invasao['created_at'],
                            'bruteforce_ativo': bruteforce_status.get('status') == 'executando',
                            'acesso_banco': bruteforce_status.get('acesso_banco_liberado', False),
                            'nivel_ameaca': 'CRÍTICO' if bruteforce_status.get('acesso_banco_liberado') else 'ALTO' if bruteforce_status.get('status') == 'executando' else 'MÉDIO',
                            'is_anonimo': is_anonimo
                        })
                except Exception as e:
                    print(f"Erro ao processar invasão: {e}")
                    continue

        return {
            "sucesso": True,
            "invasoes": invasoes_ativas,
            "total_invasoes": len(invasoes_ativas),
            "alvo_ip": alvo_ip,
            "alvo_nick": alvo.get('nick', 'Desconhecido')
        }

    except Exception as e:
        print(f"[INVASOES DETECTADAS] Erro: {e}")
        return {"sucesso": False, "mensagem": f"Erro interno: {str(e)}"}

def bloquear_invasao(alvo_uid: str, atacante_uid: str) -> Dict[str, Any]:
    """
    Bloqueia uma invasão específica fechando a conexão do atacante e encerrando bruteforces
    """
    try:
        if not supabase_client.is_connected():
            return {"sucesso": False, "mensagem": "Database não disponível"}

        # Buscar dados do alvo para obter seu IP
        alvo = get_jogador(alvo_uid)
        if not alvo:
            return {"sucesso": False, "mensagem": "Jogador alvo não encontrado"}

        alvo_ip = alvo.get('ip')

        # 1. Encerrar todos os bruteforces ativos do atacante contra este alvo
        bruteforce_result = supabase_client.client.table('bruteforce_ataques').update({
            'status': 'bloqueado_defesa',
            'finalizado_em': datetime.now(timezone.utc).isoformat(),
            'sucesso_confirmado': False,
            'alvo_dados': None
        }).eq('atacante_uid', atacante_uid).eq('alvo_ip', alvo_ip).in_('status', ['executando', 'concluido_sucesso']).execute()

        bruteforces_encerrados = len(bruteforce_result.data) if bruteforce_result.data else 0

        # 2. Fechar conexão específica
        resultado = remover_conexao_ativa(atacante_uid, alvo_ip)

        if resultado or bruteforces_encerrados > 0:
            # Buscar nick do atacante para log
            atacante = get_jogador(atacante_uid)
            atacante_nick = atacante.get('nick', 'Desconhecido') if atacante else 'Desconhecido'

            # Log da ação de defesa
            supabase_client.log_atividade(
                alvo_uid,
                'bloqueio_invasao',
                {
                    'atacante_uid': atacante_uid,
                    'atacante_nick': atacante_nick,
                    'alvo_ip': alvo_ip,
                    'acao': 'conexao_bloqueada',
                    'bruteforces_encerrados': bruteforces_encerrados
                }
            )

            mensagem = f"Invasão de {atacante_nick} bloqueada com sucesso!"
            if bruteforces_encerrados > 0:
                mensagem += f" ({bruteforces_encerrados} bruteforce{'s' if bruteforces_encerrados > 1 else ''} encerrado{'s' if bruteforces_encerrados > 1 else ''})"

            return {
                "sucesso": True,
                "mensagem": mensagem,
                "atacante_bloqueado": atacante_nick,
                "bruteforces_encerrados": bruteforces_encerrados
            }
        else:
            return {"sucesso": False, "mensagem": "Conexão não encontrada ou já expirada"}

    except Exception as e:
        print(f"[BLOQUEAR INVASAO] Erro: {e}")
        return {"sucesso": False, "mensagem": f"Erro interno: {str(e)}"}

def bloquear_todas_invasoes(alvo_uid: str) -> Dict[str, Any]:
    """
    Bloqueia todas as invasões ativas contra um jogador e encerra bruteforces
    """
    try:
        if not supabase_client.is_connected():
            return {"sucesso": False, "mensagem": "Database não disponível"}

        # Obter invasões ativas
        invasoes_data = obter_invasoes_detectadas(alvo_uid)

        if not invasoes_data.get('sucesso'):
            return invasoes_data

        invasoes = invasoes_data.get('invasoes', [])

        if not invasoes:
            return {"sucesso": False, "mensagem": "Nenhuma invasão ativa detectada"}

        bloqueadas = []
        erros = 0
        total_bruteforces_encerrados = 0

        for invasao in invasoes:
            resultado = bloquear_invasao(alvo_uid, invasao['atacante_uid'])
            if resultado.get('sucesso'):
                bloqueadas.append(invasao['atacante_nick'])
                total_bruteforces_encerrados += resultado.get('bruteforces_encerrados', 0)
            else:
                erros += 1

        if bloqueadas:
            # Log da ação de defesa em massa
            supabase_client.log_atividade(
                alvo_uid,
                'bloqueio_invasoes_massa',
                {
                    'invasoes_bloqueadas': len(bloqueadas),
                    'atacantes_bloqueados': bloqueadas,
                    'bruteforces_encerrados': total_bruteforces_encerrados,
                    'erros': erros
                }
            )

            mensagem = f"✅ {len(bloqueadas)} invasões bloqueadas: {', '.join(bloqueadas[:3])}{'...' if len(bloqueadas) > 3 else ''}"
            if total_bruteforces_encerrados > 0:
                mensagem += f" ({total_bruteforces_encerrados} bruteforce{'s' if total_bruteforces_encerrados > 1 else ''} encerrado{'s' if total_bruteforces_encerrados > 1 else ''})"
            if erros > 0:
                mensagem += f" ({erros} erro{'s' if erros > 1 else ''})"

            return {
                "sucesso": True,
                "mensagem": mensagem,
                "invasoes_bloqueadas": bloqueadas,
                "total_bloqueadas": len(bloqueadas),
                "bruteforces_encerrados": total_bruteforces_encerrados,
                "erros": erros
            }
        else:
            return {"sucesso": False, "mensagem": "Erro ao bloquear invasões"}

    except Exception as e:
        print(f"[BLOQUEAR TODAS INVASOES] Erro: {e}")
        return {"sucesso": False, "mensagem": f"Erro interno: {str(e)}"}

def obter_status_seguranca(uid: str) -> Dict[str, Any]:
    """
    Retorna status geral de segurança do jogador
    """
    try:
        if not supabase_client.is_connected():
            return {"sucesso": False, "mensagem": "Database não disponível"}

        # Obter dados do jogador
        jogador = get_jogador(uid)
        if not jogador:
            return {"sucesso": False, "mensagem": "Jogador não encontrado"}

        # Obter invasões detectadas
        invasoes_data = obter_invasoes_detectadas(uid)
        total_invasoes = invasoes_data.get('total_invasoes', 0) if invasoes_data.get('sucesso') else 0

        # Obter conexões próprias
        status_conexoes = obter_status_conexoes_jogador(uid)
        conexoes_ativas = status_conexoes.get('total_conexoes', 0) if status_conexoes.get('sucesso') else 0

        # Calcular nível de segurança baseado nos apps de defesa
        firewall_nivel = jogador.get('firewall', 1)
        antivirus_nivel = jogador.get('antivirus', 1)
        bankguard_nivel = jogador.get('bankguard', 1)

        # Calcular score de segurança (0-100)
        score_defesa = min(100, (firewall_nivel + antivirus_nivel + bankguard_nivel) * 10)

        # Determinar nível de ameaça
        if total_invasoes == 0:
            nivel_ameaca = "SEGURO"
            cor_ameaca = "green"
        elif total_invasoes <= 2:
            nivel_ameaca = "BAIXO"
            cor_ameaca = "yellow"
        elif total_invasoes <= 5:
            nivel_ameaca = "MÉDIO"
            cor_ameaca = "orange"
        else:
            nivel_ameaca = "ALTO"
            cor_ameaca = "red"

        return {
            "sucesso": True,
            "status": {
                "invasoes_detectadas": total_invasoes,
                "conexoes_ativas": conexoes_ativas,
                "score_defesa": score_defesa,
                "nivel_ameaca": nivel_ameaca,
                "cor_ameaca": cor_ameaca,
                "firewall_nivel": firewall_nivel,
                "antivirus_nivel": antivirus_nivel,
                "bankguard_nivel": bankguard_nivel,
                "ip_jogador": jogador.get('ip', 'Desconhecido')
            }
        }

    except Exception as e:
        print(f"[STATUS SEGURANCA] Erro: {e}")
        return {"sucesso": False, "mensagem": f"Erro interno: {str(e)}"}

def limpar_bruteforces_orfaos(atacante_uid: str) -> int:
    """
    Limpa bruteforces órfãos (sem conexão ativa correspondente)
    Útil para resolver problemas de constraint única
    """
    try:
        if not supabase_client.is_connected():
            return 0

        # Buscar bruteforces ativos do atacante
        bruteforces_ativos = supabase_client.client.table('bruteforce_ataques').select(
            'id, alvo_ip'
        ).eq('atacante_uid', atacante_uid).in_('status', ['executando', 'concluido_sucesso']).execute()

        if not bruteforces_ativos.data:
            return 0

        bruteforces_orfaos = []

        # Verificar se cada bruteforce tem conexão ativa correspondente
        for bruteforce in bruteforces_ativos.data:
            alvo_ip = bruteforce['alvo_ip']

            # Verificar se existe conexão ativa
            conexao_ativa = supabase_client.client.table('conexoes_ativas').select('id').eq(
                'atacante_uid', atacante_uid
            ).eq('alvo_ip', alvo_ip).eq('status', 'ativa').execute()

            # Se não há conexão ativa, é órfão
            if not conexao_ativa.data:
                bruteforces_orfaos.append(bruteforce['id'])

        # Limpar bruteforces órfãos
        if bruteforces_orfaos:
            supabase_client.client.table('bruteforce_ataques').update({
                'status': 'orfao_limpo',
                'finalizado_em': datetime.now(timezone.utc).isoformat(),
                'sucesso_confirmado': False,
                'alvo_dados': None
            }).in_('id', bruteforces_orfaos).execute()

            print(f"[LIMPEZA] {len(bruteforces_orfaos)} bruteforces órfãos limpos para {atacante_uid}")

        return len(bruteforces_orfaos)

    except Exception as e:
        print(f"[LIMPEZA ORFAOS] Erro: {e}")
        return 0

# --- SISTEMA DE DEFACES ATIVOS ---

def registrar_deface_ativo(alvo_uid: str, grupo_nome: str, duracao_minutos: int = 60) -> Dict[str, Any]:
    """
    Registra um deface ativo na tabela defaces_ativos
    """
    try:
        if not supabase_client.is_connected():
            return {"sucesso": False, "mensagem": "Database não disponível"}

        agora = datetime.now(timezone.utc)
        expira_em = agora + timedelta(minutes=duracao_minutos)

        # Remover deface anterior se existir
        supabase_client.client.table('defaces_ativos').delete().eq('alvo_uid', alvo_uid).execute()

        # Inserir novo deface
        resultado = supabase_client.client.table('defaces_ativos').insert({
            'alvo_uid': alvo_uid,
            'grupo_nome': grupo_nome,
            'criado_em': agora.isoformat(),
            'expira_em': expira_em.isoformat(),
            'ativo': True
        }).execute()

        if resultado.data:
            print(f"[DEFACE] Deface ativo registrado: {grupo_nome} → {alvo_uid} (expira em {duracao_minutos}min)")
            return {
                "sucesso": True,
                "mensagem": f"Deface de {grupo_nome} registrado",
                "expira_em": expira_em.isoformat()
            }
        else:
            return {"sucesso": False, "mensagem": "Erro ao registrar deface"}

    except Exception as e:
        print(f"[DEFACE ATIVO] Erro: {e}")
        return {"sucesso": False, "mensagem": f"Erro interno: {str(e)}"}

def obter_deface_ativo(alvo_uid: str) -> Optional[Dict[str, Any]]:
    """
    Obtém o deface ativo para um jogador específico
    """
    try:
        if not supabase_client.is_connected():
            return None

        agora = datetime.now(timezone.utc)

        # Buscar deface ativo não expirado
        resultado = supabase_client.client.table('defaces_ativos').select('*').eq(
            'alvo_uid', alvo_uid
        ).eq('ativo', True).gt('expira_em', agora.isoformat()).execute()

        if resultado.data:
            deface = resultado.data[0]
            expira_em = parse_iso_datetime(deface['expira_em'])
            tempo_restante = int((expira_em - agora).total_seconds())

            if tempo_restante > 0:
                return {
                    'grupo_nome': deface['grupo_nome'],
                    'criado_em': deface['criado_em'],
                    'expira_em': deface['expira_em'],
                    'tempo_restante': tempo_restante
                }

        return None

    except Exception as e:
        print(f"[OBTER DEFACE] Erro: {e}")
        return None

def limpar_defaces_expirados() -> int:
    """
    Remove defaces expirados da tabela
    """
    try:
        if not supabase_client.is_connected():
            return 0

        agora = datetime.now(timezone.utc)

        # Marcar como inativos os defaces expirados
        resultado = supabase_client.client.table('defaces_ativos').update({
            'ativo': False
        }).lt('expira_em', agora.isoformat()).eq('ativo', True).execute()

        removidos = len(resultado.data) if resultado.data else 0

        if removidos > 0:
            print(f"[DEFACE CLEANUP] {removidos} defaces expirados removidos")

        return removidos

    except Exception as e:
        print(f"[DEFACE CLEANUP] Erro: {e}")
        return 0

def obter_logs_atividade_melhorados(uid: str, limite: int = 50) -> Dict[str, Any]:
    """
    Obtém logs de invasões sofridas pelo jogador (quando foi exploitado por outros)

    IMPORTANTE: Registra apenas quando o jogador é VÍTIMA de exploits:
    - Transferências bancárias roubadas por outros
    - Defaces aplicados por outros
    - Deface ativo no topo (se houver)

    NÃO registra quando o jogador explora outros.
    """
    try:
        if not supabase_client.is_connected():
            return {"sucesso": False, "mensagem": "Database não disponível"}

        # Limpar defaces expirados primeiro
        limpar_defaces_expirados()

        # Obter deface ativo
        deface_ativo = obter_deface_ativo(uid)

        # Obter apenas logs de security_alert (invasões, transferências, defaces)
        logs_result = supabase_client.client.table('activity_logs').select('*').eq(
            'user_uid', uid
        ).eq('action', 'security_alert').order('created_at', desc=True).limit(limite).execute()

        logs = []

        # Adicionar deface ativo no topo se existir
        if deface_ativo:
            tempo_restante_min = deface_ativo['tempo_restante'] // 60
            tempo_restante_seg = deface_ativo['tempo_restante'] % 60

            logs.append({
                'id': 'deface_ativo',
                'tipo': 'deface_ativo',
                'mensagem': f"DEFACED BY {deface_ativo['grupo_nome']}",
                'created_at': deface_ativo['criado_em'],
                'tempo_restante': f"{tempo_restante_min}m {tempo_restante_seg}s",
                'is_deface_ativo': True,
                'grupo_nome': deface_ativo['grupo_nome']
            })

        # Adicionar logs de security alert (invasões)
        if logs_result.data:
            for log in logs_result.data:
                details = log.get('details', {})

                if isinstance(details, dict):
                    # Usar mensagem do details se disponível
                    mensagem = details.get('mensagem', '')

                    # Se não tiver mensagem, construir baseado no tipo
                    if not mensagem:
                        if details.get('tipo') == 'exploit_transferencia':
                            atacante_ip = details.get('atacante_ip', 'IP desconhecido')
                            quantia = details.get('quantia_roubada', 0)
                            mensagem = f"Security alert! transferencia ${quantia} por {atacante_ip}"
                        elif details.get('tipo') == 'exploit_deface':
                            atacante_ip = details.get('atacante_ip', 'IP desconhecido')
                            grupo_nome = details.get('grupo_nome', 'Grupo Desconhecido')
                            mensagem = f"Security alert! deface aplicado por {atacante_ip} ({grupo_nome})"
                        else:
                            mensagem = "Security alert! Atividade suspeita detectada"

                    logs.append({
                        'id': log.get('id'),
                        'tipo': 'security_alert',
                        'mensagem': mensagem,
                        'created_at': log.get('created_at'),
                        'dados': details,
                        'atacante_ip': details.get('atacante_ip'),
                        'quantia_roubada': details.get('quantia_roubada'),
                        'grupo_nome': details.get('grupo_nome'),
                        'is_deface_ativo': False
                    })

        return {
            "sucesso": True,
            "logs": logs,
            "total": len(logs),
            "deface_ativo": deface_ativo is not None
        }

    except Exception as e:
        print(f"[LOGS MELHORADOS] Erro: {e}")
        return {"sucesso": False, "mensagem": f"Erro interno: {str(e)}"}

def limpar_logs_usuario(uid: str) -> Dict[str, Any]:
    """
    Limpa todos os logs de security_alert do usuário
    """
    try:
        if not supabase_client.is_connected():
            return {"sucesso": False, "mensagem": "Database não disponível"}

        # Deletar todos os logs de security_alert do usuário
        result = supabase_client.client.table('activity_logs').delete().eq(
            'user_uid', uid
        ).eq('action', 'security_alert').execute()

        logs_removidos = len(result.data) if result.data else 0

        return {
            "sucesso": True,
            "mensagem": f"Logs limpos com sucesso! {logs_removidos} registros removidos.",
            "logs_removidos": logs_removidos
        }

    except Exception as e:
        print(f"[LIMPAR LOGS] Erro: {e}")
        return {"sucesso": False, "mensagem": f"Erro ao limpar logs: {str(e)}"}

def limpar_logs_alvo(alvo_ip: str) -> Dict[str, Any]:
    """
    Limpa todos os logs de security_alert do alvo (para atacantes)
    """
    try:
        if not supabase_client.is_connected():
            return {"sucesso": False, "mensagem": "Database não disponível"}

        # Buscar o alvo pelo IP
        alvo = buscar_jogador_por_ip(alvo_ip)
        if not alvo:
            return {"sucesso": False, "mensagem": "Alvo não encontrado"}

        alvo_uid = alvo.get('uid')
        if not alvo_uid:
            return {"sucesso": False, "mensagem": "UID do alvo não encontrado"}

        # Deletar todos os logs de security_alert do alvo
        result = supabase_client.client.table('activity_logs').delete().eq(
            'user_uid', alvo_uid
        ).eq('action', 'security_alert').execute()

        logs_removidos = len(result.data) if result.data else 0

        return {
            "sucesso": True,
            "mensagem": f"Logs do alvo limpos com sucesso! {logs_removidos} registros removidos.",
            "logs_removidos": logs_removidos,
            "alvo_nick": alvo.get('nick', 'Desconhecido')
        }

    except Exception as e:
        print(f"[LIMPAR LOGS ALVO] Erro: {e}")
        return {"sucesso": False, "mensagem": f"Erro ao limpar logs do alvo: {str(e)}"}

def gerar_ip_aleatorio_unico() -> Optional[str]:
    """
    Gera um IP aleatório único simulando diferentes redes e provedores
    """
    import random

    try:
        if not supabase_client.is_connected():
            return None

        # Definir pools de IPs realistas de diferentes tipos de rede
        ip_pools = [
            # Redes residenciais comuns
            {"base": "10.0", "range_3": (0, 255), "range_4": (1, 254), "weight": 20},
            {"base": "172.16", "range_3": (0, 31), "range_4": (1, 254), "weight": 15},
            {"base": "192.168", "range_3": (0, 255), "range_4": (1, 254), "weight": 25},

            # Redes de provedores brasileiros simuladas
            {"base": "177.12", "range_3": (0, 255), "range_4": (1, 254), "weight": 8},
            {"base": "189.85", "range_3": (0, 255), "range_4": (1, 254), "weight": 8},
            {"base": "201.23", "range_3": (0, 255), "range_4": (1, 254), "weight": 6},
            {"base": "200.147", "range_3": (0, 255), "range_4": (1, 254), "weight": 5},

            # Redes corporativas/educacionais
            {"base": "143.107", "range_3": (0, 255), "range_4": (1, 254), "weight": 3},
            {"base": "150.164", "range_3": (0, 255), "range_4": (1, 254), "weight": 3},
            {"base": "164.41", "range_3": (0, 255), "range_4": (1, 254), "weight": 2},

            # Redes internacionais
            {"base": "8.8", "range_3": (8, 8), "range_4": (8, 8), "weight": 1},  # Google DNS
            {"base": "1.1", "range_3": (1, 1), "range_4": (1, 1), "weight": 1},  # Cloudflare DNS
            {"base": "208.67", "range_3": (222, 222), "range_4": (222, 222), "weight": 1},  # OpenDNS

            # Redes de datacenter/VPS
            {"base": "45.76", "range_3": (0, 255), "range_4": (1, 254), "weight": 2},
            {"base": "104.238", "range_3": (0, 255), "range_4": (1, 254), "weight": 2},
            {"base": "167.99", "range_3": (0, 255), "range_4": (1, 254), "weight": 2},
        ]

        # Criar lista ponderada baseada nos pesos
        weighted_pools = []
        for pool in ip_pools:
            weighted_pools.extend([pool] * pool["weight"])

        tentativas = 0
        max_tentativas = 100  # Aumentado para acomodar mais variedade

        while tentativas < max_tentativas:
            # Escolher pool aleatório baseado no peso
            pool = random.choice(weighted_pools)

            # Gerar terceiro e quarto octetos
            terceiro = random.randint(pool["range_3"][0], pool["range_3"][1])
            quarto = random.randint(pool["range_4"][0], pool["range_4"][1])

            ip_candidato = f"{pool['base']}.{terceiro}.{quarto}"

            # Verificar se IP já existe
            ip_existente = supabase_client.client.table('usuarios').select('id').eq('ip', ip_candidato).execute()

            if not ip_existente.data:
                print(f"[IP GENERATOR] IP único gerado: {ip_candidato} (tentativa {tentativas + 1})")
                return ip_candidato

            tentativas += 1

        print(f"[IP GENERATOR] Falha ao gerar IP único após {max_tentativas} tentativas")
        return None

    except Exception as e:
        print(f"[IP GENERATOR] Erro: {e}")
        return None

def trocar_ip_jogador(uid: str) -> Dict[str, Any]:
    """
    Troca o IP do jogador por um novo IP aleatório
    Custa 100 shacks e fecha todas as conexões ativas
    """
    try:
        if not supabase_client.is_connected():
            return {"sucesso": False, "mensagem": "Database não disponível"}

        # Buscar dados do jogador
        jogador = get_jogador(uid)
        if not jogador:
            return {"sucesso": False, "mensagem": "Jogador não encontrado"}

        # Verificar se tem shacks suficientes
        shacks_atuais = jogador.get('shack', 0)
        custo_troca = 100

        if shacks_atuais < custo_troca:
            return {
                "sucesso": False,
                "mensagem": f"❌ Shacks insuficientes! Você tem {shacks_atuais}, precisa de {custo_troca}"
            }

        # Gerar novo IP único
        novo_ip = gerar_ip_aleatorio_unico()
        if not novo_ip:
            # Fallback para geração simples se a função principal falhar
            import random
            tentativas_fallback = 0
            while tentativas_fallback < 20:
                fallback_ip = f"192.168.{random.randint(1, 255)}.{random.randint(1, 254)}"
                ip_existente = supabase_client.client.table('usuarios').select('id').eq('ip', fallback_ip).execute()
                if not ip_existente.data:
                    novo_ip = fallback_ip
                    break
                tentativas_fallback += 1

        if not novo_ip:
            return {"sucesso": False, "mensagem": "❌ Erro ao gerar novo IP. Tente novamente."}

        ip_antigo = jogador.get('ip', 'Desconhecido')

        # 1. Fechar todas as conexões ativas do jogador (como atacante)
        conexoes_fechadas = fechar_todas_conexoes(uid)

        # 2. Fechar todas as conexões direcionadas ao IP antigo (como alvo)
        conexoes_como_alvo = supabase_client.client.table('conexoes_ativas').select('atacante_uid').eq('alvo_ip', ip_antigo).eq('status', 'ativa').execute()

        invasoes_bloqueadas = 0
        if conexoes_como_alvo.data:
            for conexao in conexoes_como_alvo.data:
                # Bloquear cada invasão
                resultado_bloqueio = bloquear_invasao(uid, conexao['atacante_uid'])
                if resultado_bloqueio.get('sucesso'):
                    invasoes_bloqueadas += 1

        # 3. Atualizar IP e debitar shacks
        novos_shacks = shacks_atuais - custo_troca

        resultado_update = supabase_client.client.table('usuarios').update({
            'ip': novo_ip,
            'shack': novos_shacks
        }).eq('uid', uid).execute()

        if not resultado_update.data:
            return {"sucesso": False, "mensagem": "❌ Erro ao atualizar dados do jogador"}

        # 4. Log da ação
        supabase_client.log_atividade(
            uid,
            'troca_ip',
            {
                'ip_antigo': ip_antigo,
                'ip_novo': novo_ip,
                'custo': custo_troca,
                'shacks_restantes': novos_shacks,
                'conexoes_fechadas': conexoes_fechadas.get('total_fechadas', 0),
                'invasoes_bloqueadas': invasoes_bloqueadas
            }
        )

        # 5. Preparar mensagem de sucesso
        mensagem = f"🔄 IP alterado com sucesso!\n"
        mensagem += f"• IP anterior: {ip_antigo}\n"
        mensagem += f"• Novo IP: {novo_ip}\n"
        mensagem += f"• Custo: {custo_troca} shacks\n"
        mensagem += f"• Shacks restantes: {novos_shacks}"

        if conexoes_fechadas.get('total_fechadas', 0) > 0:
            mensagem += f"\n• {conexoes_fechadas.get('total_fechadas')} conexões próprias fechadas"

        if invasoes_bloqueadas > 0:
            mensagem += f"\n• {invasoes_bloqueadas} invasões bloqueadas"

        return {
            "sucesso": True,
            "mensagem": mensagem,
            "ip_antigo": ip_antigo,
            "ip_novo": novo_ip,
            "custo": custo_troca,
            "shacks_restantes": novos_shacks,
            "conexoes_fechadas": conexoes_fechadas.get('total_fechadas', 0),
            "invasoes_bloqueadas": invasoes_bloqueadas
        }

    except Exception as e:
        print(f"[TROCAR IP] Erro: {e}")
        return {"sucesso": False, "mensagem": f"Erro interno: {str(e)}"}

def obter_detalhes_conexoes_para_ui(atacante_uid: str) -> Dict[str, Any]:
    """
    Obtém informações detalhadas das conexões para a interface do usuário
    Formato otimizado para o terminal/interface
    """
    try:
        status_conexoes = obter_status_conexoes_jogador(atacante_uid)
        
        if not status_conexoes['sucesso']:
            return status_conexoes
        
        conexoes_formatadas = []
        
        for conexao in status_conexoes['conexoes_ativas']:
            # Verificar se há bruteforce ativo
            bruteforce_status = verificar_status_bruteforce_conexao(atacante_uid, conexao['alvo_ip'])

            # IMPORTANTE: Verificar atacante_acess diretamente da conexão para persistência após reload
            acesso_banco_persistente = conexao.get('atacante_acess', False)

            # Se há acesso persistente, buscar alvo_dados do último bruteforce bem-sucedido
            alvo_dados = None
            if acesso_banco_persistente:
                try:
                    # Buscar dados do último bruteforce bem-sucedido para esta conexão
                    bruteforce_result = supabase_client.client.table('bruteforce_ataques').select(
                        'alvo_dados'
                    ).eq('atacante_uid', atacante_uid).eq('alvo_ip', conexao['alvo_ip']).eq(
                        'status', 'concluido_sucesso'
                    ).order('created_at', desc=True).limit(1).execute()

                    if bruteforce_result.data:
                        alvo_dados = bruteforce_result.data[0].get('alvo_dados')
                        print(f"🔍 [PERSISTENCIA] Acesso bancário mantido para {conexao['alvo_ip']} com dados: {alvo_dados}")
                except Exception as e:
                    print(f"⚠️ [PERSISTENCIA] Erro ao buscar alvo_dados: {e}")

            conexao_info = {
                "alvo_nick": conexao.get('alvo_nick', 'Desconhecido'),
                "alvo_ip": conexao['alvo_ip'],
                "tempo_restante_min": conexao['tempo_restante_minutos'],
                "tempo_expiracao": conexao['expires_in_format'],
                "bruteforce_ativo": bruteforce_status.get('status') == 'executando',
                "acesso_banco_liberado": acesso_banco_persistente or bruteforce_status.get('acesso_banco_liberado', False),
                "alvo_dados": alvo_dados or bruteforce_status.get('alvo_dados'),
                "tempo_bruteforce_restante": bruteforce_status.get('tempo_restante', 0) if bruteforce_status.get('status') == 'executando' else 0
            }

            conexoes_formatadas.append(conexao_info)
        
        return {
            "sucesso": True,
            "conexoes": conexoes_formatadas,
            "total_conexoes": status_conexoes['total_conexoes'],
            "limite_conexoes": status_conexoes['limite_conexoes'],
            "conexoes_disponiveis": status_conexoes['conexoes_disponiveis'],
            "no_limite": status_conexoes['no_limite'],
            "resumo": status_conexoes['resumo'],
            "pode_exploitar": not status_conexoes['no_limite']
        }
        
    except Exception as e:
        print(f"[DETALHES CONEXOES UI] Erro: {e}")
        return {"sucesso": False, "mensagem": f"Erro interno: {str(e)}"}

def definir_tempo_expiracao_conexao(minutos: int = 30) -> str:
    """
    Calcula timestamp de expiração para conexões
    """
    return (datetime.now(timezone.utc) + timedelta(minutes=minutos)).isoformat()

def obter_historico_bruteforce(atacante_uid: str) -> List[Dict[str, Any]]:
    """
    Obtém o histórico de bruteforce do jogador
    """
    try:
        # Por enquanto, vamos retornar o histórico do jogador
        # Em versões futuras, podemos criar uma tabela específica para histórico de bruteforce
        atacante = get_jogador(atacante_uid)
        if not atacante:
            return []
        
        historico = atacante.get('historico', [])
        
        # Filtrar apenas entradas relacionadas a bruteforce/exploits
        historico_bruteforce = []
        for entrada in historico:
            if any(termo in entrada.lower() for termo in ['roubou', 'exploit', 'hack', 'bruteforce', '$']):
                historico_bruteforce.append({
                    'descricao': entrada,
                    'timestamp': datetime.now(timezone.utc).isoformat()
                })
        
        return historico_bruteforce[-20:]  # Últimas 20 entradas
        
    except Exception as e:
        print(f"[HISTORICO ERROR] Erro ao obter histórico: {e}")
        return []

def verificar_status_bruteforce(atacante_uid: str, alvo_ip: str) -> Dict[str, Any]:
    """
    Verifica se existe uma conexão ativa e o status de bruteforce
    Permite ataques repetidos durante a mesma conexão
    """
    try:
        if not supabase_client.is_connected():
            return {"sucesso": False, "mensagem": "Database não disponível"}
        
        # Verificar se há ataque bruteforce em andamento
        resultado_ataque = supabase_client.client.table('bruteforce_ataques').select('*').eq('atacante_uid', atacante_uid).eq('alvo_ip', alvo_ip).eq('status', 'executando').execute()
        
        if resultado_ataque.data:
            # Há ataque em andamento - verificar se terminou
            return verificar_e_finalizar_bruteforce(atacante_uid, alvo_ip)
        
        # Buscar conexão ativa para bruteforce
        conexoes_result = supabase_client.client.table('conexoes_ativas').select('*').eq('atacante_uid', atacante_uid).eq('alvo_ip', alvo_ip).eq('status', 'ativa').execute()
        
        if not conexoes_result.data:
            return {
                "sucesso": True,
                "conexao_ativa": False,
                "mensagem": "Nenhuma conexão ativa com este alvo"
            }
        
        conexao = conexoes_result.data[0]
        
        # Verificar se não expirou
        expires_at = parse_iso_datetime(conexao['expires_at'])
        tempo_restante = max(0, int((expires_at - datetime.now(timezone.utc)).total_seconds()))
        
        if tempo_restante <= 0:
            # Remover conexão expirada
            remover_conexao_ativa(atacante_uid, alvo_ip)
            return {
                "sucesso": True,
                "conexao_ativa": False,
                "mensagem": "Conexão expirou"
            }
        
        # Verificar se há bruteforce concluído com sucesso (para acesso ao banco)
        resultado_sucesso = supabase_client.client.table('bruteforce_ataques').select('*').eq('atacante_uid', atacante_uid).eq('alvo_ip', alvo_ip).eq('status', 'sucesso').order('finalizado_em', desc=True).limit(1).execute()
        
        banco_liberado = bool(resultado_sucesso.data)
        alvo_dados = None
        
        if banco_liberado and resultado_sucesso.data:
            alvo_dados = resultado_sucesso.data[0].get('alvo_dados')
            if not alvo_dados:
                alvo_dados = buscar_jogador_por_ip(alvo_ip)
        
        return {
            "sucesso": True,
            "conexao_ativa": True,
            "tempo_restante": tempo_restante,
            "alvo_nick": conexao.get('alvo_nick', 'Desconhecido'),
            "alvo_ip": alvo_ip,
            "pode_bruteforce": True,  # Sempre permite novos ataques enquanto há conexão
            "acesso_banco_liberado": banco_liberado,
            "alvo_dados": alvo_dados
        }
        
    except Exception as e:
        print(f"[BRUTEFORCE STATUS ERROR] {str(e)}")
        return {"sucesso": False, "mensagem": f"Erro ao verificar status: {str(e)}"}

def verificar_status_bruteforce_antigo(atacante_uid: str, alvo_ip: str) -> Dict[str, Any]:
    """
    Verifica o status de um bruteforce em andamento (função legado - não mais usada)
    """
    try:
        atacante = get_jogador(atacante_uid)
        if not atacante:
            return {"sucesso": False, "mensagem": "Atacante não encontrado"}
        
        # NOTA: Esta função é legado do sistema antigo - agora usamos tabela separada
        # Mantida para compatibilidade mas não deve ser chamada
        print("[WARNING] Função verificar_status_bruteforce_antigo chamada - deve usar nova tabela")
        
        return {"sucesso": False, "mensagem": "Função obsoleta - use nova tabela de conexões"}
        
    except Exception as e:
        return {"sucesso": False, "mensagem": f"Erro: {str(e)}"}

# --- SISTEMA DE CONEXÕES ATIVAS (NOVA TABELA) ---

def criar_conexao_ativa(atacante_uid: str, alvo_ip: str, alvo_dados: Dict[str, Any]) -> Dict[str, Any]:
    """
    Cria uma conexão ativa após exploit bem-sucedido
    Agora usa tabela separada com expiração automática
    LIMITE: Máximo 5 conexões ativas por jogador
    """
    try:
        if not supabase_client.is_connected():
            return {"sucesso": False, "mensagem": "Database não disponível"}
        
        # VERIFICAR LIMITE DE CONEXÕES (5 base + bônus NFT Firstsupp)
        # Buscar dados do jogador para verificar NFT Firstsupp
        jogador = get_jogador(atacante_uid)
        limite_conexoes = 5

        if jogador:
            # Aplicar efeito da NFT Firstsupp (+2 slots de conexão)
            limite_conexoes = aplicar_efeito_nft_firstsupp_slots(limite_conexoes, jogador)

        conexoes_existentes = supabase_client.client.table('conexoes_ativas').select('*').eq('atacante_uid', atacante_uid).eq('status', 'ativa').execute()

        # Limpar conexões expiradas primeiro
        agora = datetime.now(timezone.utc)
        conexoes_ativas_validas = []

        for conexao in conexoes_existentes.data if conexoes_existentes.data else []:
            expires_at = parse_iso_datetime(conexao['expires_at'])
            if agora < expires_at and conexao['alvo_ip'] != alvo_ip:  # Não contar a mesma IP que vamos substituir
                conexoes_ativas_validas.append(conexao)

        # Verificar se já atingiu o limite de conexões
        if len(conexoes_ativas_validas) >= limite_conexoes:
            # Mostrar conexões ativas para o jogador
            lista_conexoes = []
            for conn in conexoes_ativas_validas[:3]:  # Mostrar as 3 primeiras
                expires_in = int((parse_iso_datetime(conn['expires_at']) - agora).total_seconds() / 60)
                lista_conexoes.append(f"{conn['alvo_nick']} ({conn['alvo_ip']}) - expira em {expires_in}min")
            
            return {
                "sucesso": False,
                "mensagem": f"🚫 Limite de conexões atingido! Você já tem {len(conexoes_ativas_validas)} conexões ativas (máximo: {limite_conexoes}). Aguarde algumas expirarem ou execute bruteforce nas existentes. Conexões: {', '.join(lista_conexoes)}..."
            }
        
        # Remover conexão existente com mesmo IP (se houver)
        supabase_client.client.table('conexoes_ativas').delete().eq('atacante_uid', atacante_uid).eq('alvo_ip', alvo_ip).execute()
        
        # Calcular tempo de expiração (30 minutos)
        expires_at = datetime.now(timezone.utc) + timedelta(minutes=30)
        
        # Criar nova conexão na tabela separada
        conexao_data = {
            "atacante_uid": atacante_uid,
            "alvo_ip": alvo_ip,
            "alvo_nick": alvo_dados.get('nick', alvo_dados.get('nome', 'Alvo')),
            "alvo_bankguard": alvo_dados.get('bankguard', 1),
            "alvo_dinheiro": alvo_dados.get('dinheiro', 0),
            "status": "ativa",
            "expires_at": expires_at.isoformat()
        }
        
        resultado = supabase_client.client.table('conexoes_ativas').insert(conexao_data).execute()
        
        if resultado.data:
            conexoes_atuais = len(conexoes_ativas_validas) + 1  # +1 para a nova conexão
            print(f"[CONEXAO] Criada para {atacante_uid} -> {alvo_ip} | Expira: {expires_at} | Total: {conexoes_atuais}/5")
            return {
                "sucesso": True,
                "mensagem": f"Conexão estabelecida! Acesse o Terminal para executar o bruteforce. ({conexoes_atuais}/{limite_conexoes} conexões ativas)",
                "conexao": resultado.data[0],
                "expires_at": expires_at.isoformat(),
                "conexoes_ativas": conexoes_atuais,
                "limite_conexoes": limite_conexoes
            }
        else:
            return {"sucesso": False, "mensagem": "Erro ao criar conexão"}
        
    except Exception as e:
        print(f"[CONEXAO ERROR] {str(e)}")
        return {"sucesso": False, "mensagem": f"Erro interno: {str(e)}"}

# === FUNÇÕES AUXILIARES PARA ADMINISTRAÇÃO ===

def is_user_admin(uid: str) -> bool:
    """
    Verifica se o usuário é administrador
    """
    try:
        jogador = get_jogador(uid)
        if not jogador:
            return False
        
        return jogador.get('is_admin', False)
    except Exception as e:
        print(f"Erro ao verificar admin: {e}")
        return False

def get_admin_tournament_stats() -> Dict[str, Any]:
    """
    Obtém estatísticas dos torneios para administradores
    """
    try:
        # Obter ranking de grupos
        ranking_grupos = get_groups_deface_ranking()
        
        # Obter ranking individual
        ranking_individual = get_individual_deface_ranking()
        
        # Contar totais
        total_grupos = len(ranking_grupos.get('ranking_grupos', []))
        total_jogadores = len(ranking_individual.get('ranking_jogadores', []))
        
        # Obter tempo restante
        tempo_restante = get_tournament_time_remaining()
        
        return {
            "sucesso": True,
            "total_grupos": total_grupos,
            "total_jogadores": total_jogadores,
            "tempo_restante": tempo_restante,
            "ultimo_reset": "Informação não disponível",
            "proximo_reset_automatico": "00:00 UTC"
        }
    except Exception as e:
        print(f"Erro ao obter stats admin: {e}")
        return {
            "sucesso": False,
            "mensagem": f"Erro: {str(e)}"
        }

# --- INICIALIZAÇÃO ---
def get_grupo_by_id(grupo_id: str) -> Optional[Dict[str, Any]]:
    """Busca dados de um grupo específico pelo ID no Supabase"""
    try:
        if not supabase_client.is_connected():
            return None

        response = supabase_client.client.table('grupos').select('*').eq('id', grupo_id).limit(1).execute()
        if response.data:
            return response.data[0]
        return None
    except Exception as e:
        print(f"Erro ao buscar grupo por ID: {e}")
        return None

# === SUPPORTER SHOP FUNCTIONS ===

def processar_compra_shop(uid: str, item_id: str) -> Dict[str, Any]:
    """Processa a compra de um item do shop para supporters"""
    try:
        if not supabase_client.is_connected():
            return {"sucesso": False, "mensagem": "Database não disponível"}

        # Buscar dados do jogador
        jogador = get_jogador(uid)
        if not jogador:
            return {"sucesso": False, "mensagem": "Jogador não encontrado"}

        # Verificar se é supporter
        if not jogador.get('is_supporter', False):
            return {"sucesso": False, "mensagem": "Apenas supporters podem comprar itens"}

        # Definir itens e preços (agora em spoints)
        shop_items = {
            # Packs de dinheiro
            "money_pack_small": {"preco": 50, "nome": "Pack Dinheiro Pequeno", "valor": 500000},
            "money_pack_medium": {"preco": 150, "nome": "Pack Dinheiro Médio", "valor": 2000000},
            "money_pack_large": {"preco": 300, "nome": "Pack Dinheiro Grande", "valor": 5000000},

            # Habilidades exclusivas
            "ghost_mode": {"preco": 400, "nome": "Modo Fantasma"},
            "super_bruteforce": {"preco": 200, "nome": "Super Bruteforce"},
            "shield_protection": {"preco": 600, "nome": "Escudo de Proteção"},

            # Boosts
            "xp_boost_24h": {"preco": 100, "nome": "XP Boost 24h"},
            "money_boost_12h": {"preco": 150, "nome": "Money Boost 12h"},
            "mining_boost_24h": {"preco": 250, "nome": "Mining Boost 24h"},

            # Visual e social
            "premium_skin_1": {"preco": 200, "nome": "Skin Premium Cyber"},
            "exclusive_title": {"preco": 50, "nome": "Título [SUPPORTER]"},
            "custom_ip_color": {"preco": 100, "nome": "IP Colorido"},

            # Serviços gratuitos
            "priority_support": {"preco": 0, "nome": "Suporte Prioritário"},
            "beta_access": {"preco": 0, "nome": "Acesso Beta"},

            # NFT exclusivo
            "nft_firstsupp": {"preco": 1000, "nome": "Firstsupp NFT"}
        }

        if item_id not in shop_items:
            return {"sucesso": False, "mensagem": "Item não encontrado"}

        item = shop_items[item_id]
        preco = item["preco"]
        nome_item = item["nome"]

        # Verificar se tem spoints suficientes (se não for grátis)
        if preco > 0:
            spoints_atuais = jogador.get('spoints', 0)
            if spoints_atuais < preco:
                return {"sucesso": False, "mensagem": f"Spoints insuficientes. Você tem {spoints_atuais}, precisa de {preco}"}

        # Processar compra baseado no tipo de item
        updates = {}
        mensagem_sucesso = ""
        from datetime import datetime, timedelta, timezone

        # === PACKS DE DINHEIRO ===
        if item_id in ["money_pack_small", "money_pack_medium", "money_pack_large"]:
            valor_dinheiro = item.get("valor", 0)
            updates['dinheiro'] = jogador.get('dinheiro', 0) + valor_dinheiro
            updates['spoints'] = jogador.get('spoints', 0) - preco
            mensagem_sucesso = f"${valor_dinheiro:,} adicionados à sua conta!"

        # === HABILIDADES EXCLUSIVAS ===
        elif item_id == "ghost_mode":
            expira_em = datetime.now(timezone.utc) + timedelta(hours=24)
            updates['ghost_mode_expira'] = expira_em.isoformat()
            updates['ghost_mode_ativo'] = True
            updates['spoints'] = jogador.get('spoints', 0) - preco
            mensagem_sucesso = "Modo Fantasma ativado por 24 horas! Você está invisível nos logs."

        elif item_id == "super_bruteforce":
            expira_em = datetime.now(timezone.utc) + timedelta(hours=12)
            updates['super_bruteforce_expira'] = expira_em.isoformat()
            updates['super_bruteforce_ativo'] = True
            updates['spoints'] = jogador.get('spoints', 0) - preco
            mensagem_sucesso = "Super Bruteforce ativado por 12 horas! Bruteforce 3x mais rápido."

        elif item_id == "shield_protection":
            expira_em = datetime.now(timezone.utc) + timedelta(hours=6)
            updates['shield_protection_expira'] = expira_em.isoformat()
            updates['shield_protection_ativo'] = True
            updates['spoints'] = jogador.get('spoints', 0) - preco
            mensagem_sucesso = "Escudo de Proteção ativado por 6 horas! Você está imune a ataques."

        # === BOOSTS ===
        elif item_id == "xp_boost_24h":
            expira_em = datetime.now(timezone.utc) + timedelta(hours=24)
            updates['xp_boost_expira'] = expira_em.isoformat()
            updates['xp_boost_ativo'] = True
            updates['spoints'] = jogador.get('spoints', 0) - preco
            mensagem_sucesso = "XP Boost ativado por 24 horas! Ganho de XP dobrado."

        elif item_id == "money_boost_12h":
            expira_em = datetime.now(timezone.utc) + timedelta(hours=12)
            updates['money_boost_expira'] = expira_em.isoformat()
            updates['money_boost_ativo'] = True
            updates['spoints'] = jogador.get('spoints', 0) - preco
            mensagem_sucesso = "Money Boost ativado por 12 horas! Ganho de dinheiro dobrado."

        elif item_id == "mining_boost_24h":
            expira_em = datetime.now(timezone.utc) + timedelta(hours=24)
            updates['mining_boost_expira'] = expira_em.isoformat()
            updates['mining_boost_ativo'] = True
            updates['spoints'] = jogador.get('spoints', 0) - preco
            mensagem_sucesso = "Mining Boost ativado por 24 horas! Mineração 5x mais rápida."

        # === VISUAL E SOCIAL ===
        elif item_id == "premium_skin_1":
            updates['premium_skin'] = True
            updates['spoints'] = jogador.get('spoints', 0) - preco
            mensagem_sucesso = "Skin Premium ativada! Recarregue a página para ver as mudanças."

        elif item_id == "exclusive_title":
            updates['titulo_supporter'] = True
            updates['spoints'] = jogador.get('spoints', 0) - preco
            mensagem_sucesso = "Título [SUPPORTER] adicionado ao seu perfil!"

        elif item_id == "custom_ip_color":
            updates['ip_colorido'] = True
            updates['spoints'] = jogador.get('spoints', 0) - preco
            mensagem_sucesso = "IP Colorido ativado! Seu IP aparece em dourado para outros jogadores."

        # === SERVIÇOS GRATUITOS ===
        elif item_id == "priority_support":
            updates['suporte_prioritario'] = True
            mensagem_sucesso = "Acesso ao suporte prioritário ativado! Entre no Discord para mais informações."

        elif item_id == "beta_access":
            updates['beta_tester'] = True
            mensagem_sucesso = "Acesso beta ativado! Você receberá notificações sobre novas funcionalidades."

        # === NFT EXCLUSIVO ===
        elif item_id == "nft_firstsupp":
            # Usar função específica para NFTs
            return processar_compra_nft(uid, "firstsupp")

        # Atualizar dados do jogador
        if updates:
            resultado_update = atualizar_jogador(uid, updates)
            if not resultado_update.get('sucesso', True):
                return {"sucesso": False, "mensagem": "Erro ao processar compra"}

        # Registrar compra no log (opcional)
        try:
            log_compra = {
                "tipo": "compra_shop",
                "item_id": item_id,
                "nome_item": nome_item,
                "preco": preco,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
            # Aqui você pode adicionar o log se tiver sistema de logs
        except:
            pass  # Log não é crítico

        return {
            "sucesso": True,
            "mensagem": mensagem_sucesso,
            "item_comprado": nome_item,
            "spoints_restantes": updates.get('spoints', jogador.get('spoints', 0))
        }

    except Exception as e:
        print(f"Erro ao processar compra do shop: {e}")
        return {"sucesso": False, "mensagem": f"Erro interno: {str(e)}"}

# === NFT FUNCTIONS ===

def get_nft_estoque(nft_id: str) -> int:
    """Retorna o estoque atual de um NFT específico usando a tabela habilidades_nft"""
    try:
        if not supabase_client.is_connected():
            print(f"[DEBUG NFT] Supabase não conectado")
            return 0

        print(f"[DEBUG NFT] Buscando estoque para NFT: {nft_id}")

        # Primeiro, buscar dados da habilidade na tabela habilidades_nft
        habilidade_response = supabase_client.client.table('habilidades_nft').select('estoque_atual, estoque_maximo').eq('skill_id', nft_id).execute()

        if habilidade_response.data:
            estoque_atual = habilidade_response.data[0].get('estoque_atual', 0)
            estoque_maximo = habilidade_response.data[0].get('estoque_maximo', 10)
            print(f"[DEBUG NFT] Estoque da tabela habilidades_nft: {estoque_atual}/{estoque_maximo}")
            return max(0, estoque_atual)
        else:
            print(f"[DEBUG NFT] Habilidade {nft_id} não encontrada na tabela habilidades_nft")
            # Fallback: calcular baseado em quantos jogadores possuem o NFT
            response = supabase_client.client.table('usuarios').select('uid').eq(f'nft_{nft_id}', True).execute()

            print(f"[DEBUG NFT] Resposta da query usuarios: {response.data}")

            if nft_id == "firstsupp":
                total_estoque = 10
                vendidos = len(response.data) if response.data else 0
                estoque_restante = max(0, total_estoque - vendidos)
                print(f"[DEBUG NFT] Fallback - Total: {total_estoque}, Vendidos: {vendidos}, Restante: {estoque_restante}")
                return estoque_restante

        return 0
    except Exception as e:
        print(f"[DEBUG NFT] Erro ao buscar estoque NFT: {e}")
        # Se der erro na coluna, provavelmente ela não existe, então retorna estoque cheio
        if "column" in str(e).lower() and "does not exist" in str(e).lower():
            print(f"[DEBUG NFT] Coluna nft_{nft_id} não existe, retornando estoque cheio")
            return 10 if nft_id == "firstsupp" else 0
        return 0

def atualizar_estoque_nft(nft_id: str, quantidade_vendida: int = 1) -> bool:
    """Atualiza o estoque de um NFT na tabela habilidades_nft"""
    try:
        if not supabase_client.is_connected():
            print(f"[DEBUG NFT ESTOQUE] Supabase não conectado")
            return False

        print(f"[DEBUG NFT ESTOQUE] Atualizando estoque do NFT {nft_id}, vendendo {quantidade_vendida}")

        # Buscar estoque atual
        habilidade_response = supabase_client.client.table('habilidades_nft').select('estoque_atual').eq('skill_id', nft_id).execute()

        if habilidade_response.data:
            estoque_atual = habilidade_response.data[0].get('estoque_atual', 0)
            novo_estoque = max(0, estoque_atual - quantidade_vendida)

            print(f"[DEBUG NFT ESTOQUE] Estoque atual: {estoque_atual}, Novo estoque: {novo_estoque}")

            # Atualizar estoque
            update_response = supabase_client.client.table('habilidades_nft').update({
                'estoque_atual': novo_estoque
            }).eq('skill_id', nft_id).execute()

            if update_response.data:
                print(f"[DEBUG NFT ESTOQUE] Estoque atualizado com sucesso para {novo_estoque}")
                return True
            else:
                print(f"[DEBUG NFT ESTOQUE] Erro ao atualizar estoque")
                return False
        else:
            print(f"[DEBUG NFT ESTOQUE] Habilidade {nft_id} não encontrada na tabela")
            return False

    except Exception as e:
        print(f"[DEBUG NFT ESTOQUE] Erro ao atualizar estoque: {e}")
        return False

def processar_compra_nft(uid: str, nft_id: str) -> Dict[str, Any]:
    """Processa a compra de um NFT específico"""
    try:
        if not supabase_client.is_connected():
            return {"sucesso": False, "mensagem": "Database não disponível"}

        # Verificar estoque
        estoque_atual = get_nft_estoque(nft_id)
        if estoque_atual <= 0:
            return {"sucesso": False, "mensagem": "NFT esgotado! Não há mais unidades disponíveis."}

        # Verificar se o jogador já possui este NFT
        jogador = get_jogador(uid)
        if not jogador:
            return {"sucesso": False, "mensagem": "Jogador não encontrado"}

        if jogador.get(f'nft_{nft_id}', False):
            return {"sucesso": False, "mensagem": "Você já possui este NFT!"}

        # Processar compra do NFT
        if nft_id == "firstsupp":
            # Preço fixo em spoints (independente do que está na tabela habilidades_nft)
            preco_spoints = 1000
            spoints_atuais = jogador.get('spoints', 0)

            print(f"[DEBUG NFT COMPRA] Preço em spoints: {preco_spoints}, Spoints do jogador: {spoints_atuais}")

            if spoints_atuais < preco_spoints:
                return {"sucesso": False, "mensagem": f"Spoints insuficientes. Você tem {spoints_atuais}, precisa de {preco_spoints}"}

            print(f"[DEBUG NFT COMPRA] Processando compra do NFT Firstsupp para {uid}")
            print(f"[DEBUG NFT COMPRA] Estoque atual: {estoque_atual}, Preço: {preco_spoints}, Spoints do jogador: {spoints_atuais}")

            # Primeiro, adicionar NFT às habilidades
            nft_data = {
                "nome": "Firstsupp",
                "descricao": "NFT exclusivo que dobra a geração de dinheiro e adiciona +2 slots de conexão",
                "efeitos": {
                    "money_multiplier": 2.0,
                    "connection_slots_bonus": 2
                },
                "raridade": "Legendary",
                "numero": estoque_atual,  # Número da unidade (10, 9, 8, etc.)
                "data_aquisicao": datetime.now(timezone.utc).isoformat(),
                "preco_pago": preco_spoints,
                "moeda": "spoints"
            }

            print(f"[DEBUG NFT COMPRA] Dados do NFT: {nft_data}")

            # Tentar adicionar às habilidades primeiro
            habilidade_adicionada = adicionar_habilidade_nft(uid, "firstsupp", nft_data)
            print(f"[DEBUG NFT COMPRA] Habilidade adicionada: {habilidade_adicionada}")

            # Atualizar jogador com NFT e efeitos
            updates = {
                'nft_firstsupp': True,
                'money_multiplier': 2.0,  # x2 geração de dinheiro
                'connection_slots_bonus': 2,  # +2 slots de conexão
                'spoints': spoints_atuais - preco_spoints
            }

            print(f"[DEBUG NFT COMPRA] Updates a aplicar: {updates}")

            resultado_update = atualizar_jogador(uid, updates)
            print(f"[DEBUG NFT COMPRA] Resultado da atualização: {resultado_update}")

            if not resultado_update.get('sucesso', True):
                return {"sucesso": False, "mensagem": "Erro ao processar compra do NFT"}

            # Atualizar estoque na tabela habilidades_nft
            estoque_atualizado = atualizar_estoque_nft(nft_id, 1)
            print(f"[DEBUG NFT COMPRA] Estoque atualizado na tabela: {estoque_atualizado}")

            # Verificar se tudo foi salvo corretamente
            jogador_verificacao = get_jogador(uid)
            if jogador_verificacao:
                print(f"[DEBUG NFT COMPRA] Verificação final - NFT: {jogador_verificacao.get('nft_firstsupp')}")
                print(f"[DEBUG NFT COMPRA] Verificação final - Habilidades: {jogador_verificacao.get('habilidades_nft')}")
                print(f"[DEBUG NFT COMPRA] Verificação final - Spoints: {jogador_verificacao.get('spoints')}")

            return {
                "sucesso": True,
                "mensagem": f"NFT Firstsupp #{estoque_atual} adquirido com sucesso! Efeitos ativados permanentemente.",
                "nft_numero": estoque_atual,
                "spoints_restantes": spoints_atuais - preco_spoints,
                "preco_pago": preco_spoints,
                "moeda": "spoints"
            }

        return {"sucesso": False, "mensagem": "NFT não encontrado"}

    except Exception as e:
        print(f"Erro ao processar compra NFT: {e}")
        return {"sucesso": False, "mensagem": f"Erro interno: {str(e)}"}

def adicionar_habilidade_nft(uid: str, nft_id: str, nft_data: Dict[str, Any]) -> bool:
    """Adiciona um NFT às habilidades do jogador"""
    try:
        print(f"[DEBUG NFT HABILIDADE] Iniciando adição de NFT {nft_id} para jogador {uid}")

        if not supabase_client.is_connected():
            print(f"[DEBUG NFT HABILIDADE] Supabase não conectado")
            return False

        # Buscar habilidades atuais do jogador
        jogador = get_jogador(uid)
        if not jogador:
            print(f"[DEBUG NFT HABILIDADE] Jogador não encontrado: {uid}")
            return False

        print(f"[DEBUG NFT HABILIDADE] Jogador encontrado: {jogador.get('nick', 'Sem nick')}")

        habilidades_atuais = jogador.get('habilidades_nft', {})
        print(f"[DEBUG NFT HABILIDADE] Habilidades atuais (raw): {habilidades_atuais}")

        if isinstance(habilidades_atuais, str):
            import json
            try:
                habilidades_atuais = json.loads(habilidades_atuais)
                print(f"[DEBUG NFT HABILIDADE] Habilidades parseadas do JSON: {habilidades_atuais}")
            except Exception as parse_error:
                print(f"[DEBUG NFT HABILIDADE] Erro ao parsear JSON: {parse_error}")
                habilidades_atuais = {}
        elif habilidades_atuais is None:
            print(f"[DEBUG NFT HABILIDADE] Habilidades eram None, inicializando como dict vazio")
            habilidades_atuais = {}

        # Adicionar novo NFT
        habilidades_atuais[nft_id] = nft_data
        print(f"[DEBUG NFT HABILIDADE] Habilidades após adicionar NFT: {habilidades_atuais}")

        # Atualizar no banco
        print(f"[DEBUG NFT HABILIDADE] Tentando atualizar jogador com habilidades: {habilidades_atuais}")
        resultado = atualizar_jogador(uid, {'habilidades_nft': habilidades_atuais})
        print(f"[DEBUG NFT HABILIDADE] Resultado da atualização: {resultado}")

        # Verificar se realmente foi salvo
        jogador_atualizado = get_jogador(uid)
        if jogador_atualizado:
            habilidades_verificacao = jogador_atualizado.get('habilidades_nft', {})
            print(f"[DEBUG NFT HABILIDADE] Verificação - habilidades salvas: {habilidades_verificacao}")

        return resultado.get('sucesso', True)

    except Exception as e:
        print(f"[DEBUG NFT HABILIDADE] Erro ao adicionar habilidade NFT: {e}")
        import traceback
        print(f"[DEBUG NFT HABILIDADE] Traceback: {traceback.format_exc()}")
        return False

def realizar_deface(atacante_uid: str, alvo_uid: str) -> Dict[str, Any]:
    """
    Função de deface simples e funcional - apenas jogadores reais
    """
    try:
        print(f"[DEFACE] Iniciando deface: {atacante_uid} -> {alvo_uid}")

        # Buscar atacante
        atacante = get_jogador(atacante_uid)
        if not atacante:
            return {"sucesso": False, "mensagem": "Atacante não encontrado"}

        # Buscar alvo (apenas jogadores reais)
        alvo = get_jogador(alvo_uid)
        if not alvo:
            return {"sucesso": False, "mensagem": "Alvo não encontrado"}

        # Verificar se atacante tem grupo
        grupo_id = atacante.get('grupo_id')
        if not grupo_id:
            return {"sucesso": False, "mensagem": "Você precisa estar em um grupo para realizar deface"}

        # Verificar se alvo já tem deface ativo (sistema de proteção de 30 minutos)
        try:
            deface_status = supabase_client.verificar_deface_ativo(alvo_uid)
            if deface_status.get("tem_deface", False):
                minutos_restantes = deface_status["minutos_restantes"]
                return {
                    "sucesso": False,
                    "mensagem": f"Este alvo já foi defaceado! Tempo restante: {minutos_restantes} minutos."
                }
        except Exception as e:
            print(f"[DEFACE] Erro ao verificar deface ativo: {e}")
            # Continua mesmo se não conseguir verificar

        # Verificar cooldown (30 minutos)
        from datetime import datetime, timezone
        timestamp_atual = datetime.now(timezone.utc).timestamp()
        last_deface = atacante.get('last_deface_timestamp', 0)
        cooldown = 30 * 60  # 30 minutos

        if timestamp_atual - last_deface < cooldown:
            tempo_restante = cooldown - (timestamp_atual - last_deface)
            minutos = int(tempo_restante // 60)
            segundos = int(tempo_restante % 60)
            return {"sucesso": False, "mensagem": f"Cooldown ativo! Aguarde {minutos}m {segundos}s"}

        # Lógica de combate: CPU + Malware Kit vs Antivirus
        cpu_atacante = atacante.get('cpu', 1)
        malware_kit_atacante = atacante.get('malware_kit', 1)
        poder_atacante = cpu_atacante + malware_kit_atacante

        antivirus_alvo = alvo.get('antivirus', 1)
        alvo_nick = alvo.get('nick', 'Jogador')

        # Calcular chance de sucesso
        import random
        poder_minimo = antivirus_alvo * 2

        if poder_atacante >= poder_minimo:
            sucesso = True
            chance = 100
        else:
            proporcao = poder_atacante / antivirus_alvo
            chance = min(95, max(5, int(proporcao * 50)))
            sucesso = random.randint(1, 100) <= chance

        if not sucesso:
            # Atualizar cooldown mesmo em falha
            atualizar_jogador(atacante_uid, {'last_deface_timestamp': int(timestamp_atual)})
            return {
                "sucesso": False,
                "mensagem": f"Deface falhou! Poder {poder_atacante} vs Antivirus {antivirus_alvo}. Chance era {chance}%"
            }

        # Deface bem-sucedido
        pontos_ganhos = 30

        # Atualizar atacante (manter compatibilidade com sistema antigo)
        pontos_individuais = atacante.get('deface_points_individual', 0) + pontos_ganhos
        pontos_torneio = atacante.get('tournament_points_individual', 0) + pontos_ganhos

        atualizar_jogador(atacante_uid, {
            'deface_points_individual': pontos_individuais,
            'tournament_points_individual': pontos_torneio,
            'last_deface_timestamp': int(timestamp_atual)
        })

        # NOVO SISTEMA: Adicionar pontos na tabela torneio_pontuacoes
        try:
            # Buscar dados do grupo
            grupo_nome = None
            if grupo_id:
                print(f"[DEFACE] Buscando dados do grupo: {grupo_id}")
                grupo_response = supabase_client.client.table('grupos').select('nome').eq('id', grupo_id).execute()
                if grupo_response.data:
                    grupo_nome = grupo_response.data[0].get('nome')
                    print(f"[DEFACE] Nome do grupo encontrado: {grupo_nome}")
                else:
                    print(f"[DEFACE] Grupo não encontrado na tabela grupos")
            else:
                print(f"[DEFACE] Atacante não tem grupo_id")

            # Usar sistema TTL para adicionar pontos
            from .tournament_ttl import torneio_manager
            print(f"[DEFACE] Chamando TTL com: jogador={atacante_uid}, pontos={pontos_ganhos}, nick={atacante.get('nick')}, grupo={grupo_id} ({grupo_nome})")

            resultado_ttl = torneio_manager.adicionar_pontos_deface(
                jogador_uid=atacante_uid,
                pontos=pontos_ganhos,
                nick=atacante.get('nick'),
                grupo_id=grupo_id,
                grupo_nome=grupo_nome
            )

            print(f"[DEFACE] Resultado TTL: {resultado_ttl}")

            if resultado_ttl.get('sucesso'):
                print(f"[DEFACE] ✅ Pontos TTL adicionados: +{pontos_ganhos} para {atacante.get('nick')}")
            else:
                print(f"[DEFACE] ❌ Erro TTL: {resultado_ttl.get('mensagem')}")

        except Exception as e:
            print(f"[DEFACE] ❌ Erro ao adicionar pontos TTL: {e}")
            import traceback
            print(f"[DEFACE] Traceback: {traceback.format_exc()}")

        # Atualizar grupo (manter compatibilidade com sistema antigo)
        try:
            grupo_response = supabase_client.client.table('grupos').select('*').eq('id', grupo_id).execute()
            if grupo_response.data:
                grupo = grupo_response.data[0]
                novos_pontos_grupo = grupo.get('deface_points', 0) + pontos_ganhos
                novos_pontos_torneio_grupo = grupo.get('tournament_points', 0) + pontos_ganhos

                supabase_client.client.table('grupos').update({
                    'deface_points': novos_pontos_grupo,
                    'tournament_points': novos_pontos_torneio_grupo
                }).eq('id', grupo_id).execute()

                print(f"[DEFACE] Grupo atualizado: {grupo.get('nome')} - Pontos: {novos_pontos_grupo} (+{pontos_ganhos})")
            else:
                print(f"[DEFACE] Grupo não encontrado: {grupo_id}")
        except Exception as e:
            print(f"[DEFACE] Erro ao atualizar grupo: {e}")

        # Registrar deface ativo por 30 minutos
        try:
            resultado_deface_ativo = supabase_client.registrar_deface_ativo(
                alvo_uid=alvo_uid,
                atacante_uid=atacante_uid,
                grupo_id=grupo_id,
                duracao_minutos=30
            )
            if resultado_deface_ativo.get("sucesso"):
                print(f"[DEFACE] Deface ativo registrado por 30 minutos")
        except Exception as e:
            print(f"[DEFACE] Erro ao registrar deface ativo: {e}")

        return {
            "sucesso": True,
            "mensagem": f"Deface realizado com sucesso em {alvo_nick}! +{pontos_ganhos} pontos. Alvo protegido por 30 minutos. (Chance: {chance}%)",
            "pontos_ganhos": pontos_ganhos,
            "chance_sucesso": chance,
            "tempo_protecao_minutos": 30
        }

    except Exception as e:
        print(f"[DEFACE] Erro: {e}")
        return {"sucesso": False, "mensagem": f"Erro interno: {str(e)}"}

def limpar_defaces_expirados_automatico():
    """
    Função para limpeza automática de defaces expirados
    Deve ser chamada periodicamente ou antes de verificações
    """
    try:
        count = supabase_client.limpar_defaces_expirados()
        return count
    except Exception as e:
        print(f"[DEFACE CLEANUP] Erro na limpeza automática: {e}")
        return 0

print("🔧 Inicializando models com Supabase...")
try:
    init_supabase()
except Exception as e:
    print(f"⚠️ Erro na inicialização: {e}")
    print("🔧 Executando em modo offline")
