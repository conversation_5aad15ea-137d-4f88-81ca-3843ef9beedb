#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import datetime, timezone
from database.supabase_client import supabase_client
import game.new_models as models

def debug_por_que_zero():
    """Debug específico para entender por que 0 jogadores são processados"""
    print("🔍 DEBUG: POR QUE 0 JOGADORES PROCESSADOS?")
    print("=" * 50)
    
    # 1. Verificar conexão
    print("1. Verificando conexão...")
    if not supabase_client.is_connected():
        print("❌ Não conectado!")
        return
    print("✅ Conectado!")
    
    # 2. Buscar usuários
    print("\n2. Buscando usuários...")
    usuarios = supabase_client.get_ranking_usuarios(1000)
    print(f"Total usuários encontrados: {len(usuarios)}")
    
    if not usuarios:
        print("❌ Nenhum usuário encontrado!")
        return
    
    # 3. Analisar cada usuário detalhadamente
    print("\n3. Analisando cada usuário...")
    timestamp_atual = datetime.now(timezone.utc).timestamp()
    print(f"Timestamp atual: {timestamp_atual}")
    
    usuarios_validos = 0
    usuarios_com_mineradora = 0
    usuarios_bloqueados_cache = 0
    usuarios_processaveis = 0
    
    for i, usuario in enumerate(usuarios):
        uid = usuario.get('uid')
        nick = usuario.get('nick', 'Unknown')
        nivel_mineradora = usuario.get('nivel_mineradora', 1)
        dinheiro = usuario.get('dinheiro', 0)
        
        print(f"\n👤 Usuário {i+1}: {nick}")
        print(f"   UID: {uid}")
        print(f"   Nível Mineradora: {nivel_mineradora}")
        print(f"   Dinheiro: ${dinheiro}")
        
        # Verificar se UID é válido
        if not uid:
            print(f"   ❌ UID inválido")
            continue
        
        usuarios_validos += 1
        
        # Verificar se tem mineradora
        if nivel_mineradora < 1:
            print(f"   ❌ Mineradora inativa (nível {nivel_mineradora})")
            continue
        
        usuarios_com_mineradora += 1
        print(f"   ✅ Mineradora ativa (nível {nivel_mineradora})")
        
        # Verificar cache
        try:
            cache = models.MINERACAO_AUTOMATICA_CACHE
            if uid in cache:
                ultimo_processamento = cache[uid]
                tempo_desde_ultimo = timestamp_atual - ultimo_processamento
                print(f"   Cache: último processamento há {tempo_desde_ultimo:.1f}s")
                
                if tempo_desde_ultimo < 30:
                    print(f"   ❌ Bloqueado por cooldown ({tempo_desde_ultimo:.1f}s < 30s)")
                    usuarios_bloqueados_cache += 1
                    continue
                else:
                    print(f"   ✅ Cooldown expirado ({tempo_desde_ultimo:.1f}s >= 30s)")
            else:
                print(f"   ✅ Não está no cache (primeira execução)")
            
            usuarios_processaveis += 1
            print(f"   🎯 USUÁRIO PROCESSÁVEL!")
            
        except Exception as e:
            print(f"   ⚠️ Erro ao verificar cache: {e}")
            usuarios_processaveis += 1
    
    print(f"\n📊 RESUMO DETALHADO:")
    print(f"   Total usuários: {len(usuarios)}")
    print(f"   Usuários válidos (com UID): {usuarios_validos}")
    print(f"   Com mineradora ativa: {usuarios_com_mineradora}")
    print(f"   Bloqueados por cache: {usuarios_bloqueados_cache}")
    print(f"   PROCESSÁVEIS: {usuarios_processaveis}")
    
    # 4. Verificar estado do cache
    print(f"\n4. Estado do cache:")
    try:
        cache = models.MINERACAO_AUTOMATICA_CACHE
        print(f"   Entradas no cache: {len(cache)}")
        if cache:
            print("   Últimas 5 entradas:")
            for i, (uid, timestamp) in enumerate(list(cache.items())[:5]):
                tempo_desde = timestamp_atual - timestamp
                print(f"     {i+1}. {uid}: {tempo_desde:.1f}s atrás")
    except Exception as e:
        print(f"   ❌ Erro ao acessar cache: {e}")
    
    # 5. Executar função e ver onde falha
    print(f"\n5. Executando função de mineração...")
    try:
        resultado = models.processar_mineracao_automatica_dinheiro()
        print(f"   Resultado: {resultado}")
        
        if resultado.get('sucesso'):
            processados = resultado.get('jogadores_processados', 0)
            if processados == 0:
                print("   ❌ FUNÇÃO RETORNOU 0 PROCESSADOS!")
                print("   🔍 Isso indica que há um problema na lógica interna da função")
            else:
                print(f"   ✅ Função processou {processados} jogadores")
        else:
            print(f"   ❌ Função falhou: {resultado.get('mensagem')}")
            
    except Exception as e:
        print(f"   ❌ Erro na execução: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_por_que_zero()
