#!/usr/bin/env python3
"""
Teste final da mineração corrigida
"""

import os
from dotenv import load_dotenv

# Carrega variáveis de ambiente
load_dotenv()

print("🔍 Importando módulos...")

try:
    from database.supabase_client import supabase_client
    print("✅ Supabase importado")
except Exception as e:
    print(f"❌ Erro ao importar supabase: {e}")
    exit(1)

try:
    from game import new_models as models
    print("✅ Models importado")
except Exception as e:
    print(f"❌ Erro ao importar models: {e}")
    exit(1)

def teste_direto():
    """Teste direto da função corrigida"""
    print("\n🧪 Testando função corrigida...")
    
    # Verificar conexão
    if not supabase_client.is_connected():
        print("❌ Supabase não conectado")
        return False
    
    print("✅ Supabase conectado")
    
    # Buscar usuários
    usuarios = supabase_client.get_ranking_usuarios(3)
    if not usuarios:
        print("❌ Nenhum usuário encontrado")
        return False
    
    print(f"👥 {len(usuarios)} usuários encontrados")
    
    # Mostrar dados antes
    print("\n💰 Saldos ANTES do processamento:")
    saldos_antes = {}
    for usuario in usuarios:
        uid = usuario['uid']
        nick = usuario.get('nick', 'Unknown')
        dinheiro = usuario.get('dinheiro', 0)
        nivel = usuario.get('nivel_mineradora', 1)
        saldos_antes[uid] = dinheiro
        print(f"   {nick} (Nível {nivel}): ${dinheiro}")
    
    # Executar processamento
    print("\n⚡ Executando processamento...")
    try:
        resultado = models.processar_mineracao_automatica_dinheiro()
        print(f"📊 Resultado: {resultado}")
    except Exception as e:
        print(f"❌ Erro no processamento: {e}")
        return False
    
    # Verificar resultados
    print("\n💰 Saldos DEPOIS do processamento:")
    usuarios_depois = supabase_client.get_ranking_usuarios(3)
    mudancas = 0
    
    for usuario in usuarios_depois:
        uid = usuario['uid']
        nick = usuario.get('nick', 'Unknown')
        dinheiro_depois = usuario.get('dinheiro', 0)
        dinheiro_antes = saldos_antes.get(uid, 0)
        diferenca = dinheiro_depois - dinheiro_antes
        
        if diferenca > 0:
            print(f"   {nick}: ${dinheiro_antes} → ${dinheiro_depois} (+${diferenca}) ✅")
            mudancas += 1
        else:
            print(f"   {nick}: ${dinheiro_depois} (sem mudança)")
    
    print(f"\n📈 Usuários com mudança: {mudancas}")
    
    if mudancas > 0:
        print("✅ MINERAÇÃO FUNCIONANDO!")
        return True
    else:
        print("❌ Nenhuma mudança detectada")
        return False

if __name__ == "__main__":
    print("🎯 TESTE FINAL DA MINERAÇÃO AUTOMÁTICA")
    print("=" * 45)
    
    if teste_direto():
        print("\n🎉 SUCESSO! A mineração automática está funcionando!")
        print("\n📋 Próximos passos:")
        print("1. Iniciar o servidor: python app.py")
        print("2. A mineração rodará automaticamente a cada minuto")
        print("3. Verificar logs no console do servidor")
    else:
        print("\n❌ FALHA! A mineração ainda não está funcionando")
        print("\n🔧 Possíveis problemas:")
        print("1. Cache impedindo processamento frequente")
        print("2. Erro na função de atualização")
        print("3. Problema na conexão com Supabase")
