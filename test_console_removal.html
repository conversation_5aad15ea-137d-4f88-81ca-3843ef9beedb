<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧹 Teste de Remoção de Console Logs</title>
    <style>
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            margin: 0; 
            padding: 20px; 
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%); 
            color: white; 
            min-height: 100vh;
        }
        .container { 
            max-width: 1000px; 
            margin: 0 auto; 
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(139, 92, 246, 0.3);
        }
        .info-box {
            background: #2a2a2a;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            font-size: 14px;
        }
        .status.success {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid #4CAF50;
            color: #4CAF50;
        }
        .status.error {
            background: rgba(244, 67, 54, 0.2);
            border: 1px solid #f44336;
            color: #f44336;
        }
        .status.warning {
            background: rgba(255, 193, 7, 0.2);
            border: 1px solid #ffc107;
            color: #ffc107;
        }
        button {
            padding: 12px 20px;
            background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
            margin: 5px;
        }
        button:hover {
            background: linear-gradient(135deg, #7c3aed 0%, #6d28d9 100%);
            transform: translateY(-2px);
        }
        .code-block {
            background: #1a1a1a;
            border: 1px solid #444;
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            overflow-x: auto;
        }
        .highlight {
            color: #8b5cf6;
            font-weight: bold;
        }
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        @media (max-width: 768px) {
            .grid {
                grid-template-columns: 1fr;
            }
        }
        .console-monitor {
            background: #000;
            border: 2px solid #333;
            border-radius: 8px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            height: 200px;
            overflow-y: auto;
            color: #0f0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧹 Teste de Remoção de Console Logs</h1>
            <p>Verificando se todos os console.log, console.error, etc. foram removidos</p>
        </div>
        
        <div class="grid">
            <div class="info-box">
                <h3>🎛️ Status do Sistema</h3>
                <div id="system-status" class="code-block">
                    Verificando configurações...
                </div>
            </div>
            
            <div class="info-box">
                <h3>📊 Monitor do Console</h3>
                <div id="console-monitor" class="console-monitor">
                    Aguardando atividade do console...
                </div>
            </div>
        </div>
        
        <div class="info-box">
            <h3>🧪 Testes de Console</h3>
            <button onclick="testarConsoleLogs()">📝 Testar Console Logs</button>
            <button onclick="testarFuncoes()">⚡ Testar Funções</button>
            <button onclick="simularAtividade()">🎮 Simular Atividade do Jogo</button>
            <button onclick="restaurarConsole()">🔧 Restaurar Console (Debug)</button>
            <button onclick="desabilitarConsole()">🔇 Desabilitar Console</button>
            
            <div id="test-results" class="code-block" style="margin-top: 15px;">
                Execute os testes acima para verificar se os console logs foram removidos...
            </div>
        </div>
        
        <div class="info-box">
            <h3>📋 Instruções</h3>
            <div class="code-block">
                <strong>Como verificar se os logs foram removidos:</strong><br><br>
                
                <span class="highlight">1. Abra F12 → Console</span><br>
                • O console deve estar completamente limpo<br>
                • Não deve aparecer nenhum log automático<br><br>
                
                <span class="highlight">2. Execute os testes acima</span><br>
                • "Testar Console Logs" - verifica se console.log está silenciado<br>
                • "Simular Atividade" - simula ações do jogo<br>
                • Monitor deve permanecer vazio<br><br>
                
                <span class="highlight">3. Para reativar logs (se necessário):</span><br>
                • Clique em "Restaurar Console (Debug)"<br>
                • Ou altere CONSOLE_LOGS_ENABLED = true no código<br><br>
                
                <span class="highlight">4. Resultado esperado:</span><br>
                • Console F12 completamente limpo<br>
                • Nenhum log automático aparecendo<br>
                • Funcionalidades do jogo intactas<br>
                • Experiência profissional e limpa
            </div>
        </div>
        
        <div class="info-box">
            <h3>✅ Benefícios da Remoção</h3>
            <div class="code-block">
                <span class="highlight">🧹 Console Limpo:</span><br>
                • Sem poluição visual no F12<br>
                • Experiência profissional<br>
                • Foco apenas no que importa<br><br>
                
                <span class="highlight">⚡ Performance:</span><br>
                • Menos operações de logging<br>
                • Redução de overhead<br>
                • Carregamento mais rápido<br><br>
                
                <span class="highlight">🔒 Segurança:</span><br>
                • Informações sensíveis não expostas<br>
                • Logs de debug ocultos<br>
                • Dados internos protegidos<br><br>
                
                <span class="highlight">👥 Experiência do Usuário:</span><br>
                • Interface limpa e profissional<br>
                • Sem distrações técnicas<br>
                • Foco na funcionalidade
            </div>
        </div>
    </div>

    <script>
        let consoleMessages = [];
        let originalConsole = {};
        
        // Interceptar console para monitoramento
        function setupConsoleMonitoring() {
            const monitor = document.getElementById('console-monitor');
            
            // Salvar referências originais se ainda não foram salvas
            if (!originalConsole.log) {
                originalConsole = {
                    log: console.log,
                    error: console.error,
                    warn: console.warn,
                    info: console.info
                };
            }
            
            // Interceptar para monitoramento
            console.log = function(...args) {
                consoleMessages.push({type: 'log', args: args, time: new Date()});
                updateConsoleMonitor();
                if (typeof originalConsole.log === 'function') {
                    originalConsole.log.apply(console, args);
                }
            };
            
            console.error = function(...args) {
                consoleMessages.push({type: 'error', args: args, time: new Date()});
                updateConsoleMonitor();
                if (typeof originalConsole.error === 'function') {
                    originalConsole.error.apply(console, args);
                }
            };
            
            console.warn = function(...args) {
                consoleMessages.push({type: 'warn', args: args, time: new Date()});
                updateConsoleMonitor();
                if (typeof originalConsole.warn === 'function') {
                    originalConsole.warn.apply(console, args);
                }
            };
        }
        
        function updateConsoleMonitor() {
            const monitor = document.getElementById('console-monitor');
            if (consoleMessages.length === 0) {
                monitor.innerHTML = 'Console limpo - nenhuma mensagem detectada ✅';
            } else {
                const messages = consoleMessages.slice(-10).map(msg => {
                    const time = msg.time.toLocaleTimeString();
                    const type = msg.type.toUpperCase();
                    const content = msg.args.join(' ');
                    return `[${time}] ${type}: ${content}`;
                }).join('\n');
                monitor.innerHTML = messages;
            }
        }
        
        function updateSystemStatus() {
            const statusElement = document.getElementById('system-status');
            
            let status = '<strong>🔍 Configurações Atuais:</strong><br><br>';
            
            // Verificar se CONSOLE_LOGS_ENABLED existe
            if (typeof CONSOLE_LOGS_ENABLED !== 'undefined') {
                status += `• CONSOLE_LOGS_ENABLED: <span style="color: ${CONSOLE_LOGS_ENABLED ? '#f44336' : '#4CAF50'};">${CONSOLE_LOGS_ENABLED ? 'true (logs ativos)' : 'false (logs silenciados)'}</span><br>`;
            } else {
                status += '• CONSOLE_LOGS_ENABLED: <span style="color: #ffc107;">não definido</span><br>';
            }
            
            // Verificar funções de restauração
            status += '• restoreConsole: ' + (typeof restoreConsole === 'function' ? '✅ Disponível' : '❌ Não disponível') + '<br>';
            status += '• disableConsole: ' + (typeof disableConsole === 'function' ? '✅ Disponível' : '❌ Não disponível') + '<br>';
            
            // Status do console
            status += `• Console interceptado: ${consoleMessages.length > 0 ? '⚠️ Ativo' : '✅ Silenciado'}<br>`;
            status += `• Mensagens capturadas: ${consoleMessages.length}<br>`;
            
            statusElement.innerHTML = status;
        }
        
        function testarConsoleLogs() {
            const results = document.getElementById('test-results');
            let output = '<strong>📝 Teste de Console Logs:</strong><br><br>';
            
            const initialCount = consoleMessages.length;
            
            // Tentar vários tipos de console
            console.log('🧪 Teste de console.log');
            console.error('🧪 Teste de console.error');
            console.warn('🧪 Teste de console.warn');
            console.info('🧪 Teste de console.info');
            
            const finalCount = consoleMessages.length;
            const newMessages = finalCount - initialCount;
            
            output += `• Mensagens enviadas: 4<br>`;
            output += `• Mensagens capturadas: ${newMessages}<br>`;
            
            if (newMessages === 0) {
                output += '<span style="color: #4CAF50;">✅ Console completamente silenciado!</span><br>';
            } else {
                output += '<span style="color: #f44336;">⚠️ Alguns logs ainda estão passando</span><br>';
            }
            
            results.innerHTML = output;
        }
        
        function testarFuncoes() {
            const results = document.getElementById('test-results');
            let output = '<strong>⚡ Teste de Funções:</strong><br><br>';
            
            // Testar funções de debug se existirem
            if (typeof debugLog === 'function') {
                output += '• debugLog: ✅ Disponível<br>';
                debugLog('🧪 Teste de debugLog');
            } else {
                output += '• debugLog: ❌ Não disponível<br>';
            }
            
            if (typeof debugError === 'function') {
                output += '• debugError: ✅ Disponível<br>';
                debugError('🧪 Teste de debugError');
            } else {
                output += '• debugError: ❌ Não disponível<br>';
            }
            
            results.innerHTML = output;
        }
        
        function simularAtividade() {
            const results = document.getElementById('test-results');
            let output = '<strong>🎮 Simulando Atividade do Jogo:</strong><br><br>';
            
            const initialCount = consoleMessages.length;
            
            // Simular várias ações que normalmente gerariam logs
            try {
                // Simular carregamento de dados
                if (typeof loadGameData === 'function') {
                    output += '• loadGameData: Tentando executar...<br>';
                }
                
                // Simular verificação de auth
                if (typeof checkAuthState === 'function') {
                    output += '• checkAuthState: Tentando executar...<br>';
                }
                
                // Simular fetch API
                output += '• Simulando requisições...<br>';
                
            } catch (e) {
                output += `• Erro durante simulação: ${e.message}<br>`;
            }
            
            const finalCount = consoleMessages.length;
            const newMessages = finalCount - initialCount;
            
            output += `<br>• Logs gerados durante simulação: ${newMessages}<br>`;
            
            if (newMessages === 0) {
                output += '<span style="color: #4CAF50;">✅ Nenhum log vazou durante a atividade!</span>';
            } else {
                output += '<span style="color: #ffc107;">⚠️ Alguns logs foram detectados</span>';
            }
            
            results.innerHTML = output;
        }
        
        function restaurarConsole() {
            if (typeof restoreConsole === 'function') {
                restoreConsole();
                document.getElementById('test-results').innerHTML = '🔧 Console restaurado para debug!';
            } else {
                document.getElementById('test-results').innerHTML = '❌ Função restoreConsole não disponível';
            }
        }
        
        function desabilitarConsole() {
            if (typeof disableConsole === 'function') {
                disableConsole();
                document.getElementById('test-results').innerHTML = '🔇 Console desabilitado novamente!';
            } else {
                document.getElementById('test-results').innerHTML = '❌ Função disableConsole não disponível';
            }
        }
        
        // Inicialização
        window.onload = function() {
            setupConsoleMonitoring();
            updateSystemStatus();
            updateConsoleMonitor();
            
            // Atualizar status a cada 3 segundos
            setInterval(updateSystemStatus, 3000);
            
            console.log('🌐 Página de teste de remoção de console carregada');
        };
    </script>
</body>
</html>
