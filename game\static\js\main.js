// === CONFIGURAÇÃO DE PRODUÇÃO ===
const PRODUCTION_MODE = true; // Altere para false apenas para desenvolvimento
const DEBUG_MODE = false; // Logs de debug específicos

// === SISTEMA DE INTERCEPTAÇÃO COMPLETA DE CONSOLE ===
(function() {
    'use strict';

    if (PRODUCTION_MODE) {
        // Salvar referências originais para possível restauração
        const originalConsole = {
            log: console.log.bind(console),
            error: console.error.bind(console),
            warn: console.warn.bind(console),
            info: console.info.bind(console),
            debug: console.debug.bind(console),
            trace: console.trace.bind(console),
            group: console.group ? console.group.bind(console) : function(){},
            groupEnd: console.groupEnd ? console.groupEnd.bind(console) : function(){},
            table: console.table ? console.table.bind(console) : function(){},
            time: console.time ? console.time.bind(console) : function(){},
            timeEnd: console.timeEnd ? console.timeEnd.bind(console) : function(){},
            count: console.count ? console.count.bind(console) : function(){},
            clear: console.clear ? console.clear.bind(console) : function(){}
        };

        // Função vazia para substituir todos os console logs
        const emptyFunction = function() {};

        // Substituir TODAS as funções de console por funções vazias
        console.log = emptyFunction;
        console.error = emptyFunction;
        console.warn = emptyFunction;
        console.info = emptyFunction;
        console.debug = emptyFunction;
        console.trace = emptyFunction;
        console.group = emptyFunction;
        console.groupEnd = emptyFunction;
        console.table = emptyFunction;
        console.time = emptyFunction;
        console.timeEnd = emptyFunction;
        console.count = emptyFunction;
        console.clear = emptyFunction;

        // Interceptar também outras funções de console
        if (console.dir) console.dir = emptyFunction;
        if (console.dirxml) console.dirxml = emptyFunction;
        if (console.assert) console.assert = emptyFunction;
        if (console.profile) console.profile = emptyFunction;
        if (console.profileEnd) console.profileEnd = emptyFunction;

        // Função para restaurar console (apenas para debug emergencial)
        window.restoreConsole = function() {
            Object.assign(console, originalConsole);
            console.log('Console restaurado para debug emergencial');
            console.warn('Lembre-se de desabilitar novamente em produção');
        };

        // Função para desabilitar console novamente
        window.disableConsole = function() {
            console.log = emptyFunction;
            console.error = emptyFunction;
            console.warn = emptyFunction;
            console.info = emptyFunction;
            console.debug = emptyFunction;
            console.trace = emptyFunction;
            console.group = emptyFunction;
            console.groupEnd = emptyFunction;
            console.table = emptyFunction;
            console.time = emptyFunction;
            console.timeEnd = emptyFunction;
            console.count = emptyFunction;
            console.clear = emptyFunction;
            if (console.dir) console.dir = emptyFunction;
            if (console.dirxml) console.dirxml = emptyFunction;
            if (console.assert) console.assert = emptyFunction;
            if (console.profile) console.profile = emptyFunction;
            if (console.profileEnd) console.profileEnd = emptyFunction;
        };

        // Armazenar referências originais para acesso interno se necessário
        window._originalConsole = originalConsole;
    }
})();

// === FUNÇÕES DE DEBUG CONDICIONAIS ===
function debugLog(...args) {
    if (DEBUG_MODE && !PRODUCTION_MODE) {
        window._originalConsole?.log(...args);
    }
}

function debugError(...args) {
    if (DEBUG_MODE && !PRODUCTION_MODE) {
        window._originalConsole?.error(...args);
    }
}

function debugWarn(...args) {
    if (DEBUG_MODE && !PRODUCTION_MODE) {
        window._originalConsole?.warn(...args);
    }
}

function debugInfo(...args) {
    if (DEBUG_MODE && !PRODUCTION_MODE) {
        window._originalConsole?.info(...args);
    }
}

// --- FUNÇÃO PARA MOSTRAR BOTÃO DO BANCO APÓS BRUTEFORCE EM CONEXÃO ---
function showBankButtonInConnection(alvoIp, alvoDados) {
    // Atualiza a conexão específica com botão do banco
    updateConnectionWithBankButton(alvoIp, alvoDados);
}

// Torna a função global
window.showBankButtonInConnection = showBankButtonInConnection;
// SISTEMA SIMPLES SEM FIREBASE - MIGRAÇÃO PARA SUPABASE
// Auth disponível globalmente via simple-auth.js


// --- VARIÁVEIS DE ESTADO ---
let currentPlayer = null;
let exploitedTarget = null;
let chatUnsubscribe = null;
let lastMessage = null;
let csrfToken = null; // Token CSRF global

// Sistema de transferência bancária
let selectedPercentage = null;
let calculatedTransferAmount = 0;

// Sistema de proteção de chat
let lastChatMessage = '';
let lastChatTime = 0;
let isSendingMessage = false;
const CHAT_COOLDOWN = 10; // 100ms - delay ultra-baixo para máxima responsividade

// Sistema de segurança
let securityUpdateInterval = null;
let lastSecurityCheck = 0;
let invasionNotificationInterval = null;
let lastKnownInvasions = new Set();
let isSecurityMonitoringActive = false;

// Sistema de monitoramento de conexões
let connectionMonitorInterval = null;
// Objeto para armazenar timers ativos
const activeTimers = new Map();
// --- CACHE E PERFORMANCE ---
let playerDataCache = null;
let lastPlayerDataUpdate = 0;
const CACHE_DURATION = 15000; // 15 segundos (reduzido para melhor responsividade)
const renderedSections = new Set();
const pendingRequests = new Map(); // Evita requisições duplicadas

// Sistema de invalidação inteligente do cache
let cacheInvalidated = false;
function invalidateCache(reason = 'manual') {
    console.log(`Cache invalidado: ${reason}`);
    playerDataCache = null;
    lastPlayerDataUpdate = 0;
    cacheInvalidated = true;
    
    // Remove seções renderizadas que dependem dos dados do jogador
    renderedSections.delete('appstore-section');
    renderedSections.delete('mineradora-section');
    renderedSections.delete('mercado-negro-section');
    renderedSections.delete('habilidades-section');
    renderedSections.delete('banco-section');
}

// Função para forçar atualização manual dos dados
async function forceDataRefresh(showNotification = true) {
    try {
        if (showNotification) {
            console.log('Forçando atualização manual dos dados...');
        }
        invalidateCache('manual-refresh');
        await loadGameData(true);
        
        // Re-renderiza a seção atual se necessário
        const currentSection = document.querySelector('.game-section[style*="display: block"]');
        if (currentSection) {
            const sectionId = currentSection.id;
            if (renderedSections.has(sectionId)) {
                renderedSections.delete(sectionId);
                showSection(sectionId);
            }
        }
        
        if (showNotification) {
            console.log('Dados atualizados com sucesso');
        }
    } catch (error) {
        console.error('Erro ao forçar atualização:', error);
        if (showNotification) {
            showNotification('Erro ao atualizar dados', 'error');
        }
    }
}

// Expor função para debugging
window.forceDataRefresh = forceDataRefresh;
window.invalidateCache = invalidateCache;
window.getCacheStatus = () => ({
    playerDataCache: !!playerDataCache,
    lastUpdate: new Date(lastPlayerDataUpdate).toLocaleString(),
    cacheAge: Date.now() - lastPlayerDataUpdate,
    isExpired: (Date.now() - lastPlayerDataUpdate) > CACHE_DURATION,
    pendingRequests: Array.from(pendingRequests.keys()),
    renderedSections: Array.from(renderedSections),
    isOnline,
    isPageVisible
});

// Função para atualizar indicador de cooldown do chat
function updateChatCooldownIndicator() {
    const chatForm = document.getElementById('chat-form');
    if (!chatForm) return;
    
    const now = Date.now();
    const timeSinceLastMessage = now - lastChatTime;
    const remainingCooldown = CHAT_COOLDOWN - timeSinceLastMessage;
    
    const submitBtn = chatForm.querySelector('button[type="submit"]');
    if (!submitBtn) return;
    
    if (isSendingMessage) {
        submitBtn.disabled = true;
        submitBtn.innerHTML = 'Enviando...';
        submitBtn.style.opacity = '0.6';
    } else if (remainingCooldown > 0) {
        // Para delays muito baixos, mostra em milissegundos para melhor feedback
        if (remainingCooldown < 1000) {
            const ms = Math.ceil(remainingCooldown / 10) * 10; // Arredonda para 10ms
            submitBtn.disabled = true;
            submitBtn.innerHTML = `Aguarde ${ms}ms`;
            submitBtn.style.opacity = '0.7';
        } else {
            const seconds = (remainingCooldown / 1000).toFixed(1);
            submitBtn.disabled = true;
            submitBtn.innerHTML = `Aguarde ${seconds}s`;
            submitBtn.style.opacity = '0.7';
        }
    } else {
        submitBtn.disabled = false;
        submitBtn.innerHTML = `
            <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M6 12L3.269 3.126A59.768 59.768 0 0121.485 12 59.77 59.77 0 013.27 20.876L5.999 12zm0 0h7.5" />
            </svg>
            Enviar
        `;
        submitBtn.style.opacity = '1';
    }
}

// Função auxiliar para restaurar o HTML original do botão
function restoreChatButtonHTML(button) {
    button.innerHTML = `
        <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="M6 12L3.269 3.126A59.768 59.768 0 0121.485 12 59.77 59.77 0 713.27 20.876L5.999 12zm0 0h7.5" />
        </svg>
        Enviar
    `;
}

// Função para configurar contador de caracteres no chat
function setupChatCharacterCounter() {
    const chatInput = document.getElementById('chat-input');
    if (!chatInput) return;
    
    // Cria indicador se não existir
    let charCounter = document.getElementById('chat-char-counter');
    if (!charCounter) {
        charCounter = document.createElement('div');
        charCounter.id = 'chat-char-counter';
        charCounter.className = 'text-xs text-secondary-text text-right mt-1';
        chatInput.parentNode.appendChild(charCounter);
    }
    
    // Função para atualizar contador
    const updateCounter = () => {
        const length = chatInput.value.length;
        const maxLength = 200;
        charCounter.textContent = `${length}/${maxLength}`;
        
        if (length > maxLength * 0.9) {
            charCounter.className = 'text-xs text-red-400 text-right mt-1';
        } else if (length > maxLength * 0.7) {
            charCounter.className = 'text-xs text-yellow-400 text-right mt-1';
        } else {
            charCounter.className = 'text-xs text-secondary-text text-right mt-1';
        }
    };
    
    // Event listeners
    chatInput.addEventListener('input', updateCounter);
    chatInput.addEventListener('keydown', (e) => {
        // Permite envio com Enter (mas não Shift+Enter)
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            const form = chatInput.closest('form');
            if (form) {
                form.dispatchEvent(new Event('submit'));
            }
        }
    });
    
    // Atualização inicial
    updateCounter();
}

// Atualiza indicador de cooldown do chat a cada 25ms para resposta instantânea
setInterval(updateChatCooldownIndicator, 25);

// Configurar contador de caracteres quando o chat estiver disponível
document.addEventListener('DOMContentLoaded', () => {
    setTimeout(setupChatCharacterCounter, 1000);
});

// Funções utilitárias para o chat
function resetChatCooldown() {
    lastChatTime = 0;
    lastChatMessage = '';
    console.log('Cooldown do chat resetado');
}

function getChatStatus() {
    const now = Date.now();
    const timeSinceLastMessage = now - lastChatTime;
    const remainingCooldown = Math.max(0, CHAT_COOLDOWN - timeSinceLastMessage);
    
    // ...existing code...
    // Remover qualquer uso de await aqui, pois a função não é async
}

window.comprarHabilidadeNFT = comprarHabilidadeNFT;

// Sistema de monitoramento de conectividade
let isOnline = navigator.onLine;
let lastOnlineCheck = Date.now();

function checkConnectivity() {
    const wasOnline = isOnline;
    isOnline = navigator.onLine;
    
    if (!wasOnline && isOnline) {
        console.log('Conectividade restaurada');
        showNotification('Conexão restaurada - atualizando dados', 'success');
        forceDataRefresh(false);
    } else if (wasOnline && !isOnline) {
        console.log('Perda de conectividade detectada');
        showNotification('Conexão perdida - usando dados locais', 'warning');
    }
    
    lastOnlineCheck = Date.now();
}

// Monitora mudanças de conectividade
window.addEventListener('online', checkConnectivity);
window.addEventListener('offline', checkConnectivity);

// Verifica conectividade periodicamente
setInterval(() => {
    if (Date.now() - lastOnlineCheck > 30000) {
        checkConnectivity();
    }
}, 30000);

// Atualiza indicador de status a cada segundo
setInterval(() => {
    if (currentPlayer && document.getElementById('main-dashboard') && 
        document.getElementById('main-dashboard').style.display !== 'none') {
        updateDataStatusIndicator();
    }
}, 1000);

// Debouncing para botões
const debouncedActions = new Map();
function debounce(func, delay = 500) {
    return function(...args) {
        const key = func.name || 'anonymous';
        clearTimeout(debouncedActions.get(key));
        debouncedActions.set(key, setTimeout(() => func.apply(this, args), delay));
    };
}

// Função para obter token CSRF - Versão melhorada
async function getCsrfToken(forceRefresh = false) {
    if (csrfToken && !forceRefresh) {
        console.log('Usando token CSRF em cache:', csrfToken.substring(0, 10) + '...');
        return csrfToken;
    }
    
    try {
        console.log('Obtendo novo token CSRF...');
        const response = await fetch('/api/csrf-token', {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'Cache-Control': 'no-cache'
            },
            credentials: 'same-origin' // Importante para incluir cookies de sessão
        });
        
        if (response.ok) {
            const data = await response.json();
            if (data.sucesso && data.csrf_token) {
                csrfToken = data.csrf_token;
                console.log('Token CSRF obtido:', csrfToken.substring(0, 10) + '...');
                return csrfToken;
            }
        }
        
        console.warn('Não foi possível obter token CSRF, usando token dummy');
        csrfToken = 'dummy_token';
        return csrfToken;
    } catch (error) {
        console.error('Erro ao obter token CSRF:', error);
        csrfToken = 'dummy_token';
        return csrfToken;
    }
}

// Função para renovar token CSRF quando necessário
async function refreshCsrfToken() {
    console.log('Renovando token CSRF...');
    csrfToken = null;
    return await getCsrfToken(true);
}

// Função para prevenir cliques duplos
function preventDoubleClick(buttonId, action, delay = 1000) {
    const button = document.getElementById(buttonId);
    if (!button) return;
    
    if (button.disabled) return;
    
    button.disabled = true;
    button.style.opacity = '0.6';
    
    setTimeout(() => {
        button.disabled = false;
        button.style.opacity = '1';
    }, delay);
    
    return action();
}

// Função helper para loading states
function showLoadingSpinner(containerId, message = 'Carregando...') {
    const container = document.getElementById(containerId);
    if (!container) return;
    
    container.innerHTML = `
        <div class="text-center py-8">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-accent-blue"></div>
            <p class="text-secondary-text mt-2">${message}</p>
        </div>
    `;
}

function hideLoadingSpinner(containerId) {
    const container = document.getElementById(containerId);
    if (!container) return;
    
    const spinner = container.querySelector('.animate-spin');
    if (spinner) {
        spinner.parentElement.remove();
    }
}
// --- FUNÇÃO DE API (HELPER) ---
async function fetchAPI(endpoint, method = 'GET', body = null, retries = 2) {
    // Aguarda inicialização do auth se necessário
    if (typeof auth === 'undefined' || !auth) {
        await new Promise(resolve => setTimeout(resolve, 100));
        if (typeof auth === 'undefined' || !auth) {
            throw new Error("Sistema de autenticação não disponível.");
        }
    }
    
    // Verifica se o usuário está autenticado
    const session = auth.getSession();
    if (!session.user) {
        // Tenta recarregar sessão antes de falhar
        auth.loadSession();
        const retrySession = auth.getSession();
        if (!retrySession.user) {
            console.error("Usuário não autenticado - redirecionando para login");
            // Força exibição da tela de autenticação
            if (typeof checkAuthState === 'function') {
                checkAuthState();
            }
            throw new Error("Usuário não autenticado.");
        }
    }
    
    // Cria chave única para a requisição
    const requestKey = `${method}-${endpoint}-${JSON.stringify(body)}`;
    
    // Verifica se já existe uma requisição pendente idêntica
    if (pendingRequests.has(requestKey)) {
        return await pendingRequests.get(requestKey);
    }
    
    const csrf_token = await getCsrfToken();
    
    console.log('Enviando requisição com CSRF token:', csrf_token.substring(0, 10) + '...');
    
    const options = {
        method,
        headers: { 
            'Content-Type': 'application/json',
            'Cache-Control': 'no-cache',
            'X-CSRF-TOKEN': csrf_token
        },
        credentials: 'same-origin', // Importante para incluir cookies de sessão
        timeout: 10000 // 10 segundos de timeout
    };
    
    // Se há body, adicionar token CSRF também no body para requisições POST
    if (body) {
        if (typeof body === 'object') {
            body.csrf_token = csrf_token;
        }
        options.body = JSON.stringify(body);
        console.log('Body da requisição:', options.body);
    }
    
    const requestPromise = (async () => {
        let lastError;
        
        for (let attempt = 0; attempt <= retries; attempt++) {
            try {
                console.log(`Tentativa ${attempt + 1}/${retries + 1} para ${method} ${endpoint}`);
                
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), 10000);
                
                const response = await fetch(endpoint, {
                    ...options,
                    signal: controller.signal
                });
                
                clearTimeout(timeoutId);
                
                // Verifica se a resposta é HTML (erro 500)
                const contentType = response.headers.get('content-type');
                if (contentType && contentType.includes('text/html')) {
                    const htmlText = await response.text();
                    console.error('Erro do servidor (HTML):', htmlText);
                    throw new Error(`Erro interno do servidor. Status: ${response.status}`);
                }
                
                if (!response.ok) {
                    const errorData = await response.json();
                    
                    // Se é erro de CSRF, tentar renovar token e fazer nova tentativa
                    if (response.status === 403 && 
                        (errorData.codigo === 'CSRF_TOKEN_REQUIRED' || 
                         errorData.mensagem?.includes('CSRF'))) {
                        
                        console.log('Erro CSRF detectado, renovando token...');
                        
                        // Se o servidor forneceu um novo token, usar ele
                        if (errorData.csrf_token) {
                            csrfToken = errorData.csrf_token;
                            console.log('Novo token CSRF recebido do servidor');
                        } else {
                            // Senão, renovar token
                            await refreshCsrfToken();
                        }
                        
                        // Atualizar header e body com novo token
                        const new_csrf_token = await getCsrfToken();
                        options.headers['X-CSRF-TOKEN'] = new_csrf_token;
                        
                        if (body && typeof body === 'object') {
                            body.csrf_token = new_csrf_token;
                            options.body = JSON.stringify(body);
                        }
                        
                        console.log('Tentando novamente com novo token CSRF...');
                        // Não contar esta tentativa, apenas renovar token
                        attempt--;
                        continue;
                    }
                    
                    throw new Error(errorData.mensagem || `Erro de API: ${response.status}`);
                }
                
                const result = await response.json();
                console.log(`Requisição bem-sucedida: ${method} ${endpoint}`);
                return result;
                
            } catch (error) {
                lastError = error;
                console.warn(`Tentativa ${attempt + 1} falhou:`, error.message);
                
                // Se é o último retry, não tenta novamente
                if (attempt === retries) {
                    break;
                }
                
                // Aguarda um pouco antes de tentar novamente
                await new Promise(resolve => setTimeout(resolve, 1000 * (attempt + 1)));
            }
        }
        
        throw lastError;
    })();
    
    // Armazena a promise para evitar requisições duplicadas
    pendingRequests.set(requestKey, requestPromise);
    
    try {
        return await requestPromise;
    } finally {
        // Remove a requisição do map quando finalizar
        pendingRequests.delete(requestKey);
    }
}

// --- FUNÇÃO PARA CARREGAR DADOS DO JOGO ---
async function loadGameData(forceRefresh = false) {
    const now = Date.now();
    
    // Força refresh se o cache foi invalidado ou se é forçado
    if (cacheInvalidated) {
        forceRefresh = true;
        cacheInvalidated = false;
        console.log('Forçando refresh devido à invalidação do cache');
    }
    
    // Usa cache se disponível e não forçou refresh
    if (!forceRefresh && playerDataCache && (now - lastPlayerDataUpdate) < CACHE_DURATION) {
        console.log('Usando dados do cache');
        currentPlayer = playerDataCache;
        renderDashboard();
        return playerDataCache;
    }
    
    // Evita requisições duplicadas
    const cacheKey = 'jogador-data';
    if (pendingRequests.has(cacheKey)) {
        console.log('Aguardando requisição pendente');
        return await pendingRequests.get(cacheKey);
    }
    
    const dataPromise = (async () => {
        try {
            console.log('Buscando dados atualizados do servidor');
            const data = await fetchAPI('/api/jogador');
            if (data.sucesso) {
                currentPlayer = data.jogador;
                playerDataCache = data.jogador;
                lastPlayerDataUpdate = now;

                // Coletar mineração offline automaticamente
                coletarMineracaoOffline();

                renderDashboard();
                console.log('Dados atualizados com sucesso');
                return data.jogador;
            } else {
                throw new Error(data.mensagem || 'Resposta inválida do servidor');
            }
        } catch (error) {
            console.error("Erro ao carregar dados:", error);
            
            // Se temos cache e erro não é crítico, usa cache mesmo que expirado
            if (playerDataCache && !error.message.includes('não autenticado')) {
                console.log('Usando cache expirado devido ao erro');
                currentPlayer = playerDataCache;
                renderDashboard();
                showNotification("Erro ao atualizar dados - usando dados locais", "warning");
                return playerDataCache;
            }
            
            showNotification("Erro ao carregar dados do jogador", "error");
            throw error;
        } finally {
            pendingRequests.delete(cacheKey);
        }
    })();
    
    pendingRequests.set(cacheKey, dataPromise);
    return await dataPromise;
}

// --- SISTEMA DE ATUALIZAÇÃO AUTOMÁTICA INTELIGENTE ---
let updateInterval = null;
let isPageVisible = true;
let lastUpdateTime = 0;

// Detecta quando a página está visível
document.addEventListener('visibilitychange', function() {
    isPageVisible = !document.hidden;
    
    if (isPageVisible && Date.now() - lastUpdateTime > 30000) {
        // Se a página voltou a ficar visível e faz mais de 30 segundos que não atualiza
        console.log('Página visível novamente - atualizando dados');
        invalidateCache('visibilidade');
        loadGameData(true); // Força refresh
    }
});

function iniciarAtualizacaoAutomatica() {
    // Limpa interval anterior se existir
    if (updateInterval) {
        clearInterval(updateInterval);
    }
    
    // Atualiza a cada 90 segundos, mas só se a página estiver visível
    updateInterval = setInterval(() => {
        if (isPageVisible && auth.currentUser) {
            lastUpdateTime = Date.now();
            console.log('Atualização automática iniciada');
            
            // Se o cache está muito antigo (mais de 60 segundos), força refresh
            const cacheAge = Date.now() - lastPlayerDataUpdate;
            const forceRefresh = cacheAge > 60000;
            
            if (forceRefresh) {
                console.log('Cache muito antigo, forçando refresh');
                invalidateCache('cache-expirado');
            }
            
            loadGameData(forceRefresh);
        }
    }, 90000); // 90 segundos (mais frequente)
}

function pararAtualizacaoAutomatica() {
    if (updateInterval) {
        clearInterval(updateInterval);
        updateInterval = null;
    }
}

// --- FUNÇÃO PARA INICIALIZAR LISTENERS DE CHAT ---
function iniciarListenerChat() {
    iniciarListenerChatPreview();
}

// --- LISTENER DE ESTADO DE AUTENTICAÇÃO ---
// Responsável APENAS por reagir ao LOGIN e LOGOUT do usuário.
function checkAuthState() {
    const session = auth.getSession();
    const authScreen = document.getElementById('auth-screen');
    const gameScreen = document.getElementById('game-screen');
    const footerNav = document.getElementById('footer-nav');
    const chatPreview = document.getElementById('chat-preview');
    const chatFull = document.getElementById('chat-full');

    if (session.user) {
        // Se o usuário está logado, mostra os elementos do jogo
        authScreen.style.display = 'none';
        gameScreen.style.display = 'block';
        footerNav.style.display = 'flex';
        
        // Mostra elementos de chat
        if (chatPreview) chatPreview.style.display = 'block';
        if (chatFull) chatFull.style.display = 'none';
        
        // Carrega os dados e inicia o chat
        showSection('main-dashboard');

        // Obter token CSRF primeiro, depois carregar dados
        getCsrfToken().then(() => {
            console.log('Token CSRF obtido com sucesso');
            // Só carrega dados depois de ter o token CSRF
            return loadGameData();
        }).then(() => {
            console.log('Dados do jogo carregados com sucesso');

            // Iniciar monitoramento global de invasões (sempre ativo)
            if (typeof startGlobalInvasionMonitoring === 'function') {
                startGlobalInvasionMonitoring();
                console.log('Monitoramento de invasões iniciado');
            }
        }).catch(error => {
            console.warn('Erro ao carregar dados do jogo:', error);
        });
        
        // Inicia listeners e atualização automática
        iniciarListenerChat();
        iniciarAtualizacaoAutomatica();
    } else {
        // Se o usuário está deslogado, mostra a tela de autenticação
        currentPlayer = null;
        exploitedTarget = null;
        pararListenerChat();
        pararAtualizacaoAutomatica();
        
        // Limpa cache
        playerDataCache = null;
        lastPlayerDataUpdate = 0;
        cacheInvalidated = false;
        renderedSections.clear();
        pendingRequests.clear();
        
        // Limpa variáveis do chat
        lastChatMessage = '';
        lastChatTime = 0;
        isSendingMessage = false;
        
        authScreen.style.display = 'block';
        gameScreen.style.display = 'none';
        footerNav.style.display = 'none';
        
        // Esconde elementos de chat
        if (chatPreview) chatPreview.style.display = 'none';
        if (chatFull) chatFull.style.display = 'none';
    }
}

// Chama a verificação inicial e configura listeners
checkAuthState();

// Torna a função global para que o simple-auth.js possa chamá-la
window.checkAuthState = checkAuthState;

// --- CONFIGURAÇÃO DE EVENT LISTENERS QUANDO O DOM ESTIVER PRONTO ---
document.addEventListener('DOMContentLoaded', function() {
    // Carrega configurações salvas automaticamente
    autoLoadUserSettings();
    
    // Event listeners para os formulários de autenticação
    const formLogin = document.getElementById('form-login');
    const formCadastro = document.getElementById('form-cadastro');
    
    if (formLogin) {
        formLogin.addEventListener('submit', async function(e) {
            e.preventDefault();
            const formData = new FormData(e.target);
            const email = formData.get('email');
            const password = formData.get('password');
            
            try {
                await loginUser(email, password);
            } catch (error) {
                console.error('Erro no login:', error);
                showNotification('Erro no login: ' + error.message, 'error');
            }
        });
    }
    
    if (formCadastro) {
        formCadastro.addEventListener('submit', async function(e) {
            e.preventDefault();
            const formData = new FormData(e.target);
            const email = formData.get('email');
            const password = formData.get('password');
            const nick = formData.get('nick');
            
            try {
                await registerUser(email, password, nick);
            } catch (error) {
                console.error('Erro no cadastro:', error);
                showNotification('Erro no cadastro: ' + error.message, 'error');
            }
        });
    }
    
    // Event listeners para botões do dashboard
    document.querySelectorAll('.dashboard-btn').forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            const sectionId = this.getAttribute('data-section-id');
            if (sectionId) {
                showSection(sectionId);
            }
        });
    });
    
    // Event listeners para navegação do footer
    document.querySelectorAll('.nav-item').forEach(item => {
        item.addEventListener('click', function(e) {
            e.preventDefault();
            const sectionId = this.getAttribute('data-section-id');
            if (sectionId) {
                showSection(sectionId);
            }
        });
    });
    
    // Event listeners para o chat
    const chatPreview = document.getElementById('chat-preview');
    if (chatPreview) {
        chatPreview.addEventListener('click', toggleChat);
    }

    const closeChatBtn = document.getElementById('close-chat-btn');
    if (closeChatBtn) {
        closeChatBtn.addEventListener('click', closeChat);
    }

    // Inicializar chat preview
    iniciarListenerChatPreview();

    const chatForm = document.getElementById('chat-form');
    if (chatForm) {
        chatForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            const messageInput = document.getElementById('chat-input');
            const message = messageInput.value.trim();

            if (message) {
                // Verifica se já está enviando uma mensagem
                if (isSendingMessage) {
                    showNotification('Aguarde, enviando mensagem anterior...', 'warning');
                    return;
                }

                try {
                    isSendingMessage = true;

                    // Feedback visual imediato
                    const submitBtn = chatForm.querySelector('button[type="submit"]');
                    if (submitBtn) {
                        submitBtn.disabled = true;
                        submitBtn.innerHTML = 'Enviando...';
                        submitBtn.style.opacity = '0.6';
                    }

                    const response = await fetchAPI('/api/chat/enviar', 'POST', { texto: message });

                    if (response.sucesso) {
                        // Sucesso - limpa o input
                        messageInput.value = '';

                        // Feedback visual de sucesso
                        if (submitBtn) {
                            submitBtn.style.backgroundColor = '#10b981';
                            submitBtn.innerHTML = 'Enviado';
                            setTimeout(() => {
                                submitBtn.style.backgroundColor = '';
                                submitBtn.innerHTML = `
                                    <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M6 12L3.269 3.126A59.768 59.768 0 0121.485 12 59.77 59.77 0 013.27 20.876L5.999 12zm0 0h7.5" />
                                    </svg>
                                    Enviar
                                `;
                            }, 1000);
                        }

                        // Atualizar mensagens imediatamente
                        setTimeout(() => {
                            carregarMensagensChat(false);
                        }, 100);

                    } else {
                        // Erro do servidor (anti-spam, etc.)
                        showNotification(response.mensagem || 'Erro ao enviar mensagem', 'error');
                    }

                } catch (error) {
                    console.error('Erro ao enviar mensagem:', error);
                    showNotification('Erro de conexão ao enviar mensagem', 'error');
                } finally {
                    isSendingMessage = false;

                    // Restaura o botão após um pequeno delay
                    setTimeout(() => {
                        const submitBtn = chatForm.querySelector('button[type="submit"]');
                        if (submitBtn) {
                            submitBtn.disabled = false;
                            submitBtn.style.opacity = '1';
                            submitBtn.style.backgroundColor = '';
                            submitBtn.innerHTML = `
                                <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M6 12L3.269 3.126A59.768 59.768 0 0721.485 12 59.77 59.77 0 013.27 20.876L5.999 12zm0 0h7.5" />
                                </svg>
                                Enviar
                            `;
                        }
                    }, 500);
                }
            }
        });
    }

    // Event listener para o botão de fechar chat já foi adicionado acima
});

// === EVENT LISTENER GLOBAL PARA TODOS OS CLIQUES ===
document.addEventListener('click', function(e) {
    // Botões de upgrade da App Store
    if (e.target.classList.contains('upgrade-btn')) {
        const item = e.target.getAttribute('data-item');
        const quantity = parseInt(e.target.getAttribute('data-quantity'));
        
        if (!item || !quantity) {
            showNotification('Erro: Dados do upgrade inválidos', 'error');
            return;
        }
        
        handleUpgradePurchase(item, quantity);
        return;
    }
    
    // Botão de scan rápido
    if (e.target.id === 'quick-scan-btn') {
        handleQuickScan();
        return;
    }
    
    // Botões de exploitar alvos
    if (e.target.classList.contains('exploit-btn')) {
        const targetData = JSON.parse(e.target.getAttribute('data-target'));
        handleExploit(targetData);
        return;
    }
    
    // Botões da tela de invasão (usando closest para capturar cliques em elementos filhos)
    let targetButton = e.target.closest('#access-bank-btn, #deface-btn, #view-log-btn, #back-to-scan-btn');
    
    if (targetButton) {
        console.log('Botão da tela de invasão clicado:', targetButton.id);
        
        if (targetButton.id === 'access-bank-btn') {
            handleAccessBank();
            return;
        }
        
        if (targetButton.id === 'deface-btn') {
            handleDeface();
            return;
        }
        
        if (targetButton.id === 'view-log-btn') {
            handleViewLog();
            return;
        }
        
        if (targetButton.id === 'back-to-scan-btn') {
            handleBackToScan();
            return;
        }
    }
    
    // Fallback para compatibilidade com versão antiga
    if (e.target.id === 'access-bank-btn') {
        handleAccessBank();
        return;
    }
    
    if (e.target.id === 'deface-btn') {
        handleDeface();
        return;
    }
    
    if (e.target.id === 'view-log-btn') {
        handleViewLog();
        return;
    }
    
    if (e.target.id === 'back-to-scan-btn') {
        handleBackToScan();
        return;
    }
    
    // Botões de seleção de porcentagem (novo sistema)
    let percentageButton = e.target.closest('.percentage-btn');
    if (percentageButton) {
        handlePercentageSelection(percentageButton);
        return;
    }
    
    // Botão de confirmar transferência
    if (e.target.id === 'confirm-transfer-btn' || e.target.closest('#confirm-transfer-btn')) {
        handleConfirmTransfer();
        return;
    }
    
    // Botões da seção Mineradora
    if (e.target.id === 'buy-rig-btn') {
        handleBuyRig();
        return;
    }
    
    // Botão de coletar Shacks (novo sistema)
    if (e.target.id === 'coletar-shacks-btn' || e.target.closest('#coletar-shacks-btn')) {
        handleColetarShacks();
        return;
    }
    
    // Filtros da seção Log
    if (e.target.classList.contains('log-filter-btn')) {
        const filter = e.target.getAttribute('data-filter');
        handleLogFilter(filter, e.target);
        return;
    }
    
    // Botão de logout
    if (e.target.id === 'btn-logout' || e.target.closest('#btn-logout')) {
        handleLogout();
        return;
    }
    
    // Botões do Mercado Negro
    if (e.target.classList.contains('mercado-negro-btn')) {
        const itemId = e.target.getAttribute('data-item-id');
        handleMercadoNegroCompra(itemId);
        return;
    }
});

// --- FUNÇÕES DE VISUALIZAÇÃO (UI) ---
function showNotification(message, type = 'info') {
    const container = document.getElementById('notification-container');
    if (!container) return;
    const toast = document.createElement('div');
    toast.className = `notification-toast toast-${type}`;
    toast.textContent = message;
    container.appendChild(toast);
    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    }, 4000);
}

/**
 * Alterna a visibilidade entre as telas de login e cadastro.
 */
function toggleAuthView() {
    const loginView = document.getElementById('login-view');
    const registerView = document.getElementById('register-view');
    loginView.style.display = loginView.style.display === 'none' ? 'block' : 'none';
    registerView.style.display = registerView.style.display === 'none' ? 'block' : 'none';
}
window.toggleAuthView = toggleAuthView;

/**
 * Alterna entre as abas da seção grupo
 */
function showGroupTab(tabName) {
    // Remove classe ativa de todos os botões de aba
    document.querySelectorAll('.tab-button').forEach(btn => {
        btn.classList.remove('bg-blue-600', 'text-white');
        btn.classList.add('text-gray-400', 'hover:text-white', 'hover:bg-gray-700');
    });
    
    // Adiciona classe ativa ao botão clicado
    const activeBtn = document.getElementById(`tab-${tabName}`);
    if (activeBtn) {
        activeBtn.classList.add('bg-blue-600', 'text-white');
        activeBtn.classList.remove('text-gray-400', 'hover:text-white', 'hover:bg-gray-700');
    }
    
    // Esconde todas as abas
    document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.add('hidden');
    });
    
    // Mostra a aba selecionada
    const activeContent = document.getElementById(`content-${tabName}`);
    if (activeContent) {
        activeContent.classList.remove('hidden');
    }
}
window.showGroupTab = showGroupTab;

/**
 * Mostra uma seção do jogo e esconde as outras
 */
function showSection(sectionId) {
    // Limpar intervalos de conexão ao sair do terminal
    if (sectionId !== 'terminal-section') {
        limparIntervalosConexao();
    }
    
    document.querySelectorAll('.game-section').forEach(section => section.style.display = 'none');
    const sectionToShow = document.getElementById(sectionId);
    if (sectionToShow) sectionToShow.style.display = 'block';
    
    // Remove classe ativa de todos os itens
    document.querySelectorAll('.nav-item, .dashboard-btn').forEach(item => {
        item.classList.remove('active');
    });
    
    // Adiciona classe ativa ao item correspondente
    const activeItem = document.querySelector(`[data-section-id="${sectionId}"]`);
    if (activeItem) {
        activeItem.classList.add('active');
    }
    
    // Lazy Loading: só renderiza se ainda não foi renderizado
    if (!renderedSections.has(sectionId)) {
        renderedSections.add(sectionId);
        
        switch (sectionId) {
            case 'appstore-section':
                renderAppStore();
                break;
            case 'shop-section':
                renderSupporterShop();
                break;
            case 'scan-section':
                renderScanSection();
                break;
            case 'group-section':
                renderGroupSection();
                break;
            case 'ranking-section':
                renderRankingSection();
                break;
            case 'mineradora-section':
                renderMineradoraSection();
                break;
            case 'mercado-negro-section':
                renderMercadoNegroSection();
                break;
            case 'habilidades-section':
                renderHabilidadesSection();
                break;
            case 'log-section':
                renderLogSection();
                break;
            case 'news-section':
                renderNewsSection();
                break;
            case 'banco-section':
                renderBancoSection();
                break;
            case 'configuracoes-section':
                renderConfiguracoesSection();
                break;
            case 'terminal-section':
                renderTerminalSection();
                break;
            case 'security-section':
                if (typeof startSecurityMonitoring === 'function') {
                    try {
                        startSecurityMonitoring();
                    } catch (error) {
                        console.warn('Erro ao iniciar monitoramento de segurança:', error);
                    }
                }
                break;
        }
    }

    // Parar monitoramento de segurança ao sair da seção
    if (sectionId !== 'security-section' && typeof stopSecurityMonitoring === 'function') {
        try {
            stopSecurityMonitoring();
        } catch (error) {
            console.warn('Erro ao parar monitoramento de segurança:', error);
        }
    }
}

// --- FUNÇÕES DE RENDERIZAÇÃO DE CONTEÚDO ---
function renderDashboard() {
    if (!currentPlayer) return;
    const j = currentPlayer;
    
    // Atualiza informações do jogador
    document.getElementById('player-nick').textContent = j.nick;
    document.getElementById('player-level').textContent = j.nivel;
    document.getElementById('player-money').textContent = `$${j.dinheiro}`;
    document.getElementById('player-shack').textContent = j.shack;
    document.getElementById('player-ip').textContent = j.ip;
    document.getElementById('player-xp').textContent = j.xp;
    document.getElementById('player-xp-needed').textContent = j.xp_necessario;
    document.getElementById('xp-bar').style.width = `${(j.xp / j.xp_necessario) * 100}%`;
    
    // Atualiza indicador de status dos dados
    updateDataStatusIndicator();
}

// Função para atualizar indicador de status dos dados
function updateDataStatusIndicator() {
    const now = Date.now();
    const dataAge = now - lastPlayerDataUpdate;
    const isUsingCache = playerDataCache && dataAge < CACHE_DURATION;
    
    // Cria ou atualiza o indicador se não existir
    let indicator = document.getElementById('data-status-indicator');
    if (!indicator) {
        const playerInfoContainer = document.querySelector('#main-dashboard .bg-surface-elevated');
        if (playerInfoContainer) {
            indicator = document.createElement('div');
            indicator.id = 'data-status-indicator';
            indicator.className = 'absolute top-2 right-2 text-xs opacity-75';
            playerInfoContainer.style.position = 'relative';
            playerInfoContainer.appendChild(indicator);
        }
    }
    
    if (indicator) {
        if (!isOnline) {
            indicator.innerHTML = '<span class="text-red-400">Offline</span>';
        } else if (isUsingCache) {
            const ageSeconds = Math.floor(dataAge / 1000);
            indicator.innerHTML = `<span class="text-green-400">Cache (${ageSeconds}s)</span> <button onclick="forceDataRefresh()" class="ml-1 text-blue-400 hover:text-blue-300"></button>`;
        } else {
            indicator.innerHTML = '<span class="text-blue-400">Atualizado</span>';
        }
    }
}

// --- FUNÇÕES DO CHAT (NOVO SISTEMA SUPABASE) ---
let chatUpdateInterval = null;
let lastChatUpdate = 0;

function iniciarListenerChatPreview() {
    // Carregar última mensagem para preview
    carregarUltimaMensagemPreview();
}

async function carregarUltimaMensagemPreview() {
    try {
        const response = await fetchAPI('/api/chat/mensagens?limite=1');
        if (response.sucesso && response.mensagens && response.mensagens.length > 0) {
            const ultimaMensagem = response.mensagens[0];
            const lastMessageElement = document.getElementById('last-message');
            if (lastMessageElement) {
                lastMessageElement.textContent = `${ultimaMensagem.nick}: ${ultimaMensagem.message}`;
            }
        } else {
            const lastMessageElement = document.getElementById('last-message');
            if (lastMessageElement) {
                lastMessageElement.textContent = "Nenhuma mensagem ainda...";
            }
        }
    } catch (error) {
        console.error('Erro ao carregar preview do chat:', error);
        const lastMessageElement = document.getElementById('last-message');
        if (lastMessageElement) {
            lastMessageElement.textContent = "Chat indisponível";
        }
    }
}

function iniciarListenerChatCompleto() {
    const messagesContainer = document.getElementById('chat-messages');
    if (!messagesContainer) return;

    // Carregar mensagens iniciais
    carregarMensagensChat();

    // Configurar atualização automática a cada 2 segundos (delay mínimo)
    if (chatUpdateInterval) {
        clearInterval(chatUpdateInterval);
    }

    chatUpdateInterval = setInterval(() => {
        carregarMensagensChat(false); // false = não mostrar loading
    }, 2000);

    return () => {
        if (chatUpdateInterval) {
            clearInterval(chatUpdateInterval);
            chatUpdateInterval = null;
        }
    };
}

async function carregarMensagensChat(showLoading = true) {
    const messagesContainer = document.getElementById('chat-messages');
    if (!messagesContainer) return;

    try {
        if (showLoading) {
            messagesContainer.innerHTML = `
                <div class="text-center py-8">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-accent-blue mx-auto"></div>
                    <p class="text-secondary-text mt-2">Carregando mensagens...</p>
                </div>
            `;
        }

        const response = await fetchAPI('/api/chat/mensagens?limite=50');

        if (response.sucesso) {
            if (response.mensagens && response.mensagens.length > 0) {
                // Renderizar mensagens
                const mensagensHTML = response.mensagens.reverse().map(msg => {
                    const isCurrentUser = currentPlayer && msg.user_uid === currentPlayer.uid;
                    const messageClass = isCurrentUser ? 'bg-accent-blue text-white ml-8' : 'bg-surface-elevated mr-8';

                    return `
                        <div class="flex ${isCurrentUser ? 'justify-end' : 'justify-start'} mb-2">
                            <div class="${messageClass} rounded-lg p-3 max-w-xs break-words">
                                <div class="text-xs opacity-75 mb-1">
                                    <span ${msg.nick_color ? `style="color: ${msg.nick_color}"` : ''}>${msg.nick}</span> • ${msg.time_ago || 'agora'}
                                </div>
                                <div class="text-sm">${escapeHtml(msg.message)}</div>
                            </div>
                        </div>
                    `;
                }).join('');

                messagesContainer.innerHTML = mensagensHTML;

                // Scroll para o final
                messagesContainer.scrollTop = messagesContainer.scrollHeight;

                // Atualizar preview se necessário
                if (response.mensagens.length > 0) {
                    const ultimaMensagem = response.mensagens[response.mensagens.length - 1];
                    const lastMessageElement = document.getElementById('last-message');
                    if (lastMessageElement) {
                        lastMessageElement.textContent = `${ultimaMensagem.nick}: ${ultimaMensagem.message}`;
                    }
                }
            } else {
                messagesContainer.innerHTML = `
                    <div class="text-center py-8">
                        <p class="text-secondary-text">Nenhuma mensagem ainda</p>
                        <p class="text-secondary-text text-sm mt-2">Seja o primeiro a enviar uma mensagem!</p>
                    </div>
                `;
            }
        } else {
            messagesContainer.innerHTML = `
                <div class="text-center py-8">
                    <p class="text-red-400">Erro ao carregar mensagens</p>
                    <p class="text-secondary-text text-sm mt-2">${response.mensagem || 'Erro desconhecido'}</p>
                </div>
            `;
        }
    } catch (error) {
        console.error('Erro ao carregar mensagens do chat:', error);
        messagesContainer.innerHTML = `
            <div class="text-center py-8">
                <p class="text-red-400">Erro de conexão</p>
                <p class="text-secondary-text text-sm mt-2">Verifique sua conexão com a internet</p>
            </div>
        `;
    }
}

// Função auxiliar para escapar HTML
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

function toggleChat() {
    const chatPreview = document.getElementById('chat-preview');
    const chatFull = document.getElementById('chat-full');

    if (chatFull.style.display === 'none' || chatFull.classList.contains('chat-closed')) {
        // Abrir chat
        chatFull.style.display = 'block';
        chatFull.classList.remove('chat-closed');
        chatPreview.style.display = 'none';

        // Inicializar chat completo
        const unsubscribe = iniciarListenerChatCompleto();

        // Armazenar função de cleanup
        chatFull.unsubscribeChat = unsubscribe;

        // Configurar contador de caracteres quando o chat abrir
        setTimeout(setupChatCharacterCounter, 100);
    } else {
        // Fechar chat
        closeChat();
    }
}

function closeChat() {
    const chatPreview = document.getElementById('chat-preview');
    const chatFull = document.getElementById('chat-full');

    // Parar atualizações do chat
    if (chatFull.unsubscribeChat) {
        chatFull.unsubscribeChat();
        chatFull.unsubscribeChat = null;
    }

    // Fechar chat
    chatFull.classList.add('chat-closed');
    setTimeout(() => {
        chatFull.style.display = 'none';
        chatPreview.style.display = 'block';

        // Atualizar preview
        carregarUltimaMensagemPreview();
    }, 300);
}

function pararListenerChat() {
    if (chatUnsubscribe) {
        chatUnsubscribe();
        chatUnsubscribe = null;
    }
}

// --- FUNÇÕES DE RENDERIZAÇÃO DAS SEÇÕES ---
async function renderAppStore() {
    const container = document.getElementById('appstore-items-container');
    if (!container) return;
    
    // Animação de carregamento "apps"
    container.innerHTML = `
        <div id="apps-loading" class="flex flex-col items-center justify-center py-16 space-y-6">
            <div class="relative">
                <!-- Ícone principal de apps com animação de pulso -->
                <div class="w-20 h-20 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center animate-pulse">
                    <svg class="w-12 h-12 text-white" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 6A2.25 2.25 0 016 3.75h2.25A2.25 2.25 0 0110.5 6v2.25a2.25 2.25 0 01-2.25 2.25H6a2.25 2.25 0 01-2.25-2.25V6zM3.75 15.75A2.25 2.25 0 016 13.5h2.25a2.25 2.25 0 012.25 2.25V18a2.25 2.25 0 01-2.25 2.25H6A2.25 2.25 0 013.75 18v-2.25zM13.5 6a2.25 2.25 0 012.25-2.25H18A2.25 2.25 0 0120.25 6v2.25A2.25 2.25 0 0118 10.5h-2.25a2.25 2.25 0 01-2.25-2.25V6zM13.5 15.75a2.25 2.25 0 012.25-2.25H18a2.25 2.25 0 012.25 2.25V18A2.25 2.25 0 0118 20.25h-2.25A2.25 2.25 0 0113.5 18v-2.25z" />
                    </svg>
                </div>
                
                <!-- Círculos animados ao redor -->
                <div class="absolute -top-2 -right-2 w-6 h-6 bg-yellow-400 rounded-full animate-bounce"></div>
                <div class="absolute -bottom-2 -left-2 w-4 h-4 bg-green-400 rounded-full animate-bounce" style="animation-delay: 0.2s;"></div>
                <div class="absolute top-1/2 -right-4 w-3 h-3 bg-red-400 rounded-full animate-bounce" style="animation-delay: 0.4s;"></div>
            </div>
            
            <!-- Texto de carregamento -->
            <div class="text-center">
                <h3 class="text-xl font-semibold text-primary-text mb-2">Carregando Apps</h3>
                <p class="text-secondary-text">Buscando upgrades disponíveis...</p>
            </div>
            
            <!-- Barra de progresso animada -->
            <div class="w-64 h-2 bg-surface-default rounded-full overflow-hidden">
                <div class="h-full bg-gradient-to-r from-blue-500 to-purple-600 rounded-full animate-pulse"></div>
            </div>
            
            <!-- Ícones de apps em carregamento -->
            <div class="flex space-x-4 mt-6">
                <div class="w-12 h-12 bg-surface-elevated rounded-lg flex items-center justify-center animate-pulse" style="animation-delay: 0s;">
                    <span class="text-2xl"></span>
                </div>
                <div class="w-12 h-12 bg-surface-elevated rounded-lg flex items-center justify-center animate-pulse" style="animation-delay: 0.2s;">
                    <span class="text-2xl"></span>
                </div>
                <div class="w-12 h-12 bg-surface-elevated rounded-lg flex items-center justify-center animate-pulse" style="animation-delay: 0.4s;">
                    <span class="text-2xl"></span>
                </div>
                <div class="w-12 h-12 bg-surface-elevated rounded-lg flex items-center justify-center animate-pulse" style="animation-delay: 0.6s;">
                    <span class="text-2xl"></span>
                </div>
            </div>
        </div>
    `;
    
    try {
        // Simula um tempo mínimo de carregamento para mostrar a animação
        const [appStoreResponse, currentPlayerData] = await Promise.all([
            fetchAPI('/api/appstore'),
            currentPlayer || loadGameData(),
            new Promise(resolve => setTimeout(resolve, 1000)) // Mínimo 1 segundo de loading
        ]);
        
        if (!appStoreResponse.sucesso || !appStoreResponse.custos) {
            throw new Error(appStoreResponse.mensagem || "Resposta da API inválida.");
        }
        
        // Remove a animação de loading
        const loadingElement = document.getElementById('apps-loading');
        if (loadingElement) {
            loadingElement.style.opacity = '0';
            loadingElement.style.transform = 'scale(0.95)';
            loadingElement.style.transition = 'all 0.3s ease-out';
            
            setTimeout(() => {
                // Cria o grid de apps com scroll melhorado
                container.innerHTML = `
                    <div class="h-full overflow-y-auto custom-scrollbar">
                        <div class="max-w-6xl mx-auto p-6 pb-20">
                            <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3 space-y-0"></div>
                        </div>
                    </div>
                `;
                const scrollContainer = container.firstElementChild;
                const grid = scrollContainer.querySelector('.grid');
                
                // Adiciona animação de entrada
                grid.style.opacity = '0';
                grid.style.transform = 'translateY(20px)';
                grid.style.transition = 'all 0.5s ease-out';
                
                // Carrega os cards com novo layout
                loadNewAppStoreCards(grid, appStoreResponse);
                
                // Anima a entrada
                setTimeout(() => {
                    grid.style.opacity = '1';
                    grid.style.transform = 'translateY(0)';
                }, 100);
            }, 300);
        }
        
    } catch (error) {
        console.error("Erro ao carregar App Store:", error);
        container.innerHTML = `
            <div class="text-center py-16">
                <div class="w-16 h-16 bg-red-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-red-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m9-.75a9 9 0 11-18 0 9 9 0 0118 0zm-9 3.75h.008v.008H12v-.008z" />
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-red-400 mb-2">Erro ao Carregar Apps</h3>
                <p class="text-secondary-text">Não foi possível conectar com o servidor</p>
            </div>
        `;
    }
}

// Função separada para carregar os cards da App Store
async function loadAppStoreCards(grid, appStoreResponse) {
    const itemNames = {
        cpu: { name: 'CPU', icon: '' },
        firewall: { name: 'Firewall', icon: '' },
        antivirus: { name: 'Antivírus', icon: '' },
        malware_kit: { name: 'Kit Malware', icon: '' },
        bruteforce: { name: 'BruteForce', icon: '�' },
        bankguard: { name: 'BankGuard', icon: '' },
        proxyvpn: { name: 'ProxyVPN', icon: '' }
    };
    
    // Criar todos os cards em paralelo
    const cardPromises = Object.keys(appStoreResponse.custos).map(async (item, index) => {
        if (!currentPlayer) return null;
        
        const itemInfo = itemNames[item];
        const currentLevel = currentPlayer[item] || 1;
        
        // Calcular todos os custos em paralelo
        const [cost1, cost5, cost10] = await Promise.all([
            calculateMultipleCost(item, currentLevel, 1),
            calculateMultipleCost(item, currentLevel, 5),
            calculateMultipleCost(item, currentLevel, 10)
        ]);
        
        // Criar elemento sem innerHTML (mais rápido)
        const card = document.createElement('div');
        card.className = 'bg-surface-elevated border border-border-color rounded-lg p-4 hover:border-accent-blue transition-all duration-200 hover:shadow-lg opacity-0 transform translate-y-4 app-card-enter';
        card.style.transitionDelay = `${index * 150}ms`; // Animação escalonada
        
        card.innerHTML = `
            <div class="text-center mb-4">
                <div class="text-2xl mb-2">${itemInfo.icon}</div>
                <h3 class="text-lg font-semibold text-primary-text mb-1">${itemInfo.name}</h3>
                <div class="text-xs text-secondary-text">Nível ${currentLevel}</div>
            </div>
            
            <div class="grid grid-cols-3 gap-2">
                <button class="upgrade-btn bg-surface-default border border-border-color rounded-lg py-3 px-2 hover:border-accent-blue hover:bg-surface-hover transition-all duration-150 text-center group cursor-pointer min-h-[80px] flex flex-col items-center justify-center hover:scale-102 transform" 
                        data-item="${item}" data-quantity="1">
                    <div class="text-sm font-semibold text-primary-text group-hover:text-accent-blue mb-1">+1</div>
                    <div class="text-xs text-secondary-text mb-1">Básico</div>
                    <div class="text-xs font-semibold text-green-400">$${cost1.dinheiro.toLocaleString()}</div>
                    ${cost1.shacks > 0 ? `<div class="text-xs font-semibold text-yellow-400">${cost1.shacks} Shack</div>` : ''}
                </button>
                <button class="upgrade-btn bg-surface-default border border-border-color rounded-lg py-3 px-2 hover:border-accent-blue hover:bg-surface-hover transition-all duration-150 text-center group cursor-pointer min-h-[80px] flex flex-col items-center justify-center hover:scale-102 transform" 
                        data-item="${item}" data-quantity="5">
                    <div class="text-sm font-semibold text-primary-text group-hover:text-accent-blue mb-1">+5</div>
                    <div class="text-xs text-secondary-text mb-1">Lote</div>
                    <div class="text-xs font-semibold text-blue-400">$${cost5.dinheiro.toLocaleString()}</div>
                    ${cost5.shacks > 0 ? `<div class="text-xs font-semibold text-yellow-400">${cost5.shacks} Shack</div>` : ''}
                </button>
                <button class="upgrade-btn bg-surface-default border border-border-color rounded-lg py-3 px-2 hover:border-accent-blue hover:bg-surface-hover transition-all duration-150 text-center group cursor-pointer min-h-[80px] flex flex-col items-center justify-center hover:scale-102 transform" 
                        data-item="${item}" data-quantity="10">
                    <div class="text-sm font-semibold text-primary-text group-hover:text-accent-blue mb-1">+10</div>
                    <div class="text-xs text-secondary-text mb-1">Máximo</div>
                    <div class="text-xs font-semibold text-purple-400">$${cost10.dinheiro.toLocaleString()}</div>
                    ${cost10.shacks > 0 ? `<div class="text-xs font-semibold text-yellow-400">${cost10.shacks} Shack</div>` : ''}
                </button>
            </div>
        `;
        
        return card;
    });
    
    // Aguardar todos os cards e adicionar ao grid
    const cards = await Promise.all(cardPromises);
    cards.filter(card => card !== null).forEach(card => {
        grid.appendChild(card);
        
        // Anima a entrada de cada card
        setTimeout(() => {
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, 100);
    });
}

// Função auxiliar para calcular custos múltiplos (otimizada)
async function calculateMultipleCost(item, currentLevel, quantity) {
    try {
        // Tentar calcular localmente primeiro (mais rápido)
        const localCost = calculateCostLocally(item, currentLevel, quantity);
        
        // Se for um cálculo simples (quantidade pequena), usar resultado local
        if (quantity <= 5) {
            return localCost;
        }
        
        // Para quantidades maiores, validar com API apenas se necessário
        const response = await fetchAPI('/api/appstore/custos', 'POST', {
            item: item,
            nivel_atual: currentLevel,
            quantidade: quantity
        });
        
        if (response.sucesso) {
            return {
                dinheiro: response.custo_dinheiro || 0,
                shacks: response.custo_shacks || 0
            };
        } else {
            return localCost; // Fallback para cálculo local
        }
    } catch (error) {
        // Em caso de erro, usar sempre o cálculo local
        return calculateCostLocally(item, currentLevel, quantity);
    }
}

// Função para calcular custos localmente
function calculateCostLocally(item, currentLevel, quantity) {
    const custos_base = {
        "cpu": 1,            // Reduzido de 10 para 1
        "firewall": 1,       // Reduzido de 10 para 1
        "antivirus": 1,      // Reduzido de 10 para 1
        "malware_kit": 1,    // Reduzido de 10 para 1
        "bruteforce": 1,     // Reduzido de 10 para 1
        "bankguard": 1,      // Reduzido de 10 para 1
        "proxyvpn": 1,       // Reduzido de 10 para 1
        "ram": 1             // Reduzido de 10 para 1
    };
    const custo_base = custos_base[item] || 1;
    let custo_total_dinheiro = 0;
    let custo_total_shacks = 0;
    
    for (let i = 0; i < quantity; i++) {
        const nivel_upgrade = currentLevel + i + 1; // Nível que será alcançado após o upgrade
        
        // Custo em dinheiro (sempre cresce)
        custo_total_dinheiro += Math.floor(custo_base * Math.pow(1.15, (currentLevel + i - 1)));
        
        // Custo em Shacks a partir do nível 10
        if (nivel_upgrade >= 10) {
            if (nivel_upgrade >= 10 && nivel_upgrade < 20) {
                // Nível 10-19: 2 Shacks por upgrade
                custo_total_shacks += 2;
            } else if (nivel_upgrade >= 20 && nivel_upgrade < 30) {
                // Nível 20-29: 4 Shacks por upgrade
                custo_total_shacks += 4;
            } else if (nivel_upgrade >= 30 && nivel_upgrade < 40) {
                // Nível 30-39: 6 Shacks por upgrade
                custo_total_shacks += 6;
            } else if (nivel_upgrade >= 40 && nivel_upgrade < 50) {
                // Nível 40-49: 8 Shacks por upgrade
                custo_total_shacks += 8;
            } else {
                // Nível 50+: incrementa 2 Shacks a cada 10 níveis
                const shacks_base = 2 + Math.floor((nivel_upgrade - 10) / 10) * 2;
                custo_total_shacks += shacks_base;
            }
        }
    }
    
    return {
        dinheiro: custo_total_dinheiro,
        shacks: custo_total_shacks
    };
}

// --- FUNÇÃO RENDERIZAR TELA DE INVASÃO COMPLETA ---
function renderInvadedScreen() {
    if (!exploitedTarget) return;
    
    const container = document.getElementById('invaded-screen');
    if (!container) return;
    container.innerHTML = `
        <div class="h-full overflow-y-auto custom-scrollbar">
            <div class="max-w-6xl mx-auto p-6 space-y-6 pb-20">
                <!-- Header da Invasão -->
            <div class="relative overflow-hidden rounded-xl bg-gradient-to-br from-red-900/20 via-primary-bg to-red-900/10 border border-red-500/30 backdrop-blur-sm">
                <div class="absolute inset-0 bg-grid-pattern opacity-5"></div>
                <div class="relative p-8 text-center">
                    <div class="flex items-center justify-center mb-4">
                        <div class="flex items-center justify-center w-16 h-16 rounded-full bg-red-500/20 border border-red-500/50">
                            <svg class="w-8 h-8 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                            </svg>
                        </div>
                    </div>
                    <h2 class="text-3xl font-bold text-red-400 mb-2">SISTEMA COMPROMETIDO </h2>
                    <p class="text-xl text-primary-text mb-2">Acesso total obtido ao sistema de</p>
                    <p class="text-2xl font-bold text-accent-blue mb-4">${exploitedTarget.nick}</p>
                    <div class="flex items-center justify-center space-x-6 text-sm text-secondary-text">
                        <div class="flex items-center space-x-2">
                            <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z" />
                            </svg>
                            <span>IP: ${exploitedTarget.ip}</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                            </svg>
                            <span>Nível: ${exploitedTarget.nivel}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Grid de Ações Principais -->
            <div class="grid md:grid-cols-3 gap-6">
                <!-- Botão Acessar Banco -->
                <button id="access-bank-btn" class="group relative overflow-hidden rounded-xl bg-surface-card border border-red-500/50 transition-all duration-300 hover:transform hover:scale-[1.02]">
                    <div class="absolute inset-0 bg-gradient-to-br from-red-500/10 to-transparent"></div>
                    <div class="relative p-8 text-center">
                        <div class="flex items-center justify-center mb-4">
                            <div class="flex items-center justify-center w-12 h-12 rounded-full bg-red-500/20 border border-red-500/30 transition-colors duration-300">
                                <svg class="w-6 h-6 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16.5 10.5V6.75a4.5 4.5 0 10-9 0v3.75m-.75 11.25h10.5a2.25 2.25 0 002.25-2.25v-6.75a2.25 2.25 0 00-2.25-2.25H6.75a2.25 2.25 0 00-2.25 2.25v6.75a2.25 2.25 0 002.25 2.25z" />
                                </svg>
                            </div>
                        </div>
                        <h3 class="text-lg font-semibold text-red-400 mb-2">Banco Bloqueado</h3>
                        <p class="text-sm text-secondary-text">Execute BruteForce primeiro</p>
                        <div class="mt-3 text-xs text-red-400">
                            BankGuard Ativo
                        </div>
                    </div>
                </button>

                <!-- Botão Deface -->
                <button id="deface-btn" class="group relative overflow-hidden rounded-xl bg-surface-card border border-border-color hover:border-purple-500/50 transition-all duration-300 hover:transform hover:scale-[1.02]">
                    <div class="absolute inset-0 bg-gradient-to-br from-purple-500/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    <div class="relative p-8 text-center">
                        <div class="flex items-center justify-center mb-4">
                            <div class="flex items-center justify-center w-12 h-12 rounded-full bg-purple-500/20 border border-purple-500/30 group-hover:bg-purple-500/30 transition-colors duration-300">
                                <svg class="w-6 h-6 text-purple-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18a3.75 3.75 0 0 0 .495-7.468 5.99 5.99 0 0 0-1.925 3.547 5.975 5.975 0 0 1-2.133-1.001A3.75 3.75 0 0 0 12 18Z" />
                                </svg>
                            </div>
                        </div>
                        <h3 class="text-lg font-semibold text-primary-text mb-2">Deface</h3>
                        <p class="text-sm text-secondary-text">Alterar página do alvo</p>
                    </div>
                </button>

                <!-- Botão Ver Log -->
                <button id="view-log-btn" class="group relative overflow-hidden rounded-xl bg-surface-card border border-border-color hover:border-accent-blue/50 transition-all duration-300 hover:transform hover:scale-[1.02]">
                    <div class="absolute inset-0 bg-gradient-to-br from-blue-500/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    <div class="relative p-8 text-center">
                        <div class="flex items-center justify-center mb-4">
                            <div class="flex items-center justify-center w-12 h-12 rounded-full bg-accent-blue/20 border border-accent-blue/30 group-hover:bg-accent-blue/30 transition-colors duration-300">
                                <svg class="w-6 h-6 text-accent-blue" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08M15.75 18.75v-1.875a3.375 3.375 0 0 0-3.375-3.375h-1.5a1.125 1.125 0 0 1-1.125-1.125v-1.5A3.375 3.375 0 0 0 6.375 7.5H5.25m11.9-3.664A2.251 2.251 0 0 0 15 2.25h-1.5a2.251 2.251 0 0 0-2.15 1.586m5.8 0c.065.21.1.433.1.664v.75h-6v-.75c0-.231.035-.454.1-.664M6.75 7.5H4.875c-.621 0-1.125.504-1.125 1.125v10.5c0 .621.504 1.125 1.125 1.125h10.5c.621 0 1.125-.504 1.125-1.125v-1.5" />
                                </svg>
                            </div>
                        </div>
                        <h3 class="text-lg font-semibold text-primary-text mb-2">Ver Log</h3>
                        <p class="text-sm text-secondary-text">Histórico de atividades</p>
                    </div>
                </button>
            </div>

            <!-- Seção de Banco (inicialmente oculta) -->
            <div id="bank-section" class="hidden">
                <div class="rounded-xl bg-surface-card border border-border-color overflow-hidden">
                    <div class="bg-gradient-to-r from-accent-green/10 to-transparent p-6 border-b border-border-color">
                        <h4 class="text-xl font-semibold text-primary-text flex items-center">
                            <div class="flex items-center justify-center w-8 h-8 rounded-lg bg-accent-green/20 border border-accent-green/30 mr-3">
                                <svg class="w-4 h-4 text-accent-green" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 21v-8.25M15.75 21v-8.25M8.25 21v-8.25M3 9l9-6 9 6m-1.5 12V10.332A48.36 48.36 0 0 0 12 9.75c-2.551 0-5.056.2-7.5.582V21M3 21h18M12 6.75h.008v.008H12V6.75Z" />
                                </svg>
                            </div>
                            Sistema Bancário do Alvo
                        </h4>
                    </div>
                    <div class="p-6 space-y-6">
                        <div class="text-center p-6 rounded-xl bg-gradient-to-br from-accent-green/10 to-transparent border border-accent-green/20">
                            ${exploitedTarget.dinheiro === 'unknown' ? `
                                <p class="text-3xl font-bold text-gray-400 mb-2">unknown</p>
                                <p class="text-sm text-gray-500">Saldo Oculto (ProxyVPN)</p>
                            ` : `
                                <p class="text-3xl font-bold text-accent-green mb-2">$${exploitedTarget.dinheiro || 0}</p>
                                <p class="text-sm text-secondary-text">Saldo Disponível</p>
                            `}
                        </div>
                        
                        <!-- Seleção de Porcentagem -->
                        <div class="space-y-4">
                            <h5 class="text-lg font-semibold text-primary-text">Selecione a porcentagem para roubar:</h5>
                            <div class="grid grid-cols-2 md:grid-cols-4 gap-3" id="percentage-buttons">
                                <button class="percentage-btn group relative overflow-hidden rounded-lg bg-surface-hover border-2 border-border-color hover:border-accent-green/50 py-3 px-4 transition-all duration-300 hover:transform hover:scale-105" data-percentage="25">
                                    <div class="absolute inset-0 bg-accent-green/10 opacity-0 transition-opacity duration-300"></div>
                                    <div class="relative text-primary-text font-semibold">25%</div>
                                </button>
                                <button class="percentage-btn group relative overflow-hidden rounded-lg bg-surface-hover border-2 border-border-color hover:border-accent-green/50 py-3 px-4 transition-all duration-300 hover:transform hover:scale-105" data-percentage="50">
                                    <div class="absolute inset-0 bg-accent-green/10 opacity-0 transition-opacity duration-300"></div>
                                    <div class="relative text-primary-text font-semibold">50%</div>
                                </button>
                                <button class="percentage-btn group relative overflow-hidden rounded-lg bg-surface-hover border-2 border-border-color hover:border-accent-green/50 py-3 px-4 transition-all duration-300 hover:transform hover:scale-105" data-percentage="75">
                                    <div class="absolute inset-0 bg-accent-green/10 opacity-0 transition-opacity duration-300"></div>
                                    <div class="relative text-primary-text font-semibold">75%</div>
                                </button>
                                <button class="percentage-btn group relative overflow-hidden rounded-lg bg-surface-hover border-2 border-border-color hover:border-accent-green/50 py-3 px-4 transition-all duration-300 hover:transform hover:scale-105" data-percentage="100">
                                    <div class="absolute inset-0 bg-accent-green/10 opacity-0 transition-opacity duration-300"></div>
                                    <div class="relative text-primary-text font-semibold">100%</div>
                                </button>
                            </div>
                            
                            <!-- Valor a ser transferido -->
                            <div id="transfer-preview" class="text-center p-4 rounded-lg bg-surface-hover border border-border-color hidden">
                                <p class="text-sm text-secondary-text mb-1">Valor a ser transferido:</p>
                                <p id="transfer-amount" class="text-2xl font-bold text-accent-green">$0</p>
                            </div>
                            
                            <!-- Botão de Transferência -->
                            <button id="confirm-transfer-btn" class="w-full group relative overflow-hidden rounded-lg bg-gradient-to-br from-red-600 to-red-700 hover:from-red-500 hover:to-red-600 border border-red-500/30 py-4 px-6 transition-all duration-300 hover:transform hover:scale-[1.02] disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none" disabled>
                                <div class="absolute inset-0 bg-white/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                                <div class="relative flex items-center justify-center space-x-2 text-white font-semibold">
                                    <svg class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                                    </svg>
                                    <span>Transferir Dinheiro</span>
                                </div>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Seção de Log (inicialmente oculta) -->
            <div id="log-section" class="hidden">
                <div class="rounded-xl bg-surface-card border border-border-color overflow-hidden">
                    <div class="bg-gradient-to-r from-accent-blue/10 to-transparent p-6 border-b border-border-color">
                        <div class="flex items-center justify-between">
                            <h4 class="text-xl font-semibold text-primary-text flex items-center">
                                <div class="flex items-center justify-center w-8 h-8 rounded-lg bg-accent-blue/20 border border-accent-blue/30 mr-3">
                                    <svg class="w-4 h-4 text-accent-blue" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08M15.75 18.75v-1.875a3.375 3.375 0 0 0-3.375-3.375h-1.5a1.125 1.125 0 0 1-1.125-1.125v-1.5A3.375 3.375 0 0 0 6.375 7.5H5.25m11.9-3.664A2.251 2.251 0 0 0 15 2.25h-1.5a2.251 2.251 0 0 0-2.15 1.586m5.8 0c.065.21.1.433.1.664v.75h-6v-.75c0-.231.035-.454.1-.664M6.75 7.5H4.875c-.621 0-1.125.504-1.125 1.125v10.5c0 .621.504 1.125 1.125 1.125h10.5c.621 0 1.125-.504 1.125-1.125v-1.5" />
                                    </svg>
                                </div>
                                Log de Atividades do Alvo
                            </h4>
                            <button onclick="limparLogsAlvo()" class="bg-red-600 hover:bg-red-700 text-white px-3 py-2 rounded-lg text-sm font-medium transition-colors duration-200 flex items-center gap-2">
                                <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1-1H9a1 1 0 00-1 1v3M4 7h16" />
                                </svg>
                                Limpar
                            </button>
                        </div>
                    </div>
                    <div class="p-6">
                        <div id="target-log-content" class="space-y-3 max-h-80 overflow-y-auto custom-scrollbar">
                            <!-- Logs do alvo serão carregados aqui -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- Botões de Navegação -->
            <div class="flex justify-center pt-6">
                <button id="back-to-scan-btn" class="group relative overflow-hidden rounded-lg bg-surface-hover border border-border-color hover:border-accent-blue/50 px-6 py-3 transition-all duration-300 hover:transform hover:scale-105">
                    <div class="absolute inset-0 bg-gradient-to-r from-accent-blue/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    <div class="relative flex items-center space-x-2 text-primary-text font-medium">
                        <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                        </svg>
                        <span>Voltar ao Scan</span>
                    </div>
                </button>
            </div>
        </div>
        </div>
    `;
    
    setupInvadedScreenListeners();
}

// --- FUNÇÃO PARA CONFIGURAR EVENT LISTENERS DA TELA DE INVASÃO ---
function setupInvadedScreenListeners() {
    // Event listeners são tratados pelo document.addEventListener('click') global
    // Esta função é mantida apenas para compatibilidade
}

// --- FUNÇÃO PARA CARREGAR LOG DO ALVO NA TELA DE INVASÃO ---
async function loadTargetLogInInvasion() {
    if (!exploitedTarget || !exploitedTarget.uid) {
        showNotification('Erro: Dados do alvo não encontrados', 'error');
        return;
    }
    
    const targetLogContent = document.getElementById('target-log-content');
    if (!targetLogContent) return;
    
    targetLogContent.innerHTML = '<p class="text-gray-400 text-center">Carregando log do alvo...</p>';
    
    try {
        const response = await fetchAPI(`/api/alvo/${exploitedTarget.uid}/log`);
        if (response.sucesso) {
            if (response.logs.length === 0) {
                targetLogContent.innerHTML = '<p class="text-gray-400 text-center">Nenhuma atividade registrada no sistema do alvo.</p>';
                return;
            }
            
            const logsHtml = response.logs.map(log => {
                let dataFormatada = 'Data desconhecida';
                if (log.timestamp && typeof log.timestamp === 'number') {
                    dataFormatada = new Date(log.timestamp * 1000).toLocaleString('pt-BR');
                }
                return `<div class="bg-gray-800 p-2 rounded mb-2"><p class="text-sm text-white">${log.mensagem}</p><p class="text-xs text-gray-400">${dataFormatada}</p></div>`;
            }).join('');
            
            targetLogContent.innerHTML = logsHtml;
        } else {
            targetLogContent.innerHTML = '<p class="text-red-400 text-center">Erro ao carregar log do alvo.</p>';
        }
    } catch (error) {
        targetLogContent.innerHTML = '<p class="text-red-400 text-center">Erro de conexão.</p>';
        showNotification(error.message, 'error');
    }
}

// --- FUNÇÃO PARA TRANSFERIR DINHEIRO ---
async function transferMoney(percentage) {
    if (!exploitedTarget) return;
    
    try {
        const response = await fetchAPI('/api/alvo/transferir', 'POST', { 
            alvo_uid: exploitedTarget.uid,
            porcentagem: percentage 
        });
        
        if (response.sucesso) {
            showNotification(`${response.mensagem}`, 'success');
            // Atualiza os dados do jogador
            await loadGameData();
        } else {
            showNotification(`${response.mensagem}`, 'error');
        }
    } catch (error) {
        console.error('Erro na transferência:', error);
        showNotification('Erro ao transferir dinheiro', 'error');
    }
}

// --- FUNÇÃO DE CONFIGURAÇÕES ---
function renderConfiguracoesSection() {
    const container = document.getElementById('configuracoes-container');
    if (!container) return;

    const currentWallpaper = localStorage.getItem('shack-wallpaper') || 'wallpaper-dark';
    const customWallpaperInfo = getCustomWallpaperInfo();

    container.innerHTML = `
        <div class="bg-gray-800 rounded-lg p-6 space-y-6">
            <!-- Seção de Wallpaper -->
            <div class="border-b border-gray-700 pb-6">
                <h4 class="text-lg font-semibold text-white mb-4 flex items-center">
                    <svg class="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="m2.25 15.75 5.159-5.159a2.25 2.25 0 0 1 3.182 0l5.159 5.159m-1.5-1.5 1.409-1.409a2.25 2.25 0 0 1 3.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 0 0 1.5-1.5V6a1.5 1.5 0 0 0-1.5-1.5H3.75A1.5 1.5 0 0 0 2.25 6v12a1.5 1.5 0 0 0 1.5 1.5Z" />
                    </svg>
                    Plano de Fundo
                </h4>
                
                <!-- Wallpapers Predefinidos -->
                <div class="grid grid-cols-3 gap-4 mb-6">
                    <div class="wallpaper-option wallpaper-dark rounded-lg p-4 h-24 flex items-end ${currentWallpaper === 'wallpaper-dark' ? 'selected' : ''}" data-wallpaper="wallpaper-dark">
                        <span class="text-xs font-medium text-white bg-black/50 px-2 py-1 rounded">Escuro</span>
                    </div>
                    
                    <div class="wallpaper-option wallpaper-cyber rounded-lg p-4 h-24 flex items-end ${currentWallpaper === 'wallpaper-cyber' ? 'selected' : ''}" data-wallpaper="wallpaper-cyber">
                        <span class="text-xs font-medium text-white bg-black/50 px-2 py-1 rounded">Cyber</span>
                    </div>
                    
                    <div class="wallpaper-option wallpaper-matrix rounded-lg p-4 h-24 flex items-end ${currentWallpaper === 'wallpaper-matrix' ? 'selected' : ''}" data-wallpaper="wallpaper-matrix">
                        <span class="text-xs font-medium text-white bg-black/50 px-2 py-1 rounded">Matrix</span>
                    </div>
                    
                    <div class="wallpaper-option wallpaper-neon rounded-lg p-4 h-24 flex items-end ${currentWallpaper === 'wallpaper-neon' ? 'selected' : ''}" data-wallpaper="wallpaper-neon">
                        <span class="text-xs font-medium text-white bg-black/50 px-2 py-1 rounded">Neon</span>
                    </div>
                    
                    <div class="wallpaper-option wallpaper-hacker rounded-lg p-4 h-24 flex items-end ${currentWallpaper === 'wallpaper-hacker' ? 'selected' : ''}" data-wallpaper="wallpaper-hacker">
                        <span class="text-xs font-medium text-white bg-black/50 px-2 py-1 rounded">Hacker</span>
                    </div>
                    
                    <div class="wallpaper-option wallpaper-purple rounded-lg p-4 h-24 flex items-end ${currentWallpaper === 'wallpaper-purple' ? 'selected' : ''}" data-wallpaper="wallpaper-purple">
                        <span class="text-xs font-medium text-white bg-black/50 px-2 py-1 rounded">Roxo</span>
                    </div>
                </div>
                
                <!-- Wallpaper Personalizado Atual -->
                ${customWallpaperInfo ? `
                    <div class="bg-gradient-to-r from-cyan-900/30 to-blue-900/30 border border-cyan-700 rounded-lg p-4 mb-4">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <div class="w-12 h-12 rounded-lg bg-gradient-to-br from-cyan-400 to-blue-600 flex items-center justify-center">
                                    <svg class="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="m2.25 15.75 5.159-5.159a2.25 2.25 0 0 1 3.182 0l5.159 5.159m-1.5-1.5 1.409-1.409a2.25 2.25 0 0 1 3.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 0 0 1.5-1.5V6a1.5 1.5 0 0 0-1.5-1.5H3.75A1.5 1.5 0 0 0 2.25 6v12a1.5 1.5 0 0 0 1.5 1.5Z" />
                                </div>
                                <div>
                                    <h5 class="text-white font-medium">${customWallpaperInfo.filename}</h5>
                                    <div class="text-sm text-cyan-300 space-x-4">
                                        <span>${(customWallpaperInfo.size / 1024 / 1024).toFixed(2)} MB</span>
                                        <span>${new Date(customWallpaperInfo.uploadDate).toLocaleDateString('pt-BR')}</span>
                                        ${currentWallpaper === 'custom' ? '<span class="text-green-400">✓ Ativo</span>' : '<span class="text-gray-400">○ Inativo</span>'}
                                    </div>
                                </div>
                            </div>
                            <div class="flex space-x-2">
                                ${currentWallpaper !== 'custom' ? `
                                    <button onclick="changeWallpaper('custom')" class="bg-cyan-600 hover:bg-cyan-700 text-white px-3 py-2 rounded-lg text-sm transition-colors flex items-center">
                                        <svg class="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" d="M5.25 5.653c0-.856.917-1.398 1.667-.986l11.54 6.347a1.125 1.125 0 0 1 0 1.972l-11.54 6.347a1.125 1.125 0 0 1-1.667-.986V5.653Z" />
                                        </svg>
                                        Ativar
                                    </button>
                                ` : ''}
                                <button onclick="removeCustomWallpaper()" class="bg-red-600 hover:bg-red-700 text-white px-3 py-2 rounded-lg text-sm transition-colors flex items-center">
                                    <svg class="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0" />
                                    </svg>
                                    Remover
                                </button>
                            </div>
                        </div>
                    </div>
                ` : ''}
                
                <!-- Upload de Wallpaper Personalizado -->
                <div class="bg-gray-700 rounded-lg p-4">
                    <h5 class="text-md font-medium text-white mb-3 flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5" />
                        </svg>
                        ${customWallpaperInfo ? 'Alterar Wallpaper Personalizado' : 'Enviar Wallpaper Personalizado'}
                    </h5>
                    <input type="file" id="wallpaper-upload" accept="image/*" class="w-full p-3 bg-gray-600 text-white rounded-lg border border-gray-500 mb-3 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-semibold file:bg-cyan-600 file:text-white hover:file:bg-cyan-700">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-xs text-gray-400">
                        <div>
                            <strong class="text-gray-300">Formatos aceitos:</strong><br>
                            PNG, JPG, JPEG, WebP
                        </div>
                        <div>
                            <strong class="text-gray-300">Tamanho máximo:</strong><br>
                            10 MB
                        </div>
                        <div>
                            <strong class="text-gray-300">Resolução recomendada:</strong><br>
                            1920x1080 ou superior
                        </div>
                        <div>
                            <strong class="text-gray-300">Dica:</strong><br>
                            Imagens escuras ficam melhores
                        </div>
                    </div>
                </div>
            </div>

            <!-- Seção de Cores de Nickname (Supporters) -->
            <div class="border-b border-gray-700 pb-6" id="nickname-colors-section">
                <h4 class="text-lg font-semibold text-white mb-4 flex items-center">
                    <svg class="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M9.53 16.122a3 3 0 0 0-5.78 1.128 2.25 2.25 0 0 1-2.4 0 3 3 0 0 0-1.07-4.131A48.583 48.583 0 0 1 12 2.25c2.7 0 5.37.117 7.99.341a3 3 0 0 1 2.504 2.85c.101 1.052.101 2.107 0 3.159a3 3 0 0 1-2.504 2.85A48.583 48.583 0 0 1 12 21.75c-2.7 0-5.37-.117-7.99-.341Z" />
                    </svg>
                    Cores de Nickname
                    <span class="ml-2 text-xs bg-gradient-to-r from-yellow-400 to-orange-500 text-black px-2 py-1 rounded-full font-bold">SUPPORTER</span>
                </h4>
                <div id="nickname-colors-content">
                    <div class="text-center py-4">
                        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-cyan-400 mx-auto"></div>
                        <p class="text-gray-400 mt-2">Carregando cores...</p>
                    </div>
                </div>
            </div>

            <!-- Seção de Informações do Sistema -->
            <div>
                <h4 class="text-lg font-semibold text-white mb-4 flex items-center">
                    <svg class="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M11.25 11.25l.041-.02a.75.75 0 011.063.852l-.708 2.836a.75.75 0 001.063.853l.041-.021M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-9-3.75h.008v.008H12V8.25z" />
                    </svg>
                    Informações do Sistema
                </h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div class="bg-gray-700 rounded-lg p-4">
                        <strong class="text-cyan-400">Armazenamento Local:</strong><br>
                        <span class="text-gray-300">${localStorage.length} itens salvos</span>
                    </div>
                    <div class="bg-gray-700 rounded-lg p-4">
                        <strong class="text-cyan-400">Wallpaper Atual:</strong><br>
                        <span class="text-gray-300">${currentWallpaper === 'custom' ? 'Personalizado' : currentWallpaper.replace('wallpaper-', '').replace('-', ' ').toUpperCase()}</span>
                    </div>
                </div>
            </div>
            
            <!-- Seção de Conta -->
            <div class="border-t border-gray-700 pt-6">
                <h4 class="text-lg font-semibold text-white mb-4 flex items-center">
                    <svg class="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z" />
                    </svg>
                    Conta
                </h4>
                <div class="bg-gray-700 rounded-lg p-4 mb-4">
                    <div class="flex flex-col sm:flex-row items-center justify-between gap-4">
                        <div class="text-sm text-gray-300">
                            <strong class="text-cyan-400">Usuário conectado:</strong><br>
                            <span id="current-user-display">${currentPlayer?.username || 'Carregando...'}</span>
                        </div>
                        <button id="logout-btn" class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-lg transition-all duration-300 flex items-center justify-center">
                            <svg class="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 9V5.25A2.25 2.25 0 0 0 13.5 3h-6a2.25 2.25 0 0 0-2.25 2.25v13.5A2.25 2.25 0 0 0 7.5 21h6a2.25 2.25 0 0 0 2.25-2.25V15m3 0 3-3m0 0-3-3m3 3H9" />
                            </svg>
                            Logout
                        </button>
                    </div>
                    <p class="text-xs text-gray-400 mt-2">
                        Desconecta sua conta e volta para a tela de login.
                    </p>
                </div>
            </div>
            
            <!-- Seção de Ações -->
            <div class="border-t border-gray-700 pt-6">
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <button id="save-settings-btn" class="bg-green-600 hover:bg-green-700 text-white font-bold py-3 px-6 rounded-lg transition-all duration-300 flex items-center justify-center">
                        <svg class="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M17.593 3.322c1.1.128 1.907 1.077 1.907 2.185V21L12 17.25 4.5 21V5.507c0-1.108.806-2.057 1.907-2.185a48.507 48.507 0 0 1 11.186 0Z" />
                        </svg>
                        Salvar Configurações
                    </button>
                    <button id="reset-settings-btn" class="bg-red-600 hover:bg-red-700 text-white font-bold py-3 px-6 rounded-lg transition-all duration-300 flex items-center justify-center">
                        <svg class="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0 3.181 3.183a8.25 8.25 0 0 0 13.803-3.7M4.031 9.865a8.25 8.25 0 0 1 13.803-3.7l3.181 3.182m0-4.991v4.99" />
                        </svg>
                        Resetar Configurações
                    </button>
                </div>
                <p class="text-xs text-gray-400 text-center mt-3">
                    As configurações são salvas automaticamente no seu navegador e serão carregadas na próxima visita.
                </p>
            </div>
        </div>
    `;

    // Event listeners para wallpapers predefinidos
    container.querySelectorAll('.wallpaper-option').forEach(option => {
        option.addEventListener('click', function() {
            const wallpaperClass = this.getAttribute('data-wallpaper');
            changeWallpaper(wallpaperClass);
        });
    });

    // Event listener para upload de imagem
    document.getElementById('wallpaper-upload').addEventListener('change', handleWallpaperUpload);
    
    // Event listeners para botões de ação
    document.getElementById('save-settings-btn').addEventListener('click', saveUserSettings);
    document.getElementById('reset-settings-btn').addEventListener('click', resetUserSettings);
    
    // Event listener para botão de logout
    document.getElementById('logout-btn').addEventListener('click', handleLogout);

    // Carregar cores de nickname
    loadNicknameColors();
}

// --- FUNÇÕES DE CORES DE NICKNAME ---
// Função para carregar cores de nickname
async function loadNicknameColors() {
    const container = document.getElementById('nickname-colors-content');
    if (!container) return;

    try {
        // Temporariamente usar dados estáticos para testar a interface
        const response = {
            sucesso: true,
            cores: [
                {
                    "id": "nick_color_red",
                    "nome": "Vermelho",
                    "descricao": "Cor vermelha vibrante",
                    "preco": 25,
                    "color": "#FF4444",
                    "disponivel": true
                },
                {
                    "id": "nick_color_blue",
                    "nome": "Azul",
                    "descricao": "Cor azul elegante",
                    "preco": 25,
                    "color": "#4A90E2",
                    "disponivel": true
                },
                {
                    "id": "nick_color_green",
                    "nome": "Verde",
                    "descricao": "Cor verde natural",
                    "preco": 25,
                    "color": "#4CAF50",
                    "disponivel": true
                },
                {
                    "id": "nick_color_purple",
                    "nome": "Roxo",
                    "descricao": "Cor roxa misteriosa",
                    "preco": 30,
                    "color": "#9C27B0",
                    "disponivel": true
                },
                {
                    "id": "nick_color_orange",
                    "nome": "Laranja",
                    "descricao": "Cor laranja energética",
                    "preco": 25,
                    "color": "#FF9800",
                    "disponivel": true
                },
                {
                    "id": "nick_color_pink",
                    "nome": "Rosa",
                    "descricao": "Cor rosa delicada",
                    "preco": 30,
                    "color": "#E91E63",
                    "disponivel": true
                },
                {
                    "id": "nick_color_gold",
                    "nome": "Dourado",
                    "descricao": "Cor dourada premium",
                    "preco": 50,
                    "color": "#FFD700",
                    "disponivel": true
                },
                {
                    "id": "nick_color_cyan",
                    "nome": "Ciano",
                    "descricao": "Cor ciano futurística",
                    "preco": 35,
                    "color": "#00BCD4",
                    "disponivel": true
                }
            ],
            is_supporter: true,
            spoints_disponiveis: 1000,
            cor_atual: null
        };

        if (response.sucesso) {
            const { cores, is_supporter, spoints_disponiveis, cor_atual } = response;

            if (!is_supporter) {
                container.innerHTML = `
                    <div class="bg-gradient-to-r from-yellow-900/30 to-orange-900/30 border border-yellow-700 rounded-lg p-4">
                        <div class="text-center">
                            <svg class="w-12 h-12 mx-auto mb-3 text-yellow-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M16.5 18.75h-9m9 0a3 3 0 0 1 3 3h-15a3 3 0 0 1 3-3m9 0v-3.375c0-.621-.503-1.125-1.125-1.125h-.871M7.5 18.75v-3.375c0-.621.504-1.125 1.125-1.125h.872m5.007 0H9.497m5.007 0a7.454 7.454 0 0 1-.982-3.172M9.497 14.25a7.454 7.454 0 0 0 .981-3.172M5.25 4.236c-.982.143-1.954.317-2.916.52A6.003 6.003 0 0 0 7.73 9.728M5.25 4.236V4.5c0 2.108.966 3.99 2.48 5.228M5.25 4.236l2.48 5.492m0 0A199.047 199.047 0 0 1 12 4.5c.322 0 .644.004.966.011M9.73 9.728a199.047 199.047 0 0 0 2.27-.225m0 0a199.047 199.047 0 0 1 2.27.225m-2.27-.225v0M21.75 4.236c.982.143 1.954.317 2.916.52a6.003 6.003 0 0 1-5.395 4.972M21.75 4.236V4.5a9.00 9.00 0 0 1-2.48 5.228M21.75 4.236l-2.48 5.492M18.75 9.728a199.047 199.047 0 0 0-2.27-.225" />
                            </svg>
                            <h5 class="text-lg font-semibold text-yellow-400 mb-2">Funcionalidade Exclusiva</h5>
                            <p class="text-gray-300 mb-4">As cores de nickname são exclusivas para supporters ativos.</p>
                            <p class="text-sm text-gray-400">Torne-se um supporter para personalizar a cor do seu nickname!</p>
                        </div>
                    </div>
                `;
                return;
            }

            // Renderizar cores disponíveis
            container.innerHTML = `
                <div class="mb-4">
                    <div class="flex items-center justify-between mb-3">
                        <span class="text-sm text-gray-300">SPoints disponíveis: <span class="text-cyan-400 font-semibold">${spoints_disponiveis}</span></span>
                        ${cor_atual ? `<span class="text-sm text-gray-300">Cor atual: <span style="color: ${cor_atual}" class="font-semibold">■</span></span>` : ''}
                    </div>

                    <div class="grid grid-cols-2 md:grid-cols-4 gap-3 mb-4">
                        ${cores.map(cor => `
                            <div class="bg-gray-700 rounded-lg p-3 text-center hover:bg-gray-600 transition-colors cursor-pointer ${cor_atual === cor.color ? 'ring-2 ring-cyan-400' : ''}"
                                 onclick="aplicarCorNickname('${cor.id}', '${cor.color}', ${cor.preco})">
                                <div class="w-8 h-8 rounded-full mx-auto mb-2" style="background-color: ${cor.color}"></div>
                                <div class="text-xs font-medium text-white">${cor.nome}</div>
                                <div class="text-xs text-gray-400">${cor.preco} SP</div>
                            </div>
                        `).join('')}
                    </div>

                    ${cor_atual ? `
                        <div class="text-center">
                            <button onclick="removerCorNickname()" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm transition-colors">
                                Remover Cor (Grátis)
                            </button>
                        </div>
                    ` : ''}
                </div>
            `;
        } else {
            container.innerHTML = `
                <div class="text-center py-4">
                    <p class="text-red-400">${response.mensagem || 'Erro ao carregar cores'}</p>
                </div>
            `;
        }
    } catch (error) {
        console.error('Erro ao carregar cores:', error);
        container.innerHTML = `
            <div class="text-center py-4">
                <p class="text-red-400">Erro de conexão. Tente novamente.</p>
            </div>
        `;
    }
}

// Função para aplicar cor de nickname
async function aplicarCorNickname(colorId, color, preco) {
    try {
        // Temporariamente simular aplicação bem-sucedida
        const cores_info = {
            "nick_color_red": "Vermelho",
            "nick_color_blue": "Azul",
            "nick_color_green": "Verde",
            "nick_color_purple": "Roxo",
            "nick_color_orange": "Laranja",
            "nick_color_pink": "Rosa",
            "nick_color_gold": "Dourado",
            "nick_color_cyan": "Ciano"
        };

        const nome_cor = cores_info[colorId] || "Cor";
        showNotification(`Cor ${nome_cor} aplicada com sucesso! (DEMO)`, 'success');

        // Simular recarregamento
        setTimeout(() => {
            loadNicknameColors();
        }, 1000);

    } catch (error) {
        console.error('Erro ao aplicar cor:', error);
        showNotification('Erro de conexão', 'error');
    }
}

// Função para remover cor de nickname
async function removerCorNickname() {
    try {
        // Temporariamente simular remoção bem-sucedida
        showNotification('Cor removida com sucesso! (DEMO)', 'success');

        // Simular recarregamento
        setTimeout(() => {
            loadNicknameColors();
        }, 1000);

    } catch (error) {
        console.error('Erro ao remover cor:', error);
        showNotification('Erro de conexão', 'error');
    }
}

// --- FUNÇÕES AUXILIARES DE WALLPAPER ---
function handleWallpaperUpload(event) {
    const file = event.target.files[0];
    if (!file) return;

    if (!file.type.startsWith('image/')) {
        showNotification('Por favor, selecione apenas arquivos de imagem.', 'error');
        return;
    }

    if (file.size > 10 * 1024 * 1024) { // Aumentado para 10MB
        showNotification('A imagem deve ter no máximo 10MB.', 'error');
        return;
    }

    // Mostra loading
    showNotification('Processando imagem...', 'info');

    const reader = new FileReader();
    reader.onload = function(e) {
        const imageUrl = e.target.result;
        
        // Comprime a imagem antes de salvar
        compressAndSaveImage(imageUrl, (compressedUrl) => {
            // Salva no localStorage com timestamp para controle
            const wallpaperData = {
                url: compressedUrl,
                filename: file.name,
                size: file.size,
                uploadDate: new Date().toISOString(),
                lastUsed: new Date().toISOString()
            };
            
            localStorage.setItem('custom-wallpaper-data', JSON.stringify(wallpaperData));
            localStorage.setItem('shack-wallpaper', 'custom');
            
            // Aplica o wallpaper
            applyCustomWallpaper(compressedUrl);
            
            showNotification(`Wallpaper "${file.name}" aplicado com sucesso!`, 'success');
            renderConfiguracoesSection(); // Recarrega a seção
            
            // Limpa o input para permitir re-upload da mesma imagem
            event.target.value = '';
        });
    };
    
    reader.readAsDataURL(file);
}

function compressAndSaveImage(imageUrl, callback) {
    const img = new Image();
    img.onload = function() {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        
        // Define tamanho máximo para otimização
        const maxWidth = 1920;
        const maxHeight = 1080;
        
        let { width, height } = img;
        
        // Redimensiona se necessário
        if (width > maxWidth || height > maxHeight) {
            const ratio = Math.min(maxWidth / width, maxHeight / height);
            width *= ratio;
            height *= ratio;
        }
        
        canvas.width = width;
        canvas.height = height;
        
        // Desenha a imagem comprimida
        ctx.drawImage(img, 0, 0, width, height);
        
        // Converte para base64 com qualidade otimizada
        const compressedUrl = canvas.toDataURL('image/jpeg', 0.8);
        callback(compressedUrl);
    };
    img.src = imageUrl;
}

function applyCustomWallpaper(imageUrl) {
    const body = document.getElementById('game-body');
    
    // Remove classes de wallpaper padrão
    body.className = 'bg-gray-900 text-white min-h-screen flex flex-col';
    
    // Aplica a imagem personalizada
    body.style.backgroundImage = `url('${imageUrl}')`;
    body.style.backgroundSize = 'cover';
    body.style.backgroundPosition = 'center';
    body.style.backgroundAttachment = 'fixed';
    body.style.backgroundRepeat = 'no-repeat';
    
    // Adiciona overlay para melhor legibilidade
    if (!document.getElementById('wallpaper-overlay')) {
        const overlay = document.createElement('div');
        overlay.id = 'wallpaper-overlay';
        overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(0,0,0,0.4), rgba(0,0,0,0.6));
            pointer-events: none;
            z-index: -1;
        `;
        body.appendChild(overlay);
    }
}

function removeCustomWallpaper() {
    // Remove dados do localStorage
    localStorage.removeItem('custom-wallpaper-data');
    localStorage.setItem('shack-wallpaper', 'wallpaper-dark');
    
    // Remove overlay se existir
    const overlay = document.getElementById('wallpaper-overlay');
    if (overlay) overlay.remove();
    
    // Volta para wallpaper padrão
    changeWallpaper('wallpaper-dark');
    showNotification('Wallpaper personalizado removido.', 'success');
    renderConfiguracoesSection();
}

function changeWallpaper(wallpaperClass) {
    const body = document.getElementById('game-body');
    
    // Remove todas as classes de wallpaper
    body.className = 'bg-gray-900 text-white min-h-screen flex flex-col';
    
    if (wallpaperClass === 'custom') {
        const wallpaperData = localStorage.getItem('custom-wallpaper-data');
        if (wallpaperData) {
            try {
                const data = JSON.parse(wallpaperData);
                // Atualiza último uso
                data.lastUsed = new Date().toISOString();
                localStorage.setItem('custom-wallpaper-data', JSON.stringify(data));
                
                applyCustomWallpaper(data.url);
            } catch (e) {
                console.error('Erro ao carregar wallpaper personalizado:', e);
                changeWallpaper('wallpaper-dark');
                return;
            }
        } else {
            // Se não tem wallpaper personalizado, volta para o padrão
            changeWallpaper('wallpaper-dark');
            return;
        }
    } else {
        // Remove estilos inline e overlay de wallpaper personalizado
        body.style.backgroundImage = '';
        body.style.backgroundSize = '';
        body.style.backgroundPosition = '';
        body.style.backgroundAttachment = '';
        body.style.backgroundRepeat = '';
        
        const overlay = document.getElementById('wallpaper-overlay');
        if (overlay) overlay.remove();
        
        // Adiciona a classe do wallpaper padrão
        body.classList.add(wallpaperClass);
    }
    
    // Salva a preferência
    localStorage.setItem('shack-wallpaper', wallpaperClass);
    
    // Atualiza a seleção visual na interface
    updateWallpaperSelection(wallpaperClass);
}

function updateWallpaperSelection(wallpaperClass) {
    document.querySelectorAll('.wallpaper-option').forEach(option => {
        option.classList.remove('selected');
        if (option.getAttribute('data-wallpaper') === wallpaperClass) {
            option.classList.add('selected');
        }
    });
    
    // Atualiza botão de remoção se é wallpaper personalizado
    const removeBtn = document.getElementById('remove-wallpaper-btn');
    if (removeBtn) {
        removeBtn.style.display = wallpaperClass === 'custom' ? 'block' : 'none';
    }
}

function loadSavedWallpaper() {
    const savedWallpaper = localStorage.getItem('shack-wallpaper');
    if (savedWallpaper) {
        changeWallpaper(savedWallpaper);
    } else {
        // Primeiro acesso, define padrão
        changeWallpaper('wallpaper-dark');
    }
}

function getCustomWallpaperInfo() {
    const wallpaperData = localStorage.getItem('custom-wallpaper-data');
    if (wallpaperData) {
        try {
            return JSON.parse(wallpaperData);
        } catch (e) {
            return null;
        }
    }
    return null;
}

// Torna as funções globais para uso nos event handlers
window.removeCustomWallpaper = removeCustomWallpaper;
window.changeWallpaper = changeWallpaper;
window.getCustomWallpaperInfo = getCustomWallpaperInfo;

// --- FUNÇÕES DE GERENCIAMENTO DE CONFIGURAÇÕES ---
function saveUserSettings() {
    try {
        const settings = {
            wallpaper: localStorage.getItem('shack-wallpaper') || 'wallpaper-dark',
            customWallpaper: localStorage.getItem('shack-custom-wallpaper') || null,
            customWallpaperInfo: localStorage.getItem('shack-custom-wallpaper-info') || null,
            saveDate: new Date().toISOString(),
            version: '1.0'
        };
        
        localStorage.setItem('shack-user-settings', JSON.stringify(settings));
        showNotification('Configurações salvas com sucesso!', 'success');
        
        // Força recarregamento das configurações visuais
        loadSavedWallpaper();
        
    } catch (error) {
        console.error('Erro ao salvar configurações:', error);
        showNotification('Erro ao salvar configurações', 'error');
    }
}

function loadUserSettings() {
    try {
        const savedSettings = localStorage.getItem('shack-user-settings');
        if (!savedSettings) {
            // Primeira vez ou sem configurações salvas
            setDefaultSettings();
            return false;
        }
        
        const settings = JSON.parse(savedSettings);
        
        // Aplica as configurações salvas
        if (settings.wallpaper) {
            localStorage.setItem('shack-wallpaper', settings.wallpaper);
        }
        
        if (settings.customWallpaper) {
            localStorage.setItem('shack-custom-wallpaper', settings.customWallpaper);
        }
        
        if (settings.customWallpaperInfo) {
            localStorage.setItem('shack-custom-wallpaper-info', settings.customWallpaperInfo);
        }
        
        // Aplica o wallpaper carregado
        loadSavedWallpaper();
        
        console.log('Configurações carregadas:', settings);
        return true;
        
    } catch (error) {
        console.error('Erro ao carregar configurações:', error);
        setDefaultSettings();
        return false;
    }
}

function resetUserSettings() {
    if (confirm('Tem certeza que deseja resetar todas as configurações? Esta ação não pode ser desfeita.')) {
        try {
            // Remove todas as configurações relacionadas
            localStorage.removeItem('shack-user-settings');
            localStorage.removeItem('shack-wallpaper');
            localStorage.removeItem('shack-custom-wallpaper');
            localStorage.removeItem('shack-custom-wallpaper-info');
            
            // Define configurações padrão
            setDefaultSettings();
            
            // Recarrega a interface
            renderConfiguracoesSection();
            loadSavedWallpaper();
            
            showNotification('Configurações resetadas para o padrão', 'info');
            
        } catch (error) {
            console.error('Erro ao resetar configurações:', error);
            showNotification('Erro ao resetar configurações', 'error');
        }
    }
}

function setDefaultSettings() {
    try {
        localStorage.setItem('shack-wallpaper', 'wallpaper-dark');
        changeWallpaper('wallpaper-dark');
    } catch (error) {
        console.error('Erro ao definir configurações padrão:', error);
    }
}

// --- FUNÇÃO MELHORADA PARA CARREGAMENTO AUTOMÁTICO ---
function autoLoadUserSettings() {
    // Carrega configurações salvas automaticamente quando a página carrega
    const loaded = loadUserSettings();
    if (loaded) {
        console.log('Configurações do usuário carregadas automaticamente');
    } else {
        console.log('Usando configurações padrão');
    }
}

// --- FUNÇÕES DE RANKING E BANCO ---
async function renderRankingSection() {
    const container = document.getElementById('ranking-container');
    if (!container) return;
    container.innerHTML = '<p class="text-center text-secondary-text">Carregando ranking...</p>';
    
    try {
        const response = await fetchAPI('/api/ranking/level');
        if (response.sucesso) {
            container.innerHTML = `
                <div class="space-y-6">
                    <!-- Header do Ranking -->
                    <div class="bg-gradient-to-r from-accent-blue to-blue-600 p-6 rounded-lg text-center">
                        <h4 class="text-2xl font-semibold text-white mb-2">Top Jogadores</h4>
                        <p class="text-blue-100">Ranking por Nível de Hacking</p>
                    </div>
                    
                    <!-- Lista de Jogadores com Scroll Melhorado -->
                    <div class="bg-surface-elevated rounded-lg overflow-hidden shadow-xl border border-border-color">
                        <div class="bg-surface-hover px-6 py-4 border-b border-border-color">
                            <h5 class="text-lg font-semibold text-primary-text flex items-center">
                                <svg class="w-5 h-5 mr-2 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                </svg>
                                Ranking por Nível
                            </h5>
                            <div class="text-sm text-secondary-text mt-1 flex items-center justify-between">
                                <span>Classificação por Nível de Hacking</span>
                                <div class="flex items-center space-x-3">
                                    <span class="text-xs text-yellow-400 flex items-center">
                                        <svg class="w-3 h-3 mr-1" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" d="M8.25 15L12 18.75 15.75 15m-7.5-6L12 5.25 15.75 9" />
                                        </svg>
                                        ${response.ranking.length} jogadores
                                    </span>
                                    <div id="ranking-scroll-indicator" class="text-xs text-accent-blue hidden">
                                        <span id="scroll-position">1</span>-<span id="visible-count">8</span> de ${response.ranking.length}
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Container de Scroll Externo Melhorado -->
                        <div class="relative bg-surface-card">
                            <!-- Indicador de Scroll Superior -->
                            <div id="scroll-top-fade" class="absolute top-0 left-0 right-0 h-6 bg-gradient-to-b from-surface-card to-transparent z-10 pointer-events-none opacity-0 transition-opacity duration-300"></div>
                            
                            <!-- Área de Scroll Principal Expandida -->
                            <div class="max-h-[800px] overflow-y-auto ranking-scrollbar p-2" 
                                 style="scroll-behavior: smooth; scrollbar-width: thin; scroll-padding: 20px;" 
                                 onscroll="updateRankingScrollIndicators(this)">
                                <div class="divide-y divide-border-color space-y-1">
                            ${response.ranking.map((jogador, index) => {
                                let positionStyle = '';
                                let positionIcon = '';
                                let rankingBadge = '';
                                
                                if (index === 0) {
                                    positionStyle = 'bg-gradient-to-r from-yellow-500/20 to-yellow-600/20 border-l-4 border-yellow-500 transform hover:scale-[1.02] transition-all duration-200';
                                    positionIcon = '';
                                    rankingBadge = '<span class="bg-yellow-500 text-black text-xs px-2 py-1 rounded-full font-bold">1º LUGAR</span>';
                                } else if (index === 1) {
                                    positionStyle = 'bg-gradient-to-r from-gray-400/20 to-gray-500/20 border-l-4 border-gray-400 transform hover:scale-[1.01] transition-all duration-200';
                                    positionIcon = '';
                                    rankingBadge = '<span class="bg-gray-400 text-black text-xs px-2 py-1 rounded-full font-bold">2º LUGAR</span>';
                                } else if (index === 2) {
                                    positionStyle = 'bg-gradient-to-r from-amber-600/20 to-amber-700/20 border-l-4 border-amber-600 transform hover:scale-[1.01] transition-all duration-200';
                                    positionIcon = '';
                                    rankingBadge = '<span class="bg-amber-600 text-white text-xs px-2 py-1 rounded-full font-bold">3º LUGAR</span>';
                                } else if (index < 10) {
                                    positionStyle = 'bg-gradient-to-r from-accent-blue/10 to-blue-600/10 border-l-2 border-accent-blue/50 hover:bg-surface-hover transition-all duration-200';
                                    rankingBadge = '<span class="bg-accent-blue text-white text-xs px-2 py-1 rounded-full">TOP 10</span>';
                                } else {
                                    positionStyle = 'hover:bg-surface-hover transition-colors duration-200 border-l border-border-color/30';
                                }
                                
                                return `
                                    <div class="p-5 ${positionStyle} my-1 mx-2 rounded-lg">
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center space-x-4">
                                                <div class="flex items-center justify-center w-12 h-12 rounded-full bg-gradient-to-br from-accent-blue to-blue-600 text-white font-bold text-lg shadow-lg">
                                                    ${positionIcon || `#${index + 1}`}
                                                </div>
                                                <div class="player-info" data-nick="${jogador.nick}" data-nivel="${jogador.nivel}" data-grupo="${jogador.grupo_nome || jogador.grupo || ''}" data-group-name="${jogador.grupo_nome || ''}">
                                                    <div class="flex items-center space-x-2 mb-1">
                                                        <h6 class="text-lg font-semibold text-primary-text cursor-pointer hover:text-accent-blue transition-colors duration-200" onclick="openPlayerModal('${jogador.nick}', event)">${jogador.nick}</h6>
                                                        ${rankingBadge}
                                                    </div>
                                                    <div class="flex items-center space-x-4 text-sm text-secondary-text">
                                                        <span class="flex items-center">
                                                            <svg class="w-4 h-4 mr-1 text-accent-blue" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                                <path stroke-linecap="round" stroke-linejoin="round" d="M13 10V3L4 14h7v7l9-11h-7z" />
                                                            </svg>
                                                            Nível ${jogador.nivel}
                                                        </span>
                                                        ${jogador.grupo_nome ? `
                                                            <span class="flex items-center text-purple-400">
                                                                <svg class="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                                    <path stroke-linecap="round" stroke-linejoin="round" d="M18 18.72a9.094 9.094 0 003.741-.479 3 3 0 00-4.682-2.72m.94 3.198l.001.031c0 .225-.012.447-.037.666A11.944 11.944 0 0112 21c-2.17 0-4.207-.576-5.963-1.584A6.062 6.062 0 016 18.719m12 0a5.971 5.971 0 00-.941-3.197m0 0A5.995 5.995 0 0012 12.75a5.995 5.995 0 00-5.058 2.772m0 0a3 3 0 00-4.681 2.72 8.986 8.986 0 003.74.477m.94-3.197a5.971 5.971 0 00-.94 3.197M15 6.75a3 3 0 11-6 0 3 3 0 016 0zm6 3a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0zm-13.5 0a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z" />
                                                                </svg>
                                                                ${jogador.grupo_nome}
                                                            </span>
                                                        ` : ''}
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="text-right">
                                                <div class="text-3xl font-bold text-accent-blue mb-1">
                                                    ${jogador.nivel}
                                                </div>
                                                <div class="text-xs text-secondary-text">
                                                    Posição #${index + 1}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                `;
                            }).join('')}
                                </div>
                            </div>
                            
                            <!-- Indicador de Scroll Inferior -->
                            <div id="scroll-bottom-fade" class="absolute bottom-0 left-0 right-0 h-6 bg-gradient-to-t from-surface-card to-transparent z-10 pointer-events-none opacity-0 transition-opacity duration-300"></div>
                            
                            <!-- Barra de Progresso do Scroll -->
                            <div class="absolute right-2 top-4 bottom-4 w-1 bg-border-color/30 rounded-full">
                                <div id="scroll-progress-bar" class="w-full bg-accent-blue rounded-full transition-all duration-150" style="height: 20%; transform: translateY(0%)"></div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            // Armazena os dados para uso em troca de abas
            container.levelData = response.ranking || [];
            
            // Inicializa os indicadores de scroll
            setTimeout(() => {
                const scrollContainer = container.querySelector('.ranking-scrollbar');
                if (scrollContainer) {
                    updateRankingScrollIndicators(scrollContainer);
                }
            }, 100);
        } else {
            container.innerHTML = '<p class="text-center text-red-400">Erro ao carregar ranking.</p>';
        }
    } catch (error) {
        container.innerHTML = `<p class="text-center text-red-400">Erro: ${error.message}</p>`;
    }
}

// --- FUNÇÃO PARA ATUALIZAR INDICADORES DE SCROLL DO RANKING ---
function updateRankingScrollIndicators(scrollContainer) {
    if (!scrollContainer) return;
    
    const scrollTop = scrollContainer.scrollTop;
    const scrollHeight = scrollContainer.scrollHeight;
    const clientHeight = scrollContainer.clientHeight;
    const scrollPercent = scrollTop / (scrollHeight - clientHeight);
    
    // Atualiza a barra de progresso
    const progressBar = document.getElementById('scroll-progress-bar');
    if (progressBar) {
        const progressHeight = Math.max(20, (clientHeight / scrollHeight) * 100);
        const progressPosition = scrollPercent * (100 - progressHeight);
        progressBar.style.height = `${progressHeight}%`;
        progressBar.style.transform = `translateY(${progressPosition}%)`;
    }
    
    // Atualiza os indicadores de fade
    const topFade = document.getElementById('scroll-top-fade');
    const bottomFade = document.getElementById('scroll-bottom-fade');
    const scrollIndicator = document.getElementById('ranking-scroll-indicator');
    
    if (topFade) {
        topFade.style.opacity = scrollTop > 20 ? '1' : '0';
    }
    
    if (bottomFade) {
        const isAtBottom = scrollTop >= (scrollHeight - clientHeight - 20);
        bottomFade.style.opacity = isAtBottom ? '0' : '1';
    }
    
    // Calcula itens visíveis
    const itemHeight = 120; // Altura aproximada de cada item do ranking
    const visibleStart = Math.floor(scrollTop / itemHeight) + 1;
    const visibleCount = Math.min(Math.ceil(clientHeight / itemHeight), 
                                  Math.ceil((scrollHeight - scrollTop) / itemHeight));
    const totalItems = Math.ceil(scrollHeight / itemHeight);
    
    // Atualiza o indicador de posição
    if (scrollIndicator) {
        const positionElement = document.getElementById('scroll-position');
        const visibleCountElement = document.getElementById('visible-count');
        
        if (positionElement && visibleCountElement) {
            positionElement.textContent = visibleStart;
            visibleCountElement.textContent = Math.min(visibleStart + visibleCount - 1, totalItems);
            scrollIndicator.classList.remove('hidden');
        }
    }
}

// Torna a função global para uso nos event handlers
window.updateRankingScrollIndicators = updateRankingScrollIndicators;

async function renderBancoSection() {
    const container = document.getElementById('banco-container');
    if (!container) return;
    container.innerHTML = '<p class="text-center text-secondary-text">Carregando dados bancários...</p>';
    
    try {
        const response = await fetchAPI('/api/banco');
        if (response.sucesso) {
            container.innerHTML = `
                <div class="space-y-6">
                    <!-- Header do Banco -->
                    <div class="bg-gradient-to-r from-green-600 to-accent-blue p-6 rounded-lg text-center">
                        <h4 class="text-2xl font-semibold text-white mb-2">Banco Digital</h4>
                        <p class="text-green-100">Suas finanças em tempo real</p>
                    </div>
                    
                    <!-- Saldo Atual -->
                    <div class="bg-surface-elevated rounded-lg p-6 text-center border border-border-color">
                        <h5 class="text-lg font-semibold text-secondary-text mb-2">Saldo Atual</h5>
                        <div class="text-4xl font-semibold text-green-400">
                            $${response.saldo_atual.toLocaleString()}
                        </div>
                    </div>
                    
                    <!-- Estatísticas -->
                    <div class="grid md:grid-cols-2 gap-4">
                        <div class="bg-surface-elevated rounded-lg p-4 text-center border border-border-color">
                            <div class="text-2xl font-semibold text-green-400">+$${response.total_entradas.toLocaleString()}</div>
                            <div class="text-sm text-secondary-text">Total de Entradas</div>
                        </div>
                        <div class="bg-surface-elevated rounded-lg p-4 text-center border border-border-color">
                            <div class="text-2xl font-semibold text-red-400">-$${response.total_saidas.toLocaleString()}</div>
                            <div class="text-sm text-secondary-text">Total de Saídas</div>
                        </div>
                    </div>
                    
                    <!-- Últimas Transações -->
                    <div class="bg-surface-elevated rounded-lg p-6 border border-border-color">
                        <h5 class="text-lg font-semibold text-primary-text mb-4 flex items-center">
                            <svg class="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M12 6v6h4.5m4.5 0a9 9 0 11-18 0 9 9 0 0118 0zm-9-3.75h.008v.008H12V8.25z" />
                            </svg>
                            Últimas Transações
                        </h5>
                        <div class="space-y-3">
                            ${response.transacoes.length > 0 ? response.transacoes.map(transacao => {
                                const isPositive = transacao.valor > 0;
                                const date = new Date(transacao.timestamp * 1000);
                                return `
                                    <div class="flex justify-between items-center p-3 bg-surface-hover rounded-lg border border-border-color">
                                        <div class="flex items-center gap-3 flex-1">
                                            <div>
                                                <div class="flex items-center gap-2">
                                                    <span class="text-white font-medium font-mono">${transacao.tipo || transacao.acao || 'IP não identificado'}</span>
                                                    <button onclick="copyToClipboard('${transacao.tipo || transacao.acao || ''}', this)" 
                                                            class="copy-btn text-xs px-2 py-1 bg-accent-blue hover:bg-blue-600 text-white rounded transition-colors duration-200 flex items-center gap-1"
                                                            title="Copiar IP">
                                                        <svg class="w-3 h-3" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor">
                                                            <path stroke-linecap="round" stroke-linejoin="round" d="M15.666 3.888A2.25 2.25 0 0013.5 2.25h-3c-1.03 0-1.9.693-2.166 1.638m7.332 0c.*************.084.612v0a.75.75 0 01-.75.75H9a.75.75 0 01-.75-.75v0c0-.212.03-.418.084-.612m7.332 0c.646.049 1.288.11 1.927.184 1.1.128 1.907 1.077 1.907 2.185V19.5a2.25 2.25 0 01-2.25 2.25H6.75A2.25 2.25 0 014.5 19.5V6.257c0-1.108.806-2.057 1.907-2.185a48.208 48.208 0 011.927-.184" />
                                                        </svg>
                                                        Copiar
                                                    </button>
                                                </div>
                                                <div class="text-xs text-gray-400">${date.toLocaleString('pt-BR')}</div>
                                            </div>
                                        </div>
                                        <div class="text-lg font-bold ${isPositive ? 'text-green-400' : 'text-red-400'}">
                                            ${isPositive ? '+' : ''}$${Math.abs(transacao.valor).toLocaleString()}
                                        </div>
                                    </div>
                                `;
                            }).join('') : '<p class="text-center text-gray-400">Nenhuma transação encontrada</p>'}
                        </div>
                    </div>
                </div>
            `;
        } else {
            container.innerHTML = '<p class="text-center text-red-400">Erro ao carregar dados bancários.</p>';
        }
    } catch (error) {
        container.innerHTML = `<p class="text-center text-red-400">Erro: ${error.message}</p>`;
    }
}

async function renderGroupSection() {
    const container = document.getElementById('group-info-container');
    if (!container) return;
    container.innerHTML = '<p class="text-center text-gray-400">Carregando...</p>';

    try {
        const response = await fetchAPI('/api/grupo');
        const tournamentResponse = await getTournamentRanking(); // Nova API do torneio
        
        if (response.sucesso && response.grupo) {
            container.innerHTML = `
                <div class="space-y-6">
                    <!-- Header do Grupo com botão Sair -->
                    <div class="bg-surface-elevated border border-border-color rounded-lg p-6 shadow-lg">
                        <div class="flex items-start justify-between mb-4">
                            <div class="flex items-center gap-3">
                                <div class="bg-accent-blue p-3 rounded-lg">
                                    <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"/>
                                    </svg>
                                </div>
                                <div>
                                    <h4 class="text-2xl font-semibold text-primary-text">${response.grupo.nome}</h4>
                                    <p class="text-secondary-text text-sm">Líder: <span class="text-primary-text">${response.grupo.lider}</span></p>
                                </div>
                            </div>
                            
                            <!-- Botão Sair menos exposto -->
                            <button id="leave-group-btn" class="text-xs bg-red-500/20 hover:bg-red-500/30 text-red-400 hover:text-red-300 px-3 py-2 rounded-lg border border-red-500/30 hover:border-red-400 transition-all duration-300 flex items-center">
                                <svg class="w-3 h-3 mr-1" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 9V5.25A2.25 2.25 0 0013.5 3h-6a2.25 2.25 0 00-2.25 2.25v13.5A2.25 2.25 0 007.5 21h6a2.25 2.25 0 002.25-2.25V15M12 9l-3 3m0 0l3 3m-3-3h12.75"/>
                                </svg>
                                Sair
                            </button>
                        </div>
                        
                        <div class="grid grid-cols-2 gap-4">
                            <div class="bg-surface-default border border-border-color rounded-lg p-4 text-center">
                                <div class="text-2xl font-bold text-accent-blue">${response.grupo.membros?.length || 0}/${response.grupo.max_membros || 5}</div>
                                <div class="text-xs text-secondary-text">Membros</div>
                            </div>
                            <div class="bg-surface-default border border-border-color rounded-lg p-4 text-center">
                                <div class="text-2xl font-bold text-purple-400">${response.grupo.deface_points || 0}</div>
                                <div class="text-xs text-secondary-text">Pontos de Torneio</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Painel de Admin (só aparece para admins) -->
                    <div id="admin-tournament-panel" class="bg-red-500/10 border border-red-500/30 rounded-lg p-4 mb-6" style="display: none;">
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center">
                                <div class="bg-red-500/20 p-2 rounded-lg mr-3">
                                    <svg class="w-5 h-5 text-red-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.324.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 011.37.49l1.296 2.247a1.125 1.125 0 01-.26 1.431l-1.003.827c-.293.24-.438.613-.431.992a6.759 6.759 0 010 .255c-.007.378.138.75.43.99l1.005.828c.424.35.534.954.26 1.43l-1.298 2.247a1.125 1.125 0 01-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.57 6.57 0 01-.22.128c-.331.183-.581.495-.644.869l-.213 1.28c-.09.543-.56.941-1.11.941h-2.594c-.55 0-1.019-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 01-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 01-1.369-.49l-1.297-2.247a1.125 1.125 0 01.26-1.431l1.004-.827c.292-.24.437-.613.43-.992a6.932 6.932 0 010-.255c.007-.378-.138-.75-.43-.99l-1.004-.828a1.125 1.125 0 01-.26-1.43l1.297-2.247a1.125 1.125 0 011.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.087.22-.128.332-.183.582-.495.644-.869l.214-1.281z" />
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                    </svg>
                                </div>
                                <div>
                                    <h5 class="text-lg font-semibold text-red-400">Painel Administrativo</h5>
                                    <p class="text-sm text-secondary-text">Controles de administrador para torneios</p>
                                </div>
                            </div>
                            <div class="flex items-center space-x-2">
                                <span id="admin-tournament-status" class="text-xs text-secondary-text">Carregando...</span>
                            </div>
                        </div>
                        
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div class="bg-surface-default border border-border-color rounded-lg p-3">
                                <div class="text-sm text-secondary-text mb-1">Grupos Participando</div>
                                <div id="admin-total-grupos" class="text-xl font-bold text-accent-blue">-</div>
                            </div>
                            <div class="bg-surface-default border border-border-color rounded-lg p-3">
                                <div class="text-sm text-secondary-text mb-1">Jogadores Ativos</div>
                                <div id="admin-total-jogadores" class="text-xl font-bold text-green-400">-</div>
                            </div>
                            <div class="bg-surface-default border border-border-color rounded-lg p-3">
                                <div class="text-sm text-secondary-text mb-1">Próximo Reset</div>
                                <div id="admin-proximo-reset" class="text-sm font-bold text-yellow-400">00:00 UTC</div>
                            </div>
                        </div>
                        
                        <div class="mt-4 flex flex-col sm:flex-row gap-3">
                            <button id="admin-reset-tournament-btn" class="flex-1 bg-red-600 hover:bg-red-700 text-white font-semibold py-3 px-4 rounded-lg transition-all duration-300 flex items-center justify-center">
                                <svg class="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0l3.181 3.183a8.25 8.25 0 0013.803-3.7M4.031 9.865a8.25 8.25 0 0113.803-3.7l3.181 3.182m0-4.991v4.99" />
                                </svg>
                                Resetar Torneio Agora
                            </button>
                            <button id="admin-refresh-info-btn" class="bg-gray-600 hover:bg-gray-700 text-white font-semibold py-3 px-4 rounded-lg transition-all duration-300 flex items-center justify-center">
                                <svg class="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0l3.181 3.183a8.25 8.25 0 0013.803-3.7M4.031 9.865a8.25 8.25 0 0113.803-3.7l3.181 3.182m0-4.991v4.99" />
                                </svg>
                                Atualizar Info
                            </button>
                        </div>
                    </div>
                    
                    <!-- Navegação de Abas modernizada -->
                    <div class="bg-surface-elevated border border-border-color rounded-lg p-1">
                        <div class="flex space-x-1">
                            <button id="tab-membros" class="tab-button flex-1 py-3 px-4 rounded-lg text-sm font-semibold transition-all duration-300 bg-accent-blue text-white" onclick="showGroupTab('membros')">
                                <svg class="w-4 h-4 inline mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"/>
                                </svg>
                                Membros
                            </button>
                            <button id="tab-ranking-grupos" class="tab-button flex-1 py-3 px-4 rounded-lg text-sm font-semibold transition-all duration-300 text-secondary-text hover:text-primary-text hover:bg-surface-hover" onclick="showGroupTab('ranking-grupos')">
                                <svg class="w-4 h-4 inline mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                </svg>
                                Ranking Grupos
                            </button>
                            <button id="tab-ranking-jogadores" class="tab-button flex-1 py-3 px-4 rounded-lg text-sm font-semibold transition-all duration-300 text-secondary-text hover:text-primary-text hover:bg-surface-hover" onclick="showGroupTab('ranking-jogadores')">
                                <svg class="w-4 h-4 inline mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"/>
                                </svg>
                                Ranking Jogadores
                            </button>
                        </div>
                    </div>
                    
                    <!-- Conteúdo das Abas -->
                    <div id="group-tab-content">
                        <!-- Aba Membros -->
                        <div id="content-membros" class="tab-content">
                            <div class="bg-surface-elevated border border-border-color rounded-lg overflow-hidden shadow-lg">
                                <div class="bg-surface-hover px-6 py-4 border-b border-border-color">
                                    <h5 class="text-lg font-semibold text-primary-text flex items-center">
                                        <svg class="w-5 h-5 mr-2 text-accent-blue" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"/>
                                        </svg>
                                        Lista de Membros
                                    </h5>
                                </div>
                                <div class="divide-y divide-border-color max-h-80 overflow-y-auto custom-scrollbar">
                                    ${(response.grupo.membros || []).map(membro => `
                                        <div class="p-4 hover:bg-surface-hover transition-colors duration-300">
                                            <div class="flex items-center justify-between">
                                                <div class="flex items-center space-x-3">
                                                    <div class="flex items-center justify-center w-10 h-10 rounded-full bg-accent-blue text-white font-semibold">
                                                        ${membro.charAt(0).toUpperCase()}
                                                    </div>
                                                    <div>
                                                        <h6 class="text-lg font-semibold text-primary-text">${membro}</h6>
                                                        ${membro === response.grupo.lider ? '<span class="text-xs bg-yellow-500/20 text-yellow-400 px-2 py-1 rounded-md border border-yellow-500/30 font-semibold">LÍDER</span>' : '<span class="text-xs text-secondary-text">Membro</span>'}
                                                    </div>
                                                </div>
                                                <div class="text-right">
                                                    <svg class="w-5 h-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                                                    </svg>
                                                </div>
                                            </div>
                                        </div>
                                    `).join('')}
                                </div>
                            </div>
                        </div>
                        
                        <!-- Aba Ranking Grupos -->
                        <div id="content-ranking-grupos" class="tab-content hidden">
                            <div class="max-h-[750px] overflow-y-auto ranking-scrollbar pr-4 space-y-6 pb-8" style="scroll-behavior: smooth;">
                                <!-- Torneio Diário Banner -->
                            <div class="bg-gradient-to-r from-yellow-600 to-orange-600 rounded-lg p-4 mb-6 border-2 border-yellow-500">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <div class="bg-white/20 rounded-full p-3 mr-4">
                                            <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                            </svg>
                                        </div>
                                        <div>
                                            <h3 class="text-xl font-bold text-white">Torneio Diário de Deface</h3>
                                            <p class="text-yellow-100">Competição entre grupos • Reinicia a cada 24h</p>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <div id="tournament-timer" class="text-lg font-bold text-white bg-black/20 px-3 py-1 rounded-lg">
                                            --:--:--
                                        </div>
                                        <div class="text-xs text-yellow-100 mt-1">Tempo restante</div>
                                    </div>
                                </div>
                                
                                <!-- Recompensas do Torneio -->
                                <div class="mt-4 grid grid-cols-3 gap-3">
                                    <div class="bg-yellow-500/20 rounded-lg p-3 text-center border border-yellow-400/30">
                                        <div class="text-2xl mb-1">1º</div>
                                        <div class="text-xs font-semibold text-yellow-100">1º LUGAR</div>
                                        <div class="text-sm font-bold text-white">$50,000</div>
                                        <div class="text-xs text-yellow-200">+ 500 Shacks</div>
                                    </div>
                                    <div class="bg-gray-400/20 rounded-lg p-3 text-center border border-gray-400/30">
                                        <div class="text-2xl mb-1"></div>
                                        <div class="text-xs font-semibold text-gray-200">2º LUGAR</div>
                                        <div class="text-sm font-bold text-white">$30,000</div>
                                        <div class="text-xs text-gray-300">+ 300 Shacks</div>
                                    </div>
                                    <div class="bg-amber-600/20 rounded-lg p-3 text-center border border-amber-500/30">
                                        <div class="text-2xl mb-1"></div>
                                        <div class="text-xs font-semibold text-amber-200">3º LUGAR</div>
                                        <div class="text-sm font-bold text-white">$20,000</div>
                                        <div class="text-xs text-amber-300">+ 200 Shacks</div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="bg-gray-800 rounded-lg overflow-hidden shadow-xl">
                                <div class="bg-gray-700 px-6 py-4 border-b border-gray-600">
                                    <h5 class="text-lg font-semibold text-white flex items-center">
                                        <svg class="w-5 h-5 mr-2 text-purple-400" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                        </svg>
                                        Ranking do Torneio Atual
                                    </h5>
                                    <div class="text-sm text-gray-300 mt-1 flex items-center justify-between">
                                        <span>Pontuação desde: <span id="tournament-start-date">--</span></span>
                                        <span class="text-xs text-purple-400 flex items-center">
                                            <svg class="w-3 h-3 mr-1" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" d="M8.25 15L12 18.75 15.75 15m-7.5-6L12 5.25 15.75 9" />
                                            </svg>
                                            Role para ver mais
                                        </span>
                                    </div>
                                </div>
                                <div class="max-h-[500px] overflow-y-auto divide-y divide-gray-700 ranking-scrollbar pr-4 pb-6" style="scroll-behavior: smooth;">
                                    ${tournamentResponse.sucesso && tournamentResponse.ranking_grupos ? 
                                        tournamentResponse.ranking_grupos.map((grupo, index) => {
                                            let positionStyle = '';
                                            let positionIcon = '';
                                            let rewardInfo = '';
                                            
                                            if (index === 0) {
                                                positionStyle = 'bg-gradient-to-r from-yellow-500/20 to-yellow-600/20 border-l-4 border-yellow-500';
                                                positionIcon = '';
                                                rewardInfo = '<div class="text-xs text-yellow-400 mt-1">$50,000 + 500 Shacks</div>';
                                            } else if (index === 1) {
                                                positionStyle = 'bg-gradient-to-r from-gray-400/20 to-gray-500/20 border-l-4 border-gray-400';
                                                positionIcon = '';
                                                rewardInfo = '<div class="text-xs text-gray-400 mt-1">$30,000 + 300 Shacks</div>';
                                            } else if (index === 2) {
                                                positionStyle = 'bg-gradient-to-r from-amber-600/20 to-amber-700/20 border-l-4 border-amber-600';
                                                positionIcon = '';
                                                rewardInfo = '<div class="text-xs text-amber-400 mt-1">$20,000 + 200 Shacks</div>';
                                            } else {
                                                positionStyle = 'hover:bg-gray-700/50 transition-colors duration-200';
                                                positionIcon = '';
                                                rewardInfo = '<div class="text-xs text-gray-500 mt-1">Sem recompensa</div>';
                                            }
                                            
                                            return `
                                                <div class="p-4 ${positionStyle}">
                                                    <div class="flex items-center justify-between">
                                                        <div class="flex items-center gap-4">
                                                            <div class="flex items-center justify-center w-10 h-10 rounded-full bg-gray-700 text-white font-bold">
                                                                ${positionIcon || `#${index + 1}`}
                                                            </div>
                                                            <div>
                                                                <h6 class="text-lg font-semibold text-white cursor-pointer hover:text-purple-300 transition-colors" onclick="showGroupDetails('${grupo.nome}', ${JSON.stringify(grupo).replace(/"/g, '&quot;')})">${grupo.nome}</h6>
                                                                <div class="text-xs text-gray-400">${grupo.membros?.length || 0} membros</div>
                                                            </div>
                                                        </div>
                                                        <div class="text-right">
                                                            <div class="text-2xl font-bold text-purple-400 mb-1">
                                                                ${grupo.tournament_points || 0} pts
                                                            </div>
                                                            ${rewardInfo}
                                                        </div>
                                                    </div>
                                                </div>
                                            `;
                                        }).join('') 
                                        : '<div class="p-4 text-center text-gray-400">Nenhum grupo encontrado</div>'
                                    }
                                </div>
                            </div>
                            <div class="pb-6"></div>
                            </div>
                        </div>
                        
                        <!-- Aba Ranking Jogadores -->
                        <div id="content-ranking-jogadores" class="tab-content hidden">
                            <div class="bg-gray-800 rounded-lg overflow-hidden shadow-xl">
                                <div class="bg-gray-700 px-6 py-4 border-b border-gray-600">
                                    <h5 class="text-lg font-semibold text-white flex items-center">
                                        <svg class="w-5 h-5 mr-2 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"/>
                                        </svg>
                                        Top Jogadores por Deface
                                    </h5>
                                    <div class="text-sm text-gray-300 mt-1 flex items-center justify-between">
                                        <span>Ranking Individual de Defaces</span>
                                        <span class="text-xs text-blue-400 flex items-center">
                                            <svg class="w-3 h-3 mr-1" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" d="M8.25 15L12 18.75 15.75 15m-7.5-6L12 5.25 15.75 9" />
                                            </svg>
                                            Role para ver mais
                                        </span>
                                    </div>
                                </div>
                                <div class="max-h-[500px] overflow-y-auto divide-y divide-gray-700 ranking-scrollbar pr-4 pb-6" style="scroll-behavior: smooth;">
                                    ${tournamentResponse.sucesso && tournamentResponse.ranking_jogadores ? 
                                        tournamentResponse.ranking_jogadores.map((jogador, index) => {
                                            let positionStyle = '';
                                            let positionIcon = '';
                                            
                                            if (index === 0) {
                                                positionStyle = 'bg-gradient-to-r from-yellow-500/20 to-yellow-600/20 border-l-4 border-yellow-500';
                                                positionIcon = '';
                                            } else if (index === 1) {
                                                positionStyle = 'bg-gradient-to-r from-gray-400/20 to-gray-500/20 border-l-4 border-gray-400';
                                                positionIcon = '';
                                            } else if (index === 2) {
                                                positionStyle = 'bg-gradient-to-r from-amber-600/20 to-amber-700/20 border-l-4 border-amber-600';
                                                positionIcon = '';
                                            } else {
                                                positionStyle = 'hover:bg-gray-700/50 transition-colors duration-200';
                                                positionIcon = '';
                                            }
                                            
                                            return `
                                                <div class="p-4 ${positionStyle}">
                                                    <div class="flex items-center justify-between">
                                                        <div class="flex items-center gap-4">
                                                            <div class="flex items-center justify-center w-10 h-10 rounded-full bg-gray-700 text-white font-bold">
                                                                ${positionIcon || `#${index + 1}`}
                                                            </div>
                                                            <div>
                                                                <h6 class="text-lg font-semibold text-white cursor-pointer hover:text-blue-300 transition-colors" onclick="showPlayerDetails('${jogador.nick}', ${JSON.stringify(jogador).replace(/"/g, '&quot;')})">${jogador.nick}</h6>
                                                                ${jogador.grupo ? `<div class="text-xs text-gray-400">Grupo: ${jogador.grupo}</div>` : '<div class="text-xs text-gray-500">Sem grupo</div>'}
                                                            </div>
                                                        </div>
                                                        <div class="text-right">
                                                            <div class="text-2xl font-bold text-blue-400 mb-1">
                                                                ${jogador.tournament_points || 0} pts
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            `;
                                        }).join('') 
                                        : '<div class="p-4 text-center text-gray-400">Nenhum jogador encontrado</div>'
                                    }
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            // Event listener para o botão "Sair do Grupo"
            document.getElementById('leave-group-btn').addEventListener('click', debounce(async function() {
                return preventDoubleClick('leave-group-btn', async () => {
                    const confirmMessage = 'Tem certeza que deseja sair do grupo? Esta ação não pode ser desfeita.';
                    if (!confirm(confirmMessage)) {
                        return;
                    }
                    
                    try {
                        const response = await fetchAPI('/api/grupo/sair', 'POST');
                        console.log('DEBUG: Resposta da API sair do grupo:', response);
                        
                        if (response.sucesso) {
                            showNotification(response.mensagem || 'Saiu do grupo com sucesso!', 'success');
                            renderGroupSection(); // Recarrega a seção
                        } else {
                            showNotification('Erro: ' + response.mensagem, 'error');
                        }
                    } catch (error) {
                        console.error('ERRO ao sair do grupo:', error);
                        showNotification('Erro ao sair do grupo: ' + error.message, 'error');
                    }
                });
            }, 1000));
            
        } else {
            // Jogador não está em nenhum grupo - mostra interface de criação/entrada
            container.innerHTML = `
                <div class="space-y-6">
                    <!-- Header modernizado -->
                    <div class="bg-surface-elevated border border-border-color rounded-lg p-6 shadow-lg text-center">
                        <div class="bg-accent-blue p-4 rounded-full w-16 h-16 mx-auto mb-4">
                            <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"/>
                            </svg>
                        </div>
                        <h4 class="text-2xl font-semibold text-primary-text mb-2">Grupos</h4>
                        <p class="text-secondary-text">Você ainda não faz parte de um grupo</p>
                        <p class="text-sm text-secondary-text mt-2">Crie um novo grupo ou entre em um existente para começar a competir!</p>
                    </div>
                    
                    <!-- Opções para Grupos modernizadas -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Criar Grupo -->
                        <div class="bg-surface-elevated border border-border-color rounded-lg p-6 hover:border-accent-blue transition-all duration-300">
                            <h5 class="text-xl font-semibold text-primary-text mb-4 flex items-center">
                                <div class="bg-green-500/20 p-2 rounded-lg mr-3">
                                    <svg class="w-5 h-5 text-green-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M12 4.5v15m7.5-7.5h-15" />
                                    </svg>
                                </div>
                                Criar Novo Grupo
                            </h5>
                            <p class="text-secondary-text mb-4 text-sm">Crie seu próprio grupo e convide outros jogadores.</p>
                            <div class="space-y-3">
                                <input type="text" id="create-group-name" placeholder="Nome do grupo" maxlength="30" class="w-full p-3 bg-surface-default text-primary-text rounded-lg border border-border-color focus:border-accent-blue focus:outline-none transition-colors">
                                <div class="relative">
                                    <input type="text" id="create-group-id" placeholder="ID personalizado (OBRIGATÓRIO, ex: C184C2)" maxlength="6" class="w-full p-3 bg-surface-default text-primary-text rounded-lg border border-border-color focus:border-accent-blue focus:outline-none transition-colors uppercase" style="text-transform: uppercase;" required>
                                    <div class="absolute right-3 top-1/2 transform -translate-y-1/2">
                                        <div class="group relative">
                                            <svg class="w-4 h-4 text-secondary-text cursor-help" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" d="M9.879 7.519c1.171-1.025 3.071-1.025 4.242 0 1.172 1.025 1.172 2.687 0 3.712-.203.179-.43.326-.67.442-.745.361-1.45.999-1.45 1.827v.75M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-9 5.25h.008v.008H12v-.008z" />
                                            </svg>
                                            <div class="absolute bottom-6 right-0 w-64 p-2 bg-gray-900 text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-10">
                                                6 caracteres (letras e números). Se vazio, será gerado automaticamente.
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <button id="create-group-btn" class="w-full bg-green-600 hover:bg-green-700 text-white font-semibold py-3 px-4 rounded-lg transition-all duration-300 transform hover:scale-105 flex items-center justify-center">
                                    <svg class="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M12 6v12m-3-2.818l.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.003-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                    Criar Grupo ($10,000)
                                </button>
                            </div>
                        </div>
                        
                        <!-- Entrar em Grupo -->
                        <div class="bg-surface-elevated border border-border-color rounded-lg p-6 hover:border-accent-blue transition-all duration-300">
                            <h5 class="text-xl font-semibold text-primary-text mb-4 flex items-center">
                                <div class="bg-blue-500/20 p-2 rounded-lg mr-3">
                                    <svg class="w-5 h-5 text-blue-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 9V5.25A2.25 2.25 0 0013.5 3h-6a2.25 2.25 0 00-2.25 2.25v13.5A2.25 2.25 0 007.5 21h6a2.25 2.25 0 002.25-2.25V15M12 9l3 3m0 0l-3 3m3-3H9" />
                                    </svg>
                                </div>
                                Entrar em Grupo
                            </h5>
                            <p class="text-secondary-text mb-4 text-sm">Digite o ID de um grupo existente para entrar.</p>
                            <div class="space-y-3">
                                <input type="text" id="join-group-id" placeholder="ID do grupo (ex: ABC123)" maxlength="6" class="w-full p-3 bg-surface-default text-primary-text rounded-lg border border-border-color focus:border-accent-blue focus:outline-none transition-colors uppercase">
                                <button id="join-group-btn" class="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-4 rounded-lg transition-all duration-300 transform hover:scale-105 flex items-center justify-center">
                                    <svg class="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M18 7.5v3m0 0v3m0-3h3m-3 0h-3m-2.25-4.125a3.375 3.375 0 11-6.75 0 3.375 3.375 0 016.75 0zM3 19.235v-.11a6.375 6.375 0 0112.674-1.334c.343.177.676.364 1.002.553m-13.676-2.109a9.035 9.035 0 00-.622-2.256c-.377-1.183-.377-2.477 0-3.66a9.035 9.035 0 00.622-2.256m0 7.173a9.037 9.037 0 00.622 2.256c.377 1.183.377 2.477 0 3.66a9.035 9.035 0 00-.622 2.256" />
                                    </svg>
                                    Entrar no Grupo
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Informações sobre Grupos -->
                    <div class="bg-gray-800 rounded-lg p-6 border border-gray-700">
                        <h5 class="text-lg font-semibold text-white mb-3 flex items-center">
                            <svg class="w-5 h-5 mr-2 text-yellow-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M11.25 11.25l.041-.02a.75.75 0 0 1 1.063.852l-.708 2.836a.75.75 0 0 0 1.063.853l.041-.021M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9-3.75h.008v.008H12V8.25Z" />
                            </svg>
                            Sobre os Grupos
                        </h5>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                            <div class="bg-gray-700 rounded-lg p-3">
                                <div class="text-green-400 font-semibold">Deface</div>
                                <div class="text-gray-300">Realize ataques em grupo contra outros jogadores</div>
                            </div>
                            <div class="bg-gray-700 rounded-lg p-3">
                                <div class="text-blue-400 font-semibold">Máximo 5 Membros</div>
                                <div class="text-gray-300">Cada grupo pode ter até 5 jogadores</div>
                            </div>
                            <div class="bg-gray-700 rounded-lg p-3">
                                <div class="text-purple-400 font-semibold">Ranking</div>
                                <div class="text-gray-300">Compita no ranking de grupos por pontos</div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            // Event listeners para os botões
            document.getElementById('create-group-btn').addEventListener('click', debounce(async function() {
                return preventDoubleClick('create-group-btn', async () => {
                    const groupName = document.getElementById('create-group-name').value.trim();
                    const customId = document.getElementById('create-group-id').value.trim().toUpperCase();
                    console.log('DEBUG: Nome do grupo digitado:', groupName);
                    console.log('DEBUG: ID personalizado:', customId);
                    
                    if (!groupName) {
                        showNotification('Digite um nome para o grupo', 'error');
                        return;
                    }
                    
                    if (groupName.length < 3) {
                        showNotification('O nome do grupo deve ter pelo menos 3 caracteres', 'error');
                        return;
                    }
                    
                    // ID personalizado é OBRIGATÓRIO
                    if (!customId) {
                        showNotification('ID personalizado é obrigatório', 'error');
                        return;
                    }
                    
                    // Validar formato do ID obrigatório
                    if (!/^[A-Z0-9]{6}$/.test(customId)) {
                        showNotification('ID deve ter exatamente 6 caracteres (letras e números)', 'error');
                        return;
                    }
                    
                    const requestData = { 
                        nome: groupName,
                        id_grupo: customId
                    };
                    console.log('DEBUG: Dados enviados para API:', requestData);
                    
                    try {
                        const response = await fetchAPI('/api/grupo/criar', 'POST', requestData);
                        console.log('DEBUG: Resposta da API:', response);
                        
                        if (response.sucesso) {
                            showNotification('Grupo criado com sucesso!', 'success');
                            document.getElementById('create-group-name').value = '';
                            document.getElementById('create-group-id').value = '';
                            renderGroupSection(); // Recarrega a seção
                        } else {
                            showNotification('Erro: ' + response.mensagem, 'error');
                        }
                    } catch (error) {
                        console.error('ERRO ao criar grupo:', error);
                        showNotification('Erro ao criar grupo: ' + error.message, 'error');
                    }
                });
            }, 1000));
            
            document.getElementById('join-group-btn').addEventListener('click', debounce(async function() {
                return preventDoubleClick('join-group-btn', async () => {
                    const groupId = document.getElementById('join-group-id').value.trim().toUpperCase();
                    console.log('DEBUG: ID do grupo digitado:', groupId);
                    
                    if (!groupId) {
                        showNotification('Digite o ID do grupo', 'error');
                        return;
                    }
                    
                    if (groupId.length !== 6) {
                        showNotification('O ID do grupo deve ter exatamente 6 caracteres', 'error');
                        return;
                    }
                    
                    const requestData = { id_grupo: groupId };
                    console.log('DEBUG: Dados enviados para API:', requestData);
                    
                    try {
                        const response = await fetchAPI('/api/grupo/entrar', 'POST', requestData);
                        console.log('DEBUG: Resposta da API:', response);
                        
                        if (response.sucesso) {
                            showNotification('Entrou no grupo com sucesso!', 'success');
                            renderGroupSection(); // Recarrega a seção
                        } else {
                            showNotification('Erro: ' + response.mensagem, 'error');
                        }
                    } catch (error) {
                        console.error('ERRO ao entrar no grupo:', error);
                        showNotification('Erro ao entrar no grupo: ' + error.message, 'error');
                    }
                });
            }, 1000));
        }
    } catch (error) {
        container.innerHTML = `<p class="text-center text-red-400">Erro: ${error.message}</p>`;
    }
    
    // Verificar se o usuário é admin e mostrar painel se for
    await checkAndShowAdminPanel();
}

// Função para verificar se o usuário é admin e mostrar painel
async function checkAndShowAdminPanel() {
    try {
        const adminInfo = await fetchAPI('/api/admin/torneio/info');
        if (adminInfo && adminInfo.sucesso && adminInfo.is_admin) {
            const adminPanel = document.getElementById('admin-tournament-panel');
            if (adminPanel) {
                adminPanel.style.display = 'block';
                
                // Atualizar informações do painel
                updateAdminPanelInfo(adminInfo);
                
                // Adicionar event listeners para os botões de admin
                setupAdminEventListeners();
            }
        }
    } catch (error) {
        // Se não for admin ou houver erro, o painel permanece oculto
        console.log('Usuário não é admin ou erro ao verificar:', error.message);
    }
}

// Função para atualizar informações do painel de admin
function updateAdminPanelInfo(adminInfo) {
    const statusElement = document.getElementById('admin-tournament-status');
    const gruposElement = document.getElementById('admin-total-grupos');
    const jogadoresElement = document.getElementById('admin-total-jogadores');
    const resetElement = document.getElementById('admin-proximo-reset');
    
    if (statusElement) {
        statusElement.textContent = 'Conectado como Admin';
    }
    
    if (gruposElement && adminInfo.total_grupos !== undefined) {
        gruposElement.textContent = adminInfo.total_grupos;
    }
    
    if (jogadoresElement && adminInfo.total_jogadores !== undefined) {
        jogadoresElement.textContent = adminInfo.total_jogadores;
    }
    
    if (resetElement) {
        resetElement.textContent = adminInfo.proximo_reset_automatico || '00:00 UTC';
    }
}

// Função para configurar event listeners do painel de admin
function setupAdminEventListeners() {
    const resetBtn = document.getElementById('admin-reset-tournament-btn');
    const refreshBtn = document.getElementById('admin-refresh-info-btn');
    
    if (resetBtn) {
        resetBtn.addEventListener('click', async function() {
            if (confirm('🏆 RESET MANUAL DO TORNEIO\n\nTem certeza que deseja resetar o torneio manualmente?\n\nEste processo irá:\n✅ Calcular rankings finais\n💰 Distribuir recompensas aos vencedores\n🔄 Resetar pontos de todos os participantes\n🎯 Reiniciar o torneio\n\n⚠️ Esta ação não pode ser desfeita!\n\nContinuar?')) {
                await resetTournamentManually();
            }
        });
    }
    
    if (refreshBtn) {
        refreshBtn.addEventListener('click', async function() {
            await refreshAdminInfo();
        });
    }
}

// Função para resetar torneio manualmente
async function resetTournamentManually() {
    const resetBtn = document.getElementById('admin-reset-tournament-btn');
    if (resetBtn) {
        resetBtn.disabled = true;
        resetBtn.innerHTML = `
            <svg class="w-4 h-4 mr-2 animate-spin" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Distribuindo Recompensas...
        `;
    }

    try {
        // Mostrar progresso
        showNotification('🏆 Calculando rankings finais...', 'info');

        const response = await fetchAPI('/api/torneio/reset-deface', 'POST');

        if (response && response.sucesso) {
            // Mostrar estatísticas detalhadas
            let mensagem = `🎉 Reset Manual Concluído!\n\n${response.mensagem}`;

            if (response.estatisticas) {
                const stats = response.estatisticas;
                mensagem += `\n\n📊 ESTATÍSTICAS DETALHADAS:`;
                mensagem += `\n🏆 Grupos premiados: ${stats.grupos_premiados || 0}`;
                mensagem += `\n👤 Jogadores premiados: ${stats.jogadores_premiados || 0}`;
                mensagem += `\n🔄 Grupos resetados: ${stats.grupos_resetados || 0}`;
                mensagem += `\n🔄 Jogadores resetados: ${stats.jogadores_resetados || 0}`;
            }

            if (response.reset_por) {
                mensagem += `\n\n👤 Reset por: ${response.reset_por}`;
            }

            showNotification(mensagem, 'success');

            // Recarregar os rankings
            await loadTournamentRankings();

            // Atualizar informações do admin
            await refreshAdminInfo();

            // Log detalhado
            console.log('🏆 Reset manual realizado:', response);
        } else {
            showNotification(`❌ Erro ao resetar torneio:\n\n${response?.mensagem || 'Erro desconhecido'}`, 'error');
        }
    } catch (error) {
        console.error('❌ Erro ao resetar torneio:', error);
        showNotification(`❌ Erro ao resetar torneio:\n\n${error.message}`, 'error');
    } finally {
        // Restaurar botão
        if (resetBtn) {
            resetBtn.disabled = false;
            resetBtn.innerHTML = `
                <svg class="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0l3.181 3.183a8.25 8.25 0 0013.803-3.7M4.031 9.865a8.25 8.25 0 0113.803-3.7l3.181 3.182m0-4.991v4.99" />
                </svg>
                Resetar Torneio Agora
            `;
        }
    }
}

// Função para atualizar informações do admin
async function refreshAdminInfo() {
    const refreshBtn = document.getElementById('admin-refresh-info-btn');
    if (refreshBtn) {
        refreshBtn.disabled = true;
        refreshBtn.innerHTML = `
            <svg class="w-4 h-4 mr-2 animate-spin" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Atualizando...
        `;
    }
    
    try {
        const adminInfo = await fetchAPI('/api/admin/torneio/info');
        if (adminInfo && adminInfo.sucesso) {
            updateAdminPanelInfo(adminInfo);
            showNotification('Informações atualizadas!', 'success');
        }
    } catch (error) {
        console.error('Erro ao atualizar informações:', error);
        showNotification('Erro ao atualizar informações.', 'error');
    } finally {
        // Restaurar botão
        if (refreshBtn) {
            refreshBtn.disabled = false;
            refreshBtn.innerHTML = `
                <svg class="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0l3.181 3.183a8.25 8.25 0 0013.803-3.7M4.031 9.865a8.25 8.25 0 0113.803-3.7l3.181 3.182m0-4.991v4.99" />
                </svg>
                Atualizar Info
            `;
        }
    }
}

// --- SISTEMA DE TORNEIO DE DEFACE ---
async function updateTournamentTimer() {
    const now = new Date();
    const tomorrow = new Date(now);
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(0, 0, 0, 0); // Meia-noite do próximo dia
    
    const timeLeft = tomorrow - now;
    
    const hours = Math.floor(timeLeft / (1000 * 60 * 60));
    const minutes = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((timeLeft % (1000 * 60)) / 1000);
    
    const timerElement = document.getElementById('tournament-timer');
    if (timerElement) {
        timerElement.textContent = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }
    
    // Verificar se chegou na meia-noite (reset automático acontece no servidor)
    if (hours === 0 && minutes === 0 && seconds <= 5) {
        // Só recarrega os rankings quando realmente chegar na hora do reset
        // Evita requisições excessivas
        setTimeout(() => {
            loadTournamentRankings();
            showNotification('Novo dia de torneio iniciado!', 'info');
        }, 6000); // Aguarda 6 segundos para o reset no servidor completar
    }
    
    // Atualiza a data de início do torneio
    const startDateElement = document.getElementById('tournament-start-date');
    if (startDateElement) {
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        startDateElement.textContent = today.toLocaleDateString('pt-BR', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    }
}

async function getTournamentRanking() {
    try {
        // Esta API agora verifica automaticamente se precisa resetar antes de retornar os dados
        const response = await fetchAPI('/api/torneio/ranking-deface');
        return response;
    } catch (error) {
        console.error('Erro ao buscar ranking do torneio:', error);
        return { sucesso: false, ranking_grupos: [] };
    }
}

// Função para recarregar os rankings de torneio (otimizada)
async function loadTournamentRankings() {
    try {
        // Só faz uma requisição para verificar e obter os dados
        const tournamentResponse = await getTournamentRanking();
        
        if (tournamentResponse && tournamentResponse.sucesso) {
            // Atualiza ranking de grupos se a aba estiver visível
            const rankingGruposContent = document.getElementById('content-ranking-grupos');
            if (rankingGruposContent && !rankingGruposContent.classList.contains('hidden')) {
                updateGroupTournamentRanking(tournamentResponse);
            }
            
            // Para ranking individual, só faz requisição se a aba estiver visível
            const rankingJogadoresContent = document.getElementById('content-ranking-jogadores');
            if (rankingJogadoresContent && !rankingJogadoresContent.classList.contains('hidden')) {
                console.log('Carregando ranking individual de deface...');
                // Usar nova API de ranking individual de deface com cache-busting
                const timestamp = Date.now();
                console.log('Fazendo requisição para:', `/api/ranking/deface-individual?t=${timestamp}`);
                const rankingIndividual = await fetchAPI(`/api/ranking/deface-individual?t=${timestamp}`);
                console.log('Dados do ranking individual:', rankingIndividual);
                if (rankingIndividual && rankingIndividual.sucesso) {
                    console.log('Atualizando ranking individual com', rankingIndividual.ranking?.length || 0, 'jogadores');
                    updatePlayerDefaceRanking(rankingIndividual);
                } else {
                    console.error('Erro no ranking individual:', rankingIndividual?.mensagem || 'Dados inválidos');
                }
            }
        }
    } catch (error) {
        console.error('Erro ao recarregar rankings de torneio:', error);
    }
}

// Função auxiliar para atualizar ranking de grupos
function updateGroupTournamentRanking(data) {
    const container = document.querySelector('#content-ranking-grupos .ranking-container');
    if (container && data.ranking_grupos && data.ranking_grupos.length > 0) {
        container.innerHTML = data.ranking_grupos.map((grupo, index) => `
            <div class="group-ranking-item bg-surface-elevated rounded-lg p-4 border border-border-color mb-3">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <div class="ranking-position w-8 h-8 rounded-full bg-accent-blue text-white font-bold flex items-center justify-center text-sm">
                            ${index + 1}
                        </div>
                        <div>
                            <h5 class="text-lg font-semibold text-primary-text">${grupo.nome}</h5>
                            <p class="text-sm text-secondary-text">${grupo.membros || 0} membros</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <div class="text-xl font-bold text-accent-blue">${grupo.pontos_deface || 0}</div>
                        <div class="text-xs text-secondary-text">pontos</div>
                    </div>
                </div>
            </div>
        `).join('');
    }
}

// Função auxiliar para atualizar ranking individual (legado)
function updatePlayerTournamentRanking(data) {
    const container = document.querySelector('#content-ranking-jogadores .ranking-container');
    if (container && data.ranking_jogadores && data.ranking_jogadores.length > 0) {
        container.innerHTML = data.ranking_jogadores.map((jogador, index) => `
            <div class="player-ranking-item bg-surface-elevated rounded-lg p-4 border border-border-color mb-3">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <div class="ranking-position w-8 h-8 rounded-full bg-accent-blue text-white font-bold flex items-center justify-center text-sm">
                            ${index + 1}
                        </div>
                        <div>
                            <h5 class="text-lg font-semibold text-primary-text">${jogador.nick}</h5>
                            <p class="text-sm text-secondary-text">${jogador.grupo_nome || 'Sem grupo'}</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <div class="text-xl font-bold text-accent-blue">${jogador.pontos_deface || 0}</div>
                        <div class="text-xs text-secondary-text">defaces</div>
                    </div>
                </div>
            </div>
        `).join('');
    }
}

// Nova função para atualizar ranking individual de deface
function updatePlayerDefaceRanking(data) {
    console.log('updatePlayerDefaceRanking chamada com:', data);
    // Buscar a aba completa de ranking de jogadores
    const tabContainer = document.getElementById('content-ranking-jogadores');
    if (!tabContainer) {
        console.error('Aba de ranking de jogadores não encontrada');
        return;
    }
    console.log('Aba de ranking encontrada:', tabContainer);

    if (!data.sucesso || !data.ranking || data.ranking.length === 0) {
        console.log('Nenhum dado de ranking válido:', {
            sucesso: data.sucesso,
            ranking: data.ranking,
            length: data.ranking?.length
        });
        tabContainer.innerHTML = `
            <div class="bg-gray-800 rounded-lg overflow-hidden shadow-xl">
                <div class="bg-gray-700 px-6 py-4 border-b border-gray-600">
                    <h5 class="text-lg font-semibold text-white flex items-center">
                        <svg class="w-5 h-5 mr-2 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"/>
                        </svg>
                        Top Jogadores por Deface
                    </h5>
                    <div class="text-sm text-gray-300 mt-1">Ranking Individual de Defaces</div>
                </div>
                <div class="text-center py-8">
                    <div class="text-secondary-text mb-2">
                        <svg class="w-12 h-12 mx-auto mb-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M16.5 10.5V6.75a4.5 4.5 0 10-9 0v3.75m-.75 11.25h10.5a2.25 2.25 0 002.25-2.25v-6.75a2.25 2.25 0 00-2.25-2.25H6.75a2.25 2.25 0 00-2.25 2.25v6.75a2.25 2.25 0 002.25 2.25z" />
                        </svg>
                    </div>
                    <p class="text-lg font-semibold text-primary-text mb-2">Nenhum deface registrado</p>
                    <p class="text-sm text-secondary-text">Seja o primeiro a realizar um deface!</p>
                </div>
            </div>
        `;
        return;
    }

    // Função para obter recompensa baseada na posição
    function getRecompensaInfo(posicao) {
        if (posicao === 1) return { dinheiro: 50000, shacks: 500, badge: '1º LUGAR', color: 'yellow' };
        if (posicao === 2) return { dinheiro: 30000, shacks: 300, badge: '2º LUGAR', color: 'gray' };
        if (posicao === 3) return { dinheiro: 20000, shacks: 200, badge: '3º LUGAR', color: 'amber' };
        if (posicao <= 5) return { dinheiro: 15000, shacks: 150, badge: 'TOP 5', color: 'blue' };
        if (posicao <= 10) return { dinheiro: 10000, shacks: 100, badge: 'TOP 10', color: 'blue' };
        return null;
    }

    console.log('Renderizando', data.ranking.length, 'jogadores no ranking');

    // Criar o HTML completo da aba com os dados do ranking
    const rankingHTML = data.ranking.map((jogador, index) => {
        const posicao = index + 1;
        const recompensa = getRecompensaInfo(posicao);

        let positionStyle = '';
        let positionIcon = '';
        let rankingBadge = '';

        if (posicao === 1) {
            positionStyle = 'bg-gradient-to-r from-yellow-500/20 to-yellow-600/20 border-l-4 border-yellow-500 transform hover:scale-[1.02] transition-all duration-200';
            positionIcon = '';
            rankingBadge = '<span class="bg-yellow-500 text-black text-xs px-2 py-1 rounded-full font-bold">1º LUGAR</span>';
        } else if (posicao === 2) {
            positionStyle = 'bg-gradient-to-r from-gray-400/20 to-gray-500/20 border-l-4 border-gray-400 transform hover:scale-[1.01] transition-all duration-200';
            positionIcon = '';
            rankingBadge = '<span class="bg-gray-400 text-black text-xs px-2 py-1 rounded-full font-bold">2º LUGAR</span>';
        } else if (posicao === 3) {
            positionStyle = 'bg-gradient-to-r from-amber-600/20 to-amber-700/20 border-l-4 border-amber-600 transform hover:scale-[1.01] transition-all duration-200';
            positionIcon = '';
            rankingBadge = '<span class="bg-amber-600 text-white text-xs px-2 py-1 rounded-full font-bold">3º LUGAR</span>';
        } else if (posicao <= 10) {
            positionStyle = 'bg-gradient-to-r from-accent-blue/10 to-blue-600/10 border-l-2 border-accent-blue/50 hover:bg-surface-hover transition-all duration-200';
            rankingBadge = '<span class="bg-accent-blue text-white text-xs px-2 py-1 rounded-full">TOP 10</span>';
        } else {
            positionStyle = 'hover:bg-surface-hover transition-colors duration-200 border-l border-border-color/30';
        }

        return `
            <div class="p-5 ${positionStyle} my-1 mx-2 rounded-lg">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <div class="flex items-center justify-center w-12 h-12 rounded-full bg-gradient-to-br from-accent-blue to-blue-600 text-white font-bold text-lg shadow-lg">
                            ${positionIcon || `#${posicao}`}
                        </div>
                        <div class="player-info" data-nick="${jogador.nick}" data-nivel="${jogador.nivel}" data-grupo="${jogador.grupo_nome || ''}" data-group-name="${jogador.grupo_nome || ''}">
                            <div class="flex items-center space-x-2 mb-1">
                                <h6 class="text-lg font-semibold text-primary-text cursor-pointer hover:text-accent-blue transition-colors duration-200" onclick="openPlayerModal('${jogador.nick}', event)">${jogador.nick}</h6>
                                ${rankingBadge}
                            </div>
                            <div class="flex items-center space-x-4 text-sm text-secondary-text">
                                <span class="flex items-center">
                                    <svg class="w-4 h-4 mr-1 text-accent-blue" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M13 10V3L4 14h7v7l9-11h-7z" />
                                    </svg>
                                    Nível ${jogador.nivel}
                                </span>
                                ${jogador.grupo_nome && jogador.grupo_nome !== 'Sem grupo' ? `
                                    <span class="flex items-center text-purple-400">
                                        <svg class="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" d="M18 18.72a9.094 9.094 0 003.741-.479 3 3 0 00-4.682-2.72m.94 3.198l.001.031c0 .225-.012.447-.037.666A11.944 11.944 0 0112 21c-2.17 0-4.207-.576-5.963-1.584A6.062 6.062 0 016 18.719m12 0a5.971 5.971 0 00-.941-3.197m0 0A5.995 5.995 0 0012 12.75a5.995 5.995 0 00-5.058 2.772m0 0a3 3 0 00-4.681 2.72 8.986 8.986 0 003.74.477m.94-3.197a5.971 5.971 0 00-.94 3.197M15 6.75a3 3 0 11-6 0 3 3 0 016 0zm6 3a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0zm-13.5 0a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z" />
                                        </svg>
                                        ${jogador.grupo_nome}
                                    </span>
                                ` : ''}
                                ${recompensa ? `
                                    <span class="flex items-center text-green-400">
                                        <svg class="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" d="M12 6v12m-3-2.818l.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.467-.22-2.121-.659-1.172-.879-1.172-2.303 0-3.182C10.536 7.78 11.268 7.561 12 7.561s1.464.219 2.121.578" />
                                        </svg>
                                        $${recompensa.dinheiro.toLocaleString()} + ${recompensa.shacks} shacks
                                    </span>
                                ` : ''}
                            </div>
                        </div>
                    </div>
                    <div class="text-right">
                        <div class="text-3xl font-bold text-accent-blue mb-1">
                            ${jogador.deface_points || 0}
                        </div>
                        <div class="text-xs text-secondary-text">
                            pontos deface
                        </div>
                    </div>
                </div>
            </div>
        `;
    }).join('');

    // Atualizar o conteúdo completo da aba
    tabContainer.innerHTML = `
        <div class="bg-gray-800 rounded-lg overflow-hidden shadow-xl">
            <div class="bg-gray-700 px-6 py-4 border-b border-gray-600">
                <h5 class="text-lg font-semibold text-white flex items-center">
                    <svg class="w-5 h-5 mr-2 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"/>
                    </svg>
                    Top Jogadores por Deface
                </h5>
                <div class="text-sm text-gray-300 mt-1 flex items-center justify-between">
                    <span>Ranking Individual de Defaces (${data.ranking.length} jogadores)</span>
                    <span class="text-xs text-blue-400 flex items-center">
                        <svg class="w-3 h-3 mr-1" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M8.25 15L12 18.75 15.75 15m-7.5-6L12 5.25 15.75 9" />
                        </svg>
                        Role para ver mais
                    </span>
                </div>
            </div>
            <div class="max-h-[500px] overflow-y-auto divide-y divide-gray-700 ranking-scrollbar pr-4 pb-6" style="scroll-behavior: smooth;">
                ${rankingHTML}
            </div>
        </div>
    `;

    console.log('Ranking renderizado com sucesso!');
}

function startTournamentTimer() {
    // Atualiza imediatamente
    updateTournamentTimer();
    
    // Atualiza a cada segundo
    setInterval(updateTournamentTimer, 1000);
}

// Função para mostrar abas dos grupos
window.showGroupTab = function(tabName) {
    // Remove active de todos os botões
    document.querySelectorAll('.tab-button').forEach(btn => {
        btn.classList.remove('bg-blue-600', 'text-white');
        btn.classList.add('text-gray-400', 'hover:text-white', 'hover:bg-gray-700');
    });
    
    // Esconde todos os conteúdos
    document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.add('hidden');
    });
    
    // Ativa o botão selecionado
    const selectedButton = document.getElementById(`tab-${tabName}`);
    if (selectedButton) {
        selectedButton.classList.remove('text-gray-400', 'hover:text-white', 'hover:bg-gray-700');
        selectedButton.classList.add('bg-blue-600', 'text-white');
    }
    
    // Mostra o conteúdo selecionado
    const selectedContent = document.getElementById(`content-${tabName}`);
    if (selectedContent) {
        selectedContent.classList.remove('hidden');
        
        // Se for a aba de ranking, inicia o timer do torneio e carrega dados
        if (tabName === 'ranking-grupos' || tabName === 'ranking-jogadores') {
            startTournamentTimer();
            // Carrega os rankings quando a aba é aberta (verificação automática de reset incluída)
            loadTournamentRankings();
        }
    }
};

// Cache de dados de jogadores para melhor performance
let playerModalCache = new Map();
let modalCacheExpiration = 5 * 60 * 1000; // 5 minutos

// --- FUNÇÃO AUXILIAR PARA ABRIR MODAL DE JOGADOR ---
async function openPlayerModal(playerName, event = null) {
    // Mostrar modal de loading primeiro para melhor UX
    showPlayerLoadingModal(playerName);
    
    try {
        console.log(`=== INÍCIO MODAL PARA ${playerName} ===`);
        console.log('Event recebido:', event);
        if (event && event.target) {
            console.log('Elemento clicado:', event.target);
            console.log('Dados do elemento:', event.target.dataset);
        }
        
        let playerData = null;
        const cacheKey = `player_${playerName}`;
        const now = Date.now();
        
        // Verifica cache primeiro
        if (playerModalCache.has(cacheKey)) {
            const cached = playerModalCache.get(cacheKey);
            if (now - cached.timestamp < modalCacheExpiration) {
                showPlayerDetails(playerName, cached.data);
                return;
            }
        }
        
        // 1. Verifica se é o próprio jogador logado (mais rápido)
        if (currentPlayer && currentPlayer.nick === playerName) {
            playerData = {
                nick: currentPlayer.nick,
                nivel: currentPlayer.nivel,
                tournament_points: currentPlayer.tournament_points || 0,
                deface_total: currentPlayer.deface_total || 0,
                grupo_nome: currentPlayer.grupo_nome || currentPlayer.group_name || currentPlayer.grupo
            };
        }
        
        // 2. Se não é o próprio jogador, busca dados públicos
        if (!playerData) {
            // Busca nos dados de ranking já carregados primeiro
            const rankingContainer = document.getElementById('ranking-container');
            if (rankingContainer && rankingContainer.levelData) {
                const rankingPlayer = rankingContainer.levelData.find(p => p.nick === playerName);
                if (rankingPlayer) {
                    playerData = {
                        nick: rankingPlayer.nick,
                        nivel: rankingPlayer.nivel,
                        tournament_points: 0,
                        deface_total: 0,
                        grupo_nome: rankingPlayer.grupo_nome || rankingPlayer.group_name || rankingPlayer.grupo,
                        grupo: rankingPlayer.grupo,
                        grupo_id: rankingPlayer.grupo_id
                    };
                }
            }
            
            // Se não achou no ranking, tenta buscar em dados do torneio já carregados
            if (!playerData) {
                // Busca em dados de ranking de jogadores do torneio se disponível
                const groupContainer = document.getElementById('group-info-container');
                if (groupContainer) {
                    // Tenta encontrar nos dados do torneio que podem estar em cache
                    const tournamentSections = document.querySelectorAll('[data-player-nick]');
                    for (const section of tournamentSections) {
                        if (section.dataset.playerNick === playerName) {
                            playerData = {
                                nick: playerName,
                                nivel: section.dataset.playerLevel || 'N/A',
                                tournament_points: section.dataset.playerPoints || 0,
                                deface_total: section.dataset.playerDeface || 0,
                                grupo_nome: section.dataset.playerGroup || null
                            };
                            break;
                        }
                    }
                }
            }
        }
        
        // 3. Se ainda não encontrou, busca nas APIs (apenas se necessário)
        if (!playerData) {
            try {
                // Busca dados do ranking de níveis
                const rankingResponse = await fetchAPI('/api/ranking/level');
                if (rankingResponse.sucesso && rankingResponse.ranking) {
                    const rankingPlayer = rankingResponse.ranking.find(p => p.nick === playerName);
                    if (rankingPlayer) {
                        playerData = {
                            nick: rankingPlayer.nick,
                            nivel: rankingPlayer.nivel,
                            tournament_points: 0,
                            deface_total: 0,
                            grupo_nome: rankingPlayer.grupo_nome || rankingPlayer.group_name || rankingPlayer.grupo
                        };
                    }
                }
            } catch (error) {
                console.log('API de ranking não disponível:', error);
            }
        }
        
        // 4. Busca dados do torneio para completar informações
        if (playerData) {
            try {
                const tournamentResponse = await fetchAPI('/api/torneio/ranking');
                if (tournamentResponse.sucesso && tournamentResponse.ranking) {
                    const tournamentPlayer = tournamentResponse.ranking.find(p => p.nick === playerName);
                    if (tournamentPlayer) {
                        playerData.tournament_points = tournamentPlayer.tournament_points || tournamentPlayer.pontos || 0;
                        playerData.deface_total = tournamentPlayer.deface_total || 0;
                        // Atualiza nome do grupo se não tiver
                        if (!playerData.grupo_nome && tournamentPlayer.grupo_nome) {
                            playerData.grupo_nome = tournamentPlayer.grupo_nome;
                        }
                    }
                }
            } catch (error) {
                console.log('API de torneio não disponível:', error);
            }
        }
        
        // 5. Busca informações específicas do jogador (incluindo grupo) se ainda não tem dados completos
        if (playerData && (!playerData.grupo_nome || playerData.grupo_nome === 'null' || playerData.grupo_nome === '')) {
            try {
                const playerResponse = await fetchAPI(`/api/jogador/${encodeURIComponent(playerName)}`);
                if (playerResponse.sucesso && playerResponse.jogador) {
                    // Atualiza informações que podem estar faltando
                    if (playerResponse.jogador.grupo_nome && playerResponse.jogador.grupo_nome !== 'null') {
                        playerData.grupo_nome = playerResponse.jogador.grupo_nome;
                    } else if (playerResponse.jogador.grupo && playerResponse.jogador.grupo !== 'null' && playerResponse.jogador.grupo !== '') {
                        playerData.grupo_nome = playerResponse.jogador.grupo;
                    }
                    
                    // Atualiza outros dados se necessário
                    if (playerData.nivel === 'N/A' && playerResponse.jogador.nivel) {
                        playerData.nivel = playerResponse.jogador.nivel;
                    }
                    if (!playerData.tournament_points && playerResponse.jogador.tournament_points) {
                        playerData.tournament_points = playerResponse.jogador.tournament_points;
                    }
                    if (!playerData.deface_total && playerResponse.jogador.deface_total) {
                        playerData.deface_total = playerResponse.jogador.deface_total;
                    }
                }
            } catch (error) {
                console.log('API específica do jogador não disponível:', error);
            }
        }
        
        // 6. Se não achou ainda, tenta buscar no próprio elemento do DOM que chamou o modal
        if (!playerData || !playerData.grupo_nome) {
            // Busca no elemento pai que pode conter dados do jogador
            try {
                const clickElement = event && event.target;
                if (clickElement) {
                    const playerElement = clickElement.closest('[data-nick], [data-player], .player-nick, .player-info');
                    if (playerElement) {
                        const domPlayerData = {
                            nick: playerName,
                            nivel: playerElement.dataset.nivel || playerElement.dataset.level || 'N/A',
                            tournament_points: playerElement.dataset.points || 0,
                            deface_total: playerElement.dataset.deface || 0,
                            grupo_nome: playerElement.dataset.grupo || playerElement.dataset.group || 
                                       playerElement.dataset.groupName || playerElement.dataset.grupoNome,
                            grupo: playerElement.dataset.grupo || playerElement.dataset.group
                        };
                        
                        if (!playerData) {
                            playerData = domPlayerData;
                        } else if (!playerData.grupo_nome && domPlayerData.grupo_nome) {
                            playerData.grupo_nome = domPlayerData.grupo_nome;
                        }
                        
                        console.log('Dados do jogador encontrados no DOM:', domPlayerData);
                    }
                }
            } catch (error) {
                console.log('Erro ao buscar dados no DOM:', error);
            }
        }
        
        // 7. Fallback para dados básicos se nada foi encontrado
        if (!playerData) {
            playerData = {
                nick: playerName,
                nivel: 'N/A',
                tournament_points: 0,
                deface_total: 0,
                grupo_nome: null
            };
        }
        
        // Armazena no cache para melhor performance
        playerModalCache.set(cacheKey, {
            data: playerData,
            timestamp: now
        });
        
        console.log('=== DADOS FINAIS PARA O MODAL ===');
        console.log('Player Data completo:', playerData);
        console.log('Grupo Nome:', playerData?.grupo_nome);
        console.log('Grupo:', playerData?.grupo);
        console.log('Grupo ID:', playerData?.grupo_id);
        console.log('================================');
        
        // Mostra o modal com os dados encontrados
        showPlayerDetails(playerName, playerData);
        
    } catch (error) {
        console.error('Erro ao carregar dados do jogador:', error);
        // Em caso de erro total, ainda mostra o modal com dados básicos
        const basicPlayerData = {
            nick: playerName,
            nivel: 'N/A',
            tournament_points: 0,
            deface_total: 0,
            grupo_nome: null
        };
        showPlayerDetails(playerName, basicPlayerData);
    }
}

// Função auxiliar para mostrar modal de loading
function showPlayerLoadingModal(playerName) {
    const loadingHTML = `
        <div id="player-details-modal" class="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
            <div class="bg-surface-elevated border border-border-color rounded-xl max-w-md w-full shadow-2xl">
                <!-- Header -->
                <div class="bg-gradient-to-r from-blue-600 to-cyan-600 p-6 rounded-t-xl">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center gap-3">
                            <div class="bg-white/20 rounded-full p-3">
                                <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"/>
                                </svg>
                            </div>
                            <div>
                                <h3 class="text-xl font-bold text-white">${playerName}</h3>
                                <p class="text-blue-100 text-sm">Carregando perfil...</p>
                            </div>
                        </div>
                        <button onclick="closePlayerDetailsModal()" class="text-white/70 hover:text-white transition-colors rounded-full p-2 hover:bg-white/10">
                            <svg class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>
                </div>
                
                <!-- Loading Content -->
                <div class="p-6 text-center">
                    <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-accent-blue mx-auto mb-4"></div>
                    <p class="text-secondary-text">Buscando informações do jogador...</p>
                </div>
            </div>
        </div>
    `;
    
    // Remove modal existente se houver
    const existingModal = document.getElementById('player-details-modal');
    if (existingModal) {
        existingModal.remove();
    }
    
    // Adiciona o modal de loading ao body
    document.body.insertAdjacentHTML('beforeend', loadingHTML);
    
    // Adiciona evento para fechar ao clicar fora
    document.getElementById('player-details-modal').addEventListener('click', (e) => {
        if (e.target.id === 'player-details-modal') {
            closePlayerDetailsModal();
        }
    });
}

// Torna a função global
window.openPlayerModal = openPlayerModal;

// --- FUNÇÕES DE MODAL DE DETALHES DOS GRUPOS E JOGADORES ---
function showGroupDetails(groupName, groupData) {
    const group = typeof groupData === 'string' ? JSON.parse(groupData.replace(/&quot;/g, '"')) : groupData;
    
    const modalHTML = `
        <div id="group-details-modal" class="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
            <div class="bg-surface-elevated border border-border-color rounded-xl max-w-md w-full max-h-[90vh] overflow-y-auto shadow-2xl">
                <!-- Header -->
                <div class="bg-gradient-to-r from-accent-blue to-purple-600 p-6 rounded-t-xl">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center gap-3">
                            <div class="bg-white/20 rounded-full p-3">
                                <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"/>
                                </svg>
                            </div>
                            <div>
                                <h3 class="text-xl font-bold text-white">${group.nome}</h3>
                                <p class="text-blue-100 text-sm">Detalhes do Grupo</p>
                            </div>
                        </div>
                        <button onclick="closeGroupDetailsModal()" class="text-white/70 hover:text-white transition-colors rounded-full p-2 hover:bg-white/10">
                            <svg class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>
                </div>
                
                <!-- Content -->
                <div class="p-6 space-y-6">
                    <!-- Stats do Grupo -->
                    <div class="grid grid-cols-2 gap-4">
                        <div class="bg-surface-default border border-border-color rounded-lg p-4 text-center">
                            <div class="text-2xl font-bold text-purple-400">${group.tournament_points || 0}</div>
                            <div class="text-xs text-secondary-text">Pontos de Torneio</div>
                        </div>
                        <div class="bg-surface-default border border-border-color rounded-lg p-4 text-center">
                            <div class="text-2xl font-bold text-accent-blue">${(group.membros?.length || 0)}/5</div>
                            <div class="text-xs text-secondary-text">Membros</div>
                        </div>
                    </div>
                    

                    
                    <!-- Lista de Membros -->
                    <div>
                        <h4 class="text-sm font-semibold text-secondary-text mb-3">Membros do Grupo</h4>
                        <div class="space-y-2 max-h-48 overflow-y-auto scrollbar-thin scrollbar-thumb-accent-blue scrollbar-track-surface-default">
                            ${(group.membros || []).map(membro => {
                                const membroNome = typeof membro === 'string' ? membro : (membro.nick || membro.nome || 'Membro');
                                return `
                                <div class="bg-surface-default border border-border-color rounded-lg p-3 flex items-center justify-between hover:bg-surface-hover transition-colors duration-300">
                                    <div class="flex items-center gap-3">
                                        <div class="flex items-center justify-center w-10 h-10 rounded-full bg-accent-blue text-white text-sm font-bold">
                                            ${membroNome.charAt(0).toUpperCase()}
                                        </div>
                                        <span class="text-primary-text font-medium cursor-pointer hover:text-accent-blue transition-colors" onclick="openPlayerModal('${membroNome}')">${membroNome}</span>
                                    </div>
                                    ${membroNome === group.lider ?
                                        '<span class="text-xs bg-yellow-500/20 text-yellow-400 px-2 py-1 rounded border border-yellow-500/30 font-semibold">LÍDER</span>' :
                                        '<span class="text-xs text-secondary-text bg-surface-hover px-2 py-1 rounded border border-border-color">Membro</span>'
                                    }
                                </div>
                                `;
                            }).join('')}
                        </div>
                    </div>
                    
                    <!-- Data de Criação (se disponível) -->
                    ${group.created_at ? `
                        <div class="bg-surface-default border border-border-color rounded-lg p-4">
                            <h4 class="text-sm font-semibold text-secondary-text mb-2">Criado em</h4>
                            <p class="text-primary-text font-medium">${new Date(group.created_at).toLocaleDateString('pt-BR', { 
                                day: '2-digit', 
                                month: '2-digit', 
                                year: 'numeric',
                                hour: '2-digit',
                                minute: '2-digit'
                            })}</p>
                        </div>
                    ` : ''}
                </div>
                
                <!-- Footer -->
                <div class="bg-surface-hover border-t border-border-color p-4 rounded-b-xl">
                    <button onclick="closeGroupDetailsModal()" class="w-full bg-surface-default hover:bg-surface-hover border border-border-color text-primary-text font-semibold py-3 px-4 rounded-lg transition-all duration-300 hover:border-accent-blue">
                        Fechar
                    </button>
                </div>
            </div>
        </div>
    `;
    
    // Remove modal existente se houver
    const existingModal = document.getElementById('group-details-modal');
    if (existingModal) {
        existingModal.remove();
    }
    
    // Adiciona o modal ao body
    document.body.insertAdjacentHTML('beforeend', modalHTML);
    
    // Adiciona evento para fechar ao clicar fora
    document.getElementById('group-details-modal').addEventListener('click', (e) => {
        if (e.target.id === 'group-details-modal') {
            closeGroupDetailsModal();
        }
    });
}

function showPlayerDetails(playerName, playerData) {
    const player = typeof playerData === 'string' ? JSON.parse(playerData.replace(/&quot;/g, '"')) : playerData;
    
    // Debug: Verificar dados do grupo recebidos
    console.log('Dados do jogador para modal:', {
        nick: player.nick,
        grupo_nome: player.grupo_nome,
        group_name: player.group_name,
        grupo: player.grupo,
        grupo_id: player.grupo_id
    });
    
    // Função auxiliar para determinar o nome do grupo
    function getGroupName(player) {
        console.log('Debug getGroupName - Dados do jogador:', player);
        
        // Prioridade: grupo_nome > group_name > grupo (se não for ID ou vazio)
        if (player.grupo_nome && player.grupo_nome !== 'null' && player.grupo_nome !== '') {
            console.log('Grupo encontrado em grupo_nome:', player.grupo_nome);
            return player.grupo_nome;
        }
        if (player.group_name && player.group_name !== 'null' && player.group_name !== '') {
            console.log('Grupo encontrado em group_name:', player.group_name);
            return player.group_name;
        }
        if (player.grupo && 
            player.grupo !== 'null' && 
            player.grupo !== '' && 
            player.grupo !== player.grupo_id &&
            player.grupo.length > 6) { // Nome do grupo geralmente é maior que 6 caracteres
            console.log('Grupo encontrado em grupo:', player.grupo);
            return player.grupo;
        }
        
        console.log('Nenhum grupo válido encontrado para:', player.nick || 'jogador desconhecido');
        return null;
    }
    
    const groupName = getGroupName(player);
    
    const modalHTML = `
        <div id="player-details-modal" class="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
            <div class="bg-surface-elevated border border-border-color rounded-xl max-w-md w-full max-h-[90vh] overflow-y-auto shadow-2xl">
                <!-- Header -->
                <div class="bg-gradient-to-r from-blue-600 to-cyan-600 p-6 rounded-t-xl">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center gap-3">
                            <div class="bg-white/20 rounded-full p-3">
                                <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"/>
                                </svg>
                            </div>
                            <div>
                                <h3 class="text-xl font-bold text-white" ${player.nick_color ? `style="color: ${player.nick_color}"` : ''}>${player.nick}</h3>
                                <p class="text-blue-100 text-sm">Perfil do Jogador</p>
                            </div>
                        </div>
                        <button onclick="closePlayerDetailsModal()" class="text-white/70 hover:text-white transition-colors rounded-full p-2 hover:bg-white/10">
                            <svg class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>
                </div>
                
                <!-- Content -->
                <div class="p-6 space-y-6">
                    <!-- Informações Públicas -->
                    <div class="bg-surface-default border border-border-color rounded-lg p-4 text-center">
                        <div class="text-3xl font-bold text-accent-blue mb-2">${player.nivel || 'N/A'}</div>
                        <div class="text-sm text-secondary-text">Nível de Hacking</div>
                    </div>
                    
                    <!-- Stats de Jogo -->
                    <div class="grid grid-cols-2 gap-4">
                        <div class="bg-surface-default border border-border-color rounded-lg p-4 text-center">
                            <div class="text-2xl font-bold text-purple-400">${player.tournament_points || 0}</div>
                            <div class="text-xs text-secondary-text">Pontos de Torneio</div>
                        </div>
                        <div class="bg-surface-default border border-border-color rounded-lg p-4 text-center">
                            <div class="text-2xl font-bold text-orange-400">${player.deface_total || 0}</div>
                            <div class="text-xs text-secondary-text">Defacements</div>
                        </div>
                    </div>
                    
                    <!-- Grupo do Jogador -->
                    <div class="bg-surface-default border border-border-color rounded-lg p-4">
                        <h4 class="text-sm font-semibold text-secondary-text mb-3">Grupo</h4>
                        ${groupName ? `
                            <div class="flex items-center gap-3">
                                <div class="flex items-center justify-center w-10 h-10 rounded-full bg-purple-600 text-white text-sm font-bold">
                                    ${groupName.charAt(0).toUpperCase()}
                                </div>
                                <div>
                                    <span class="text-primary-text font-medium">${groupName}</span>
                                    <div class="text-xs bg-purple-500/20 text-purple-400 px-2 py-1 rounded border border-purple-500/30 inline-block mt-1">Membro</div>
                                </div>
                            </div>
                        ` : '<p class="text-secondary-text">Não pertence a nenhum grupo</p>'}
                    </div>
                    

                </div>
                
                <!-- Footer -->
                <div class="bg-surface-hover border-t border-border-color p-4 rounded-b-xl">
                    <button onclick="closePlayerDetailsModal()" class="w-full bg-surface-default hover:bg-surface-hover border border-border-color text-primary-text font-semibold py-3 px-4 rounded-lg transition-all duration-300 hover:border-accent-blue">
                        Fechar
                    </button>
                </div>
            </div>
        </div>
    `;
    
    // Remove modal existente se houver
    const existingModal = document.getElementById('player-details-modal');
    if (existingModal) {
        existingModal.remove();
    }
    
    // Adiciona o modal ao body
    document.body.insertAdjacentHTML('beforeend', modalHTML);
    
    // Adiciona evento para fechar ao clicar fora
    document.getElementById('player-details-modal').addEventListener('click', (e) => {
        if (e.target.id === 'player-details-modal') {
            closePlayerDetailsModal();
        }
    });
}

function closeGroupDetailsModal() {
    const modal = document.getElementById('group-details-modal');
    if (modal) {
        modal.remove();
    }
}

function closePlayerDetailsModal() {
    const modal = document.getElementById('player-details-modal');
    if (modal) {
        modal.remove();
    }
}



// Função para copiar IP do extrato bancário
function copyToClipboard(text, buttonElement) {
    if (!text) {
        showNotification('Nenhum IP para copiar', 'error');
        return;
    }
    
    if (navigator.clipboard && window.isSecureContext) {
        navigator.clipboard.writeText(text).then(() => {
            showNotification('IP copiado!', 'success');
            // Feedback visual no botão
            const originalText = buttonElement.innerHTML;
            buttonElement.innerHTML = `
                <svg class="w-3 h-3" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M4.5 12.75l6 6 9-13.5" />
                </svg>
                Copiado!
            `;
            buttonElement.classList.add('bg-green-600');
            buttonElement.classList.remove('bg-accent-blue', 'hover:bg-blue-600');
            
            setTimeout(() => {
                buttonElement.innerHTML = originalText;
                buttonElement.classList.remove('bg-green-600');
                buttonElement.classList.add('bg-accent-blue', 'hover:bg-blue-600');
            }, 2000);
        }).catch(() => {
            showNotification('Erro ao copiar IP', 'error');
        });
    } else {
        // Fallback para navegadores sem clipboard API
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        try {
            document.execCommand('copy');
            showNotification('IP copiado!', 'success');
        } catch (err) {
            showNotification('Erro ao copiar IP', 'error');
        }
        document.body.removeChild(textArea);
    }
}

// Torna as funções globais
window.showGroupDetails = showGroupDetails;
window.showPlayerDetails = showPlayerDetails;
window.closeGroupDetailsModal = closeGroupDetailsModal;
window.closePlayerDetailsModal = closePlayerDetailsModal;
window.copyGroupId = copyGroupId;

// --- FUNÇÃO RENDERIZAR SEÇÃO DE SCAN ---
async function renderScanSection() {
    const container = document.getElementById('scan-content');
    if (!container) return;
    
    // Verificar status das conexões antes de renderizar
    await atualizarStatusConexoes();
    
    container.innerHTML = `
        <div class="h-full overflow-y-auto custom-scrollbar">
            <div class="max-w-4xl mx-auto p-6 pb-20 space-y-6">
                <div class="bg-surface-elevated border border-border-color rounded-xl p-6">
                    <h2 class="text-2xl font-bold text-primary-text mb-4 flex items-center gap-3">
                        <svg class="w-8 h-8 text-accent-blue" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 15.75l-2.489-2.489m0 0a3.375 3.375 0 10-4.773-4.773 3.375 3.375 0 004.774 4.774zM21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        Scanner de Rede
                    </h2>
                    <p class="text-secondary-text mb-6">Escaneie a rede em busca de alvos vulneráveis</p>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="bg-surface-default border border-border-color rounded-lg p-4">
                            <h3 class="text-lg font-semibold text-primary-text mb-3">Scan Rápido</h3>
                            <p class="text-secondary-text text-sm mb-4">Busca alvos próximos ao seu nível</p>
                            <button id="quick-scan-btn" class="w-full bg-accent-blue hover:bg-blue-600 text-white font-bold py-3 px-4 rounded-lg transition-all duration-300">
                                Iniciar Scan Rápido
                            </button>
                        </div>
                        
                        <div class="bg-surface-default border border-border-color rounded-lg p-4">
                            <h3 class="text-lg font-semibold text-primary-text mb-3">Scan Avançado</h3>
                            <p class="text-secondary-text text-sm mb-4">Busca por IP específico</p>
                            <div class="space-y-3">
                                <input type="text" id="advanced-scan-ip" 
                                       placeholder="Digite o IP (ex: ***********)" 
                                       class="w-full p-3 bg-surface-elevated border border-border-color rounded-lg text-primary-text placeholder-gray-500 font-mono"
                                       pattern="^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$">
                                <button id="advanced-scan-btn" class="w-full bg-purple-600 hover:bg-purple-700 text-white font-bold py-3 px-4 rounded-lg transition-all duration-300">
                                    Scan por IP
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div id="scan-results" class="bg-surface-elevated border border-border-color rounded-xl p-6">
                    <h3 class="text-xl font-semibold text-primary-text mb-4">Resultados do Scan</h3>
                    <div id="scan-results-content">
                        <div class="text-center text-secondary-text py-8">
                            <svg class="w-16 h-16 mx-auto mb-4 text-gray-500" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M21 21l-5.197-5.197m0 0A7.5 7.5 0 105.196 5.196a7.5 7.5 0 0010.607 10.607z" />
                            </svg>
                            <p>Nenhum scan realizado ainda</p>
                            <p class="text-sm">Execute um scan para ver os alvos disponíveis</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // Adicionar event listeners após renderizar o HTML
    setTimeout(() => {
        const quickScanBtn = document.getElementById('quick-scan-btn');
        const advancedScanBtn = document.getElementById('advanced-scan-btn');
        const advancedScanInput = document.getElementById('advanced-scan-ip');
        
        if (quickScanBtn) {
            quickScanBtn.addEventListener('click', handleQuickScan);
        }
        
        if (advancedScanBtn) {
            advancedScanBtn.addEventListener('click', handleAdvancedScan);
        }
        
        if (advancedScanInput) {
            // Permitir scan ao pressionar Enter
            advancedScanInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    handleAdvancedScan();
                }
            });
            
            // Validação de IP em tempo real
            advancedScanInput.addEventListener('input', (e) => {
                const ip = e.target.value;
                const isValidIP = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/.test(ip);
                const btn = document.getElementById('advanced-scan-btn');
                
                if (ip.length > 0 && !isValidIP) {
                    e.target.style.borderColor = '#ef4444';
                    if (btn) btn.disabled = true;
                } else {
                    e.target.style.borderColor = '';
                    if (btn) btn.disabled = false;
                }
            });
        }
    }, 100);
}

// --- FUNÇÃO RENDERIZAR SEÇÃO DE MINERADORA (NOVO SISTEMA) ---
async function renderMineradoraSection() {
    const container = document.getElementById('mineradora-container');
    if (!container) return;
    
    // Mostra loading inicial
    container.innerHTML = `
        <div class="flex items-center justify-center py-16">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-accent-blue"></div>
            <p class="text-secondary-text ml-3">Carregando mineração...</p>
        </div>
    `;
    
    try {
        // Carrega status dos Shacks disponíveis
        const shacksResponse = await fetchAPI('/api/mineracao/shacks-disponiveis');
        
        const nivel_mineradora = currentPlayer?.nivel_mineradora || 1;
        const habilidades = currentPlayer?.habilidades_adquiridas || {};
        const cryptoMinerAtivo = 'crypto_miner' in habilidades;
        
        // Calcular produção com nova fórmula mais atrativa
        // Nível 1: $15/min, Nível 2: $35/min, Nível 3: $60/min, etc.
        const dinheiroPorMinuto = 15 + (nivel_mineradora - 1) * 20 + (nivel_mineradora - 1) * 5;
        let shackPorMinuto = nivel_mineradora * 0.6; // Nível 1 = 0.6 Shacks/min

        // Aplica bônus do Crypto Miner
        if (cryptoMinerAtivo) {
            shackPorMinuto *= 1.15; // +15%
        }
        
        container.innerHTML = `
            <div class="h-full overflow-y-auto custom-scrollbar">
                <div class="max-w-4xl mx-auto p-6 pb-20 space-y-6">
                    
                    <!-- Header da Mineração -->
                    <div class="bg-surface-elevated border border-border-color rounded-xl p-6">
                        <h2 class="text-2xl font-bold text-primary-text mb-4 flex items-center gap-3">
                            <svg class="w-8 h-8 text-accent-blue" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M21.75 17.25v-.228a4.5 4.5 0 0 0-.12-1.03l-2.268-9.64a3.375 3.375 0 0 0-3.285-2.816H7.905a3.375 3.375 0 0 0-3.285 2.816L2.37 16.02a4.5 4.5 0 0 0-.12 1.03v.228m19.5 0a3 3 0 0 1-3 3H5.25a3 3 0 0 1-3-3m19.5 0a3 3 0 0 0-3-3H5.25a3 3 0 0 0-3 3m16.5 0h.008v.008h-.008v-.008Z"></path>
                            </svg>
                            Mineração de Recursos
                        </h2>
                        <p class="text-secondary-text mb-6">Sistema duplo: dinheiro automático e Shacks coletáveis</p>
                        
                        <!-- Status das Habilidades NFT -->
                        ${cryptoMinerAtivo ? `
                            <div class="bg-gradient-to-r from-yellow-500/10 to-orange-500/10 border border-yellow-500/30 rounded-lg p-3 mb-6">
                                <div class="flex items-center">
                                    <svg class="w-5 h-5 text-yellow-400 mr-2" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M9.813 15.904 9 18.75l-.813-2.846a4.5 4.5 0 0 0-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 0 0 3.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 0 0 3.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 0 0-3.09 3.09Z" />
                                    </svg>
                                    <span class="text-yellow-400 font-semibold">Crypto Miner Ativo (+15% Shacks)</span>
                                </div>
                            </div>
                        ` : ''}
                        
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <div class="bg-surface-default border border-border-color rounded-lg p-4 text-center">
                                <h3 class="text-lg font-semibold text-primary-text mb-2">Nível</h3>
                                <p class="text-3xl font-bold text-green-400">${nivel_mineradora}</p>
                            </div>
                            
                            <div class="bg-surface-default border border-border-color rounded-lg p-4 text-center">
                                <h3 class="text-lg font-semibold text-primary-text mb-2">Dinheiro</h3>
                                <p class="text-xl font-bold text-green-400">$${dinheiroPorMinuto}/min</p>
                                <p class="text-xs text-secondary-text mt-1">Automático</p>
                            </div>
                            
                            <div class="bg-surface-default border border-border-color rounded-lg p-4 text-center">
                                <h3 class="text-lg font-semibold text-primary-text mb-2">Shacks</h3>
                                <p class="text-xl font-bold text-accent-blue">${shackPorMinuto.toFixed(1)}/min</p>
                                <p class="text-xs text-secondary-text mt-1">Coletável</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Seção de Coleta de Shacks -->
                    <div class="bg-surface-elevated border border-border-color rounded-xl p-6">
                        <h3 class="text-xl font-semibold text-primary-text mb-4 flex items-center">
                            <svg class="w-6 h-6 text-accent-blue mr-2" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M21 11.25v8.25a1.5 1.5 0 0 1-1.5 1.5H5.25a1.5 1.5 0 0 1-1.5-1.5v-8.25M12 4.875A2.625 2.625 0 1 0 9.375 7.5H12m0-2.625V7.5m0-2.625A2.625 2.625 0 1 1 14.625 7.5H12m0 0V21m-8.625-9.75h18c.621 0 1.125-.504 1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125h-18c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125Z" />
                            </svg>
                            Coleta de Shacks
                        </h3>
                        
                        <div id="shacks-collection-area">
                            ${shacksResponse.sucesso ? renderShacksCollectionArea(shacksResponse) : `
                                <div class="text-center py-8">
                                    <div class="text-red-400 mb-2">Erro ao carregar status dos Shacks</div>
                                    <p class="text-secondary-text text-sm">${shacksResponse.mensagem || 'Erro desconhecido'}</p>
                                </div>
                            `}
                        </div>
                    </div>
                    
                    <!-- Informações do Sistema -->
                    <div class="bg-surface-elevated border border-border-color rounded-xl p-6">
                        <h3 class="text-xl font-semibold text-primary-text mb-4">Sistema de Mineração</h3>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Dinheiro Automático -->
                            <div class="bg-surface-default border border-border-color rounded-lg p-4">
                                <h4 class="font-semibold text-green-400 mb-3 flex items-center">
                                    <svg class="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M12 6v12m-3-2.818.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.003-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
                                    </svg>
                                    Dinheiro Automático
                                </h4>
                                <div class="space-y-3 text-sm">
                                    <div class="flex justify-between">
                                        <span class="text-secondary-text">Por minuto:</span>
                                        <span class="text-green-400 font-semibold">$${dinheiroPorMinuto}</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-secondary-text">Por hora:</span>
                                        <span class="text-green-400 font-semibold">$${dinheiroPorMinuto * 60}</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-secondary-text">Frequência:</span>
                                        <span class="text-primary-text">A cada hora</span>
                                    </div>
                                    <div class="bg-green-500/10 border border-green-500/30 rounded p-2 mt-3">
                                        <div class="text-xs text-green-400">Processamento automático - sem ação necessária</div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Shacks Coletáveis -->
                            <div class="bg-surface-default border border-border-color rounded-lg p-4">
                                <h4 class="font-semibold text-accent-blue mb-3 flex items-center">
                                    <svg class="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M21 11.25v8.25a1.5 1.5 0 0 1-1.5 1.5H5.25a1.5 1.5 0 0 1-1.5-1.5v-8.25M12 4.875A2.625 2.625 0 1 0 9.375 7.5H12m0-2.625V7.5m0-2.625A2.625 2.625 0 1 1 14.625 7.5H12m0 0V21m-8.625-9.75h18c.621 0 1.125-.504 1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125h-18c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125Z" />
                                    </svg>
                                    Shacks Coletáveis
                                </h4>
                                <div class="space-y-3 text-sm">
                                    <div class="flex justify-between">
                                        <span class="text-secondary-text">Por minuto:</span>
                                        <span class="text-accent-blue font-semibold">${shackPorMinuto.toFixed(1)}</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-secondary-text">Por hora:</span>
                                        <span class="text-accent-blue font-semibold">${(shackPorMinuto * 60).toFixed(1)}</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-secondary-text">Máximo acúmulo:</span>
                                        <span class="text-primary-text">24 horas</span>
                                    </div>
                                    <div class="bg-blue-500/10 border border-blue-500/30 rounded p-2 mt-3">
                                        <div class="text-xs text-blue-400">Shacks requerem coleta manual</div>
                                    </div>
                                    <div class="bg-green-500/10 border border-green-500/30 rounded p-2 mt-2">
                                        <div class="text-xs text-green-400">💰 Dinheiro é adicionado automaticamente a cada minuto</div>
                                        <div class="text-xs text-green-300 mt-1">Valores aumentados para progressão mais rápida!</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Próximo Upgrade -->
                        <div class="mt-6 bg-surface-default border border-border-color rounded-lg p-4">
                            <h4 class="font-semibold text-primary-text mb-3">Próximo Upgrade (Nível ${nivel_mineradora + 1})</h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                                <div class="flex justify-between">
                                    <span class="text-secondary-text">Novo dinheiro/min:</span>
                                    <span class="text-green-400 font-semibold">$${Math.max(0, nivel_mineradora * 3)}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-secondary-text">Novo Shack/min:</span>
                                    <span class="text-accent-blue font-semibold">${(Math.max(0.6, (nivel_mineradora + 1) * 0.6) * (cryptoMinerAtivo ? 1.15 : 1)).toFixed(1)}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-secondary-text">Aumento dinheiro:</span>
                                    <span class="text-yellow-400 font-semibold">+$${Math.max(0, 3)}/min</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-secondary-text">Aumento Shack:</span>
                                    <span class="text-yellow-400 font-semibold">+${(0.6 * (cryptoMinerAtivo ? 1.15 : 1)).toFixed(1)}/min</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    } catch (error) {
        console.error('Erro ao carregar seção de mineração:', error);
        container.innerHTML = `
            <div class="text-center py-16">
                <div class="w-16 h-16 bg-red-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-red-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m9-.75a9 9 0 11-18 0 9 9 0 0118 0zm-9 3.75h.008v.008H12v-.008z" />
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-red-400 mb-2">Erro ao Carregar Mineração</h3>
                <p class="text-secondary-text">Não foi possível conectar com o servidor</p>
                <button onclick="renderMineradoraSection()" class="mt-4 bg-accent-blue hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors">
                    Tentar Novamente
                </button>
            </div>
        `;
    }
}

// Função auxiliar para renderizar a área de coleta de Shacks
function renderShacksCollectionArea(shacksData) {
    const shacksDisponiveis = shacksData.shacks_disponiveis || 0;
    const shackPorMinuto = shacksData.shack_por_minuto || 0;
    const minutosAcumulados = shacksData.minutos_acumulados || 0;
    const tempoProximaColeta = shacksData.tempo_proxima_coleta || 0;
    const cryptoMinerAtivo = shacksData.crypto_miner_ativo || false;
    
    // Sempre mostra a interface, mas adapta com base na disponibilidade
    const temShacks = shacksDisponiveis > 0;
    const statusMensagem = temShacks ? 
        'Prontos para coleta!' : 
        (shackPorMinuto > 0 ? 
            `Produzindo ${shackPorMinuto.toFixed(1)} Shacks/min - próxima coleta em ${Math.ceil(tempoProximaColeta/60)} minutos` : 
            'Mineradora não ativa - upgrade necessário'
        );
    
    return `
        <div class="text-center py-6">
            <!-- Shacks Disponíveis -->
            <div class="bg-gradient-to-br from-blue-500/10 to-purple-500/10 border border-blue-500/30 rounded-xl p-6 mb-6">
                <div class="flex items-center justify-center mb-4">
                    <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center ${temShacks ? 'animate-pulse' : ''}">
                        <svg class="w-8 h-8 text-white" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M21 11.25v8.25a1.5 1.5 0 0 1-1.5 1.5H5.25a1.5 1.5 0 0 1-1.5-1.5v-8.25M12 4.875A2.625 2.625 0 1 0 9.375 7.5H12m0-2.625V7.5m0-2.625A2.625 2.625 0 1 1 14.625 7.5H12m0 0V21m-8.625-9.75h18c.621 0 1.125-.504 1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125h-18c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125Z" />
                        </svg>
                    </div>
                </div>
                
                <h4 class="text-2xl font-bold text-accent-blue mb-2">${shacksDisponiveis} Shacks</h4>
                <p class="text-secondary-text mb-4">${statusMensagem}</p>
                
                <button id="coletar-shacks-btn" 
                        class="group relative overflow-hidden ${temShacks ? 
                            'bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-500 hover:to-purple-500' : 
                            'bg-gray-600 cursor-not-allowed opacity-50'
                        } text-white font-semibold py-3 px-8 rounded-xl transition-all duration-300 ${temShacks ? 'transform hover:scale-105 shadow-lg hover:shadow-xl' : ''}"
                        ${!temShacks ? 'disabled' : ''}>
                    <div class="absolute inset-0 bg-white/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    <div class="relative flex items-center justify-center space-x-2">
                        <svg class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M21 11.25v8.25a1.5 1.5 0 0 1-1.5 1.5H5.25a1.5 1.5 0 0 1-1.5-1.5v-8.25M12 4.875A2.625 2.625 0 1 0 9.375 7.5H12m0-2.625V7.5m0-2.625A2.625 2.625 0 1 1 14.625 7.5H12m0 0V21m-8.625-9.75h18c.621 0 1.125-.504 1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125h-18c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125Z" />
                        </svg>
                        <span>${temShacks ? `Coletar ${shacksDisponiveis} Shacks` : 'Aguardando Produção'}</span>
                    </div>
                </button>
            </div>
            
            <!-- Detalhes da Mineração -->
            <div class="bg-surface-default border border-border-color rounded-lg p-4">
                <h5 class="font-semibold text-primary-text mb-3">Detalhes da Coleta</h5>
                <div class="grid grid-cols-2 gap-4 text-sm">
                    <div class="flex justify-between">
                        <span class="text-secondary-text">Taxa:</span>
                        <span class="text-accent-blue font-semibold">${shackPorMinuto.toFixed(1)}/min</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-secondary-text">Acumulado:</span>
                        <span class="text-primary-text">${(minutosAcumulados/60).toFixed(1)}h</span>
                    </div>
                    ${cryptoMinerAtivo ? `
                        <div class="col-span-2 bg-yellow-500/10 border border-yellow-500/30 rounded p-2">
                            <div class="text-xs text-yellow-400 flex items-center justify-center">
                                <svg class="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M9.813 15.904 9 18.75l-.813-2.846a4.5 4.5 0 0 0-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 0 0 3.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 0 0 3.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 0 0-3.09 3.09Z" />
                                </svg>
                                Crypto Miner: +15% bônus aplicado
                            </div>
                        </div>
                    ` : ''}
                </div>
            </div>
        </div>
    `;
}

// --- FUNÇÃO RENDERIZAR SEÇÃO DE MERCADO NEGRO ---
async function renderMercadoNegroSection() {
    const container = document.getElementById('mercado-negro-container');
    if (!container) return;
    
    // Calcula o preço dinâmico baseado no nível da mineradora
    const nivelMineradora = currentPlayer?.nivel_mineradora || 1;
    const precoUpgradeMineradora = calculateMinerUpgradePrice(nivelMineradora);
    
    container.innerHTML = `
        <div class="bg-surface-elevated border border-border-color rounded-xl p-6 mb-6">
            <h2 class="text-2xl font-bold text-primary-text mb-4 flex items-center gap-3">
                <svg class="w-8 h-8 text-red-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m-6.75 0h13.5m-13.5 0a2.25 2.25 0 0 0-2.25 2.25v6.75a2.25 2.25 0 0 0 2.25 2.25h13.5a2.25 2.25 0 0 0 2.25-2.25v-6.75a2.25 2.25 0 0 0-2.25-2.25m-13.5 0V9a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 9v.75" />
                </svg>
                Mercado Negro
            </h2>
            <p class="text-secondary-text mb-6">Compre upgrades especiais com Shack</p>
            
            <div class="grid grid-cols-1 gap-6">
                <div class="bg-surface-default border border-border-color rounded-lg p-4">
                    <h3 class="text-lg font-semibold text-primary-text mb-2">Upgrade Mineradora</h3>
                    <p class="text-secondary-text mb-4">Aumenta o nível da mineradora em +1</p>
                    <div class="text-xs text-gray-400 mb-3">
                        Nível atual: ${nivelMineradora} → ${nivelMineradora + 1}
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-2xl font-bold text-yellow-400">${precoUpgradeMineradora.toLocaleString()} Shack</span>
                        <button class="mercado-negro-btn bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-all duration-300 ${(currentPlayer?.shack || 0) < precoUpgradeMineradora ? 'opacity-50 cursor-not-allowed' : ''}" data-item-id="upgrade_miner_1" ${(currentPlayer?.shack || 0) < precoUpgradeMineradora ? 'disabled' : ''}>
                            ${(currentPlayer?.shack || 0) < precoUpgradeMineradora ? 'Shack Insuficiente' : 'Comprar'}
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="bg-surface-elevated border border-border-color rounded-xl p-6">
            <h3 class="text-xl font-semibold text-primary-text mb-4">Seu Saldo</h3>
            <div class="text-center">
                <p class="text-4xl font-bold text-yellow-400 mb-2">${currentPlayer?.shack || 0} Shack</p>
                <p class="text-secondary-text">Saldo disponível para compras</p>
            </div>
        </div>
    `;
}

// Função para calcular o preço do upgrade da mineradora baseado no nível atual
function calculateMinerUpgradePrice(currentLevel) {
    // Progressão mais suave: base 300 Shack, aumenta 25% a cada nível
    const basePrice = 300;  // Reduzido de 500 para 300
    const multiplier = Math.pow(1.25, currentLevel - 1);  // Reduzido de 1.5 para 1.25 (25% em vez de 50%)
    return Math.floor(basePrice * multiplier);
}

// --- FUNÇÃO PARA CALCULAR PONTOS DE HABILIDADE BASEADO NO NÍVEL ---
function calculateSkillPointsFromLevel(nivel) {
    let totalPoints = 0;
    
    // Lógica de pontos por faixa de nível
    if (nivel >= 5) {
        totalPoints += 1; // Nível 1-5 = 1 ponto
    }
    if (nivel >= 10) {
        totalPoints += 1; // Nível 5-10 = 1 ponto
    }
    if (nivel >= 15) {
        totalPoints += 1; // Nível 10-15 = 1 ponto
    }
    if (nivel >= 20) {
        totalPoints += 1; // Nível 15-20 = 1 ponto
    }
    if (nivel >= 30) {
        totalPoints += 4; // Nível 20-30 = 4 pontos
    }
    if (nivel >= 40) {
        totalPoints += 4; // Nível 30-40 = 4 pontos
    }
    if (nivel >= 50) {
        totalPoints += 4; // Nível 40-50 = 4 pontos
    }
    
    return totalPoints;
}

// --- FUNÇÃO PARA CALCULAR PRÓXIMOS PONTOS DE HABILIDADE ---
function getNextSkillPointInfo(nivel) {
    let nextMilestone = 0;
    let pointsToGain = 0;
    
    if (nivel < 5) {
        nextMilestone = 5;
        pointsToGain = 1;
    } else if (nivel < 10) {
        nextMilestone = 10;
        pointsToGain = 1;
    } else if (nivel < 15) {
        nextMilestone = 15;
        pointsToGain = 1;
    } else if (nivel < 20) {
        nextMilestone = 20;
        pointsToGain = 1;
    } else if (nivel < 30) {
        nextMilestone = 30;
        pointsToGain = 4;
    } else if (nivel < 40) {
        nextMilestone = 40;
        pointsToGain = 4;
    } else if (nivel < 50) {
        nextMilestone = 50;
        pointsToGain = 4;
    } else {
        nextMilestone = null; // Nível máximo alcançado
        pointsToGain = 0;
    }
    
    return { nextMilestone, pointsToGain };
}

// --- FUNÇÃO RENDERIZAR SEÇÃO DE HABILIDADES ---
async function renderHabilidadesSection() {
    const container = document.getElementById('habilidades-container');
    if (!container) return;
    
    container.innerHTML = `
        <div class="h-full overflow-y-auto custom-scrollbar">
            <div class="max-w-4xl mx-auto p-6 pb-20">
                <!-- Cabeçalho -->
                <div class="bg-surface-elevated border border-border-color rounded-xl p-6 mb-6">
                    <h2 class="text-2xl font-bold text-primary-text mb-2 flex items-center gap-3">
                        <svg class="w-8 h-8 text-purple-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M9.813 15.904 9 18.75l-.813-2.846a4.5 4.5 0 0 0-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 0 0 3.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 0 0 3.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 0 0-3.09 3.09ZM18.259 8.715 18 9.75l-.259-1.035a3.375 3.375 0 0 0-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 0 0 2.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 0 0 2.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 0 0-2.456 2.456ZM16.894 20.567 16.5 21.75l-.394-1.183a2.25 2.25 0 0 0-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 0 0 1.423-1.423l.394-1.183.394 1.183a2.25 2.25 0 0 0 1.423 1.423l1.183.394-1.183.394a2.25 2.25 0 0 0-1.423 1.423Z" />
                        </svg>
                        Sistema de Habilidades
                    </h2>
                    <p class="text-secondary-text">Gerencie e visualize suas habilidades especiais de hacking</p>
                </div>

                <!-- Sistema de Abas -->
                <div class="bg-surface-elevated border border-border-color rounded-xl overflow-hidden">
                    <!-- Navegação das Abas -->
                    <div class="border-b border-border-color bg-surface-default">
                        <nav class="flex space-x-8 px-6" aria-label="Tabs">
                            <button 
                                onclick="switchHabilidadesTab('adquiridas')" 
                                class="tab-button tab-adquiridas whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition-all duration-200"
                            >
                                📚 Habilidades Adquiridas
                            </button>
                            <button 
                                onclick="switchHabilidadesTab('melhorias')" 
                                class="tab-button tab-melhorias whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition-all duration-200"
                            >
                                🔮 Habilidades NFT
                            </button>
                        </nav>
                    </div>

                    <!-- Conteúdo das Abas -->
                    <div id="habilidades-tab-content" class="p-6">
                        <!-- Conteúdo será inserido aqui dinamicamente -->
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // Inicializa na aba "Habilidades Adquiridas"
    switchHabilidadesTab('adquiridas');
}

// Função para alternar entre as abas de habilidades
function switchHabilidadesTab(tabName) {
    // Atualiza os estilos das abas
    const allTabs = document.querySelectorAll('.tab-button');
    allTabs.forEach(tab => {
        tab.classList.remove('border-purple-400', 'text-purple-400');
        tab.classList.add('border-transparent', 'text-secondary-text', 'hover:text-primary-text', 'hover:border-border-color');
    });
    
    const activeTab = document.querySelector(`.tab-${tabName}`);
    if (activeTab) {
        activeTab.classList.remove('border-transparent', 'text-secondary-text', 'hover:text-primary-text', 'hover:border-border-color');
        activeTab.classList.add('border-purple-400', 'text-purple-400');
    }
    
    // Renderiza o conteúdo da aba selecionada
    const tabContent = document.getElementById('habilidades-tab-content');
    if (!tabContent) return;
    
    if (tabName === 'adquiridas') {
        renderHabilidadesAdquiridas(tabContent);
    } else if (tabName === 'melhorias') {
        renderHabilidadesMelhorias(tabContent);
    }
}

// Função para renderizar a aba "Habilidades Adquiridas"
async function renderHabilidadesAdquiridas(container) {
    // Primeiro, precisamos carregar a lista de habilidades NFT disponíveis
    let nftHabilidades = {};
    let loading = true;
    let error = null;
    
    try {
        const response = await fetchAPI('/api/habilidades/disponiveis', 'GET');
        if (response.sucesso) {
            nftHabilidades = response.habilidades || {};
        } else {
            error = response.mensagem || 'Erro ao carregar habilidades';
        }
    } catch (err) {
        error = err.message || 'Falha na conexão';
    } finally {
        loading = false;
    }

    // Habilidades adquiridas pelo usuário
    const habilidadesAdquiridas = currentPlayer?.habilidades_adquiridas || {};
    const temHabilidadesAdquiridas = Object.keys(habilidadesAdquiridas).length > 0;
    
    // Renderiza o container
    container.innerHTML = `
        <div class="space-y-6">
            <div class="bg-surface-default border border-border-color rounded-lg p-4">
                <h3 class="text-lg font-semibold text-primary-text mb-4 flex items-center gap-2">
                    🔮 Suas Habilidades NFT
                </h3>
                <p class="text-secondary-text text-sm mb-6">
                    Visualize as habilidades exclusivas que você adquiriu. Cada habilidade possui apenas um número limitado de unidades no jogo.
                </p>
            </div>

            ${loading ? `
                <div class="text-center py-12">
                    <div class="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-purple-500"></div>
                    <p class="mt-3 text-secondary-text">Carregando habilidades...</p>
                </div>
            ` : error ? `
                <div class="bg-red-500/10 border border-red-500/30 text-red-500 p-4 rounded-lg text-center">
                    <p>Erro ao carregar habilidades: ${error}</p>
                    <button onclick="renderHabilidadesSection()" class="mt-2 px-3 py-1 bg-red-500 text-white rounded text-sm">Tentar novamente</button>
                </div>
            ` : !temHabilidadesAdquiridas ? `
                <div class="bg-surface-elevated border border-border-color rounded-lg p-8 text-center">
                    <div class="w-16 h-16 mx-auto mb-4 bg-purple-500/20 rounded-full flex items-center justify-center">
                        <svg class="w-8 h-8 text-purple-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M9.813 15.904 9 18.75l-.813-2.846a4.5 4.5 0 0 0-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 0 0 3.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 0 0 3.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 0 0-3.09 3.09Z" />
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-primary-text mb-2">Nenhuma habilidade NFT adquirida</h3>
                    <p class="text-secondary-text mb-4">Você ainda não adquiriu nenhuma habilidade NFT exclusiva.</p>
                    <button onclick="switchHabilidadesTab('melhorias')" class="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 transition-colors">
                        Ver habilidades disponíveis
                    </button>
                </div>
            ` : `
                <!-- Grid de Habilidades NFT Adquiridas -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    ${Object.entries(habilidadesAdquiridas).map(([id, habilidade]) => `
                        <div class="bg-surface-default border border-border-color rounded-lg p-4 hover:border-purple-400 transition-all duration-300">
                            <div class="flex items-center gap-3 mb-3">
                                <div class="w-12 h-12 bg-purple-500/20 rounded-lg flex items-center justify-center">
                                    <svg class="w-6 h-6 text-purple-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M9.813 15.904 9 18.75l-.813-2.846a4.5 4.5 0 0 0-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 0 0 3.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 0 0 3.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 0 0-3.09 3.09Z" />
                                    </svg>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-primary-text">${habilidade.nome}</h4>
                                    <div class="text-xs text-purple-400">Habilidade Exclusiva NFT</div>
                                </div>
                            </div>
                            <p class="text-secondary-text text-sm mb-3">${habilidade.descricao}</p>
                            <div class="flex items-center justify-between text-xs text-secondary-text">
                                <span>Efeito: ${habilidade.efeito * 100}%</span>
                                <span>Adquirido em ${new Date(habilidade.adquirido_em * 1000).toLocaleDateString()}</span>
                            </div>
                        </div>
                    `).join('')}
                </div>
            `}
            
            <!-- Informações sobre o Sistema -->
            <div class="bg-surface-default border border-border-color rounded-lg p-4">
                <h4 class="font-semibold text-primary-text mb-3 flex items-center gap-2">
                    Sistema de Habilidades NFT
                </h4>
                <div class="text-sm text-secondary-text space-y-2">
                    <p class="flex items-center gap-2">
                        <span class="w-2 h-2 bg-purple-400 rounded-full"></span>
                        <strong>Habilidades exclusivas:</strong> Cada habilidade tem um número limitado de unidades disponíveis
                    </p>
                    <p class="flex items-center gap-2">
                        <span class="w-2 h-2 bg-blue-400 rounded-full"></span>
                        <strong>Colecionáveis:</strong> Uma vez adquiridas, as habilidades são suas permanentemente
                    </p>
                    <p class="flex items-center gap-2">
                        <span class="w-2 h-2 bg-green-400 rounded-full"></span>
                        <strong>Efeitos passivos:</strong> As habilidades aplicam seus efeitos automaticamente durante o jogo
                    </p>
                </div>
            </div>
        </div>
    `;
}

// Função para renderizar a aba "Melhorias" com habilidades NFT
async function renderHabilidadesMelhorias(container) {
    // Primeiro, precisamos carregar a lista de habilidades NFT disponíveis
    let nftHabilidades = {};
    let loading = true;
    let error = null;
    
    try {
        const response = await fetchAPI('/api/habilidades/disponiveis', 'GET');
        if (response.sucesso) {
            nftHabilidades = response.habilidades || {};
        } else {
            error = response.mensagem || 'Erro ao carregar habilidades';
        }
    } catch (err) {
        error = err.message || 'Falha na conexão';
    } finally {
        loading = false;
    }
    
    // Habilidades já adquiridas pelo usuário (para não mostrar novamente)
    const habilidadesAdquiridas = currentPlayer?.habilidades_adquiridas || {};
    
    container.innerHTML = `
        <div class="space-y-6">
            <!-- Informações sobre o Novo Sistema -->
            <div class="bg-surface-default border border-purple-500/30 rounded-lg p-4">
                <div class="flex items-start gap-3">
                    <div class="w-10 h-10 bg-purple-500/20 rounded-lg flex items-center justify-center flex-shrink-0 mt-1">
                        <svg class="w-6 h-6 text-purple-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M9.813 15.904 9 18.75l-.813-2.846a4.5 4.5 0 0 0-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 0 0 3.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 0 0 3.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 0 0-3.09 3.09Z" />
                        </svg>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-purple-400 mb-2">Novo Sistema de Habilidades NFT</h3>
                        <p class="text-secondary-text text-sm">
                            O sistema de habilidades agora funciona como NFTs! Cada habilidade tem um estoque limitado e 
                            quando acabar, ninguém mais poderá adquiri-la. Aproveite para coletar habilidades exclusivas 
                            antes que se esgotem.
                        </p>
                    </div>
                </div>
            </div>
            
            ${loading ? `
                <!-- Loading State -->
                <div class="text-center py-12">
                    <div class="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-purple-500"></div>
                    <p class="mt-3 text-secondary-text">Carregando habilidades disponíveis...</p>
                </div>
            ` : error ? `
                <!-- Error State -->
                <div class="bg-red-500/10 border border-red-500/30 text-red-500 p-4 rounded-lg text-center">
                    <p>Erro ao carregar habilidades: ${error}</p>
                    <button onclick="switchHabilidadesTab('melhorias')" class="mt-2 px-3 py-1 bg-red-500 text-white rounded text-sm">Tentar novamente</button>
                </div>
            ` : Object.keys(nftHabilidades).length === 0 ? `
                <!-- Empty State -->
                <div class="bg-surface-elevated border border-border-color rounded-lg p-8 text-center">
                    <div class="w-16 h-16 mx-auto mb-4 bg-gray-500/20 rounded-full flex items-center justify-center">
                        <svg class="w-8 h-8 text-gray-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126ZM12 15.75h.007v.008H12v-.008Z" />
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-primary-text mb-2">Nenhuma habilidade disponível</h3>
                    <p class="text-secondary-text">Não há habilidades NFT disponíveis no momento. Volte mais tarde!</p>
                </div>
            ` : `
                <!-- Lista de Habilidades NFT disponíveis -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    ${Object.entries(nftHabilidades).map(([id, habilidade]) => {
                        // Verifica se o jogador já possui esta habilidade
                        const jaAdquirida = id in habilidadesAdquiridas;
                        // Verifica se tem estoque disponível
                        const semEstoque = habilidade.estoque_atual <= 0;
                        // Verifica se o jogador tem Shacks suficientes
                        const shackSuficientes = (currentPlayer?.shack || 0) >= habilidade.preco_shack;
                        
                        // Status do botão
                        let btnStatus = '';
                        if (jaAdquirida) {
                            btnStatus = 'Adquirido';
                        } else if (semEstoque) {
                            btnStatus = 'Esgotado';
                        } else if (!shackSuficientes) {
                            btnStatus = 'Shacks insuficientes';
                        } else {
                            btnStatus = 'Adquirir';
                        }
                        
                        // CSS do botão
                        let btnCss = '';
                        if (jaAdquirida) {
                            btnCss = 'bg-green-600 text-white cursor-not-allowed';
                        } else if (semEstoque) {
                            btnCss = 'bg-gray-600 text-gray-400 cursor-not-allowed';
                        } else if (!shackSuficientes) {
                            btnCss = 'bg-red-600 text-white cursor-not-allowed';
                        } else {
                            btnCss = 'bg-purple-600 hover:bg-purple-700 text-white';
                        }
                        
                        return `
                            <div class="bg-surface-default border ${jaAdquirida ? 'border-green-400' : 'border-border-color'} rounded-lg p-4 hover:border-purple-400 transition-all duration-300">
                                <div class="flex items-center gap-3 mb-3">
                                    <div class="w-12 h-12 bg-purple-500/20 rounded-lg flex items-center justify-center">
                                        <svg class="w-6 h-6 text-purple-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" d="M9.813 15.904 9 18.75l-.813-2.846a4.5 4.5 0 0 0-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 0 0 3.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 0 0 3.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 0 0-3.09 3.09Z" />
                                        </svg>
                                    </div>
                                    <div>
                                        <h4 class="font-semibold text-primary-text">${habilidade.nome}</h4>
                                        <div class="flex items-center gap-2">
                                            <span class="text-xs text-purple-400">Habilidade NFT</span>
                                            ${jaAdquirida ? `
                                                <span class="px-1.5 py-0.5 text-xs bg-green-500/20 text-green-500 rounded">Adquirido</span>
                                            ` : semEstoque ? `
                                                <span class="px-1.5 py-0.5 text-xs bg-red-500/20 text-red-500 rounded">Esgotado</span>
                                            ` : `
                                                <span class="px-1.5 py-0.5 text-xs bg-yellow-500/20 text-yellow-500 rounded">${habilidade.estoque_atual} em estoque</span>
                                            `}
                                        </div>
                                    </div>
                                </div>
                                <p class="text-secondary-text text-sm mb-3">${habilidade.descricao}</p>
                                <div class="flex items-center justify-between">
                                    <span class="text-accent-blue font-medium">${habilidade.preco_shack} Shacks</span>
                                    <button 
                                        class="px-3 py-1 rounded text-sm transition-all ${btnCss}" 
                                        ${jaAdquirida || semEstoque || !shackSuficientes ? 'disabled' : ''}
                                        ${!jaAdquirida && !semEstoque && shackSuficientes ? `onclick="comprarHabilidadeNFT('${id}')"` : ''}
                                    >
                                        ${btnStatus}
                                    </button>
                                </div>
                            </div>
                        `;
                    }).join('')}
                </div>
            `}
            
            <!-- Informações sobre o Sistema -->
            <div class="bg-surface-default border border-border-color rounded-lg p-4">
                <h4 class="font-semibold text-primary-text mb-3 flex items-center gap-2">
                    Como funciona
                </h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div>
                        <h5 class="text-accent-blue font-semibold mb-2">Habilidades NFT</h5>
                        <div class="space-y-2 text-secondary-text">
                            <p class="flex items-center gap-2">
                                <span class="w-2 h-2 bg-purple-400 rounded-full"></span>
                                Cada habilidade tem estoque limitado
                            </p>
                            <p class="flex items-center gap-2">
                                <span class="w-2 h-2 bg-purple-400 rounded-full"></span>
                                Uma vez adquirida, a habilidade é sua permanentemente
                            </p>
                            <p class="flex items-center gap-2">
                                <span class="w-2 h-2 bg-purple-400 rounded-full"></span>
                                Cada habilidade tem um efeito único no jogo
                            </p>
                        </div>
                    </div>
                    
                    <div>
                        <h5 class="text-accent-blue font-semibold mb-2">Custos</h5>
                        <div class="space-y-2 text-secondary-text">
                            <p class="flex items-center gap-2">
                                <span class="w-2 h-2 bg-purple-400 rounded-full"></span>
                                Preços variam conforme a raridade e efeito
                            </p>
                            <p class="flex items-center gap-2">
                                <span class="w-2 h-2 bg-purple-400 rounded-full"></span>
                                Habilidades são compradas com Shacks
                            </p>
                            <p class="flex items-center gap-2">
                                <span class="w-2 h-2 bg-purple-400 rounded-full"></span>
                                O estoque não é reposto quando esgota
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
}

// Função para comprar uma habilidade NFT
async function comprarHabilidadeNFT(habilidadeId) {
    if (!habilidadeId) {
        showNotification('Erro: ID de habilidade inválido', 'error');
        return;
    }

    try {
        // Mostrar indicador de carregamento
        const button = document.querySelector(`[onclick="comprarHabilidadeNFT('${habilidadeId}')"]`);
        if (button) {
            button.disabled = true;
            button.innerHTML = 'Processando...';
            button.classList.add('opacity-75');
        }

        // Fazer requisição para comprar a habilidade
        const response = await fetchAPI('/api/habilidades/comprar', 'POST', {
            habilidade_id: habilidadeId
        });

        if (response.sucesso) {
            showNotification(`${response.mensagem}`, 'success');
            
            // Atualizar dados do jogador
            await loadGameData(true);
            
            // Atualizar a interface
            renderHabilidadesSection();
        } else {
            showNotification(`${response.mensagem}`, 'error');
        }
    } catch (error) {
        console.error('Erro ao comprar habilidade:', error);
        showNotification('Erro ao processar compra da habilidade', 'error');
    } finally {
        // Restaurar botão
        const button = document.querySelector(`[onclick="comprarHabilidadeNFT('${habilidadeId}')"]`);
        if (button) {
            button.disabled = false;
            button.innerHTML = 'Adquirir';
            button.classList.remove('opacity-75');
        }
    }
}

// --- FUNÇÃO RENDERIZAR SEÇÃO DE LOG ---
async function renderLogSection() {
    const container = document.getElementById('log-container');
    if (!container) return;
    
    try {
        const response = await fetchAPI('/api/logs/atividade');
        if (response.sucesso && response.logs) {
            const logs = response.logs;
            
            if (logs.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-8 text-secondary-text">
                        <svg class="w-12 h-12 mx-auto mb-3 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12.75L11.25 15 15 9.75m-3-7.036A11.959 11.959 0 013.598 6 11.99 11.99 0 003 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.623 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285z" />
                        </svg>
                        <p class="text-lg font-medium text-green-400">Sistema Seguro</p>
                        <p class="text-sm">Nenhuma invasão detectada</p>
                        <p class="text-xs mt-2 opacity-75">Logs de transferências e defaces aparecerão aqui quando você for exploitado</p>
                    </div>
                `;
                return;
            }
            
            const logsHtml = logs.map(log => {
                // Tratamento especial para deface ativo
                if (log.is_deface_ativo) {
                    return `
                        <div class="bg-red-900/30 border-2 border-red-500 rounded-lg p-6 mb-4 animate-pulse">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center gap-3">
                                    <div class="w-10 h-10 rounded-lg bg-red-500 flex items-center justify-center">
                                        <svg class="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                                        </svg>
                                    </div>
                                    <div>
                                        <h3 class="text-xl font-bold text-red-400">${log.mensagem}</h3>
                                        <p class="text-sm text-red-300">Sistema comprometido por ${log.grupo_nome}</p>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <div class="text-lg font-bold text-red-400">${log.tempo_restante}</div>
                                    <div class="text-xs text-red-300">Tempo restante</div>
                                </div>
                            </div>
                        </div>
                    `;
                }

                // Security Alert (invasão)
                const timeStr = new Date(log.created_at).toLocaleString('pt-BR');

                // Extrair informações do log
                const ipAtacante = log.atacante_ip || (log.dados && log.dados.atacante_ip) || 'IP desconhecido';
                const quantiaRoubada = log.quantia_roubada || (log.dados && log.dados.quantia_roubada);
                const grupoNome = log.grupo_nome || (log.dados && log.dados.grupo_nome);

                // Determinar tipo de invasão
                const isTransferencia = log.dados && log.dados.tipo === 'exploit_transferencia';
                const isDeface = log.dados && log.dados.tipo === 'exploit_deface';
                const isExploitInicial = log.dados && log.dados.tipo === 'exploit_inicial';

                // Ícone baseado no tipo
                const icon = isTransferencia ?
                    '<svg class="w-5 h-5 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" /></svg>' :
                    isDeface ? '<svg class="w-5 h-5 text-orange-500" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" /></svg>' :
                    '<svg class="w-5 h-5 text-purple-500" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z" /></svg>';

                // Valor roubado (se for transferência)
                const valorDisplay = quantiaRoubada ? `<span class="text-red-400 font-bold">$${quantiaRoubada.toLocaleString()}</span>` : '';

                // Grupo (se for deface)
                const grupoDisplay = grupoNome ? `<span class="text-orange-400 font-medium">${grupoNome}</span>` : '';

                // IP copiável
                const ipDisplay = ipAtacante && ipAtacante !== 'IP desconhecido' ? `
                    <div class="flex items-center gap-2 mt-2">
                        <span class="text-xs text-secondary-text">Invasor:</span>
                        <span class="font-mono text-accent-blue select-all cursor-text">${ipAtacante}</span>
                        <button onclick="copyToClipboard('${ipAtacante}', this)"
                                class="text-xs px-2 py-1 bg-accent-blue hover:bg-blue-600 text-white rounded transition-colors duration-200 flex items-center gap-1"
                                title="Copiar IP do invasor">
                            <svg class="w-3 h-3" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M15.666 3.888A2.25 2.25 0 0013.5 2.25h-3c-1.03 0-1.9.693-2.166 1.638m7.332 0c.*************.084.612v0a.75.75 0 01-.75.75H9a.75.75 0 01-.75-.75v0c0-.212.03-.418.084-.612m7.332 0c.646.049 1.288.11 1.927.184 1.1.128 1.907 1.077 1.907 2.185V19.5a2.25 2.25 0 01-2.25 2.25H6.75A2.25 2.25 0 014.5 19.5V6.257c0-1.108.806-2.057 1.907-2.185a48.208 48.208 0 011.927-.184" />
                            </svg>
                            Copiar
                        </button>
                    </div>
                ` : '';

                return `
                    <div class="bg-surface-elevated border border-red-500/50 bg-red-900/10 rounded-lg p-4 hover:border-red-400 transition-all duration-300">
                        <div class="flex items-start gap-3">
                            <div class="flex-shrink-0 mt-1">
                                ${icon}
                            </div>
                            <div class="flex-1">
                                <div class="flex items-center justify-between mb-2">
                                    <p class="text-red-400 font-mono text-sm">${log.mensagem}</p>
                                    <div class="flex items-center gap-2">
                                        ${valorDisplay}
                                        ${grupoDisplay}
                                    </div>
                                </div>
                                <div class="flex items-center justify-between">
                                    <p class="text-secondary-text text-xs">${timeStr}</p>
                                    <span class="text-xs px-2 py-1 rounded ${isTransferencia ? 'bg-red-500' : isDeface ? 'bg-orange-500' : 'bg-purple-500'} text-white">
                                        ${isTransferencia ? 'TRANSFERÊNCIA' : isDeface ? 'DEFACE' : 'EXPLOIT'}
                                    </span>
                                </div>
                                ${ipDisplay}
                            </div>
                        </div>
                    </div>
                `;
            }).join('');
            
            container.innerHTML = `
                <div class="space-y-4">
                    <div class="flex justify-between items-center">
                        <div>
                            <h3 class="text-xl font-bold text-primary-text">📜 Logs de Segurança</h3>
                            <p class="text-sm text-secondary-text">Invasões recebidas e transferências realizadas</p>
                        </div>
                        <button onclick="clearLogs()" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm transition-all duration-300">
                            Limpar Logs
                        </button>
                    </div>
                    <div class="space-y-3">
                        ${logsHtml}
                    </div>
                </div>
            `;
        } else {
            throw new Error(response.mensagem || 'Erro ao carregar logs');
        }
    } catch (error) {
        console.error("Erro ao carregar logs:", error);
        container.innerHTML = `
            <div class="bg-surface-elevated border border-border-color rounded-xl p-6 text-center">
                <h2 class="text-2xl font-bold text-primary-text mb-4">Erro de Conexão</h2>
                <p class="text-secondary-text">Não foi possível carregar os logs do sistema</p>
            </div>
        `;
    }
}

// Função auxiliar para limpar logs do usuário
async function clearLogs() {
    if (confirm('Tem certeza que deseja limpar todos os seus logs de segurança? Esta ação não pode ser desfeita.')) {
        try {
            const response = await fetchAPI('/api/logs/limpar', 'POST');
            if (response.sucesso) {
                showNotification(`Logs limpos com sucesso! ${response.logs_removidos} registros removidos.`, 'success');
                renderLogSection();
            } else {
                showNotification('Erro ao limpar logs: ' + response.mensagem, 'error');
            }
        } catch (error) {
            showNotification('Erro ao limpar logs: ' + error.message, 'error');
        }
    }
}

// Função para limpar logs do usuário (alias)
async function limparLogsUsuario() {
    await clearLogs();
}

// Função para limpar logs do alvo exploitado
async function limparLogsAlvo() {
    if (!exploitedTarget) {
        showNotification('Erro: Dados do alvo não encontrados', 'error');
        return;
    }

    const alvoNick = exploitedTarget.nick || 'Alvo';
    if (!confirm(`Tem certeza que deseja limpar todos os logs de segurança de ${alvoNick}? Esta ação não pode ser desfeita.`)) {
        return;
    }

    try {
        const response = await fetchAPI(`/api/logs/limpar-alvo/${exploitedTarget.ip}`, 'POST');
        if (response.sucesso) {
            showNotification(`Logs de ${response.alvo_nick} limpos com sucesso! ${response.logs_removidos} registros removidos.`, 'success');
            loadTargetLog(); // Recarregar logs do alvo
        } else {
            showNotification('Erro ao limpar logs do alvo: ' + response.mensagem, 'error');
        }
    } catch (error) {
        console.error('Erro ao limpar logs do alvo:', error);
        showNotification('Erro ao limpar logs do alvo: ' + error.message, 'error');
    }
}

// Torna a função global
window.clearLogs = clearLogs;

// Funções auxiliares para os logs
function getLogIcon(tipo) {
    const icons = {
        invasao_recebida: '<svg class="w-6 h-6 text-red-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126ZM12 15.75h.007v.008H12v-.008Z" /></svg>',
        roubo_recebido: '<svg class="w-6 h-6 text-orange-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M12 6v12m-3-2.818l.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.003-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>',
        transferencia_recebida: '<svg class="w-6 h-6 text-green-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5M16.5 12L12 16.5m0 0L7.5 12m4.5 4.5V3" /></svg>',
        hack: '<svg class="w-6 h-6 text-red-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m9-.75a9 9 0 11-18 0 9 9 0 0118 0zm-9 3.75h.008v.008H12v-.008z" /></svg>',
        defense: '<svg class="w-6 h-6 text-green-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75L11.25 15 15 9.75m-3-7.036A11.959 11.959 0 013.598 6 11.99 11.99 0 003 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285z" /></svg>',
        system: '<svg class="w-6 h-6 text-accent-blue" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.324.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 011.37.49l1.296 2.247a1.125 1.125 0 01-.26 1.431l-1.003.827c-.293.24-.438.613-.431.992a6.759 6.759 0 010 .255c-.007.378.138.75.43.99l1.005.828c.424.35.534.954.26 1.43l-1.298 2.247a1.125 1.125 0 01-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.57 6.57 0 01-.22.128c-.331.183-.581.495-.644.869l-.213 1.28c-.09.543-.56.941-1.11.941h-2.594c-.55 0-1.02-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 01-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 01-1.369-.49l-1.297-2.247a1.125 1.125 0 01.26-1.431l1.004-.827c.292-.24.437-.613.43-.992a6.932 6.932 0 010-.255c.007-.378-.138-.75-.43-.99l-1.004-.828a1.125 1.125 0 01-.26-1.43l1.297-2.247a1.125 1.125 0 011.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.087.22-.128.332-.183.582-.495.644-.869l.214-1.281z" /><path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /></svg>'
    };
    return icons[tipo] || icons.system;
}

function formatLogTime(timestamp) {
    if (!timestamp) return 'Data não disponível';
    
    // Converter timestamp para objeto Date
    let date;
    if (typeof timestamp === 'number') {
        // Se for um timestamp em segundos, converter para milissegundos
        date = new Date(timestamp > 1000000000000 ? timestamp : timestamp * 1000);
    } else {
        date = new Date(timestamp);
    }
    
    // Verificar se a data é válida
    if (isNaN(date.getTime())) {
        return 'Data inválida';
    }
    
    // Formatar data e hora no formato brasileiro
    const options = {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        timeZone: 'America/Sao_Paulo'
    };
    
    return date.toLocaleString('pt-BR', options);
}

// === SEÇÃO DE NOTÍCIAS ===
async function renderNewsSection() {
    const container = document.getElementById('news-container');
    if (!container) return;
    
    container.innerHTML = `
        <div class="h-full overflow-y-auto custom-scrollbar">
            <div class="max-w-4xl mx-auto p-6 pb-20 space-y-6">
                <div class="text-center mb-6">
                    <h1 class="text-3xl font-bold text-primary-text mb-2">NOTÍCIAS</h1>
                    <p class="text-secondary-text">Últimas atualizações e notícias do sistema</p>
                </div>

                <div id="news-list" class="space-y-4 max-h-96 overflow-y-auto custom-scrollbar border border-border-color rounded-lg p-4">
                    <div id="news-loading" class="text-center py-8">
                        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-accent-blue mx-auto"></div>
                        <p class="text-secondary-text mt-2">Carregando notícias...</p>
                    </div>
                </div>

                <div id="admin-panel" class="mt-8 hidden">
                    <div class="bg-surface-elevated border border-border-color rounded-lg p-6">
                        <h2 class="text-xl font-bold text-primary-text mb-4">Painel Administrativo</h2>
                        
                        <form id="create-news-form" class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-secondary-text mb-2">Título</label>
                                <input type="text" id="news-title" required 
                                       class="w-full p-3 bg-surface-default border border-border-color rounded-lg text-primary-text placeholder-gray-500"
                                       placeholder="Digite o título da notícia">
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-secondary-text mb-2">Conteúdo</label>
                                <textarea id="news-content" rows="4" required
                                          class="w-full p-3 bg-surface-default border border-border-color rounded-lg text-primary-text placeholder-gray-500 resize-vertical"
                                          placeholder="Digite o conteúdo da notícia"></textarea>
                            </div>
                            
                            <div class="flex items-center space-x-2">
                                <input type="checkbox" id="news-priority" 
                                       class="w-4 h-4 text-accent-blue bg-surface-default border-border-color rounded">
                                <label class="text-sm text-secondary-text">Notícia prioritária (destaque)</label>
                            </div>
                            
                            <button type="submit" class="w-full bg-accent-blue hover:bg-blue-600 text-white font-bold py-3 px-4 rounded-lg transition-colors">
                                📢 Publicar Notícia
                            </button>
                        </form>
                        
                        <!-- Seção de Administração do Torneio -->
                        <div class="mt-6 pt-6 border-t border-border-color">
                            <h3 class="text-lg font-bold text-primary-text mb-3">Administração do Torneio</h3>
                            <div class="space-y-3">
                                <button id="reset-tournament-btn" 
                                        class="w-full bg-red-600 hover:bg-red-700 text-white font-bold py-3 px-4 rounded-lg transition-colors">
                                    Reset Manual do Torneio
                                </button>
                                <div id="tournament-reset-status" class="text-sm text-secondary-text hidden"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Inicializar
    await loadNewsData();
    checkNewsAdminAccess();
    setupNewsFormHandler();
    setupTournamentAdminHandlers();
}

async function loadNewsData() {
    try {
        const response = await fetchAPI('/api/news');
        if (response.sucesso) {
            displayNewsData(response.noticias || []);
        }
    } catch (error) {
        console.error('Erro ao carregar notícias:', error);
        showNotification('Erro ao carregar notícias', 'error');
    } finally {
        const loading = document.getElementById('news-loading');
        if (loading) loading.classList.add('hidden');
    }
}

function displayNewsData(noticias) {
    const newsList = document.getElementById('news-list');
    if (!newsList) return;
    
    const loading = document.getElementById('news-loading');
    if (loading) loading.classList.add('hidden');
    
    if (noticias.length === 0) {
        newsList.innerHTML += `
            <div class="text-center py-8">
                <p class="text-secondary-text">Nenhuma notícia disponível</p>
            </div>
        `;
        return;
    }
    
    noticias.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
    
    const noticiasHtml = noticias.map(noticia => {
        const date = new Date(noticia.created_at).toLocaleString('pt-BR');
        const isAdmin = currentPlayer?.is_admin || false;
        const priorityClass = noticia.priority ? 'border-yellow-500' : '';
        
        return `
            <div class="bg-surface-elevated border border-border-color rounded-lg p-6 transition-all duration-300 hover:border-accent-blue ${priorityClass}">
                <div class="flex items-start justify-between mb-3">
                    <div class="flex-1">
                        <h3 class="text-lg font-bold text-primary-text">${noticia.title}</h3>
                        <div class="text-xs text-secondary-text flex items-center gap-2 mt-1">
                            <span>${date}</span>
                            ${noticia.priority ? '<span class="bg-yellow-500 text-black text-xs px-2 py-1 rounded-full font-medium">DESTAQUE</span>' : ''}
                        </div>
                    </div>
                </div>
                <div class="text-secondary-text leading-relaxed">${noticia.content.replace(/\n/g, '<br>')}</div>
                ${isAdmin ? `
                    <div class="flex gap-2 mt-4 pt-4 border-t border-border-color">
                        <button onclick="deleteNewsItem('${noticia.id}')" class="bg-red-600 hover:bg-red-700 text-white text-xs px-3 py-1 rounded transition-colors">
                            Excluir
                        </button>
                    </div>
                ` : ''}
            </div>
        `;
    }).join('');
    
    newsList.innerHTML = noticiasHtml;
}

function checkNewsAdminAccess() {
    const isAdmin = currentPlayer?.is_admin || false;
    const adminPanel = document.getElementById('admin-panel');
    if (adminPanel && isAdmin) {
        adminPanel.classList.remove('hidden');
    }
}

function setupNewsFormHandler() {
    const form = document.getElementById('create-news-form');
    if (form) {
        form.addEventListener('submit', async (e) => {
            e.preventDefault();
            const title = document.getElementById('news-title').value;
            const content = document.getElementById('news-content').value;
            const priority = document.getElementById('news-priority').checked;
            
            try {
                const response = await fetchAPI('/api/news', 'POST', {
                    title: title,
                    content: content,
                    priority: priority
                });
                
                if (response.sucesso) {
                    showNotification('Notícia publicada com sucesso!', 'success');
                    form.reset();
                    await loadNewsData();
                } else {
                    showNotification('Erro ao criar notícia: ' + response.mensagem, 'error');
                }
            } catch (error) {
                showNotification('Erro ao criar notícia', 'error');
            }
        });
    }
}

function setupTournamentAdminHandlers() {
    const resetBtn = document.getElementById('reset-tournament-btn');
    const statusDiv = document.getElementById('tournament-reset-status');
    
    if (resetBtn) {
        resetBtn.addEventListener('click', async () => {
            if (!confirm('🏆 RESET MANUAL DO TORNEIO DE UPGRADE\n\nTem certeza que deseja resetar o torneio manualmente?\n\nEste processo irá:\n✅ Calcular rankings finais\n💰 Premiar os vencedores\n🔄 Zerar todos os pontos\n🎯 Sortear novo app do dia\n🔄 Reiniciar a competição\n\n⚠️ Esta ação não pode ser desfeita!\n\nContinuar?')) {
                return;
            }
            
            try {
                // Desabilitar botão e mostrar loading
                resetBtn.disabled = true;
                resetBtn.innerHTML = 'Resetando...';
                
                if (statusDiv) {
                    statusDiv.classList.remove('hidden');
                    statusDiv.innerHTML = 'Processando reset do torneio...';
                    statusDiv.className = 'text-sm text-yellow-500';
                }
                
                const response = await fetchAPI('/api/torneio/reset', 'POST');
                
                if (response.sucesso) {
                    if (response.grupos_premiados && response.grupos_premiados.length > 0) {
                        let mensagem = 'Torneio resetado com sucesso!\n\nGrupos premiados:\n';
                        response.grupos_premiados.forEach(grupo => {
                            mensagem += `${grupo.posicao}º lugar: ${grupo.grupo} (${grupo.pontos} pts)\n`;
                        });
                        
                        showNotification('Torneio resetado com sucesso!', 'success');
                        
                        if (statusDiv) {
                            statusDiv.innerHTML = `Reset concluído! ${response.grupos_premiados.length} grupos premiados.`;
                            statusDiv.className = 'text-sm text-green-500';
                        }
                        
                        // Atualizar ranking do torneio se estivermos na página principal
                        if (typeof updateTournamentTimer === 'function') {
                            await updateTournamentTimer();
                        }
                    } else {
                        showNotification('Reset realizado, mas nenhum grupo foi premiado', 'info');
                        if (statusDiv) {
                            statusDiv.innerHTML = 'Reset concluído. Nenhum grupo foi premiado.';
                            statusDiv.className = 'text-sm text-blue-500';
                        }
                    }
                } else {
                    showNotification('Erro ao resetar torneio: ' + response.mensagem, 'error');
                    if (statusDiv) {
                        statusDiv.innerHTML = `Erro: ${response.mensagem}`;
                        statusDiv.className = 'text-sm text-red-500';
                    }
                }
            } catch (error) {
                console.error('Erro ao resetar torneio:', error);
                showNotification('Erro de conexão ao resetar torneio', 'error');
                if (statusDiv) {
                    statusDiv.innerHTML = 'Erro de conexão';
                    statusDiv.className = 'text-sm text-red-500';
                }
            } finally {
                // Reabilitar botão
                resetBtn.disabled = false;
                resetBtn.innerHTML = 'Reset Manual do Torneio';
                
                // Esconder status após 10 segundos
                setTimeout(() => {
                    if (statusDiv) {
                        statusDiv.classList.add('hidden');
                    }
                }, 10000);
            }
        });
    }
}

async function deleteNewsItem(newsId) {
    if (!confirm('Tem certeza que deseja excluir esta notícia?')) return;
    
    try {
        const response = await fetchAPI(`/api/news/${newsId}`, 'DELETE');
        if (response.sucesso) {
            showNotification('Notícia excluída com sucesso!', 'success');
            await loadNewsData();
        } else {
            showNotification('Erro ao excluir notícia: ' + response.mensagem, 'error');
        }
    } catch (error) {
        showNotification('Erro ao excluir notícia', 'error');
    }
}

window.deleteNewsItem = deleteNewsItem;

// === HANDLERS DOS BOTÕES ===

async function handleUpgradePurchase(item, quantity) {
    const button = event.target.closest('.upgrade-btn');
    if (!button) return;
    
    // Feedback visual mais rápido
    button.style.transform = 'scale(0.98)';
    button.style.opacity = '0.8';
    
    try {
        // Delay reduzido para resposta mais rápida
        await new Promise(resolve => setTimeout(resolve, 50));
        
        const response = await fetchAPI('/api/appstore/custos', 'POST', {
            item: item,
            nivel_atual: currentPlayer[item] || 1,
            quantidade: quantity
        });
        
        if (!response.sucesso) {
            showNotification('Erro ao obter custos: ' + response.mensagem, 'error');
            return;
        }
        
        const custoTotalDinheiro = response.custo_dinheiro || 0;
        const custoTotalShacks = response.custo_shacks || 0;
        
        // Verificar se tem dinheiro suficiente
        if (custoTotalDinheiro > currentPlayer.dinheiro) {
            showNotification(`Dinheiro insuficiente! Precisa de $${custoTotalDinheiro.toLocaleString()}`, 'error');
            return;
        }
        
        // Verificar se tem Shacks suficientes (se necessário)
        if (custoTotalShacks > 0 && custoTotalShacks > (currentPlayer.shack || 0)) {
            showNotification(`Shacks insuficientes! Precisa de ${custoTotalShacks} Shack`, 'error');
            return;
        }
        
        const purchaseResponse = await fetchAPI('/api/appstore/comprar', 'POST', {
            item: item,
            quantidade: quantity
        });
        
        if (purchaseResponse.sucesso) {
            let mensagem = `${purchaseResponse.mensagem}`;
            if (custoTotalShacks > 0) {
                mensagem += ` (Custo: $${custoTotalDinheiro.toLocaleString()} + ${custoTotalShacks} Shack)`;
            }
            showNotification(mensagem, 'success');
            
            // Invalida cache e força atualização
            invalidateCache('compra-upgrade');
            await loadGameData(true);
            
            // Delay reduzido para atualização mais rápida
            setTimeout(() => {
                renderAppStore();
            }, 100);
        } else {
            showNotification('Erro: ' + purchaseResponse.mensagem, 'error');
        }
    } catch (error) {
        showNotification('Erro ao processar upgrade: ' + error.message, 'error');
    } finally {
        // Restauração mais rápida do visual
        if (button) {
            setTimeout(() => {
                button.style.transform = '';
                button.style.opacity = '';
            }, 100);
        }
    }
}

async function handleQuickScan() {
    const button = document.getElementById('quick-scan-btn');
    if (!button) return;
    
    button.textContent = 'Escaneando...';
    button.disabled = true;
    
    try {
        const response = await fetchAPI('/api/scan');
        const resultsContent = document.getElementById('scan-results-content');
        
        if (response.sucesso && response.alvos) {
            resultsContent.innerHTML = `
                <div class="space-y-3">
                    ${response.alvos.map(alvo => {
                        const playerCPU = currentPlayer?.cpu || 1;
                        const targetFirewall = alvo.firewall || 1;
                        const canExploit = playerCPU > targetFirewall;
                        const conexoesNoLimite = window.conexoesNoLimite || false;
                        const canExecuteExploit = canExploit && !conexoesNoLimite;
                        
                        let buttonClass, buttonText, buttonDisabled, warningText;
                        
                        if (!canExploit) {
                            buttonClass = 'bg-gray-600 cursor-not-allowed';
                            buttonText = 'Bloqueado';
                            buttonDisabled = 'disabled';
                            warningText = '';
                        } else if (conexoesNoLimite) {
                            buttonClass = 'bg-orange-600 cursor-not-allowed';
                            buttonText = 'Limite 5/5';
                            buttonDisabled = 'disabled';
                            warningText = '<span class="text-xs text-orange-400">(Feche conexões no Terminal)</span>';
                        } else {
                            buttonClass = 'bg-red-600 hover:bg-red-700';
                            buttonText = 'Exploitar';
                            buttonDisabled = '';
                            warningText = '';
                        }
                        
                        return `
                        <div class="bg-surface-default border border-border-color rounded-lg p-4 flex items-center justify-between">
                            <div class="flex-1">
                                <h4 class="font-semibold text-primary-text">${alvo.nick}</h4>
                                <p class="text-secondary-text text-sm">IP: ${alvo.ip || 'N/A'} | Nível: ${alvo.nivel}</p>
                                <p class="text-secondary-text text-xs">Firewall: ${alvo.firewall} | Antivirus: ${alvo.antivirus}</p>
                                <div class="mt-2 flex items-center gap-2">
                                    <span class="text-xs ${canExploit ? 'text-green-400' : 'text-red-400'}">
                                        ${canExploit ? '' : ''} CPU ${playerCPU} vs FW ${targetFirewall}
                                    </span>
                                    ${!canExploit ? '<span class="text-xs text-yellow-400">(Upgrade necessário)</span>' : ''}
                                    ${warningText}
                                </div>
                            </div>
                            <button class="exploit-btn ${buttonClass} text-white px-4 py-2 rounded-lg font-semibold transition-colors" 
                                    data-target='${JSON.stringify(alvo)}' ${buttonDisabled}>
                                ${buttonText}
                            </button>
                        </div>
                    `;
                    }).join('')}
                </div>
            `;
        } else {
            resultsContent.innerHTML = `
                <div class="text-center text-secondary-text py-8">
                    <p>Nenhum alvo encontrado</p>
                    <p class="text-sm">Tente novamente mais tarde</p>
                </div>
            `;
        }
    } catch (error) {
        showNotification('Erro no scan: ' + error.message, 'error');
    } finally {
        button.textContent = 'Iniciar Scan Rápido';
        button.disabled = false;
    }
}

// Função para o scan avançado por IP específico
async function handleAdvancedScan() {
    const input = document.getElementById('advanced-scan-ip');
    const button = document.getElementById('advanced-scan-btn');
    
    if (!input || !button) return;
    
    const targetIP = input.value.trim();
    
    // Validar IP
    const ipRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
    if (!targetIP || !ipRegex.test(targetIP)) {
        showNotification('Por favor, digite um IP válido (ex: ***********)', 'error');
        return;
    }
    
    button.textContent = 'Escaneando...';
    button.disabled = true;
    
    try {
        const response = await fetchAPI(`/api/scan/ip/${targetIP}`);
        const resultsContent = document.getElementById('scan-results-content');
        
        if (response.sucesso && response.alvo) {
            const alvo = response.alvo;
            const playerCPU = currentPlayer?.cpu || 1;
            const targetFirewall = alvo.firewall || 1;
            const canExploit = playerCPU > targetFirewall;
            
            resultsContent.innerHTML = `
                <div class="space-y-3">
                    <div class="text-center mb-4">
                        <h4 class="text-lg font-semibold text-primary-text">Scan Avançado - Alvo Específico</h4>
                        <p class="text-secondary-text text-sm">Resultado do scan para IP: ${targetIP}</p>
                    </div>
                    
                    <div class="bg-surface-default border border-border-color rounded-lg p-6">
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex-1">
                                <h4 class="text-xl font-semibold text-primary-text">${alvo.nick}</h4>
                                <p class="text-secondary-text">IP: <span class="font-mono text-accent-blue">${alvo.ip || 'N/A'}</span></p>
                                <p class="text-secondary-text">Nível: <span class="text-green-400">${alvo.nivel}</span></p>
                            </div>
                            <div class="text-right">
                                <div class="text-2xl mb-2">${alvo.nivel >= 10 ? '' : alvo.nivel >= 5 ? '' : ''}</div>
                                <span class="text-xs ${canExploit ? 'text-green-400' : 'text-red-400'}">
                                    ${canExploit ? 'Vulnerável' : 'Protegido'}
                                </span>
                            </div>
                        </div>
                        
                        <div class="grid grid-cols-2 gap-4 mb-4">
                            <div class="bg-surface-hover rounded-lg p-3">
                                <h5 class="text-sm font-semibold text-primary-text mb-1">Firewall</h5>
                                <p class="text-lg text-orange-400">${alvo.firewall}</p>
                            </div>
                            <div class="bg-surface-hover rounded-lg p-3">
                                <h5 class="text-sm font-semibold text-primary-text mb-1">Antivirus</h5>
                                <p class="text-lg text-blue-400">${alvo.antivirus}</p>
                            </div>
                        </div>
                        
                        <div class="bg-surface-elevated border border-border-color rounded-lg p-4 mb-4">
                            <h5 class="text-sm font-semibold text-primary-text mb-2">Análise de Compatibilidade</h5>
                            <div class="flex items-center justify-between">
                                <span class="text-secondary-text">Seu CPU: <span class="text-accent-blue">${playerCPU}</span></span>
                                <span class="text-secondary-text">vs</span>
                                <span class="text-secondary-text">Firewall Alvo: <span class="text-orange-400">${targetFirewall}</span></span>
                            </div>
                            <div class="mt-2 p-2 rounded ${canExploit ? 'bg-green-500/20 border border-green-500/30' : 'bg-red-500/20 border border-red-500/30'}">
                                <p class="text-sm ${canExploit ? 'text-green-400' : 'text-red-400'}">
                                    ${canExploit ? 
                                        'Exploit possível! Seu CPU é superior ao firewall do alvo.' : 
                                        `Exploit impossível! Upgrade seu CPU para nível ${targetFirewall + 1} ou superior.`
                                    }
                                </p>
                            </div>
                        </div>
                        
                        <div class="text-center">
                            <button class="exploit-btn ${canExploit ? 'bg-red-600 hover:bg-red-700' : 'bg-gray-600 cursor-not-allowed'} text-white px-6 py-3 rounded-lg font-semibold transition-colors text-lg" 
                                    data-target='${JSON.stringify(alvo)}' ${!canExploit ? 'disabled' : ''}>
                                ${canExploit ? 'Exploitar Alvo' : 'Exploit Bloqueado'}
                            </button>
                        </div>
                    </div>
                </div>
            `;
        } else {
            resultsContent.innerHTML = `
                <div class="text-center text-secondary-text py-8">
                    <div class="w-16 h-16 mx-auto mb-4 bg-red-500/20 rounded-full flex items-center justify-center">
                        <svg class="w-8 h-8 text-red-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m9-.75a9 9 0 11-18 0 9 9 0 0118 0zm-9 3.75h.008v.008H12v-.008z" />
                        </svg>
                    </div>
                    <h4 class="text-lg font-semibold text-primary-text mb-2">Alvo não encontrado</h4>
                    <p>O IP <span class="font-mono text-accent-blue">${targetIP}</span> não foi encontrado na rede</p>
                    <p class="text-sm mt-2">Verifique se o IP está correto ou se o alvo está online</p>
                </div>
            `;
        }
    } catch (error) {
        const resultsContent = document.getElementById('scan-results-content');
        resultsContent.innerHTML = `
            <div class="text-center text-secondary-text py-8">
                <div class="w-16 h-16 mx-auto mb-4 bg-red-500/20 rounded-full flex items-center justify-center">
                    <svg class="w-8 h-8 text-red-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m9-.75a9 9 0 11-18 0 9 9 0 0118 0zm-9 3.75h.008v.008H12v-.008z" />
                    </svg>
                </div>
                <h4 class="text-lg font-semibold text-primary-text mb-2">Erro no scan</h4>
                <p>Falha ao escanear o IP: ${targetIP}</p>
                <p class="text-sm mt-2">${error.message || 'Erro de conexão'}</p>
            </div>
        `;
        showNotification('Erro no scan avançado: ' + error.message, 'error');
    } finally {
        button.textContent = 'Scan por IP';
        button.disabled = false;
    }
}

async function handleExploit(targetData) {
    try {
        // Verificar limite de conexões antes de tentar exploit
        if (window.conexoesNoLimite) {
            showNotification('🚫 Limite de conexões atingido! Feche algumas conexões no Terminal antes de executar novos exploits.', 'error');
            return;
        }
        
        // Verificar se o jogador tem CPU suficiente para quebrar o firewall do alvo
        const playerCPU = currentPlayer?.cpu || 1;
        const targetFirewall = targetData.firewall || 1;
        
        if (playerCPU <= targetFirewall) {
            showNotification(`Exploit falhou! Seu CPU (${playerCPU}) não é suficiente para quebrar o Firewall (${targetFirewall}) do alvo.`, 'error');
            showNotification(`Dica: Upgrade seu CPU na App Store para nível ${targetFirewall + 1} ou superior.`, 'info');
            return;
        }
        
        // Se passou na verificação, tentar o exploit via API
        const response = await fetchAPI(`/api/alvo/${targetData.ip}/exploit`, 'POST');
        
        if (response.sucesso && response.alvo_explorado) {
            // Usar dados completos retornados pela API (podem estar anonimizados pelo ProxyVPN)
            exploitedTarget = response.alvo_explorado;
            console.log('[PROXY VPN] Dados do alvo recebidos:', exploitedTarget);
            showNotification(`Sistema invadido com sucesso! CPU ${playerCPU} > Firewall ${targetFirewall}`, 'success');
        } else if (response.limite_conexoes_atingido) {
            // Exploit foi bem-sucedido mas não pôde criar conexão devido ao limite
            exploitedTarget = response.alvo_explorado || targetData;
            showNotification(response.mensagem, 'warning');
            return;
        } else {
            // Se API falhar mas verificação local passou, ainda permitir acesso limitado
            exploitedTarget = targetData;
            showNotification(`Exploit parcial realizado. CPU ${playerCPU} > Firewall ${targetFirewall}`, 'warning');
        }
        
        showSection('invaded-screen');
        renderInvadedScreen();
        
    } catch (error) {
        console.error('Erro no exploit:', error);
        showNotification('Erro de conexão durante o exploit', 'error');
    }
}

// === HANDLERS DOS BOTÕES DA TELA INVADIDA ===

async function handleAccessBank() {
    console.log('Tentando acessar banco...');
    
    if (!exploitedTarget) {
        showNotification('Erro: Alvo não encontrado', 'error');
        return;
    }
    
    try {
        // Verificar se há uma conexão ativa que permite bruteforce
        const statusResponse = await fetchAPI(`/api/terminal/status/${exploitedTarget.ip}`);
        
        if (statusResponse.sucesso) {
            if (statusResponse.conexao_ativa) {
                // Há conexão ativa, mas banco está bloqueado até fazer bruteforce
                showNotification('Acesso negado! Execute um ataque BruteForce primeiro para quebrar a segurança bancária.', 'warning');
                
                // Destacar área do terminal
                const terminalBtn = document.querySelector('[onclick="showSection(\'terminal-section\')"]');
                if (terminalBtn) {
                    terminalBtn.classList.add('animate-pulse', 'bg-red-500/20', 'border-red-500');
                    setTimeout(() => {
                        terminalBtn.classList.remove('animate-pulse', 'bg-red-500/20', 'border-red-500');
                    }, 3000);
                }
                return;
            } else {
                // Não há conexão ativa - erro no exploit
                showNotification('Conexão perdida! Execute um novo exploit primeiro.', 'error');
                handleBackToScan();
                return;
            }
        } else {
            // Bruteforce foi executado com sucesso, liberar acesso ao banco
            console.log('Acesso ao banco liberado após bruteforce bem-sucedido');
            const bankSection = document.getElementById('bank-section');
            if (bankSection) {
                bankSection.classList.toggle('hidden');
                if (!bankSection.classList.contains('hidden')) {
                    showNotification('Acesso ao banco liberado!', 'success');
                }
            }
        }
    } catch (error) {
        console.error('Erro ao verificar status do bruteforce:', error);
        showNotification('Erro ao verificar acesso bancário', 'error');
    }
}

function handleDeface() {
    if (!exploitedTarget) {
        showNotification('Erro: Alvo não encontrado', 'error');
        return;
    }
    
    if (confirm(`Confirma o deface do site de ${exploitedTarget.nick}?`)) {
        performDeface();
    }
}

function handleViewLog() {
    console.log('[DEBUG] Visualizando log do alvo exploitado...');
    console.log('[DEBUG] exploitedTarget:', exploitedTarget);

    if (!exploitedTarget) {
        console.error('[DEBUG] exploitedTarget não encontrado!');
        showNotification('Erro: Dados do alvo não encontrados', 'error');
        return;
    }

    // Buscar a seção de log do alvo na tela de invasão (não o app principal)
    const invasionScreen = document.getElementById('invaded-screen');
    if (!invasionScreen) {
        console.error('[DEBUG] Tela de invasão não encontrada!');
        return;
    }

    const logSection = invasionScreen.querySelector('#log-section');
    console.log('[DEBUG] logSection do alvo encontrada:', logSection);

    if (logSection) {
        // Verificar se está oculto (classe hidden OU display: none)
        const isHidden = logSection.classList.contains('hidden') ||
                        logSection.style.display === 'none' ||
                        getComputedStyle(logSection).display === 'none';

        console.log('[DEBUG] Seção do alvo está oculta?', isHidden);
        console.log('[DEBUG] Classes:', logSection.className);
        console.log('[DEBUG] Style display:', logSection.style.display);

        if (isHidden) {
            console.log('[DEBUG] Seção estava oculta, mostrando e carregando log do alvo...');
            loadTargetLog();
            logSection.classList.remove('hidden');
            logSection.style.display = 'block';
        } else {
            console.log('[DEBUG] Seção estava visível, ocultando...');
            logSection.classList.add('hidden');
            logSection.style.display = 'none';
        }
    } else {
        console.error('[DEBUG] Elemento log-section do alvo não encontrado na tela de invasão!');
    }
}

function handleBackToScan() {
    console.log('🔙 Voltando ao scan...');
    exploitedTarget = null;
    showSection('scan-section');
}

// Sistema de seleção de porcentagem para transferência
function handlePercentageSelection(button) {
    console.log('Selecionando porcentagem para transferência...');
    
    if (!exploitedTarget) {
        showNotification('Erro: Alvo não encontrado', 'error');
        return;
    }
    
    const percentage = parseInt(button.dataset.percentage);
    console.log(`Porcentagem selecionada: ${percentage}%`);
    
    // Remove seleção anterior
    document.querySelectorAll('.percentage-btn').forEach(btn => {
        btn.classList.remove('selected');
        btn.style.borderColor = '';
        btn.style.backgroundColor = '';
        const overlay = btn.querySelector('.absolute');
        if (overlay) overlay.style.opacity = '0';
    });
    
    // Adiciona seleção atual
    button.classList.add('selected');
    button.style.borderColor = '#10b981'; // accent-green
    button.style.backgroundColor = 'rgba(16, 185, 129, 0.1)';
    const overlay = button.querySelector('.absolute');
    if (overlay) overlay.style.opacity = '1';
    
    // Calcula valor a ser transferido
    selectedPercentage = percentage;
    const targetMoney = parseInt(exploitedTarget.dinheiro) || 0;
    calculatedTransferAmount = Math.floor((targetMoney * percentage) / 100);
    
    // Atualiza preview
    const transferPreview = document.getElementById('transfer-preview');
    const transferAmountEl = document.getElementById('transfer-amount');
    const confirmBtn = document.getElementById('confirm-transfer-btn');
    
    if (transferPreview && transferAmountEl && confirmBtn) {
        transferPreview.classList.remove('hidden');
        transferAmountEl.textContent = `$${calculatedTransferAmount.toLocaleString()}`;
        confirmBtn.disabled = false;
        
        console.log(`Valor calculado para transferência: $${calculatedTransferAmount}`);
    }
}

// Confirma a transferência do valor selecionado
async function handleConfirmTransfer() {
    console.log('Confirmando transferência...');
    
    if (!selectedPercentage || calculatedTransferAmount <= 0) {
        showNotification('Selecione uma porcentagem válida primeiro', 'warning');
        return;
    }
    
    // Usa a função original de transferência
    await handleMoneyTransfer(selectedPercentage);
    
    // Reset da seleção
    selectedPercentage = null;
    calculatedTransferAmount = 0;
    
    // Remove seleção visual
    document.querySelectorAll('.percentage-btn').forEach(btn => {
        btn.classList.remove('selected');
        btn.style.borderColor = '';
        btn.style.backgroundColor = '';
        const overlay = btn.querySelector('.absolute');
        if (overlay) overlay.style.opacity = '0';
    });
    
    // Esconde preview
    const transferPreview = document.getElementById('transfer-preview');
    const confirmBtn = document.getElementById('confirm-transfer-btn');
    
    if (transferPreview) transferPreview.classList.add('hidden');
    if (confirmBtn) confirmBtn.disabled = true;
}

async function handleMoneyTransfer(percentage) {
    console.log('[DEBUG] Iniciando transferência de dinheiro...');
    console.log('[DEBUG] Porcentagem recebida:', percentage);
    console.log('[DEBUG] exploitedTarget:', exploitedTarget);
    
    if (!exploitedTarget) {
        console.error('[DEBUG] exploitedTarget é null');
        showNotification('Erro: Alvo não encontrado', 'error');
        return;
    }
    
    // Debug: Verificar dados do alvo
    console.log('[DEBUG] Dados do alvo:', exploitedTarget);
    
    if (!exploitedTarget.uid) {
        console.error('[DEBUG] UID do alvo não encontrado. Campos disponíveis:', Object.keys(exploitedTarget));
        showNotification('Erro: UID do alvo não encontrado', 'error');
        return;
    }
    
    // Validação da porcentagem
    const numPercentage = Number(percentage);
    if (!numPercentage || numPercentage <= 0 || numPercentage > 100) {
        console.error('[DEBUG] Porcentagem inválida:', percentage);
        showNotification('Erro: Porcentagem inválida', 'error');
        return;
    }
    
    const targetMoney = exploitedTarget.dinheiro || 0;
    const amount = Math.floor(targetMoney * (numPercentage / 100));
    
    console.log('[DEBUG] Dinheiro do alvo:', targetMoney);
    console.log('[DEBUG] Quantia a transferir:', amount);
    
    if (amount <= 0) {
        showNotification('Alvo não possui dinheiro suficiente', 'error');
        return;
    }
    
    try {
        const requestData = {
            alvo_uid: exploitedTarget.uid,
            porcentagem: numPercentage // Garantir que é um número
        };
        
        // Debug: Verificar dados da requisição
        console.log('[DEBUG] Dados da requisição:', requestData);
        console.log('[DEBUG] Tipo do alvo_uid:', typeof exploitedTarget.uid);
        console.log('[DEBUG] Valor exato do alvo_uid:', JSON.stringify(exploitedTarget.uid));
        
        const response = await fetchAPI('/api/alvo/transferir', 'POST', requestData);
        
        console.log('[DEBUG] Resposta da API:', response);
        
        if (response.sucesso) {
            showNotification(`$${amount.toLocaleString()} transferidos com sucesso!`, 'success');
            exploitedTarget.dinheiro -= amount;
            renderInvadedScreen(); // Atualiza a tela
            invalidateCache('transferencia-dinheiro');
            await loadGameData(true); // Força atualização dos dados do jogador
        } else {
            showNotification('Erro na transferência: ' + response.mensagem, 'error');
            console.error('[DEBUG] Resposta da API:', response);
        }
    } catch (error) {
        console.error('[DEBUG] Erro na requisição:', error);
        if (error.message && error.message.includes('500')) {
            showNotification('Erro do servidor (500). Verifique o console para mais detalhes.', 'error');
        } else {
            showNotification('Erro na transferência: ' + (error.message || 'Erro desconhecido'), 'error');
        }
    }
}

async function performDeface() {
    try {
        const response = await fetchAPI('/api/alvo/deface', 'POST', { 
            alvo_uid: exploitedTarget.uid 
        });
        
        if (response.sucesso) {
            showNotification('Deface realizado com sucesso!', 'success');
            invalidateCache('deface');
            await loadGameData(true); // Força atualização para mostrar XP/dinheiro ganho
            
            // Volta para a tela principal após deface bem-sucedido
            setTimeout(() => {
                exploitedTarget = null;
                showSection('main-dashboard');
            }, 2000);
        } else {
            showNotification('Erro no deface: ' + response.mensagem, 'error');
        }
    } catch (error) {
        showNotification('Erro no deface: ' + error.message, 'error');
    }
}

async function loadTargetLog() {
    console.log('[DEBUG] Carregando log do alvo...');
    console.log('[DEBUG] exploitedTarget:', exploitedTarget);

    if (!exploitedTarget) {
        console.error('[DEBUG] exploitedTarget é null');
        return;
    }

    try {
        console.log('[DEBUG] Fazendo requisição para:', `/api/logs/alvo/${exploitedTarget.ip}`);
        const response = await fetchAPI(`/api/logs/alvo/${exploitedTarget.ip}`);
        console.log('[DEBUG] Resposta da API:', response);

        // Buscar o elemento target-log-content na tela de invasão
        const invasionScreen = document.getElementById('invaded-screen');
        const logContent = invasionScreen ? invasionScreen.querySelector('#target-log-content') : null;
        console.log('[DEBUG] Elemento target-log-content:', logContent);

        if (!logContent) {
            console.error('[DEBUG] Elemento target-log-content não encontrado na tela de invasão!');
            return;
        }
        
        if (response.sucesso && response.logs) {
            if (response.logs.length === 0) {
                logContent.innerHTML = `
                    <div class="text-center py-8 text-secondary-text">
                        <svg class="w-12 h-12 mx-auto mb-3 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12.75L11.25 15 15 9.75m-3-7.036A11.959 11.959 0 013.598 6 11.99 11.99 0 003 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.623 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285z" />
                        </svg>
                        <p class="text-lg font-medium text-green-400">Sistema Limpo</p>
                        <p class="text-sm">Nenhuma invasão detectada no alvo</p>
                        <p class="text-xs mt-2 opacity-75">Este jogador não foi exploitado recentemente</p>
                    </div>
                `;
            } else {
                // Mostrar informações do alvo
                const alvoInfo = response.alvo_info || {};
                const headerHtml = `
                    <div class="bg-blue-900/20 border border-blue-500/50 rounded-lg p-4 mb-4">
                        <h3 class="text-lg font-bold text-blue-300 mb-2">Log de Segurança do Alvo</h3>
                        <div class="text-blue-200 text-sm">
                            <p><strong>Nick:</strong> ${alvoInfo.nick || 'Desconhecido'}</p>
                            <p><strong>IP:</strong> ${alvoInfo.ip || exploitedTarget.ip}</p>
                            <p><strong>Nível:</strong> ${alvoInfo.nivel || 1}</p>
                        </div>
                    </div>
                `;

                // Renderizar logs de security alert
                const logsHtml = response.logs.map(log => {
                    // Tratamento especial para deface ativo
                    if (log.is_deface_ativo) {
                        return `
                            <div class="bg-red-900/30 border-2 border-red-500 rounded-lg p-4 mb-3 animate-pulse">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center gap-3">
                                        <div class="w-8 h-8 rounded-lg bg-red-500 flex items-center justify-center">
                                            <svg class="w-4 h-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                                            </svg>
                                        </div>
                                        <div>
                                            <h4 class="text-lg font-bold text-red-400">${log.mensagem}</h4>
                                            <p class="text-sm text-red-300">Sistema comprometido</p>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <div class="text-sm font-bold text-red-400">${log.tempo_restante}</div>
                                        <div class="text-xs text-red-300">Restante</div>
                                    </div>
                                </div>
                            </div>
                        `;
                    }

                    // Security Alert
                    const timeStr = new Date(log.created_at).toLocaleString('pt-BR');
                    const ipAtacante = log.atacante_ip || (log.dados && log.dados.atacante_ip) || 'IP desconhecido';
                    const quantiaRoubada = log.quantia_roubada || (log.dados && log.dados.quantia_roubada);
                    const grupoNome = log.grupo_nome || (log.dados && log.dados.grupo_nome);

                    const isTransferencia = log.dados && log.dados.tipo === 'exploit_transferencia';
                    const isDeface = log.dados && log.dados.tipo === 'exploit_deface';
                    const isExploitInicial = log.dados && log.dados.tipo === 'exploit_inicial';

                    let tipoLabel = 'INVASÃO';
                    let corTipo = 'bg-gray-500';
                    if (isTransferencia) {
                        tipoLabel = 'TRANSFERÊNCIA';
                        corTipo = 'bg-red-500';
                    } else if (isDeface) {
                        tipoLabel = 'DEFACE';
                        corTipo = 'bg-orange-500';
                    } else if (isExploitInicial) {
                        tipoLabel = 'EXPLOIT';
                        corTipo = 'bg-purple-500';
                    }

                    const valorDisplay = quantiaRoubada ? `<span class="text-red-400 font-bold text-xs">$${quantiaRoubada.toLocaleString()}</span>` : '';
                    const grupoDisplay = grupoNome ? `<span class="text-orange-400 font-medium text-xs">${grupoNome}</span>` : '';

                    return `
                        <div class="bg-gray-800 border border-red-500/30 rounded-lg p-3 mb-2">
                            <div class="flex items-center justify-between mb-2">
                                <p class="text-red-400 font-mono text-xs">${log.mensagem}</p>
                                <span class="text-xs px-2 py-1 rounded ${corTipo} text-white">
                                    ${tipoLabel}
                                </span>
                            </div>
                            <div class="flex items-center justify-between text-xs">
                                <span class="text-gray-400">${timeStr}</span>
                                <div class="flex items-center gap-2">
                                    ${valorDisplay}
                                    ${grupoDisplay}
                                </div>
                            </div>
                            <div class="flex items-center gap-2 mt-1">
                                <span class="text-xs text-gray-400">Invasor:</span>
                                <span class="font-mono text-blue-400 text-xs">${ipAtacante}</span>
                            </div>
                        </div>
                    `;
                }).join('');

                logContent.innerHTML = headerHtml + logsHtml;
            }
        } else {
            logContent.innerHTML = `
                <div class="text-center text-gray-400 py-4">
                    <p>Erro ao carregar log do alvo</p>
                </div>
            `;
        }
    } catch (error) {
        console.error('Erro ao carregar log:', error);
        // Buscar o elemento target-log-content na tela de invasão
        const invasionScreen = document.getElementById('invaded-screen');
        const logContent = invasionScreen ? invasionScreen.querySelector('#target-log-content') : null;

        if (logContent) {
            logContent.innerHTML = `
                <div class="text-center text-red-400 py-4">
                    <p>Erro ao carregar log do alvo</p>
                    <p class="text-xs mt-2">${error.message}</p>
                </div>
            `;
        } else {
            console.error('[DEBUG] Elemento target-log-content não encontrado para mostrar erro!');
        }
    }
}

function handleLogFilter(filter, buttonElement) {
    document.querySelectorAll('.log-filter-btn').forEach(btn => {
        btn.classList.remove('active', 'bg-accent-blue', 'text-white');
        btn.classList.add('bg-surface-default', 'text-primary-text');
    });
    
    buttonElement.classList.add('active', 'bg-accent-blue', 'text-white');
    buttonElement.classList.remove('bg-surface-default', 'text-primary-text');
    
    const logEntries = document.querySelectorAll('.log-entry');
    logEntries.forEach(entry => {
        const entryType = entry.getAttribute('data-type');
        entry.style.display = (filter === 'all' || entryType === filter) ? 'flex' : 'none';
    });
}

function handleLogout() {
    if (confirm('Deseja realmente sair?')) {
        window.auth.logout();
    }
}

// Handler para compras do Mercado Negro
async function handleMercadoNegroCompra(itemId) {
    try {
        if (!itemId) {
            showNotification('Erro: Item não especificado', 'error');
            return;
        }

        if (!currentPlayer) {
            showNotification('Erro: dados do jogador não carregados', 'error');
            return;
        }

        // Calcula preços dinâmicos
        let preco;
        if (itemId === 'upgrade_miner_1') {
            const nivelMineradora = currentPlayer.nivel_mineradora || 1;
            preco = calculateMinerUpgradePrice(nivelMineradora);
        } else {
            showNotification('Erro: Item não encontrado', 'error');
            return;
        }

        if (currentPlayer.shack < preco) {
            showNotification(`Shack insuficiente! Você precisa de ${preco.toLocaleString()} Shack`, 'error');
            return;
        }

        // Faz a compra
        const response = await fetchAPI('/api/mercado-negro/comprar', 'POST', {
            item_id: itemId
        });

        if (response.sucesso) {
            showNotification(response.mensagem, 'success');
            invalidateCache('mercado-negro-compra');
            await loadGameData(true); // Força refresh dos dados
            
            // Remove seções do cache para forçar re-renderização com dados atualizados
            renderedSections.delete('mercado-negro-section');
            renderedSections.delete('mineradora-section');
            
            renderMercadoNegroSection(); // Atualiza a interface
            
            // Se foi upgrade da mineradora, atualiza também a seção da mineradora se estiver visível
            if (itemId === 'upgrade_miner_1') {
                const mineradoraSection = document.getElementById('mineradora-section');
                if (mineradoraSection && mineradoraSection.style.display !== 'none') {
                    renderMineradoraSection();
                }
            }
        } else {
            showNotification(response.mensagem || 'Erro ao realizar compra', 'error');
        }
    } catch (error) {
        console.error('Erro na compra do mercado negro:', error);
        showNotification('Erro interno: ' + error.message, 'error');
    }
}

// Handler para coleta de Shacks (novo sistema de mineração)
async function handleColetarShacks() {
    try {
        // Previne cliques duplos
        const botao = document.getElementById('coletar-shacks-btn');
        if (botao && botao.disabled) {
            return;
        }

        // Feedback visual imediato
        if (botao) {
            botao.disabled = true;
            botao.innerHTML = `
                <div class="relative flex items-center justify-center space-x-2">
                    <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    <span>Coletando...</span>
                </div>
            `;
        }

        // Chama API para coletar Shacks
        const response = await fetchAPI('/api/mineracao/coletar-shacks', 'POST', {});

        if (response.sucesso) {
            // Feedback de sucesso
            showNotification(`🎁 ${response.mensagem}`, 'success');
            
            // Atualiza dados do jogador
            invalidateCache('coleta-shacks');
            await loadGameData(true);
            
            // Remove seção do cache e re-renderiza a mineradora
            renderedSections.delete('mineradora-section');
            await renderMineradoraSection();
            
            // Efeito visual de sucesso no botão
            if (botao) {
                botao.style.background = 'linear-gradient(to right, #10b981, #059669)';
                botao.innerHTML = `
                    <div class="relative flex items-center justify-center space-x-2">
                        <svg class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M4.5 12.75l6 6 9-13.5" />
                        </svg>
                        <span>Coletado!</span>
                    </div>
                `;
                
                // Restaura o botão após um tempo
                setTimeout(() => {
                    if (botao) {
                        botao.disabled = false;
                        botao.style.background = '';
                    }
                }, 2000);
            }
            
        } else {
            // Feedback de erro
            showNotification(`${response.mensagem}`, 'error');
            
            // Restaura o botão
            if (botao) {
                botao.disabled = false;
                botao.innerHTML = `
                    <div class="relative flex items-center justify-center space-x-2">
                        <svg class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M21 11.25v8.25a1.5 1.5 0 0 1-1.5 1.5H5.25a1.5 1.5 0 0 1-1.5-1.5v-8.25M12 4.875A2.625 2.625 0 1 0 9.375 7.5H12m0-2.625V7.5m0-2.625A2.625 2.625 0 1 1 14.625 7.5H12m0 0V21m-8.625-9.75h18c.621 0 1.125-.504 1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125h-18c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125Z" />
                        </svg>
                        <span>Tentar Novamente</span>
                    </div>
                `;
            }
        }
        
    } catch (error) {
        console.error('Erro ao coletar Shacks:', error);
        showNotification('Erro interno: ' + error.message, 'error');
        
        // Restaura o botão em caso de erro
        const botao = document.getElementById('coletar-shacks-btn');
        if (botao) {
            botao.disabled = false;
            botao.innerHTML = `
                <div class="relative flex items-center justify-center space-x-2">
                    <svg class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M21 11.25v8.25a1.5 1.5 0 0 1-1.5 1.5H5.25a1.5 1.5 0 0 1-1.5-1.5v-8.25M12 4.875A2.625 2.625 0 1 0 9.375 7.5H12m0-2.625V7.5m0-2.625A2.625 2.625 0 1 1 14.625 7.5H12m0 0V21m-8.625-9.75h18c.621 0 1.125-.504 1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125h-18c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125Z" />
                    </svg>
                    <span>Erro - Tentar Novamente</span>
                </div>
            `;
        }
    }
}

// Handler para coleta de Shacks (novo sistema de mineração)
async function handleColetarShacks() {
    try {
        // Previne cliques duplos
        const botao = document.getElementById('coletar-shacks-btn');
        if (botao && botao.disabled) {
            return;
        }

        // Feedback visual imediato
        if (botao) {
            botao.disabled = true;
            botao.innerHTML = `
                <div class="relative flex items-center justify-center space-x-2">
                    <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    <span>Coletando...</span>
                </div>
            `;
        }

        // Chama API para coletar Shacks
        const response = await fetchAPI('/api/mineracao/coletar-shacks', 'POST', {});

        if (response.sucesso) {
            // Feedback de sucesso
            showNotification(`🎁 ${response.mensagem}`, 'success');
            
            // Atualiza dados do jogador
            invalidateCache('coleta-shacks');
            await loadGameData(true);
            
            // Remove seção do cache e re-renderiza a mineradora
            renderedSections.delete('mineradora-section');
            await renderMineradoraSection();
            
            // Efeito visual de sucesso no botão
            if (botao) {
                botao.style.background = 'linear-gradient(to right, #10b981, #059669)';
                botao.innerHTML = `
                    <div class="relative flex items-center justify-center space-x-2">
                        <svg class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M4.5 12.75l6 6 9-13.5" />
                        </svg>
                        <span>Coletado!</span>
                    </div>
                `;
                
                // Restaura o botão após um tempo
                setTimeout(() => {
                    if (botao) {
                        botao.disabled = false;
                        botao.style.background = '';
                    }
                }, 2000);
            }
            
        } else {
            // Feedback de erro
            showNotification(`${response.mensagem}`, 'error');
            
            // Restaura o botão
            if (botao) {
                botao.disabled = false;
                botao.innerHTML = `
                    <div class="relative flex items-center justify-center space-x-2">
                        <svg class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M21 11.25v8.25a1.5 1.5 0 0 1-1.5 1.5H5.25a1.5 1.5 0 0 1-1.5-1.5v-8.25M12 4.875A2.625 2.625 0 1 0 9.375 7.5H12m0-2.625V7.5m0-2.625A2.625 2.625 0 1 1 14.625 7.5H12m0 0V21m-8.625-9.75h18c.621 0 1.125-.504 1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125h-18c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125Z" />
                        </svg>
                        <span>Tentar Novamente</span>
                    </div>
                `;
            }
        }
        
    } catch (error) {
        console.error('Erro ao coletar Shacks:', error);
        showNotification('Erro interno: ' + error.message, 'error');
        
        // Restaura o botão em caso de erro
        const botao = document.getElementById('coletar-shacks-btn');
        if (botao) {
            botao.disabled = false;
            botao.innerHTML = `
                <div class="relative flex items-center justify-center space-x-2">
                    <svg class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M21 11.25v8.25a1.5 1.5 0 0 1-1.5 1.5H5.25a1.5 1.5 0 0 1-1.5-1.5v-8.25M12 4.875A2.625 2.625 0 1 0 9.375 7.5H12m0-2.625V7.5m0-2.625A2.625 2.625 0 1 1 14.625 7.5H12m0 0V21m-8.625-9.75h18c.621 0 1.125-.504 1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125h-18c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125Z" />
                    </svg>
                    <span>Erro - Tentar Novamente</span>
                </div>
            `;
        }
    }
}

// Handler para upgrade de habilidades
async function upgradeHabilidade(tipoHabilidade) {
    try {
        if (!currentPlayer) {
            showNotification('Erro: dados do jogador não carregados', 'error');
            return;
        }

        if (currentPlayer.pontos_habilidade <= 0) {
            showNotification('Você não possui pontos de habilidade suficientes!', 'error');
            return;
        }

        // Verifica se a habilidade já está no nível máximo
        const nivelAtual = currentPlayer[`habilidade_${tipoHabilidade}`] || 0;
        if (nivelAtual >= 5) {
            showNotification('Esta habilidade já está no nível máximo!', 'warning');
            return;
        }

        // Faz o upgrade
        const response = await fetchAPI('/api/habilidades/upgrade', 'POST', {
            tipo_habilidade: tipoHabilidade
        });

        if (response.sucesso) {
            showNotification(response.mensagem, 'success');
            invalidateCache('habilidade-upgrade');
            await loadGameData(true); // Força refresh dos dados
            
            // Remove seção do cache para forçar re-renderização com dados atualizados
            renderedSections.delete('habilidades-section');
            renderHabilidadesSection(); // Atualiza a interface
        } else {
            showNotification('Erro: ' + response.mensagem, 'error');
        }
    } catch (error) {
        console.error('Erro ao fazer upgrade da habilidade:', error);
        showNotification('Erro ao processar upgrade: ' + error.message, 'error');
    }
}

// Torna a função global para poder ser chamada pelos botões
window.upgradeHabilidade = upgradeHabilidade;
window.switchHabilidadesTab = switchHabilidadesTab;
window.comprarHabilidadeNFT = comprarHabilidadeNFT;

// --- FUNÇÃO RENDERIZAR SEÇÃO DO TERMINAL ---
async function renderTerminalSection() {
    const container = document.getElementById('terminal-container');
    if (!container) return;
    
    container.innerHTML = `
        <div class="h-full overflow-y-auto custom-scrollbar">
            <div class="max-w-4xl mx-auto p-6 pb-20 space-y-6">
                <!-- Header do Terminal -->
                <div class="bg-surface-elevated border border-border-color rounded-xl p-6">
                    <h2 class="text-2xl font-bold text-primary-text mb-4 flex items-center gap-3">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-8 h-8 text-accent-blue">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M6.75 7.5l3 2.25-3 2.25m4.5 0h3m-9 8.25h13.5A2.25 2.25 0 0021 18V6a2.25 2.25 0 00-2.25-2.25H5.25A2.25 2.25 0 003 6v12a2.25 2.25 0 002.25 2.25z" />
                        </svg>
                        Terminal de Hacking
                    </h2>
                    <p class="text-secondary-text mb-6">Execute ataques de BruteForce contra sistemas bancários comprometidos</p>
                    
                    <!-- Status das Conexões e Controles -->
                    <div class="bg-surface-default border border-border-color rounded-lg p-4">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-3">
                                <div class="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                                <div>
                                    <h3 class="text-lg font-semibold text-primary-text">Status das Conexões</h3>
                                    <p id="connections-status" class="text-2xl font-bold text-accent-blue">0/5 conexões ativas</p>
                                </div>
                            </div>
                            <div class="flex gap-2">
                                <button id="btn-fechar-todas-conexoes" 
                                        onclick="fecharTodasConexoes()" 
                                        class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300 hover:scale-105"
                                        style="display: none;">
                                    🔌 Fechar Todas
                                </button>
                                <button id="btn-atualizar-conexoes" 
                                        onclick="atualizarStatusConexoes()" 
                                        class="bg-accent-blue hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300 hover:scale-105">
                                    Atualizar
                                </button>
                            </div>
                        </div>
                        <div id="exploits-warning" class="mt-3 p-3 bg-red-500/20 border border-red-500 rounded-lg" style="display: none;">
                            <p class="text-red-400 text-sm">🚫 Limite de conexões atingido! Feche algumas conexões para executar novos exploits.</p>
                        </div>
                    </div>
                </div>
                
                <!-- Conexões Ativas -->
                <div class="bg-surface-elevated border border-border-color rounded-xl p-6">
                    <h3 class="text-xl font-semibold text-primary-text mb-4 flex items-center gap-2">
                        🔗 Conexões Ativas
                    </h3>
                    <div id="active-connections" class="space-y-4">
                        <!-- Conexões serão carregadas aqui -->
                    </div>
                </div>
                
                <!-- Histórico de Ataques -->
                <div class="bg-surface-elevated border border-border-color rounded-xl p-6">
                    <h3 class="text-xl font-semibold text-primary-text mb-4 flex items-center gap-2">
                        Histórico de Ataques BruteForce
                    </h3>
                    <div id="bruteforce-history" class="space-y-3">
                        <!-- Histórico será carregado aqui -->
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // Carrega as conexões ativas
    await loadActiveConnections();
    
    // Carrega o histórico de ataques
    await loadBruteforceHistory();
    
    // Verificar status das conexões automaticamente a cada 30 segundos
    if (window.terminalIntervalId) {
        clearInterval(window.terminalIntervalId);
    }
    window.terminalIntervalId = setInterval(async () => {
        await atualizarStatusConexoes();
    }, 30000);
}

// --- FUNÇÃO PARA CARREGAR CONEXÕES ATIVAS ---
async function loadActiveConnections() {
    const container = document.getElementById('active-connections');
    if (!container) return;
    
    container.innerHTML = `
        <div class="text-center py-4">
            <div class="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-accent-blue"></div>
            <p class="text-secondary-text mt-2">Carregando conexões...</p>
        </div>
    `;
    
    try {
        const response = await fetchAPI('/api/conexoes/status', 'POST', { uid: currentPlayer?.uid });
        
        if (response.sucesso && response.conexoes) {
            // Atualizar status das conexões
            atualizarStatusConexoesUI(response.conexoes.length, 5);
            
            if (response.conexoes.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-8">
                        <div class="w-16 h-16 mx-auto mb-4 bg-gray-500/20 rounded-full flex items-center justify-center">
                            <svg class="w-8 h-8 text-gray-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M8.25 15L12 18.75 15.75 15m-7.5-6L12 5.25 15.75 9" />
                            </svg>
                        </div>
                        <p class="text-secondary-text">Nenhuma conexão ativa</p>
                        <p class="text-secondary-text text-sm">Execute exploits para criar conexões e acessar o sistema bancário</p>
                    </div>
                `;
                return;
            }
            
            // Renderiza as conexões usando sistema de renderização dinâmica
            container.innerHTML = response.conexoes.map(conexao => renderConnectionCard(conexao)).join('');

            // Após renderizar, reinserir o botão do banco para conexões ativas e liberadas
            for (const conexao of response.conexoes) {
                // Verificar tanto banco_liberado quanto acesso_banco_liberado para compatibilidade
                const temAcesso = conexao.banco_liberado || conexao.acesso_banco_liberado;
                if (temAcesso && conexao.alvo_dados) {
                    console.log(`[PERSISTENCIA] Restaurando acesso bancário para ${conexao.alvo_ip}`, conexao.alvo_dados);
                    updateConnectionWithBankButton(conexao.alvo_ip, conexao.alvo_dados);
                }
            }

            // Inicia monitoramento de conexões para detectar expirações
            startConnectionMonitoring();
        } else {
            container.innerHTML = `
                <div class="text-center py-8 text-red-400">
                    <p>Erro ao carregar conexões: ${response.mensagem || 'Erro desconhecido'}</p>
                </div>
            `;
        }
    } catch (error) {
        console.error('Erro ao carregar conexões:', error);
        container.innerHTML = `
            <div class="text-center py-8 text-red-400">
                <p>Erro de conexão: ${error.message}</p>
            </div>
        `;
    }
}

// --- FUNÇÃO PARA RENDERIZAR CARD DE CONEXÃO ---
function renderConnectionCard(conexao) {
    const actionButtons = renderConnectionActions(conexao);
    // Removido: const bankButton = renderBankButton(conexao);
    // O botão do banco só será inserido após o bruteforce, via updateConnectionWithBankButton
    return `
        <div class="bg-surface-default border border-border-color rounded-lg p-4 hover:border-accent-blue transition-all duration-300" data-connection-ip="${conexao.alvo_ip}">
            <div class="flex items-center justify-between mb-3">
                <div class="flex items-center gap-3">
                    <div class="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                    <div>
                        <h4 class="font-semibold text-primary-text">${conexao.alvo_nick || 'Desconhecido'}</h4>
                        <p class="text-xs text-secondary-text">IP: ${conexao.alvo_ip || 'N/A'}</p>
                    </div>
                </div>
                <div class="text-right">
                    <p class="text-xs text-secondary-text">Conexão Ativa</p>
                    <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse ml-auto"></div>
                </div>
            </div>
            
            <div class="mb-3">
                <div class="text-xs text-secondary-text">
                    Tempo restante: ${conexao.tempo_expiracao || 'Calculando...'}
                </div>
            </div>
            
            <!-- Actions -->
            <div class="space-y-2" data-actions-container="${conexao.alvo_ip}">
                ${actionButtons}
                <!-- O botão do banco será inserido aqui após o bruteforce -->
            </div>
            
            <!-- Container para progresso do bruteforce -->
            <div data-bruteforce-ip="${conexao.alvo_ip}" class="mt-3"></div>
        </div>
    `;
}

// --- FUNÇÃO PARA RENDERIZAR AÇÕES DA CONEXÃO ---
function renderConnectionActions(conexao) {
    let actionButton = '';
    
    if (conexao.bruteforce_ativo) {
        // Bruteforce em andamento - mostrar progresso
        actionButton = `
            <div class="bg-orange-500/20 border border-orange-500 rounded-lg p-3 mb-2">
                <div class="flex items-center gap-2 mb-2">
                    <div class="w-2 h-2 bg-orange-400 rounded-full animate-pulse"></div>
                    <span class="text-orange-400 text-sm font-medium">BruteForce em execução...</span>
                </div>
                <div class="text-xs text-secondary-text">
                    Tempo restante: ${conexao.tempo_bruteforce_restante ? Math.floor(conexao.tempo_bruteforce_restante / 60) + 'm ' + (conexao.tempo_bruteforce_restante % 60) + 's' : 'Calculando...'}
                </div>
            </div>
        `;
    } else {
        // Status disponível - mostrar botão inicial
        actionButton = `
            <button onclick="iniciarBruteforce('${conexao.alvo_ip}')" 
                    class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300 hover:scale-105 mr-2">
                Iniciar BruteForce
            </button>
        `;
    }
    
    // Adicionar botão de fechar conexão individual
    const closeButton = `
        <button onclick="fecharConexaoEspecifica('${conexao.alvo_ip}')" 
                class="bg-gray-600 hover:bg-gray-700 text-white px-3 py-2 rounded-lg text-sm font-medium transition-all duration-300 hover:scale-105"
                title="Fechar esta conexão">
            Fechar
        </button>
    `;
    
    return `
        <div class="flex items-center gap-2">
            ${actionButton}
            ${closeButton}
        </div>
    `;
}

// --- FUNÇÃO PARA RENDERIZAR BOTÃO DO BANCO ---
// Função utilitária para garantir que alvo_dados seja sempre um objeto válido
function parseAlvoDados(alvo_dados) {
    if (!alvo_dados) return { nick: 'Alvo', dinheiro: 0 };
    if (typeof alvo_dados === 'string') {
        try {
            const obj = JSON.parse(alvo_dados);
            if (obj && typeof obj === 'object') return obj;
        } catch (e) {
            return { nick: 'Alvo', dinheiro: 0 };
        }
    }
    if (typeof alvo_dados === 'object') return alvo_dados;
    return { nick: 'Alvo', dinheiro: 0 };
}

function renderBankButton(conexao) {
    // Verificar tanto banco_liberado quanto acesso_banco_liberado para compatibilidade
    const temAcesso = conexao.banco_liberado || conexao.acesso_banco_liberado;

    if (temAcesso) {
        // Sempre garantir alvo_dados válido
        const alvoDadosObj = parseAlvoDados(conexao.alvo_dados);
        // Se alvoDadosObj não tem dinheiro ou nick, não renderiza botão
        if (!alvoDadosObj || typeof alvoDadosObj.dinheiro === 'undefined' || typeof alvoDadosObj.nick === 'undefined') {
            return `<div class="text-xs text-red-400">Dados do alvo indisponíveis para acesso ao banco.</div>`;
        }
        const alvoDadosString = JSON.stringify(alvoDadosObj);
        return `
            <button onclick="openBankInterface('${conexao.alvo_ip || conexao.ip}', '${encodeURIComponent(alvoDadosString)}')"
                    class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300 hover:scale-105">
                Acessar Banco
            </button>
        `;
    }
    return '';
}

// --- FUNÇÕES AUXILIARES PARA GERENCIAMENTO DE CONEXÕES ---

// Atualiza a interface do status das conexões
function atualizarStatusConexoesUI(totalConexoes, limiteConexoes) {
    const statusElement = document.getElementById('connections-status');
    const btnFecharTodas = document.getElementById('btn-fechar-todas-conexoes');
    const exploitsWarning = document.getElementById('exploits-warning');
    
    if (statusElement) {
        statusElement.textContent = `${totalConexoes}/${limiteConexoes} conexões ativas`;
        
        // Aplicar cor baseada no status
        if (totalConexoes >= limiteConexoes) {
            statusElement.className = 'text-2xl font-bold text-red-400';
        } else if (totalConexoes >= limiteConexoes - 1) {
            statusElement.className = 'text-2xl font-bold text-orange-400';
        } else {
            statusElement.className = 'text-2xl font-bold text-accent-blue';
        }
    }
    
    // Mostrar/ocultar botão "Fechar Todas"
    if (btnFecharTodas) {
        btnFecharTodas.style.display = totalConexoes > 0 ? 'block' : 'none';
    }
    
    // Mostrar/ocultar aviso de limite
    if (exploitsWarning) {
        exploitsWarning.style.display = totalConexoes >= limiteConexoes ? 'block' : 'none';
    }
    
    // Atualizar estado global para controle de exploits
    window.conexoesNoLimite = totalConexoes >= limiteConexoes;
}

// Função para atualizar status das conexões (chamada pelos botões)
async function atualizarStatusConexoes() {
    const btnAtualizar = document.getElementById('btn-atualizar-conexoes');
    
    if (btnAtualizar) {
        btnAtualizar.innerHTML = `
            <div class="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            Atualizando...
        `;
        btnAtualizar.disabled = true;
    }
    
    try {
        loadActiveConnections();
        showNotification('Status das conexões atualizado!', 'success');
    } catch (error) {
        console.error('Erro ao atualizar conexões:', error);
        showNotification('Erro ao atualizar conexões', 'error');
    } finally {
        if (btnAtualizar) {
            btnAtualizar.innerHTML = 'Atualizar';
            btnAtualizar.disabled = false;
        }
    }
}

// Função para fechar todas as conexões
async function fecharTodasConexoes() {
    const btnFecharTodas = document.getElementById('btn-fechar-todas-conexoes');
    
    if (btnFecharTodas) {
        btnFecharTodas.innerHTML = `
            <div class="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            Fechando...
        `;
        btnFecharTodas.disabled = true;
    }
    
    try {
        const response = await fetchAPI('/api/conexoes/fechar-todas', 'POST', { uid: currentPlayer?.uid });
        
        if (response.sucesso) {
            showNotification(response.mensagem, 'success');
            await loadActiveConnections(); // Recarregar lista
        } else {
            showNotification(response.mensagem || 'Erro ao fechar conexões', 'error');
        }
    } catch (error) {
        console.error('Erro ao fechar todas as conexões:', error);
        showNotification('Erro de conexão ao fechar todas as conexões', 'error');
    } finally {
        if (btnFecharTodas) {
            btnFecharTodas.innerHTML = '🔌 Fechar Todas';
            btnFecharTodas.disabled = false;
        }
    }
}

// Função para limpar intervalos de verificação
function limparIntervalosConexao() {
    if (window.terminalIntervalId) {
        clearInterval(window.terminalIntervalId);
        window.terminalIntervalId = null;
    }
}

// Função para fechar conexão específica
async function fecharConexaoEspecifica(alvoIp) {
    try {
        const response = await fetchAPI('/api/conexoes/fechar', 'POST', { 
            uid: currentPlayer?.uid, 
            alvo_ip: alvoIp 
        });
        
        if (response.sucesso) {
            showNotification(response.mensagem, 'success');
            await loadActiveConnections(); // Recarregar lista
        } else {
            showNotification(response.mensagem || 'Erro ao fechar conexão', 'error');
        }
    } catch (error) {
        console.error('Erro ao fechar conexão:', error);
        showNotification('Erro de conexão ao fechar a conexão', 'error');
    }
}

// --- FUNÇÃO PARA CARREGAR HISTÓRICO DE BRUTEFORCE ---
async function loadBruteforceHistory() {
    const container = document.getElementById('bruteforce-history');
    if (!container) return;
    
    container.innerHTML = `
        <div class="text-center py-4">
            <div class="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-accent-blue"></div>
            <p class="text-secondary-text mt-2">Carregando histórico...</p>
        </div>
    `;
    
    try {
        const response = await fetchAPI('/api/terminal/history');
        
        if (response.sucesso && response.historico) {
            if (response.historico.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-8">
                        <p class="text-secondary-text">Nenhum ataque realizado ainda</p>
                        <p class="text-secondary-text text-sm">Execute ataques BruteForce para ver o histórico aqui</p>
                    </div>
                `;
                return;
            }
            
            // Renderiza o histórico
            container.innerHTML = response.historico.map(ataque => `
                <div class="bg-surface-default border border-border-color rounded-lg p-3">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center gap-3">
                            <div class="w-2 h-2 bg-${ataque.sucesso ? 'green' : 'red'}-400 rounded-full"></div>
                            <div>
                                <span class="text-primary-text font-medium">${ataque.alvo_nick}</span>
                                <span class="text-secondary-text text-sm ml-2">${ataque.alvo_ip || 'N/A'}</span>
                            </div>
                        </div>
                        <div class="text-right">
                            <div class="text-sm ${ataque.sucesso ? 'text-green-400' : 'text-red-400'}">
                                ${ataque.sucesso ? `+$${ataque.valor_roubado}` : 'Falhou'}
                            </div>
                            <div class="text-xs text-secondary-text">
                                ${new Date(ataque.executado_em).toLocaleString()}
                            </div>
                        </div>
                    </div>
                </div>
            `).join('');
        } else {
            container.innerHTML = `
                <div class="text-center py-8 text-red-400">
                    <p>Erro ao carregar histórico: ${response.mensagem || 'Erro desconhecido'}</p>
                </div>
            `;
        }
    } catch (error) {
        console.error('Erro ao carregar histórico:', error);
        container.innerHTML = `
            <div class="text-center py-8 text-red-400">
                <p>Erro de conexão: ${error.message}</p>
            </div>
        `;
    }
}

// --- FUNÇÃO PARA INICIAR BRUTEFORCE ---
async function iniciarBruteforce(conexaoId) {
    if (!conexaoId) {
        showNotification('Erro: ID da conexão inválido', 'error');
        return;
    }
    
    try {
        // Desabilita o botão temporariamente
        const button = document.querySelector(`[onclick="iniciarBruteforce('${conexaoId}')"]`);
        if (button) {
            button.disabled = true;
            button.innerHTML = 'Iniciando...';
            button.classList.add('opacity-75');
        }
        
        const response = await fetchAPI('/api/terminal/bruteforce', 'POST', {
            alvo_ip: conexaoId
        });
        
        if (response.sucesso && response.em_andamento) {
            showNotification(`${response.mensagem}`, 'success');
            
            // Iniciar timer em tempo real
            startBruteforceTimer(conexaoId, response.tempo_execucao);
            
            // Recarregar conexões para mostrar status atualizado
            await loadActiveConnections();
        } else {
            showNotification(`${response.mensagem}`, 'error');
            // Reabilitar botão em caso de erro
            if (button) {
                button.disabled = false;
                button.innerHTML = 'Iniciar BruteForce';
                button.classList.remove('opacity-75');
            }
        }
    } catch (error) {
        console.error('Erro ao executar bruteforce:', error);
        showNotification('Erro ao executar ataque BruteForce', 'error');
        
        // Reabilitar botão em caso de erro
        const button = document.querySelector(`[onclick="iniciarBruteforce('${conexaoId}')"]`);
        if (button) {
            button.disabled = false;
            button.innerHTML = 'Iniciar BruteForce';
            button.classList.remove('opacity-75');
        }
    }
}


// --- FUNÇÃO PARA INICIAR TIMER DE BRUTEFORCE EM TEMPO REAL ---
function startBruteforceTimer(alvoIp, tempoTotal) {
    // Limpar timer existente se houver
    if (activeTimers.has(alvoIp)) {
        clearInterval(activeTimers.get(alvoIp));
    }
    
    const startTime = Date.now();
    const endTime = startTime + (tempoTotal * 1000);
    
    const timerId = setInterval(async () => {
        const now = Date.now();
        const tempoRestante = Math.max(0, Math.ceil((endTime - now) / 1000));
        
        // Atualizar interface
        updateBruteforceProgress(alvoIp, tempoRestante, tempoTotal);
        
        if (tempoRestante <= 0) {
            // Timer finalizado - verificar resultado
            clearInterval(timerId);
            activeTimers.delete(alvoIp);
            await checkBruteforceCompletion(alvoIp);
        }
    }, 1000);
    
    activeTimers.set(alvoIp, timerId);
}

// --- FUNÇÃO PARA ATUALIZAR PROGRESSO DO BRUTEFORCE ---
function updateBruteforceProgress(alvoIp, tempoRestante, tempoTotal) {
    const container = document.getElementById('active-connections');
    if (!container) return;
    
    const progressContainer = container.querySelector(`[data-bruteforce-ip="${alvoIp}"]`);
    if (progressContainer) {
        const minutos = Math.floor(tempoRestante / 60);
        const segundos = tempoRestante % 60;
        const porcentagem = Math.max(0, ((tempoTotal - tempoRestante) / tempoTotal) * 100);
        
        progressContainer.innerHTML = `
            <div class="bg-red-500/10 border border-red-500/30 rounded-lg p-4">
                <div class="flex items-center justify-between mb-3">
                    <div class="flex items-center gap-3">
                        <div class="w-3 h-3 bg-red-400 rounded-full animate-pulse"></div>
                        <div>
                            <h4 class="font-semibold text-red-400">BruteForce em Execução</h4>
                            <p class="text-xs text-secondary-text">IP: ${alvoIp}</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <div class="text-lg font-bold text-red-400">${minutos}:${segundos.toString().padStart(2, '0')}</div>
                        <div class="text-xs text-secondary-text">Tempo restante</div>
                    </div>
                </div>
                
                <div class="w-full bg-gray-600 rounded-full h-2 mb-2">
                    <div class="bg-red-400 h-2 rounded-full transition-all duration-1000" style="width: ${porcentagem}%"></div>
                </div>
                
                <div class="text-xs text-center text-red-400">
                    Quebrando sistema bancário... ${porcentagem.toFixed(1)}%
                </div>
            </div>
        `;
    }
}

// --- FUNÇÃO PARA ATUALIZAR CONEXÃO COM BOTÃO DO BANCO ---
async function updateConnectionWithBankButton(alvoIp, alvoDados) {
    const container = document.getElementById('active-connections');
    if (!container) return;

    const connectionCard = container.querySelector(`[data-connection-ip="${alvoIp}"]`);
    if (!connectionCard) return;

    const actionsContainer = connectionCard.querySelector(`[data-actions-container="${alvoIp}"]`);
    if (!actionsContainer) return;

    // Usar diretamente o alvoDados recebido do bruteforce
    const alvoDadosObj = parseAlvoDados(alvoDados);
    const alvoDadosString = JSON.stringify(alvoDadosObj);

    // Armazenar dados do alvo para uso em transferências e interface
    if (!window.selectedTransferData) window.selectedTransferData = {};
    window.selectedTransferData[alvoIp] = { alvoDados: alvoDadosObj };

    // Remover botão de bruteforce e adicionar botão do banco
    actionsContainer.innerHTML = `
        <div class="bg-green-500/20 border border-green-500 rounded-lg p-3">
            <div class="flex items-center gap-2 mb-2">
                <div class="w-2 h-2 bg-green-400 rounded-full"></div>
                <span class="text-green-400 text-sm font-medium">BruteForce concluído com sucesso!</span>
            </div>
            <div class="text-xs text-secondary-text mb-3">
                Sistema bancário comprometido. Acesso liberado.
            </div>
        </div>
        <button onclick="openBankInterface('${alvoIp}', '${encodeURIComponent(alvoDadosString)}')" 
                class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300 hover:scale-105">
            Acessar Banco
        </button>
    `;
}

// --- FUNÇÃO PARA VERIFICAR CONCLUSÃO DO BRUTEFORCE ---
async function checkBruteforceCompletion(alvoIp) {
    try {
        const response = await fetchAPI(`/api/terminal/check/${alvoIp}`);
        
        if (response.sucesso && response.finalizado) {
            // Limpar timer ativo
            if (activeTimers.has(alvoIp)) {
                clearInterval(activeTimers.get(alvoIp));
                activeTimers.delete(alvoIp);
            }
            
            // Remover progresso visual
            removeBruteforceProgress(alvoIp);
            
            if (response.ataque_sucesso) {
                showNotification(`${response.mensagem}`, 'success');
                // Mostrar botão do banco imediatamente se os dados estão disponíveis
                console.log('[DEBUG] Dados recebidos do backend após bruteforce:', response.alvo_dados);
                if (response.alvo_dados) {
                    await updateConnectionWithBankButton(alvoIp, response.alvo_dados);
                } else {
                    console.warn('[DEBUG] Nenhum dado de alvo retornado pelo backend após bruteforce!');
                }
                // Não recarregar conexões aqui para não apagar o botão do banco
                // Atualizar dados do jogador se retornados
                if (response.player_data) {
                    currentPlayer = { ...currentPlayer, ...response.player_data };
                }
            } else {
                showNotification(`${response.mensagem}`, 'error');
                // Recarregar conexões para mostrar status atualizado
                await loadActiveConnections();
            }
        } else if (response.sucesso && !response.finalizado) {
            // Ainda em andamento - continuar verificando
            const tempoRestante = response.tempo_restante || 0;
            if (tempoRestante > 0) {
                // Atualizar progresso se necessário
                updateBruteforceProgress(alvoIp, tempoRestante, response.tempo_total || 120);
                
                // Verificar novamente em alguns segundos
                setTimeout(() => checkBruteforceCompletion(alvoIp), 3000);
            }
        } else {
            console.error('Resposta inesperada do bruteforce:', response);
            showNotification(`${response.mensagem || 'Erro ao verificar bruteforce'}`, 'error');
            
            // Limpar timer em caso de erro
            if (activeTimers.has(alvoIp)) {
                clearInterval(activeTimers.get(alvoIp));
                activeTimers.delete(alvoIp);
            }
            
            // Remover progresso visual
            removeBruteforceProgress(alvoIp);
            
            // Recarregar conexões
            await loadActiveConnections();
        }
        
    } catch (error) {
        console.error('Erro ao verificar conclusão do bruteforce:', error);
        showNotification('Erro ao verificar resultado do bruteforce', 'error');
        
        // Limpar timer em caso de erro
        if (activeTimers.has(alvoIp)) {
            clearInterval(activeTimers.get(alvoIp));
            activeTimers.delete(alvoIp);
        }
        
        // Remover progresso visual
        removeBruteforceProgress(alvoIp);
        
        // Em caso de erro, recarregar conexões
        await loadActiveConnections();
        
        return true; // Parar verificação em caso de erro
    }
}

// --- FUNÇÃO AUXILIAR PARA REMOVER PROGRESSO DO BRUTEFORCE ---
function removeBruteforceProgress(alvoIp) {
    const container = document.getElementById('active-connections');
    if (container) {
        const progressContainer = container.querySelector(`[data-bruteforce-ip="${alvoIp}"]`);
        if (progressContainer) {
            progressContainer.innerHTML = '';
        }
    }
}

// --- FUNÇÃO AUXILIAR PARA OBTER CONTAINER DE PROGRESSO ---
function getBruteforceContainer(alvoIp) {
    const container = document.getElementById('active-connections');
    if (container) {
        return container.querySelector(`[data-bruteforce-ip="${alvoIp}"]`);
    }
    return null;
}

// --- FUNÇÃO PARA ABRIR INTERFACE BANCÁRIA COMPLETA ---
function openBankInterface(alvoIp, alvoDadosEncoded) {
    let alvoDados;
    
    try {
        // Tentar decodificar e fazer parse dos dados
        const decoded = decodeURIComponent(alvoDadosEncoded);
        alvoDados = JSON.parse(decoded);
        
        // Validar se os dados essenciais existem
        if (!alvoDados || typeof alvoDados !== 'object') {
            throw new Error('Dados do alvo inválidos');
        }
        
        // Garantir valores padrão
        alvoDados.nick = alvoDados.nick || 'Alvo Desconhecido';
        alvoDados.dinheiro = alvoDados.dinheiro || 0;
        
    } catch (error) {
        console.error('Erro ao processar dados do alvo:', error);
        console.error('Dados recebidos:', alvoDadosEncoded);
        showNotification('Erro ao abrir interface bancária: dados corrompidos', 'error');
        return;
    }
    
    // Criar modal para interface bancária
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4';
    modal.innerHTML = `
        <div class="bg-surface-card border border-border-color rounded-xl max-w-md w-full p-6 relative">
            <button onclick="this.closest('.fixed').remove()" 
                    class="absolute top-4 right-4 text-secondary-text hover:text-primary-text">
                <svg class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
            </button>
            
            <div class="text-center mb-6">
                <div class="w-16 h-16 bg-green-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 21v-8.25M15.75 21v-8.25M8.25 21v-8.25M3 9l9-6 9 6m-1.5 12V10.332A48.36 48.36 0 0 0 12 9.75c-2.551 0-5.056.2-7.5.582V21M3 21h18M12 6.75h.008v.008H12V6.75Z" />
                    </svg>
                </div>
                <h3 class="text-xl font-bold text-green-400 mb-2">Sistema Bancário</h3>
                <p class="text-secondary-text">Conta de <strong>${alvoDados?.nick || 'Alvo'}</strong></p>
                <p class="text-green-400 font-bold text-2xl mt-2">$${(alvoDados?.dinheiro || 0).toLocaleString()}</p>
            </div>
            
            <div class="mb-6">
                <label class="block text-sm font-medium text-primary-text mb-3">Selecionar Porcentagem:</label>
                <div class="space-y-3">
                    <input type="range" id="percentage-slider-${alvoIp}" 
                           min="20" max="80" step="20" value="20"
                           class="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider"
                           oninput="updateTransferPreview('${alvoIp}', this.value, ${alvoDados?.dinheiro || 0})">
                    <div class="flex justify-between text-xs text-secondary-text">
                        <span>20%</span>
                        <span>40%</span>
                        <span>60%</span>
                        <span>80%</span>
                    </div>
                </div>
            </div>
            
            <div id="transfer-preview-${alvoIp}" class="bg-surface-default border border-border-color rounded-lg p-4 mb-4">
                <div class="text-center">
                    <p class="text-secondary-text text-sm mb-1">Valor a transferir:</p>
                    <p id="transfer-amount-${alvoIp}" class="text-green-400 font-bold text-xl">$${Math.floor((alvoDados?.dinheiro || 0) * 0.2).toLocaleString()}</p>
                    <p id="transfer-percentage-${alvoIp}" class="text-secondary-text text-sm">(20%)</p>
                </div>
            </div>
            
            <button onclick="executeTransferFromModal('${alvoIp}', '${encodeURIComponent(JSON.stringify(alvoDados))}')" 
                    class="w-full bg-green-600 hover:bg-green-700 text-white py-3 px-4 rounded-lg font-bold transition-all duration-300 hover:scale-105">
                Confirmar Transferência
            </button>
        </div>
    `;
    
    document.body.appendChild(modal);
    
    // Fechar modal ao clicar fora
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            modal.remove();
        }
    });
}

// --- FUNÇÃO PARA ATUALIZAR PREVIEW DA TRANSFERÊNCIA ---
function updateTransferPreview(alvoIp, percentage, saldoAlvo) {
    const valorTransferencia = Math.floor((saldoAlvo * percentage) / 100);
    
    const amountElement = document.getElementById(`transfer-amount-${alvoIp}`);
    const percentageElement = document.getElementById(`transfer-percentage-${alvoIp}`);
    
    if (amountElement) {
        amountElement.textContent = `$${valorTransferencia.toLocaleString()}`;
    }
    
    if (percentageElement) {
        percentageElement.textContent = `(${percentage}%)`;
    }
}

// --- FUNÇÃO PARA EXECUTAR TRANSFERÊNCIA DO MODAL ---
async function executeTransferFromModal(alvoIp, alvoDadosEncoded) {
    let alvoDados;
    
    try {
        const decoded = decodeURIComponent(alvoDadosEncoded);
        alvoDados = JSON.parse(decoded);
        
        if (!alvoDados || !alvoDados.uid) {
            throw new Error('UID do alvo não encontrado');
        }
        
        // Debug: verificar dados do alvo
        console.log('[DEBUG] Dados do alvo para transferência:', alvoDados);
        console.log('[DEBUG] UID do alvo:', alvoDados.uid);
        console.log('[DEBUG] UID do jogador atual:', currentPlayer?.uid);
        
        // Verificar se não está tentando transferir para si mesmo
        if (alvoDados.uid === currentPlayer?.uid) {
            throw new Error('Não é possível transferir para si mesmo. Os dados do alvo podem estar incorretos.');
        }
        
    } catch (error) {
        console.error('Erro ao processar dados do alvo para transferência:', error);
        showNotification('Erro: ' + error.message, 'error');
        return;
    }
    
    const slider = document.getElementById(`percentage-slider-${alvoIp}`);
    
    if (!slider) {
        showNotification('Erro: Porcentagem não selecionada', 'error');
        return;
    }
    
    const porcentagem = parseInt(slider.value);
    const modal = document.querySelector('.fixed.inset-0');
    
    // Mostrar loading
    const button = modal.querySelector('button[onclick*="executeTransferFromModal"]');
    if (button) {
        button.disabled = true;
        button.innerHTML = 'Transferindo...';
    }
    
    try {
        console.log('[DEBUG] Enviando requisição de transferência:', {
            alvo_uid: alvoDados.uid,
            porcentagem: porcentagem
        });
        
        const response = await fetchAPI('/api/alvo/transferir', 'POST', {
            alvo_uid: alvoDados.uid,
            porcentagem: porcentagem
        });
        
        if (response.sucesso) {
            showNotification(`${response.mensagem}`, 'success');
            
            // Fechar modal
            modal.remove();
            
            // Atualizar dados do jogador
            if (response.novo_saldo !== undefined) {
                currentPlayer.dinheiro = response.novo_saldo;
            }
            
            // Recarregar conexões para atualizar interface
            await loadActiveConnections();
        } else {
            showNotification(`${response.mensagem}`, 'error');
            
            if (button) {
                button.disabled = false;
                button.innerHTML = 'Confirmar Transferência';
            }
        }
    } catch (error) {
        console.error('Erro na transferência:', error);
        showNotification('Erro ao executar transferência', 'error');
        
        if (button) {
            button.disabled = false;
            button.innerHTML = 'Confirmar Transferência';
        }
    }
}

// --- FUNÇÃO PARA MOSTRAR ACESSO BANCÁRIO NO TERMINAL (LEGADO) ---
function showBankAccessInTerminal(alvoIp, alvoDados) {
    const container = document.getElementById('active-connections');
    if (!container) return;
    
    const bankInterface = `
        <div class="bg-green-500/10 border border-green-500/30 rounded-lg p-6 mb-4">
            <div class="text-center mb-4">
                <div class="flex items-center justify-center mb-3">
                    <div class="w-12 h-12 bg-green-500/20 rounded-full flex items-center justify-center">
                        <svg class="w-6 h-6 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 21v-8.25M15.75 21v-8.25M8.25 21v-8.25M3 9l9-6 9 6m-1.5 12V10.332A48.36 48.36 0 0 0 12 9.75c-2.551 0-5.056.2-7.5.582V21M3 21h18M12 6.75h.008v.008H12V6.75Z" />
                        </svg>
                    </div>
                </div>
                <h3 class="text-lg font-bold text-green-400 mb-2">Acesso Bancário Liberado</h3>
                <p class="text-secondary-text">Sistema bancário de <strong>${alvoDados?.nick || 'Alvo'}</strong> comprometido</p>
                <p class="text-green-400 font-bold text-xl mt-2">Saldo: $${(alvoDados?.dinheiro || 0).toLocaleString()}</p>
            </div>
            
            <div class="grid grid-cols-4 gap-3 mb-4">
                <button onclick="selectTransferPercentage('${alvoIp}', 10)" class="transfer-btn bg-blue-600 hover:bg-blue-700 text-white py-2 px-3 rounded text-sm font-medium transition-colors">
                    10%
                </button>
                <button onclick="selectTransferPercentage('${alvoIp}', 25)" class="transfer-btn bg-blue-600 hover:bg-blue-700 text-white py-2 px-3 rounded text-sm font-medium transition-colors">
                    25%
                </button>
                <button onclick="selectTransferPercentage('${alvoIp}', 50)" class="transfer-btn bg-blue-600 hover:bg-blue-700 text-white py-2 px-3 rounded text-sm font-medium transition-colors">
                    50%
                </button>
                <button onclick="selectTransferPercentage('${alvoIp}', 100)" class="transfer-btn bg-blue-600 hover:bg-blue-700 text-white py-2 px-3 rounded text-sm font-medium transition-colors">
                    100%
                </button>
            </div>
            
            <div id="transfer-preview-${alvoIp}" class="bg-surface-default border border-border-color rounded-lg p-3 mb-4 hidden">
                <div class="text-center">
                    <p class="text-secondary-text text-sm">Transferir:</p>
                    <p id="transfer-amount-${alvoIp}" class="text-green-400 font-bold text-lg"></p>
                </div>
            </div>
            
            <button id="confirm-transfer-${alvoIp}" onclick="executeTransfer('${alvoIp}')" disabled
                    class="w-full bg-green-600 hover:bg-green-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white py-3 px-4 rounded-lg font-bold transition-colors">
                Confirmar Transferência
            </button>
        </div>
    `;
    
    // Inserir no início do container
    container.insertAdjacentHTML('afterbegin', bankInterface);
}

// Variáveis globais para transferência
let selectedTransferData = {};

// --- FUNÇÃO PARA SELECIONAR PORCENTAGEM DE TRANSFERÊNCIA ---
function selectTransferPercentage(alvoIp, porcentagem) {
    // Buscar dados do alvo
    const alvoDados = getCurrentAlvoData(alvoIp);
    if (!alvoDados) return;
    
    const saldoAlvo = alvoDados.dinheiro || 0;
    const valorTransferencia = Math.floor((saldoAlvo * porcentagem) / 100);
    
    // Atualizar seleção visual
    document.querySelectorAll('.transfer-btn').forEach(btn => {
        btn.classList.remove('bg-green-600', 'bg-green-700');
        btn.classList.add('bg-blue-600', 'hover:bg-blue-700');
    });
    
    event.target.classList.remove('bg-blue-600', 'hover:bg-blue-700');
    event.target.classList.add('bg-green-600', 'hover:bg-green-700');
    
    // Mostrar preview
    const preview = document.getElementById(`transfer-preview-${alvoIp}`);
    const amount = document.getElementById(`transfer-amount-${alvoIp}`);
    const confirmBtn = document.getElementById(`confirm-transfer-${alvoIp}`);
    
    if (preview && amount && confirmBtn) {
        preview.classList.remove('hidden');
        amount.textContent = `$${valorTransferencia.toLocaleString()} (${porcentagem}%)`;
        confirmBtn.disabled = false;
        
        // Armazenar dados da transferência
        selectedTransferData[alvoIp] = {
            porcentagem,
            valor: valorTransferencia,
            alvoDados
        };
    }
}

// --- FUNÇÃO PARA EXECUTAR TRANSFERÊNCIA ---
async function executeTransfer(alvoIp) {
    const transferData = selectedTransferData[alvoIp];
    if (!transferData) {
        showNotification('Erro: Nenhuma transferência selecionada', 'error');
        return;
    }
    
    const confirmBtn = document.getElementById(`confirm-transfer-${alvoIp}`);
    if (confirmBtn) {
        confirmBtn.disabled = true;
        confirmBtn.innerHTML = 'Transferindo...';
    }
    
    try {
        const response = await fetchAPI('/api/alvo/transferir', 'POST', {
            alvo_uid: transferData.alvoDados.uid,
            porcentagem: transferData.porcentagem
        });
        
        if (response.sucesso) {
            showNotification(`${response.mensagem}`, 'success');
            
            // Remover interface bancária
            const bankInterface = document.querySelector(`[id*="transfer-preview-${alvoIp}"]`)?.closest('.bg-green-500\\/10');
            if (bankInterface) {
                bankInterface.remove();
            }
            
            // Recarregar dados
            await loadGameData(true);
            await loadActiveConnections();
        } else {
            showNotification(`${response.mensagem}`, 'error');
            
            if (confirmBtn) {
                confirmBtn.disabled = false;
                confirmBtn.innerHTML = 'Confirmar Transferência';
            }
        }
    } catch (error) {
        console.error('Erro na transferência:', error);
        showNotification('Erro ao executar transferência', 'error');
        
        if (confirmBtn) {
            confirmBtn.disabled = false;
            confirmBtn.innerHTML = 'Confirmar Transferência';
        }
    }
}

// --- FUNÇÃO AUXILIAR PARA OBTER DADOS DO ALVO ---
function getCurrentAlvoData(alvoIp) {
    // Esta função deve retornar os dados atuais do alvo
    // Por enquanto, vamos usar uma implementação simples
    return selectedTransferData[alvoIp]?.alvoDados || null;
}

// --- FUNÇÃO PARA ATUALIZAR BOTÃO DO BANCO APÓS BRUTEFORCE ---
function updateBankButtonAfterBruteforce() {
    const bankBtn = document.getElementById('access-bank-btn');
    if (bankBtn) {
        bankBtn.innerHTML = `
            <div class="absolute inset-0 bg-gradient-to-br from-green-500/10 to-transparent"></div>
            <div class="relative p-8 text-center">
                <div class="flex items-center justify-center mb-4">
                    <div class="flex items-center justify-center w-12 h-12 rounded-full bg-green-500/20 border border-green-500/30 transition-colors duration-300">
                        <svg class="w-6 h-6 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 21v-8.25M15.75 21v-8.25M8.25 21v-8.25M3 9l9-6 9 6m-1.5 12V10.332A48.36 48.36 0 0 0 12 9.75c-2.551 0-5.056.2-7.5.582V21M3 21h18M12 6.75h.008v.008H12V6.75Z" />
                        </svg>
                    </div>
                </div>
                <h3 class="text-lg font-semibold text-green-400 mb-2">Banco Desbloqueado</h3>
                <p class="text-sm text-secondary-text">Transferir fundos do alvo</p>
                <div class="mt-3 text-xs text-green-400">
                    Acesso Liberado
                </div>
            </div>
        `;
        
        // Atualizar classes CSS
        bankBtn.className = 'group relative overflow-hidden rounded-xl bg-surface-card border border-green-500/50 hover:border-green-500 transition-all duration-300 hover:transform hover:scale-[1.02]';
        
        // Adicionar animação de sucesso
        bankBtn.classList.add('animate-pulse');
        setTimeout(() => {
            bankBtn.classList.remove('animate-pulse');
        }, 2000);
    }
}


function startConnectionMonitoring() {
    // Para evitar múltiplos intervals
    if (connectionMonitorInterval) {
        clearInterval(connectionMonitorInterval);
    }
    
    // Verifica conexões a cada 30 segundos
    connectionMonitorInterval = setInterval(async () => {
        try {
            const response = await fetchAPI('/api/terminal/connections');
            
            if (response.sucesso) {
                // Se não há conexões ativas, limpa a interface do banco
                if (!response.conexoes || response.conexoes.length === 0) {
                    clearBankInterface();
                } else {
                    // Verifica se alguma conexão foi removida
                    const currentConnections = response.conexoes.map(c => c.ip);
                    const displayedConnections = Array.from(
                        document.querySelectorAll('[data-connection-ip]')
                    ).map(el => el.getAttribute('data-connection-ip'));
                    
                    // Se uma conexão foi removida, recarrega a lista
                    const removedConnections = displayedConnections.filter(
                        ip => !currentConnections.includes(ip)
                    );
                    
                    if (removedConnections.length > 0) {
                        console.log('Conexões expiradas detectadas:', removedConnections);
                        loadActiveConnections(); // Recarrega a lista
                        clearBankInterface(); // Limpa interface do banco
                    }
                }
            }
        } catch (error) {
            console.error('Erro no monitoramento de conexões:', error);
        }
    }, 30000); // 30 segundos
}

function stopConnectionMonitoring() {
    if (connectionMonitorInterval) {
        clearInterval(connectionMonitorInterval);
        connectionMonitorInterval = null;
    }
}

function clearBankInterface() {
    // Limpa qualquer modal de banco aberto
    const bankModal = document.getElementById('bank-modal');
    if (bankModal && bankModal.style.display !== 'none') {
        bankModal.style.display = 'none';
        document.body.style.overflow = 'auto';
    }
    
    // Remove indicadores de banco desbloqueado
    const bankButtons = document.querySelectorAll('[data-bank-status="unlocked"]');
    bankButtons.forEach(btn => {
        btn.removeAttribute('data-bank-status');
        // Restaura estado original do botão
        btn.innerHTML = `
            <div class="relative h-32 bg-gradient-to-br from-blue-600/20 to-purple-600/20 rounded-lg p-4 flex flex-col justify-center items-center border border-blue-500/30">
                <div class="text-center">
                    <div class="w-8 h-8 mx-auto mb-2 text-blue-400">
                        <svg fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 21v-8.25M15.75 21v-8.25M8.25 21v-8.25M3 9l9-6 9 6m-1.5 12V10.332A48.36 48.36 0 0 0 12 9.75c-2.551 0-5.056.2-7.5.582V21M3 21h18M12 6.75h.008v.008H12V6.75Z" />
                        </svg>
                    </div>
                </div>
                <h3 class="text-lg font-semibold text-blue-400 mb-2">Banco Bloqueado</h3>
                <p class="text-sm text-secondary-text">Execute bruteforce para acessar</p>
                <div class="mt-3 text-xs text-blue-400/60">
                    Requer Autenticação
                </div>
            </div>
        `;
        btn.className = 'group relative overflow-hidden rounded-xl bg-surface-card border border-blue-500/50 hover:border-blue-500 transition-all duration-300 hover:transform hover:scale-[1.02]';
    });
}

// --- FUNÇÃO PARA COLETAR MINERAÇÃO OFFLINE ---
async function coletarMineracaoOffline() {
    try {
        const response = await fetchAPI('/api/mineracao/coletar-offline', 'POST');

        if (response.sucesso && response.dinheiro_coletado > 0) {
            // Mostrar notificação de dinheiro coletado
            showNotification(
                `Mineração Offline: +$${response.dinheiro_coletado} coletados! (${response.horas_offline}h offline)`,
                'success',
                5000
            );

            // Atualizar saldo do jogador na interface
            if (currentPlayer) {
                currentPlayer.dinheiro = response.novo_saldo;
                updatePlayerUI();
            }

            console.log(`[MINERACAO] Coletados $${response.dinheiro_coletado} da mineração offline`);
        }
    } catch (error) {
        console.error('Erro ao coletar mineração offline:', error);
    }
}

// --- NOVA APP STORE ROBUSTA ---

// Função para carregar cards da nova App Store
function loadNewAppStoreCards(grid, appStoreResponse) {
    const appInfo = {
        'cpu': { nome: 'CPU', icone: '', descricao: 'Poder de processamento para exploits', cor: 'blue' },
        'firewall': { nome: 'Firewall', icone: '', descricao: 'Proteção contra ataques externos', cor: 'green' },
        'antivirus': { nome: 'Antivírus', icone: '', descricao: 'Detecção e remoção de malware', cor: 'purple' },
        'malware_kit': { nome: 'Malware Kit', icone: '', descricao: 'Ferramentas avançadas de ataque', cor: 'red' },
        'bruteforce': { nome: 'Bruteforce', icone: '', descricao: 'Quebra de senhas e criptografia', cor: 'orange' },
        'bankguard': { nome: 'BankGuard', icone: '', descricao: 'Proteção de sistemas bancários', cor: 'yellow' },
        'proxyvpn': { nome: 'Proxy/VPN', icone: '', descricao: 'Anonimato e mascaramento de IP', cor: 'cyan' }
    };

    const custos = appStoreResponse.custos;
    const player = currentPlayer;

    if (!player) {
        grid.innerHTML = '<div class="col-span-full text-center text-red-400">Erro ao carregar dados do jogador</div>';
        return;
    }

    // Header da seção com recursos do jogador
    grid.innerHTML = `
        <!-- Recursos do Jogador (Simplificado) -->
        <div class="col-span-full bg-surface-elevated border border-border-color rounded-lg p-4 mb-4">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold text-primary-text">Recursos</h3>
                <div class="flex items-center gap-6">
                    <div class="text-center">
                        <div class="text-xl font-bold text-green-400">$${(player.dinheiro || 0).toLocaleString()}</div>
                        <div class="text-xs text-secondary-text">Dinheiro</div>
                    </div>
                    <div class="text-center">
                        <div class="text-xl font-bold text-blue-400">${(player.shack || 0).toLocaleString()}</div>
                        <div class="text-xs text-secondary-text">Shacks</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Header da seção (Compacto) -->
        <div class="col-span-full bg-surface-elevated border border-border-color rounded-lg p-4 mb-4">
            <div class="flex items-center justify-between">
                <h2 class="text-xl font-bold text-primary-text flex items-center gap-2">
                    Apps de Upgrade
                </h2>
                <div class="text-sm text-secondary-text">
                    Clique para ver opções (+1, +5, +10)
                </div>
            </div>
            <div class="flex items-center gap-4 text-xs text-secondary-text mt-2">
                <span>🟢 Níveis 1-9: Só dinheiro</span>
                <span>🔵 Níveis 10+: Dinheiro + Shacks</span>
            </div>
        </div>
    `;

    // Adicionar cards dos apps
    Object.entries(custos).forEach(([item, custo]) => {
        const info = appInfo[item];
        const nivelAtual = player[item] || 1;

        const cardElement = document.createElement('div');
        cardElement.className = `group bg-surface-elevated border border-border-color rounded-lg p-4 hover:border-${info.cor}-500 transition-all duration-300 hover:transform hover:scale-[1.02] cursor-pointer`;
        cardElement.onclick = () => openAppUpgradeModal(item, info.nome, info.icone, nivelAtual);

        cardElement.innerHTML = `
            <div class="text-center mb-3">
                <div class="text-3xl mb-2 group-hover:scale-110 transition-transform duration-300">${info.icone}</div>
                <h3 class="text-lg font-bold text-primary-text group-hover:text-${info.cor}-400 transition-colors">${info.nome}</h3>
                <p class="text-secondary-text text-xs mt-1 line-clamp-2">${info.descricao}</p>
            </div>

            <div class="space-y-2">
                <div class="flex justify-between items-center">
                    <span class="text-secondary-text text-sm">Nível:</span>
                    <span class="text-${info.cor}-400 font-bold">${nivelAtual}</span>
                </div>

                <div class="flex justify-between items-center">
                    <span class="text-secondary-text text-sm">Upgrade:</span>
                    <span class="text-green-400 font-bold text-sm">$${custo.toLocaleString()}</span>
                </div>

                <div class="pt-2 border-t border-border-color">
                    <div class="text-center text-xs text-secondary-text">
                        Clique para upgrades
                    </div>
                </div>
            </div>
        `;

        grid.appendChild(cardElement);
    });
}

// Função para abrir modal de upgrade de app
function openAppUpgradeModal(appId, appName, appIcon, currentLevel) {
    // Calcular custos para diferentes quantidades usando lógica correta
    const costs = {
        1: calculateBulkCost(appId, currentLevel, 1),
        5: calculateBulkCost(appId, currentLevel, 5),
        10: calculateBulkCost(appId, currentLevel, 10)
    };

    const playerMoney = currentPlayer?.dinheiro || 0;
    const playerShacks = currentPlayer?.shack || 0;

    // Adicionar desfoque apenas no conteúdo de fundo (excluindo o modal)
    // Criar overlay de desfoque que fica atrás do modal
    const blurOverlay = document.createElement('div');
    blurOverlay.className = 'fixed inset-0 z-40';
    blurOverlay.style.backdropFilter = 'blur(3px)';
    blurOverlay.style.transition = 'backdrop-filter 0.3s ease';
    blurOverlay.setAttribute('data-blur-overlay', 'true');

    // Inserir o overlay antes do modal
    document.body.insertBefore(blurOverlay, document.body.lastElementChild);

    // Criar modal (z-index maior que o overlay de desfoque)
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-50 p-4';
    modal.setAttribute('data-modal', 'app-upgrade');
    modal.innerHTML = `
        <div class="bg-surface-elevated border-2 border-accent-blue rounded-xl max-w-md w-full p-6 transform transition-all duration-300 scale-95 opacity-0 shadow-2xl" id="upgrade-modal-content">
            <!-- Header -->
            <div class="text-center mb-6">
                <div class="text-6xl mb-3">${appIcon}</div>
                <h2 class="text-2xl font-bold text-primary-text">${appName}</h2>
                <p class="text-secondary-text">Nível atual: <span class="text-accent-blue font-bold">${currentLevel}</span></p>
            </div>

            <!-- Opções de upgrade -->
            <div class="space-y-4 mb-6">
                ${[1, 5, 10].map(qty => {
                    const cost = costs[qty];
                    const costDinheiro = cost.dinheiro;
                    const costShacks = cost.shacks;
                    const newLevel = currentLevel + qty;

                    // Verificar se pode pagar
                    const canAffordMoney = playerMoney >= costDinheiro;
                    const canAffordShacks = playerShacks >= costShacks;
                    const canAfford = canAffordMoney && canAffordShacks;

                    // Determinar mensagem de erro
                    let errorMsg = '';
                    if (!canAffordMoney && !canAffordShacks) {
                        errorMsg = 'Dinheiro e Shacks insuficientes';
                    } else if (!canAffordMoney) {
                        errorMsg = 'Dinheiro insuficiente';
                    } else if (!canAffordShacks) {
                        errorMsg = 'Shacks insuficientes';
                    }

                    return `
                        <button onclick="executeAppUpgrade('${appId}', ${qty})"
                                class="w-full p-4 rounded-lg border transition-all duration-300 ${
                                    canAfford
                                        ? 'border-accent-blue bg-accent-blue/10 hover:bg-accent-blue/20 text-primary-text'
                                        : 'border-gray-600 bg-gray-800/50 text-gray-400 cursor-not-allowed'
                                }"
                                ${!canAfford ? 'disabled' : ''}>
                            <div class="flex justify-between items-center">
                                <div class="text-left">
                                    <div class="font-bold">+${qty} Nível${qty > 1 ? 's' : ''}</div>
                                    <div class="text-sm opacity-75">${currentLevel} → ${newLevel}</div>
                                </div>
                                <div class="text-right">
                                    <div class="space-y-1">
                                        <div class="font-bold ${canAffordMoney ? 'text-green-400' : 'text-red-400'}">
                                            $${costDinheiro.toLocaleString()}
                                        </div>
                                        ${costShacks > 0 ? `
                                            <div class="font-bold ${canAffordShacks ? 'text-blue-400' : 'text-red-400'}">
                                                ${costShacks} Shacks
                                            </div>
                                        ` : ''}
                                    </div>
                                    ${!canAfford ? `<div class="text-xs text-red-400 mt-1">${errorMsg}</div>` : ''}
                                </div>
                            </div>
                        </button>
                    `;
                }).join('')}
            </div>

            <!-- Footer -->
            <div class="flex gap-3">
                <button onclick="closeAppUpgradeModal()"
                        class="flex-1 py-2 px-4 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors">
                    Cancelar
                </button>
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    // Animar entrada
    setTimeout(() => {
        const content = document.getElementById('upgrade-modal-content');
        content.style.transform = 'scale(1)';
        content.style.opacity = '1';
    }, 10);

    // Fechar ao clicar fora
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            closeAppUpgradeModal();
        }
    });

    // Fechar com ESC
    const handleEscape = (e) => {
        if (e.key === 'Escape') {
            closeAppUpgradeModal();
            document.removeEventListener('keydown', handleEscape);
        }
    };
    document.addEventListener('keydown', handleEscape);
}

// Função para fechar modal
function closeAppUpgradeModal() {
    const modal = document.querySelector('[data-modal="app-upgrade"]');
    if (modal) {
        const content = modal.querySelector('#upgrade-modal-content');
        content.style.transform = 'scale(0.95)';
        content.style.opacity = '0';

        // Remover overlay de desfoque
        const blurOverlay = document.querySelector('[data-blur-overlay="true"]');
        if (blurOverlay) {
            blurOverlay.remove();
        }

        setTimeout(() => {
            modal.remove();
        }, 300);
    }
}

// Função para calcular custo em lote (usando lógica correta de dinheiro + shacks)
function calculateBulkCost(item, currentLevel, quantity) {
    // Custos base dos apps
    const custos_base = {
        "cpu": 1,
        "firewall": 1,
        "antivirus": 1,
        "malware_kit": 1,
        "bruteforce": 1,
        "bankguard": 1,
        "proxyvpn": 1
    };

    const custo_base = custos_base[item] || 1;
    let custo_total_dinheiro = 0;
    let custo_total_shacks = 0;

    for (let i = 0; i < quantity; i++) {
        const nivel_upgrade = currentLevel + i + 1; // Nível que será alcançado após o upgrade

        // Custo em dinheiro (sempre cresce com fórmula 1.15^(nivel-1))
        custo_total_dinheiro += Math.floor(custo_base * Math.pow(1.15, (currentLevel + i - 1)));

        // Custo em Shacks a partir do nível 10
        if (nivel_upgrade >= 10) {
            if (nivel_upgrade >= 10 && nivel_upgrade < 20) {
                // Nível 10-19: 2 Shacks por upgrade
                custo_total_shacks += 2;
            } else if (nivel_upgrade >= 20 && nivel_upgrade < 30) {
                // Nível 20-29: 4 Shacks por upgrade
                custo_total_shacks += 4;
            } else if (nivel_upgrade >= 30 && nivel_upgrade < 40) {
                // Nível 30-39: 6 Shacks por upgrade
                custo_total_shacks += 6;
            } else if (nivel_upgrade >= 40 && nivel_upgrade < 50) {
                // Nível 40-49: 8 Shacks por upgrade
                custo_total_shacks += 8;
            } else {
                // Nível 50+: incrementa 2 Shacks a cada 10 níveis
                const shacks_base = 2 + Math.floor((nivel_upgrade - 10) / 10) * 2;
                custo_total_shacks += shacks_base;
            }
        }
    }

    return {
        dinheiro: custo_total_dinheiro,
        shacks: custo_total_shacks
    };
}

// Função para executar upgrade
async function executeAppUpgrade(appId, quantity) {
    try {
        showNotification('Processando upgrade...', 'info');

        const response = await fetchAPI('/api/appstore/comprar', 'POST', {
            item: appId,
            quantidade: quantity
        });

        if (response.sucesso) {
            showNotification(response.mensagem, 'success');
            closeAppUpgradeModal();

            // Atualizar dados do jogador
            await loadGameData();

            // Re-renderizar app store
            renderAppStore();
        } else {
            showNotification(response.mensagem, 'error');
        }
    } catch (error) {
        console.error('Erro ao executar upgrade:', error);
        showNotification('Erro ao executar upgrade', 'error');
    }
}

// --- SISTEMA DE SEGURANÇA ---

// Função para monitorar invasões em tempo real (notificações globais)
async function monitorInvasionsGlobally() {
    try {
        // Sempre forçar refresh para monitoramento em tempo real
        const timestamp = `?_t=${Date.now()}`;
        const response = await fetchAPI(`/api/security/invasions${timestamp}`);

        if (response.sucesso) {
            const currentInvasions = new Set(response.invasoes.map(inv => inv.atacante_uid));

            console.log(`[MONITOR] Verificando invasões: ${response.total_invasoes} ativas`);

            // Detectar novas invasões
            const newInvasions = response.invasoes.filter(inv => !lastKnownInvasions.has(inv.atacante_uid));

            // Detectar invasões que terminaram
            const endedInvasions = [...lastKnownInvasions].filter(uid => !currentInvasions.has(uid));

            // Notificar novas invasões
            newInvasions.forEach(invasao => {
                showInvasionNotification(invasao, 'new');
                console.warn(`[INVASÃO DETECTADA] ${invasao.atacante_nick} (${invasao.nivel_ameaca})`);
            });

            // Notificar invasões que terminaram
            endedInvasions.forEach(atacanteUid => {
                showInvasionNotification({ atacante_uid: atacanteUid }, 'ended');
                console.log(`[INVASÃO ENCERRADA] UID: ${atacanteUid}`);
            });

            // Atualizar badge de segurança sempre
            updateSecurityBadge(response.total_invasoes);

            // Se estamos na seção de segurança, atualizar a interface também
            if (isSecurityMonitoringActive) {
                renderInvasionsList(response);
            }

            // Atualizar conjunto de invasões conhecidas
            lastKnownInvasions = currentInvasions;

            return response;
        } else {
            console.warn('[MONITOR] Falha na resposta da API:', response.mensagem);
        }
    } catch (error) {
        console.error('[MONITOR] Erro ao monitorar invasões:', error);
    }

    return null;
}

// Função para mostrar notificação de invasão
function showInvasionNotification(invasao, type) {
    const isNewInvasion = type === 'new';

    if (isNewInvasion) {
        // Determinar cor e ícone baseado no nível de ameaça
        const ameacaConfig = {
            'CRÍTICO': { color: 'error', icon: '', sound: true },
            'ALTO': { color: 'warning', icon: '', sound: true },
            'MÉDIO': { color: 'info', icon: '', sound: false }
        };

        const config = ameacaConfig[invasao.nivel_ameaca] || ameacaConfig['MÉDIO'];

        // Notificação visual
        const invasorInfo = invasao.is_anonimo ? 'ProxyVPN ativo' : (invasao.atacante_ip || 'IP desconhecido');
        const invasorNick = invasao.is_anonimo ? 'Invasor anônimo' : invasao.atacante_nick;
        const message = `${config.icon} INVASÃO DETECTADA!\n${invasorNick} (${invasorInfo}) está explorando seu sistema\nNível: ${invasao.nivel_ameaca}`;

        showNotification(message, config.color, 8000); // 8 segundos

        // Notificação do navegador (se permitido)
        if ('Notification' in window && Notification.permission === 'granted') {
            new Notification('Shack 3.0 - Invasão Detectada!', {
                body: `${invasorNick} (${invasorInfo}) está explorando seu sistema (${invasao.nivel_ameaca})`,
                icon: '/static/favicon.ico',
                tag: `invasion-${invasao.is_anonimo ? 'anonymous' : (invasao.atacante_ip || invasao.atacante_uid)}`,
                requireInteraction: invasao.nivel_ameaca === 'CRÍTICO'
            });
        }

        // Som de alerta para invasões críticas/altas
        if (config.sound) {
            playAlertSound();
        }

        // Piscar badge de segurança
        flashSecurityBadge();

    } else {
        // Invasão terminou
        showNotification('Invasão encerrada', 'success', 3000);
    }
}

// Função para piscar o badge de segurança
function flashSecurityBadge() {
    const badge = document.getElementById('security-alert-badge');
    if (badge && !badge.classList.contains('hidden')) {
        badge.style.animation = 'pulse 0.5s ease-in-out 3';
        setTimeout(() => {
            badge.style.animation = '';
        }, 1500);
    }
}

// Função para tocar som de alerta
function playAlertSound() {
    try {
        // Criar um som de alerta usando Web Audio API
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();

        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);

        oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
        oscillator.frequency.setValueAtTime(600, audioContext.currentTime + 0.1);
        oscillator.frequency.setValueAtTime(800, audioContext.currentTime + 0.2);

        gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);

        oscillator.start(audioContext.currentTime);
        oscillator.stop(audioContext.currentTime + 0.3);
    } catch (error) {
        console.warn('Não foi possível tocar som de alerta:', error);
    }
}

// Função para solicitar permissão de notificação
function requestNotificationPermission() {
    if ('Notification' in window && Notification.permission === 'default') {
        Notification.requestPermission().then(permission => {
            if (permission === 'granted') {
                showNotification('Notificações de segurança ativadas!', 'success');
            }
        });
    }
}

// Função para carregar dados de segurança
async function loadSecurityData(forceRefresh = false) {
    try {
        // Adicionar timestamp para evitar cache quando necessário
        const timestamp = forceRefresh ? `?_t=${Date.now()}` : '';

        const [statusResponse, invasionsResponse] = await Promise.all([
            fetchAPI(`/api/security/status${timestamp}`),
            fetchAPI(`/api/security/invasions${timestamp}`)
        ]);

        if (statusResponse.sucesso) {
            renderSecurityStatus(statusResponse.status);
            console.log('[SECURITY] Status atualizado:', statusResponse.status);
        }

        if (invasionsResponse.sucesso) {
            renderInvasionsList(invasionsResponse);
            updateSecurityBadge(invasionsResponse.total_invasoes);
            console.log('[SECURITY] Invasões atualizadas:', invasionsResponse.total_invasoes);
        }

        // Atualizar timestamp da última verificação
        lastSecurityCheck = Date.now();
        updateLastUpdateTime();

        return {
            status: statusResponse,
            invasions: invasionsResponse
        };

    } catch (error) {
        console.error('Erro ao carregar dados de segurança:', error);
        return null;
    }
}

// Função para renderizar status de segurança
function renderSecurityStatus(status) {
    const container = document.getElementById('security-status');
    if (!container) return;

    const ameacaColor = {
        'green': 'text-green-400',
        'yellow': 'text-yellow-400',
        'orange': 'text-orange-400',
        'red': 'text-red-400'
    }[status.cor_ameaca] || 'text-gray-400';

    container.innerHTML = `
        <div class="bg-surface-default border border-border-color rounded-lg p-4 text-center">
            <div class="text-2xl font-bold ${ameacaColor} mb-1">${status.nivel_ameaca}</div>
            <div class="text-secondary-text text-sm">Nível de Ameaça</div>
        </div>

        <div class="bg-surface-default border border-border-color rounded-lg p-4 text-center">
            <div class="text-2xl font-bold text-blue-400 mb-1">${status.score_defesa}/100</div>
            <div class="text-secondary-text text-sm">Score de Defesa</div>
        </div>

        <div class="bg-surface-default border border-border-color rounded-lg p-4 text-center">
            <div class="text-2xl font-bold text-purple-400 mb-1">${status.invasoes_detectadas}</div>
            <div class="text-secondary-text text-sm">Invasões Ativas</div>
        </div>
    `;
}

// Função para renderizar lista de invasões
function renderInvasionsList(data) {
    const container = document.getElementById('invasions-list');
    const countElement = document.getElementById('invasions-count');
    const blockAllBtn = document.getElementById('block-all-btn');

    if (!container) return;

    console.log('[RENDER] Renderizando lista de invasões:', data.total_invasoes);

    // Atualizar contador
    if (countElement) {
        countElement.textContent = data.total_invasoes;
    }

    // Habilitar/desabilitar botão de bloquear todas
    if (blockAllBtn) {
        blockAllBtn.disabled = data.total_invasoes === 0;
    }

    if (data.total_invasoes === 0) {
        container.innerHTML = `
            <div class="text-center py-8 text-secondary-text">
                <svg class="w-12 h-12 mx-auto mb-3 text-green-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75L11.25 15 15 9.75m-3-7.036A11.959 11.959 0 013.598 6 11.99 11.99 0 003 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.623 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285z" />
                </svg>
                <p class="text-lg font-medium text-green-400">Sistema Seguro</p>
                <p class="text-sm">Nenhuma invasão detectada</p>
            </div>
        `;
        return;
    }

    container.innerHTML = data.invasoes.map(invasao => {
        const ameacaColor = {
            'CRÍTICO': 'border-red-500 bg-red-500/10',
            'ALTO': 'border-orange-500 bg-orange-500/10',
            'MÉDIO': 'border-yellow-500 bg-yellow-500/10'
        }[invasao.nivel_ameaca] || 'border-gray-500 bg-gray-500/10';

        const tempoRestante = Math.max(0, invasao.tempo_restante);
        const minutos = Math.floor(tempoRestante / 60);
        const segundos = tempoRestante % 60;

        return `
            <div class="border ${ameacaColor} rounded-lg p-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center gap-3">
                        <div class="w-3 h-3 bg-red-400 rounded-full animate-pulse"></div>
                        <div>
                            <h4 class="font-semibold text-primary-text ${invasao.is_anonimo ? 'text-gray-400' : ''}">${invasao.atacante_nick}</h4>
                            <div class="flex items-center gap-2 text-sm text-secondary-text">
                                <span class="${invasao.is_anonimo ? 'text-gray-500' : ''}">IP: ${invasao.atacante_ip || 'Desconhecido'}</span>
                                ${invasao.atacante_ip && !invasao.is_anonimo && invasao.atacante_ip !== 'unknown' ? `
                                    <button onclick="copyToClipboard('${invasao.atacante_ip}', this)"
                                            class="text-xs px-2 py-1 bg-accent-blue hover:bg-blue-600 text-white rounded transition-colors duration-200 flex items-center gap-1"
                                            title="Copiar IP do invasor">
                                        <svg class="w-3 h-3" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" d="M15.666 3.888A2.25 2.25 0 0013.5 2.25h-3c-1.03 0-1.9.693-2.166 1.638m7.332 0c.*************.084.612v0a.75.75 0 01-.75.75H9a.75.75 0 01-.75-.75v0c0-.212.03-.418.084-.612m7.332 0c.646.049 1.288.11 1.927.184 1.1.128 1.907 1.077 1.907 2.185V19.5a2.25 2.25 0 01-2.25 2.25H6.75A2.25 2.25 0 014.5 19.5V6.257c0-1.108.806-2.057 1.907-2.185a48.208 48.208 0 011.927-.184" />
                                        </svg>
                                        Copiar
                                    </button>
                                ` : invasao.is_anonimo ? `
                                    <span class="text-xs px-2 py-1 bg-gray-600 text-gray-300 rounded flex items-center gap-1" title="Invasor usando ProxyVPN">
                                        <svg class="w-3 h-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                                        </svg>
                                        ProxyVPN
                                    </span>
                                ` : ''}
                            </div>
                            <div class="flex items-center gap-2 mt-1">
                                <span class="text-xs px-2 py-1 rounded ${invasao.nivel_ameaca === 'CRÍTICO' ? 'bg-red-500' : invasao.nivel_ameaca === 'ALTO' ? 'bg-orange-500' : 'bg-yellow-500'} text-white">
                                    ${invasao.nivel_ameaca}
                                </span>
                                ${invasao.bruteforce_ativo ? '<span class="text-xs px-2 py-1 rounded bg-purple-500 text-white">BRUTEFORCE ATIVO</span>' : ''}
                                ${invasao.acesso_banco ? '<span class="text-xs px-2 py-1 rounded bg-red-600 text-white">ACESSO BANCÁRIO</span>' : ''}
                            </div>
                        </div>
                    </div>
                    <div class="text-right">
                        <div class="text-sm font-bold text-red-400">${minutos}:${segundos.toString().padStart(2, '0')}</div>
                        <div class="text-xs text-secondary-text mb-2">Tempo restante</div>
                        <button onclick="blockInvasion('${invasao.atacante_uid}')"
                                class="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-xs font-medium transition-colors">
                            Bloquear
                        </button>
                    </div>
                </div>
            </div>
        `;
    }).join('');
}

// Função para atualizar badge de alerta no dashboard
function updateSecurityBadge(invasionsCount) {
    const badge = document.getElementById('security-alert-badge');
    const count = document.getElementById('security-alert-count');

    if (badge && count) {
        if (invasionsCount > 0) {
            badge.classList.remove('hidden');
            count.textContent = invasionsCount;
        } else {
            badge.classList.add('hidden');
        }
    }
}

// Função para bloquear invasão específica
async function blockInvasion(atacanteUid) {
    try {
        showNotification('Bloqueando invasão...', 'info');

        const response = await fetchAPI('/api/security/block-invasion', 'POST', {
            atacante_uid: atacanteUid
        });

        if (response.sucesso) {
            showNotification(response.mensagem, 'success');
            loadSecurityData(); // Recarregar dados
        } else {
            showNotification(response.mensagem, 'error');
        }
    } catch (error) {
        console.error('Erro ao bloquear invasão:', error);
        showNotification('Erro ao bloquear invasão', 'error');
    }
}

// Função para bloquear todas as invasões
async function blockAllInvasions() {
    try {
        showNotification('Bloqueando todas as invasões...', 'info');

        const response = await fetchAPI('/api/security/block-all-invasions', 'POST');

        if (response.sucesso) {
            showNotification(response.mensagem, 'success');
            loadSecurityData(); // Recarregar dados
        } else {
            showNotification(response.mensagem, 'error');
        }
    } catch (error) {
        console.error('Erro ao bloquear invasões:', error);
        showNotification('Erro ao bloquear todas as invasões', 'error');
    }
}

// Função para trocar IP do jogador
async function changePlayerIP() {
    try {
        // Confirmar ação
        const confirmacao = confirm('TROCAR DE IP\n\nIsso irá:\n• Gerar um novo IP aleatório\n• Fechar todas suas conexões ativas\n• Bloquear todas as invasões contra você\n• Custar 100 shacks\n\nDeseja continuar?');

        if (!confirmacao) return;

        showNotification('Trocando IP...', 'info');

        const response = await fetchAPI('/api/security/change-ip', 'POST');

        if (response.sucesso) {
            showNotification(response.mensagem, 'success', 8000);

            // Recarregar dados de segurança
            loadSecurityData();

            // Recarregar dados do jogador para atualizar shacks
            loadGameData();

            // Log da ação
            console.log('[IP CHANGE] IP alterado:', {
                ip_antigo: response.ip_antigo,
                ip_novo: response.ip_novo,
                custo: response.custo,
                shacks_restantes: response.shacks_restantes
            });

        } else {
            showNotification(response.mensagem, 'error', 6000);
        }
    } catch (error) {
        console.error('Erro ao trocar IP:', error);
        showNotification('Erro ao trocar IP', 'error');
    }
}

// Função para iniciar monitoramento de segurança
function startSecurityMonitoring() {
    isSecurityMonitoringActive = true;

    console.log('[SECURITY] Iniciando monitoramento da seção de segurança');

    // Solicitar permissão de notificação na primeira vez
    requestNotificationPermission();

    // Carregar dados iniciais com refresh forçado
    loadSecurityData(true);

    // Atualizar a cada 10 segundos quando na seção de segurança (mais frequente)
    if (typeof securityUpdateInterval !== 'undefined' && securityUpdateInterval) {
        clearInterval(securityUpdateInterval);
    }

    securityUpdateInterval = setInterval(() => {
        console.log('[SECURITY] Atualizando dados da seção...');
        loadSecurityData(true); // Sempre forçar refresh na seção de segurança
    }, 10000); // 10 segundos para atualizações mais rápidas
}

// Função para iniciar monitoramento global de invasões (sempre ativo)
function startGlobalInvasionMonitoring() {
    console.log('[MONITOR] Iniciando monitoramento global de invasões');

    // Monitoramento inicial
    monitorInvasionsGlobally();

    // Monitorar a cada 10 segundos (mais frequente para detecção rápida)
    if (invasionNotificationInterval) {
        clearInterval(invasionNotificationInterval);
    }

    invasionNotificationInterval = setInterval(() => {
        monitorInvasionsGlobally();
    }, 10000); // Reduzido para 10 segundos para detecção mais rápida

    console.log('[MONITOR] Monitoramento global ativo (10s)');
}

// Função para parar monitoramento de segurança
function stopSecurityMonitoring() {
    isSecurityMonitoringActive = false;

    if (typeof securityUpdateInterval !== 'undefined' && securityUpdateInterval) {
        clearInterval(securityUpdateInterval);
        securityUpdateInterval = null;
    }
}

// Função para parar monitoramento global de invasões
function stopGlobalInvasionMonitoring() {
    if (invasionNotificationInterval) {
        clearInterval(invasionNotificationInterval);
        invasionNotificationInterval = null;
    }

    // Limpar invasões conhecidas
    lastKnownInvasions.clear();
}

// Torna as funções globais
window.iniciarBruteforce = iniciarBruteforce;
window.loadActiveConnections = loadActiveConnections;
window.loadBruteforceHistory = loadBruteforceHistory;
window.selectTransferPercentage = selectTransferPercentage;
window.executeTransfer = executeTransfer;
window.showBankButtonInConnection = showBankButtonInConnection;
window.openBankInterface = openBankInterface;
window.startConnectionMonitoring = startConnectionMonitoring;
window.stopConnectionMonitoring = stopConnectionMonitoring;
window.updateTransferPreview = updateTransferPreview;
window.executeTransferFromModal = executeTransferFromModal;
window.coletarMineracaoOffline = coletarMineracaoOffline;
window.openAppUpgradeModal = openAppUpgradeModal;
window.closeAppUpgradeModal = closeAppUpgradeModal;
window.executeAppUpgrade = executeAppUpgrade;
window.loadSecurityData = loadSecurityData;
window.blockInvasion = blockInvasion;
window.blockAllInvasions = blockAllInvasions;
window.changePlayerIP = changePlayerIP;
window.startSecurityMonitoring = startSecurityMonitoring;
window.stopSecurityMonitoring = stopSecurityMonitoring;
window.startGlobalInvasionMonitoring = startGlobalInvasionMonitoring;
window.stopGlobalInvasionMonitoring = stopGlobalInvasionMonitoring;
window.monitorInvasionsGlobally = monitorInvasionsGlobally;
window.requestNotificationPermission = requestNotificationPermission;

// Função para atualizar timestamp da última atualização
function updateLastUpdateTime() {
    const element = document.getElementById('security-last-update');
    if (element) {
        const now = new Date();
        const timeString = now.toLocaleTimeString('pt-BR', {
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
        element.textContent = `Última atualização: ${timeString}`;
    }
}

// Função para atualização manual
async function refreshSecurityData() {
    const button = document.getElementById('refresh-security-btn');
    if (button) {
        // Mostrar loading
        button.innerHTML = `
            <svg class="w-4 h-4 animate-spin" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0l3.181 3.183a8.25 8.25 0 0013.803-3.7M4.031 9.865a8.25 8.25 0 0113.803-3.7l3.181 3.182m0-4.991v4.99" />
            </svg>
            Atualizando...
        `;
        button.disabled = true;
    }

    try {
        console.log('[REFRESH] Atualização manual solicitada');

        // Forçar refresh completo
        await loadSecurityData(true);

        // Também atualizar monitoramento global
        await monitorInvasionsGlobally();

        showNotification('Dados de segurança atualizados!', 'success', 2000);

    } catch (error) {
        console.error('Erro na atualização manual:', error);
        showNotification('Erro ao atualizar dados', 'error', 3000);
    } finally {
        // Restaurar botão
        if (button) {
            setTimeout(() => {
                button.innerHTML = `
                    <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0l3.181 3.183a8.25 8.25 0 0013.803-3.7M4.031 9.865a8.25 8.25 0 0113.803-3.7l3.181 3.182m0-4.991v4.99" />
                    </svg>
                    Atualizar
                `;
                button.disabled = false;
            }, 1000);
        }
    }
}

// Função para verificar se a seção de segurança está ativa
function isSecuritySectionActive() {
    const securitySection = document.getElementById('security-section');
    return securitySection && securitySection.style.display !== 'none';
}

// Torna as funções globais
window.updateLastUpdateTime = updateLastUpdateTime;
window.refreshSecurityData = refreshSecurityData;
window.isSecuritySectionActive = isSecuritySectionActive;

// Event listeners para botões de segurança
document.addEventListener('DOMContentLoaded', function() {
    // Botão de bloquear todas as invasões
    const blockAllBtn = document.getElementById('block-all-btn');
    if (blockAllBtn) {
        blockAllBtn.addEventListener('click', blockAllInvasions);
    }

    // Botão de trocar IP
    const changeIpBtn = document.getElementById('change-ip-btn');
    if (changeIpBtn) {
        changeIpBtn.addEventListener('click', changePlayerIP);
    }

    // Botão de bloqueio de emergência
    const emergencyBtn = document.getElementById('emergency-lockdown-btn');
    if (emergencyBtn) {
        emergencyBtn.addEventListener('click', async function() {
            if (confirm('BLOQUEIO DE EMERGÊNCIA\n\nIsso irá:\n• Bloquear TODAS as invasões detectadas\n• Trocar seu IP (custa 100 shacks)\n• Fechar todas suas conexões\n\nEsta é a defesa máxima disponível!\nContinuar?')) {
                try {
                    showNotification('Ativando bloqueio de emergência...', 'warning');

                    // Executar ações em paralelo
                    await Promise.all([
                        blockAllInvasions(),
                        changePlayerIP()
                    ]);

                    showNotification('Bloqueio de emergência ativado! Você está protegido.', 'success', 6000);

                } catch (error) {
                    console.error('Erro no bloqueio de emergência:', error);
                    showNotification('Erro no bloqueio de emergência', 'error');
                }
            }
        });
    }
});

// === SUPPORTER SHOP FUNCTIONS ===

async function renderSupporterShop() {
    const container = document.getElementById('shop-container');
    if (!container) return;

    // Mostrar loading
    container.innerHTML = `
        <div class="flex items-center justify-center h-full">
            <div class="text-center">
                <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-accent-blue mx-auto mb-4"></div>
                <p class="text-secondary-text">Carregando shop...</p>
            </div>
        </div>
    `;

    try {
        const response = await fetchAPI('/api/shop/items');

        if (!response.sucesso) {
            container.innerHTML = `
                <div class="text-center py-8">
                    <div class="text-red-400 mb-4">
                        <svg class="w-16 h-16 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m9-.75a9 9 0 11-18 0 9 9 0 0118 0zm-9 3.75h.008v.008H12v-.008z" />
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold text-primary-text mb-2">Erro ao carregar shop</h3>
                    <p class="text-secondary-text">${response.mensagem}</p>
                </div>
            `;
            return;
        }

        if (!response.is_supporter) {
            container.innerHTML = `
                <div class="text-center py-8">
                    <div class="text-yellow-400 mb-4">
                        <svg class="w-16 h-16 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M16.5 10.5V6.75a4.5 4.5 0 10-9 0v3.75m-.75 11.25h10.5a2.25 2.25 0 002.25-2.25v-6.75a2.25 2.25 0 00-2.25-2.25H6.75a2.25 2.25 0 00-2.25 2.25v6.75a2.25 2.25 0 002.25 2.25z" />
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold text-primary-text mb-2">Supporter Shop</h3>
                    <p class="text-secondary-text mb-4">Este shop é exclusivo para supporters!</p>
                    <div class="bg-surface-elevated border border-border-color rounded-lg p-6 max-w-md mx-auto">
                        <h4 class="text-lg font-semibold text-primary-text mb-3">Como se tornar Supporter?</h4>
                        <ul class="text-sm text-secondary-text space-y-2 text-left">
                            <li>• Acesso a itens exclusivos</li>
                            <li>• Boosts de XP e dinheiro</li>
                            <li>• Skins premium</li>
                            <li>• Suporte prioritário</li>
                            <li>• Acesso beta a novas funcionalidades</li>
                        </ul>
                        <button class="w-full mt-4 bg-gradient-to-r from-yellow-500 to-yellow-600 hover:from-yellow-600 hover:to-yellow-700 text-black font-bold py-3 px-4 rounded-lg transition-all duration-300 transform hover:scale-105" onclick="window.open('https://discord.gg/pV3qjS2c2M', '_blank')">
                            Tornar-se Supporter
                        </button>
                    </div>
                </div>
            `;
            return;
        }

        // Renderizar shop para supporters
        renderShopItems(response.items, response.spoints_disponiveis);

    } catch (error) {
        console.error('Erro ao carregar shop:', error);
        container.innerHTML = `
            <div class="text-center py-8">
                <div class="text-red-400 mb-4">
                    <svg class="w-16 h-16 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m9-.75a9 9 0 11-18 0 9 9 0 0118 0zm-9 3.75h.008v.008H12v-.008z" />
                    </svg>
                </div>
                <h3 class="text-xl font-bold text-primary-text mb-2">Erro de conexão</h3>
                <p class="text-secondary-text">Não foi possível carregar o shop. Tente novamente.</p>
            </div>
        `;
    }
}

function renderShopItems(items, spointsDisponiveis) {
    const container = document.getElementById('shop-container');

    // Agrupar itens por categoria
    const categorias = {
        'nft': { nome: 'NFT Exclusivo', items: [] },
        'dinheiro': { nome: 'Packs de Dinheiro', items: [] },
        'habilidade': { nome: 'Habilidades Exclusivas', items: [] },
        'boost': { nome: 'Boosts', items: [] },
        'visual': { nome: 'Visual', items: [] },
        'social': { nome: 'Social', items: [] },
        'servico': { nome: 'Serviços', items: [] }
    };

    items.forEach(item => {
        if (categorias[item.categoria]) {
            categorias[item.categoria].items.push(item);
        }
    });

    container.innerHTML = `
        <div class="p-6 space-y-6">
            <!-- Header do Shop -->
            <div class="bg-gradient-to-r from-purple-600 to-blue-600 p-6 rounded-lg text-center">
                <h2 class="text-2xl font-bold text-white mb-2">Supporter Shop</h2>
                <p class="text-purple-100">Itens exclusivos para supporters</p>
                <div class="mt-4 bg-black/20 rounded-lg p-3 inline-block">
                    <span class="text-yellow-400 font-bold">${spointsDisponiveis} Spoints disponíveis</span>
                </div>
            </div>

            <!-- Categorias de Itens -->
            ${Object.entries(categorias).map(([key, categoria]) => {
                if (categoria.items.length === 0) return '';

                return `
                    <div class="bg-surface-elevated border border-border-color rounded-lg overflow-hidden">
                        <div class="bg-surface-hover px-6 py-4 border-b border-border-color">
                            <h3 class="text-lg font-semibold text-primary-text">${categoria.nome}</h3>
                        </div>
                        <div class="p-6">
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                ${categoria.items.map(item => renderShopItem(item, spointsDisponiveis)).join('')}
                            </div>
                        </div>
                    </div>
                `;
            }).join('')}
        </div>
    `;
}

function renderShopItem(item, spointsDisponiveis) {
    const podeComprar = item.disponivel && (item.preco === 0 || spointsDisponiveis >= item.preco);
    const isGratis = item.preco === 0;
    const isNFT = item.categoria === 'nft';
    const esgotado = isNFT && item.estoque_atual <= 0;

    return `
        <div class="bg-surface-default border border-border-color rounded-lg p-4 transition-all duration-300 hover:border-accent-blue hover:shadow-lg ${isNFT ? 'border-purple-500/50 bg-gradient-to-br from-purple-900/20 to-blue-900/20' : ''}">
            <div class="text-center mb-4">
                <div class="text-4xl mb-2">${item.icone}</div>
                <h4 class="text-lg font-semibold text-primary-text mb-1">${item.nome}</h4>
                <p class="text-sm text-secondary-text">${item.descricao}</p>

                ${isNFT ? `
                    <div class="mt-2 space-y-1">
                        <div class="text-xs text-purple-400 font-bold">EDIÇÃO LIMITADA</div>
                        <div class="text-xs ${esgotado ? 'text-red-400' : 'text-yellow-400'}">
                            ${esgotado ? 'ESGOTADO' : `${item.estoque_atual}/${item.estoque_total} disponíveis`}
                        </div>
                    </div>
                ` : ''}
            </div>

            <div class="space-y-3">
                <!-- Preço -->
                <div class="text-center">
                    ${isGratis ?
                        '<span class="text-green-400 font-bold text-lg">GRÁTIS</span>' :
                        `<span class="text-yellow-400 font-bold text-lg">${item.preco} Spoints</span>`
                    }
                </div>

                <!-- Requisito -->
                <div class="text-center">
                    <span class="text-xs text-purple-400 bg-purple-400/10 px-2 py-1 rounded-full">
                        ${item.requisito}
                    </span>
                </div>

                <!-- Botão de Compra -->
                <button
                    class="w-full py-2 px-4 rounded-lg font-semibold transition-all duration-300 ${
                        (podeComprar && !esgotado)
                            ? (isNFT ? 'bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white transform hover:scale-105' : 'bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white transform hover:scale-105')
                            : 'bg-gray-600 text-gray-400 cursor-not-allowed'
                    }"
                    ${(podeComprar && !esgotado) ? `onclick="comprarItemShop('${item.id}', this)"` : 'disabled'}
                >
                    ${esgotado ? 'ESGOTADO' :
                      !item.disponivel ? 'Não Disponível' :
                      !podeComprar && item.preco > 0 ? 'Spoints Insuficientes' :
                      isGratis ? 'Ativar' :
                      isNFT ? 'Adquirir NFT' : 'Comprar'}
                </button>
            </div>
        </div>
    `;
}

async function comprarItemShop(itemId, buttonElement) {
    let button = buttonElement;
    let originalText = '';

    try {
        // Se não foi passado o botão, tentar encontrar pelo evento
        if (!button && window.event) {
            button = window.event.target;
        }

        // Se ainda não encontrou o botão, buscar pelo itemId
        if (!button) {
            button = document.querySelector(`button[onclick*="${itemId}"]`);
        }

        if (button) {
            originalText = button.textContent;
            button.disabled = true;
            button.innerHTML = '<div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mx-auto"></div>';
        }

        // Usar fetch nativo em vez de fetchAPI
        const response = await fetch('/api/shop/comprar', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': csrfToken
            },
            body: JSON.stringify({ item_id: itemId })
        }).then(r => r.json());

        if (response.sucesso) {
            showNotification(response.mensagem, 'success');
            // Recarregar o shop para atualizar os spoints disponíveis
            setTimeout(() => {
                renderSupporterShop();
            }, 1000);
        } else {
            showNotification(response.mensagem, 'error');
            if (button) {
                button.disabled = false;
                button.textContent = originalText;
            }
        }

    } catch (error) {
        console.error('Erro ao comprar item:', error);
        showNotification('Erro ao processar compra', 'error');
        if (button) {
            button.disabled = false;
            button.textContent = originalText;
        }
    }
}

// Tornar funções globais
window.renderSupporterShop = renderSupporterShop;
window.comprarItemShop = comprarItemShop;