#!/usr/bin/env python3
"""
Teste simples da mineração automática
"""

import os
import sys
from dotenv import load_dotenv

# Carrega variáveis de ambiente
load_dotenv()

try:
    from database.supabase_client import supabase_client
    print("✅ Supabase client importado")
except Exception as e:
    print(f"❌ Erro ao importar supabase_client: {e}")
    sys.exit(1)

try:
    from game import new_models as models
    print("✅ Models importado")
except Exception as e:
    print(f"❌ Erro ao importar models: {e}")
    sys.exit(1)

def teste_basico():
    """Teste básico da função"""
    print("\n🧪 Testando função básica...")
    
    # Verificar conexão
    if not supabase_client.is_connected():
        print("❌ Supabase não conectado")
        return False
    
    print("✅ Supabase conectado")
    
    # Buscar um usuário
    try:
        usuarios = supabase_client.get_ranking_usuarios(1)
        if not usuarios:
            print("❌ Nenhum usuário encontrado")
            return False
        
        usuario = usuarios[0]
        print(f"✅ Usuário encontrado: {usuario.get('nick', 'Unknown')}")
        print(f"   Nível mineradora: {usuario.get('nivel_mineradora', 1)}")
        print(f"   Dinheiro atual: ${usuario.get('dinheiro', 0)}")
        
        # Calcular dinheiro que deveria ser gerado
        nivel = usuario.get('nivel_mineradora', 1)
        dinheiro_por_minuto = nivel * 3
        print(f"   Deveria gerar: {dinheiro_por_minuto} dinheiro/min")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro ao buscar usuário: {e}")
        return False

def teste_processamento():
    """Teste do processamento"""
    print("\n⚡ Testando processamento...")
    
    try:
        resultado = models.processar_mineracao_automatica_dinheiro()
        print(f"Resultado: {resultado}")
        return resultado.get('sucesso', False)
    except Exception as e:
        print(f"❌ Erro no processamento: {e}")
        return False

if __name__ == "__main__":
    print("🔍 TESTE SIMPLES DA MINERAÇÃO")
    print("=" * 40)
    
    if teste_basico():
        if teste_processamento():
            print("\n✅ Testes passaram!")
        else:
            print("\n❌ Processamento falhou")
    else:
        print("\n❌ Teste básico falhou")
