2025-07-08 01:23:39,545 - INFO - Activity monitoring initialized
2025-07-08 01:23:41,830 - INFO - Activity monitoring initialized
2025-07-08 01:26:36,563 - INFO - Activity monitoring initialized
2025-07-08 01:27:22,568 - INFO - Activity monitoring initialized
2025-07-08 01:29:00,655 - INFO - Activity monitoring initialized
2025-07-08 01:29:13,684 - INFO - Activity monitoring initialized
2025-07-08 01:36:53,658 - INFO - Activity monitoring initialized
2025-07-08 01:37:12,338 - INFO - Activity monitoring initialized
2025-07-08 01:37:33,864 - INFO - Activity monitoring initialized
2025-07-08 01:38:24,903 - INFO - Activity monitoring initialized
2025-07-08 01:38:35,076 - INFO - Activity monitoring initialized
2025-07-08 01:44:24,061 - INFO - Activity monitoring initialized
2025-07-08 01:44:55,586 - INFO - Activity monitoring initialized
2025-07-08 01:47:52,697 - INFO - Activity monitoring initialized
2025-07-08 01:48:14,163 - INFO - Activity monitoring initialized
2025-07-08 01:48:33,459 - INFO - Activity monitoring initialized
2025-07-08 01:52:37,522 - INFO - Activity monitoring initialized
2025-07-08 01:52:51,362 - INFO - Activity monitoring initialized
2025-07-08 01:53:22,355 - INFO - Activity monitoring initialized
2025-07-08 01:53:43,447 - INFO - Activity monitoring initialized
2025-07-08 01:54:05,066 - INFO - Activity monitoring initialized
2025-07-08 01:54:24,530 - INFO - Activity monitoring initialized
2025-07-08 02:04:23,211 - INFO - Activity monitoring initialized
2025-07-08 02:04:54,213 - INFO - Activity monitoring initialized
2025-07-08 02:05:06,361 - INFO - Activity monitoring initialized
2025-07-08 02:06:04,205 - INFO - Activity monitoring initialized
2025-07-08 02:06:21,389 - INFO - Activity monitoring initialized
2025-07-08 02:08:18,202 - INFO - Activity monitoring initialized
2025-07-08 02:08:21,546 - INFO - Activity monitoring initialized
2025-07-08 02:09:55,208 - INFO - Request: {"timestamp": "2025-07-08T02:09:55.208223", "method": "GET", "path": "/api/jogador", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951395208"}
2025-07-08 02:09:55,324 - INFO - Request: {"timestamp": "2025-07-08T02:09:55.324947", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951395324"}
2025-07-08 02:09:55,361 - INFO - Response: {"request_id": "1751951395208", "status_code": 200, "duration_ms": 153}
2025-07-08 02:09:55,464 - INFO - Request: {"timestamp": "2025-07-08T02:09:55.464654", "method": "POST", "path": "/api/mineracao/coletar-offline", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951395464"}
2025-07-08 02:09:55,499 - INFO - Response: {"request_id": "1751951395324", "status_code": 200, "duration_ms": 174}
2025-07-08 02:09:55,560 - INFO - Response: {"request_id": "1751951395464", "status_code": 200, "duration_ms": 96}
2025-07-08 02:09:56,961 - INFO - Request: {"timestamp": "2025-07-08T02:09:56.961409", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951396961"}
2025-07-08 02:09:57,240 - INFO - Response: {"request_id": "1751951396961", "status_code": 200, "duration_ms": 278}
2025-07-08 02:10:00,837 - INFO - Request: {"timestamp": "2025-07-08T02:10:00.837297", "method": "GET", "path": "/api/jogador", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951400836"}
2025-07-08 02:10:00,972 - INFO - Response: {"request_id": "1751951400836", "status_code": 200, "duration_ms": 135}
2025-07-08 02:10:01,302 - INFO - Request: {"timestamp": "2025-07-08T02:10:01.301135", "method": "POST", "path": "/api/mineracao/coletar-offline", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951401301"}
2025-07-08 02:10:01,447 - INFO - Response: {"request_id": "1751951401301", "status_code": 200, "duration_ms": 146}
2025-07-08 02:10:04,234 - INFO - Request: {"timestamp": "2025-07-08T02:10:04.234091", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951404234"}
2025-07-08 02:10:04,540 - INFO - Response: {"request_id": "1751951404234", "status_code": 200, "duration_ms": 306}
2025-07-08 02:10:07,262 - INFO - Request: {"timestamp": "2025-07-08T02:10:07.262235", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951407262"}
2025-07-08 02:10:07,544 - INFO - Response: {"request_id": "1751951407262", "status_code": 200, "duration_ms": 282}
2025-07-08 02:10:07,812 - INFO - Request: {"timestamp": "2025-07-08T02:10:07.812587", "method": "GET", "path": "/api/terminal/connections", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951407812"}
2025-07-08 02:10:08,064 - INFO - Request: {"timestamp": "2025-07-08T02:10:08.064521", "method": "GET", "path": "/", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951408064"}
2025-07-08 02:10:08,081 - INFO - Response: {"request_id": "1751951408064", "status_code": 200, "duration_ms": 16}
2025-07-08 02:10:08,121 - INFO - Response: {"request_id": "1751951407812", "status_code": 200, "duration_ms": 308}
2025-07-08 02:10:08,146 - INFO - Request: {"timestamp": "2025-07-08T02:10:08.146548", "method": "GET", "path": "/static/js/simple-auth.js", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951408146"}
2025-07-08 02:10:08,229 - INFO - Response: {"request_id": "1751951408146", "status_code": 200, "duration_ms": 83}
2025-07-08 02:10:08,414 - INFO - Request: {"timestamp": "2025-07-08T02:10:08.414031", "method": "GET", "path": "/static/js/main.js", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951408414"}
2025-07-08 02:10:08,415 - INFO - Response: {"request_id": "1751951408414", "status_code": 200, "duration_ms": 1}
2025-07-08 02:10:08,510 - INFO - Request: {"timestamp": "2025-07-08T02:10:08.510904", "method": "GET", "path": "/api/csrf-token", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951408510"}
2025-07-08 02:10:08,591 - INFO - Response: {"request_id": "1751951408510", "status_code": 200, "duration_ms": 79}
2025-07-08 02:10:08,848 - INFO - Request: {"timestamp": "2025-07-08T02:10:08.848774", "method": "GET", "path": "/favicon.ico", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951408848"}
2025-07-08 02:10:08,849 - INFO - Response: {"request_id": "1751951408848", "status_code": 200, "duration_ms": 0}
2025-07-08 02:10:08,926 - INFO - Request: {"timestamp": "2025-07-08T02:10:08.926888", "method": "GET", "path": "/api/jogador", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951408926"}
2025-07-08 02:10:09,060 - INFO - Response: {"request_id": "1751951408926", "status_code": 200, "duration_ms": 133}
2025-07-08 02:10:09,384 - INFO - Request: {"timestamp": "2025-07-08T02:10:09.384143", "method": "POST", "path": "/api/mineracao/coletar-offline", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951409384"}
2025-07-08 02:10:09,397 - INFO - Request: {"timestamp": "2025-07-08T02:10:09.397358", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951409397"}
2025-07-08 02:10:09,690 - INFO - Response: {"request_id": "1751951409384", "status_code": 200, "duration_ms": 306}
2025-07-08 02:10:09,691 - INFO - Response: {"request_id": "1751951409397", "status_code": 200, "duration_ms": 294}
2025-07-08 02:10:10,709 - INFO - Request: {"timestamp": "2025-07-08T02:10:10.709288", "method": "GET", "path": "/api/scan", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951410709"}
2025-07-08 02:10:10,813 - INFO - Response: {"request_id": "1751951410709", "status_code": 200, "duration_ms": 104}
2025-07-08 02:10:13,008 - INFO - Request: {"timestamp": "2025-07-08T02:10:13.008454", "method": "POST", "path": "/api/alvo/**************/exploit", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951413008"}
2025-07-08 02:10:13,671 - INFO - Response: {"request_id": "1751951413008", "status_code": 200, "duration_ms": 662}
2025-07-08 02:10:16,254 - INFO - Request: {"timestamp": "2025-07-08T02:10:16.254483", "method": "POST", "path": "/api/alvo/deface", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951416254"}
2025-07-08 02:10:16,686 - INFO - Response: {"request_id": "1751951416254", "status_code": 200, "duration_ms": 431}
2025-07-08 02:10:16,962 - INFO - Request: {"timestamp": "2025-07-08T02:10:16.961716", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951416961"}
2025-07-08 02:10:16,996 - INFO - Request: {"timestamp": "2025-07-08T02:10:16.996369", "method": "GET", "path": "/api/jogador", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951416996"}
2025-07-08 02:10:17,112 - INFO - Response: {"request_id": "1751951416996", "status_code": 200, "duration_ms": 116}
2025-07-08 02:10:17,248 - INFO - Request: {"timestamp": "2025-07-08T02:10:17.248307", "method": "POST", "path": "/api/mineracao/coletar-offline", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951417248"}
2025-07-08 02:10:17,287 - INFO - Response: {"request_id": "1751951416961", "status_code": 200, "duration_ms": 325}
2025-07-08 02:10:17,384 - INFO - Response: {"request_id": "1751951417248", "status_code": 200, "duration_ms": 135}
2025-07-08 02:10:19,089 - INFO - Request: {"timestamp": "2025-07-08T02:10:19.089090", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951419089"}
2025-07-08 02:10:19,271 - INFO - Response: {"request_id": "1751951419089", "status_code": 200, "duration_ms": 182}
2025-07-08 02:10:21,144 - INFO - Request: {"timestamp": "2025-07-08T02:10:21.144228", "method": "GET", "path": "/api/grupo", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951421144"}
2025-07-08 02:10:21,566 - INFO - Response: {"request_id": "1751951421144", "status_code": 200, "duration_ms": 421}
2025-07-08 02:10:21,873 - INFO - Request: {"timestamp": "2025-07-08T02:10:21.873220", "method": "GET", "path": "/api/torneio/ranking-deface", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951421873"}
2025-07-08 02:10:22,055 - INFO - Response: {"request_id": "1751951421873", "status_code": 200, "duration_ms": 182}
2025-07-08 02:10:22,124 - INFO - Request: {"timestamp": "2025-07-08T02:10:22.124695", "method": "GET", "path": "/api/admin/torneio/info", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951422124"}
2025-07-08 02:10:22,308 - INFO - Response: {"request_id": "1751951422124", "status_code": 200, "duration_ms": 183}
2025-07-08 02:10:23,562 - INFO - Request: {"timestamp": "2025-07-08T02:10:23.562105", "method": "GET", "path": "/api/torneio/ranking-deface", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951423562"}
2025-07-08 02:10:23,750 - INFO - Response: {"request_id": "1751951423562", "status_code": 200, "duration_ms": 188}
2025-07-08 02:10:25,744 - INFO - Request: {"timestamp": "2025-07-08T02:10:25.744415", "method": "GET", "path": "/api/torneio/ranking-deface", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951425744"}
2025-07-08 02:10:25,945 - INFO - Response: {"request_id": "1751951425744", "status_code": 200, "duration_ms": 201}
2025-07-08 02:10:25,995 - INFO - Request: {"timestamp": "2025-07-08T02:10:25.995376", "method": "GET", "path": "/api/ranking/deface-individual", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951425995"}
2025-07-08 02:10:26,176 - INFO - Response: {"request_id": "1751951425995", "status_code": 200, "duration_ms": 180}
2025-07-08 02:10:27,266 - INFO - Request: {"timestamp": "2025-07-08T02:10:27.266041", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951427266"}
2025-07-08 02:10:27,633 - INFO - Response: {"request_id": "1751951427266", "status_code": 200, "duration_ms": 367}
2025-07-08 02:10:29,089 - INFO - Request: {"timestamp": "2025-07-08T02:10:29.089388", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951429089"}
2025-07-08 02:10:29,269 - INFO - Response: {"request_id": "1751951429089", "status_code": 200, "duration_ms": 179}
2025-07-08 02:10:36,431 - INFO - Request: {"timestamp": "2025-07-08T02:10:36.431644", "method": "GET", "path": "/api/torneio/ranking-deface", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951436431"}
2025-07-08 02:10:36,783 - INFO - Response: {"request_id": "1751951436431", "status_code": 200, "duration_ms": 351}
2025-07-08 02:10:37,021 - INFO - Request: {"timestamp": "2025-07-08T02:10:37.021523", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951437021"}
2025-07-08 02:10:37,338 - INFO - Response: {"request_id": "1751951437021", "status_code": 200, "duration_ms": 317}
2025-07-08 02:10:39,089 - INFO - Request: {"timestamp": "2025-07-08T02:10:39.089595", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951439089"}
2025-07-08 02:10:39,326 - INFO - Response: {"request_id": "1751951439089", "status_code": 200, "duration_ms": 235}
2025-07-08 02:10:42,198 - INFO - Request: {"timestamp": "2025-07-08T02:10:42.198157", "method": "GET", "path": "/api/jogador", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951442198"}
2025-07-08 02:10:42,289 - INFO - Response: {"request_id": "1751951442198", "status_code": 200, "duration_ms": 91}
2025-07-08 02:10:42,451 - INFO - Request: {"timestamp": "2025-07-08T02:10:42.451126", "method": "POST", "path": "/api/mineracao/coletar-offline", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951442451"}
2025-07-08 02:10:42,539 - INFO - Response: {"request_id": "1751951442451", "status_code": 200, "duration_ms": 87}
2025-07-08 02:10:44,516 - INFO - Request: {"timestamp": "2025-07-08T02:10:44.516867", "method": "GET", "path": "/api/jogador", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951444516"}
2025-07-08 02:10:44,646 - INFO - Response: {"request_id": "1751951444516", "status_code": 200, "duration_ms": 129}
2025-07-08 02:10:44,956 - INFO - Request: {"timestamp": "2025-07-08T02:10:44.956875", "method": "POST", "path": "/api/mineracao/coletar-offline", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951444956"}
2025-07-08 02:10:45,063 - INFO - Response: {"request_id": "1751951444956", "status_code": 200, "duration_ms": 106}
2025-07-08 02:10:46,343 - INFO - Request: {"timestamp": "2025-07-08T02:10:46.343558", "method": "GET", "path": "/", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951446343"}
2025-07-08 02:10:46,344 - INFO - Response: {"request_id": "1751951446343", "status_code": 200, "duration_ms": 0}
2025-07-08 02:10:46,660 - INFO - Request: {"timestamp": "2025-07-08T02:10:46.660919", "method": "GET", "path": "/static/js/simple-auth.js", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951446660"}
2025-07-08 02:10:46,661 - INFO - Request: {"timestamp": "2025-07-08T02:10:46.661896", "method": "GET", "path": "/static/js/main.js", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951446661"}
2025-07-08 02:10:46,663 - INFO - Response: {"request_id": "1751951446660", "status_code": 200, "duration_ms": 1}
2025-07-08 02:10:46,663 - INFO - Response: {"request_id": "1751951446661", "status_code": 200, "duration_ms": 1}
2025-07-08 02:10:46,913 - INFO - Request: {"timestamp": "2025-07-08T02:10:46.913442", "method": "GET", "path": "/api/csrf-token", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951446913"}
2025-07-08 02:10:46,914 - INFO - Response: {"request_id": "1751951446913", "status_code": 200, "duration_ms": 0}
2025-07-08 02:10:46,977 - INFO - Request: {"timestamp": "2025-07-08T02:10:46.976911", "method": "GET", "path": "/favicon.ico", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951446976"}
2025-07-08 02:10:46,979 - INFO - Response: {"request_id": "1751951446976", "status_code": 200, "duration_ms": 2}
2025-07-08 02:10:47,032 - INFO - Request: {"timestamp": "2025-07-08T02:10:47.032128", "method": "GET", "path": "/api/jogador", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951447032"}
2025-07-08 02:10:47,146 - INFO - Response: {"request_id": "1751951447032", "status_code": 200, "duration_ms": 114}
2025-07-08 02:10:47,235 - INFO - Request: {"timestamp": "2025-07-08T02:10:47.234785", "method": "POST", "path": "/api/mineracao/coletar-offline", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951447234"}
2025-07-08 02:10:47,380 - INFO - Response: {"request_id": "1751951447234", "status_code": 200, "duration_ms": 145}
2025-07-08 02:10:47,470 - INFO - Request: {"timestamp": "2025-07-08T02:10:47.470118", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951447470"}
2025-07-08 02:10:47,803 - INFO - Response: {"request_id": "1751951447470", "status_code": 200, "duration_ms": 332}
2025-07-08 02:10:48,851 - INFO - Request: {"timestamp": "2025-07-08T02:10:48.851477", "method": "GET", "path": "/api/scan", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951448851"}
2025-07-08 02:10:48,946 - INFO - Response: {"request_id": "1751951448851", "status_code": 200, "duration_ms": 95}
2025-07-08 02:10:49,399 - INFO - Request: {"timestamp": "2025-07-08T02:10:49.399324", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951449399"}
2025-07-08 02:10:49,556 - INFO - Response: {"request_id": "1751951449399", "status_code": 200, "duration_ms": 157}
2025-07-08 02:10:50,843 - INFO - Request: {"timestamp": "2025-07-08T02:10:50.843119", "method": "POST", "path": "/api/alvo/**************/exploit", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951450843"}
2025-07-08 02:10:51,519 - INFO - Response: {"request_id": "1751951450843", "status_code": 200, "duration_ms": 676}
2025-07-08 02:10:53,950 - INFO - Request: {"timestamp": "2025-07-08T02:10:53.949581", "method": "POST", "path": "/api/alvo/deface", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951453949"}
2025-07-08 02:10:54,339 - INFO - Response: {"request_id": "1751951453949", "status_code": 200, "duration_ms": 389}
2025-07-08 02:10:54,647 - INFO - Request: {"timestamp": "2025-07-08T02:10:54.647779", "method": "GET", "path": "/api/jogador", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951454647"}
2025-07-08 02:10:54,746 - INFO - Response: {"request_id": "1751951454647", "status_code": 200, "duration_ms": 99}
2025-07-08 02:10:54,898 - INFO - Request: {"timestamp": "2025-07-08T02:10:54.898797", "method": "POST", "path": "/api/mineracao/coletar-offline", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951454898"}
2025-07-08 02:10:54,987 - INFO - Response: {"request_id": "1751951454898", "status_code": 200, "duration_ms": 88}
2025-07-08 02:10:57,178 - INFO - Request: {"timestamp": "2025-07-08T02:10:57.178666", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951457178"}
2025-07-08 02:10:57,579 - INFO - Response: {"request_id": "1751951457178", "status_code": 200, "duration_ms": 400}
2025-07-08 02:10:58,243 - INFO - Request: {"timestamp": "2025-07-08T02:10:58.243178", "method": "GET", "path": "/api/grupo", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951458243"}
2025-07-08 02:10:58,644 - INFO - Response: {"request_id": "1751951458243", "status_code": 200, "duration_ms": 401}
2025-07-08 02:10:58,649 - INFO - Request: {"timestamp": "2025-07-08T02:10:58.649400", "method": "GET", "path": "/api/torneio/ranking-deface", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951458649"}
2025-07-08 02:10:58,826 - INFO - Response: {"request_id": "1751951458649", "status_code": 200, "duration_ms": 176}
2025-07-08 02:10:59,089 - INFO - Request: {"timestamp": "2025-07-08T02:10:59.089800", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951459088"}
2025-07-08 02:10:59,150 - INFO - Request: {"timestamp": "2025-07-08T02:10:59.150349", "method": "GET", "path": "/api/admin/torneio/info", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951459150"}
2025-07-08 02:10:59,264 - INFO - Response: {"request_id": "1751951459088", "status_code": 200, "duration_ms": 175}
2025-07-08 02:10:59,269 - INFO - Response: {"request_id": "1751951459150", "status_code": 403, "duration_ms": 118}
2025-07-08 02:11:00,051 - INFO - Request: {"timestamp": "2025-07-08T02:11:00.051257", "method": "GET", "path": "/api/torneio/ranking-deface", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951460051"}
2025-07-08 02:11:00,262 - INFO - Response: {"request_id": "1751951460051", "status_code": 200, "duration_ms": 211}
2025-07-08 02:11:00,580 - INFO - Request: {"timestamp": "2025-07-08T02:11:00.580114", "method": "GET", "path": "/api/admin/torneio/info", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951460580"}
2025-07-08 02:11:00,676 - INFO - Response: {"request_id": "1751951460580", "status_code": 403, "duration_ms": 96}
2025-07-08 02:11:02,214 - INFO - Request: {"timestamp": "2025-07-08T02:11:02.214771", "method": "GET", "path": "/", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951462214"}
2025-07-08 02:11:02,215 - INFO - Response: {"request_id": "1751951462214", "status_code": 200, "duration_ms": 0}
2025-07-08 02:11:02,536 - INFO - Request: {"timestamp": "2025-07-08T02:11:02.536620", "method": "GET", "path": "/static/js/simple-auth.js", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951462536"}
2025-07-08 02:11:02,538 - INFO - Request: {"timestamp": "2025-07-08T02:11:02.538572", "method": "GET", "path": "/static/js/main.js", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951462538"}
2025-07-08 02:11:02,539 - INFO - Response: {"request_id": "1751951462536", "status_code": 304, "duration_ms": 2}
2025-07-08 02:11:02,540 - INFO - Response: {"request_id": "1751951462538", "status_code": 304, "duration_ms": 0}
2025-07-08 02:11:02,791 - INFO - Request: {"timestamp": "2025-07-08T02:11:02.790147", "method": "GET", "path": "/api/csrf-token", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951462790"}
2025-07-08 02:11:02,846 - INFO - Response: {"request_id": "1751951462790", "status_code": 200, "duration_ms": 56}
2025-07-08 02:11:02,882 - INFO - Request: {"timestamp": "2025-07-08T02:11:02.881937", "method": "GET", "path": "/favicon.ico", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951462881"}
2025-07-08 02:11:02,888 - INFO - Response: {"request_id": "1751951462881", "status_code": 200, "duration_ms": 6}
2025-07-08 02:11:02,929 - INFO - Request: {"timestamp": "2025-07-08T02:11:02.928807", "method": "GET", "path": "/api/jogador", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951462928"}
2025-07-08 02:11:03,035 - INFO - Response: {"request_id": "1751951462928", "status_code": 200, "duration_ms": 106}
2025-07-08 02:11:03,227 - INFO - Request: {"timestamp": "2025-07-08T02:11:03.227615", "method": "POST", "path": "/api/mineracao/coletar-offline", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951463227"}
2025-07-08 02:11:03,320 - INFO - Response: {"request_id": "1751951463227", "status_code": 200, "duration_ms": 91}
2025-07-08 02:11:03,347 - INFO - Request: {"timestamp": "2025-07-08T02:11:03.347726", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951463347"}
2025-07-08 02:11:03,485 - INFO - Request: {"timestamp": "2025-07-08T02:11:03.485416", "method": "GET", "path": "/api/grupo", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951463485"}
2025-07-08 02:11:03,950 - INFO - Response: {"request_id": "1751951463347", "status_code": 200, "duration_ms": 603}
2025-07-08 02:11:04,118 - INFO - Response: {"request_id": "1751951463485", "status_code": 200, "duration_ms": 633}
2025-07-08 02:11:04,427 - INFO - Request: {"timestamp": "2025-07-08T02:11:04.427463", "method": "GET", "path": "/api/torneio/ranking-deface", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951464427"}
2025-07-08 02:11:04,597 - INFO - Response: {"request_id": "1751951464427", "status_code": 200, "duration_ms": 168}
2025-07-08 02:11:04,678 - INFO - Request: {"timestamp": "2025-07-08T02:11:04.678425", "method": "GET", "path": "/api/admin/torneio/info", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951464678"}
2025-07-08 02:11:04,778 - INFO - Response: {"request_id": "1751951464678", "status_code": 403, "duration_ms": 99}
2025-07-08 02:11:05,354 - INFO - Request: {"timestamp": "2025-07-08T02:11:05.354257", "method": "GET", "path": "/api/torneio/ranking-deface", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951465354"}
2025-07-08 02:11:05,540 - INFO - Response: {"request_id": "1751951465354", "status_code": 200, "duration_ms": 186}
2025-07-08 02:11:06,101 - INFO - Request: {"timestamp": "2025-07-08T02:11:06.101279", "method": "GET", "path": "/api/admin/torneio/info", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951466101"}
2025-07-08 02:11:06,197 - INFO - Response: {"request_id": "1751951466101", "status_code": 403, "duration_ms": 96}
2025-07-08 02:11:07,063 - INFO - Request: {"timestamp": "2025-07-08T02:11:07.063186", "method": "GET", "path": "/api/torneio/ranking-deface", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951467063"}
2025-07-08 02:11:07,064 - ERROR - SECURITY ALERT: Suspicious activity: 21 requests in 10s from IP 12*******
2025-07-08 02:11:07,415 - INFO - Response: {"request_id": "1751951467063", "status_code": 200, "duration_ms": 352}
2025-07-08 02:11:07,723 - INFO - Request: {"timestamp": "2025-07-08T02:11:07.723924", "method": "GET", "path": "/api/ranking/deface-individual", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951467723"}
2025-07-08 02:11:07,723 - ERROR - SECURITY ALERT: Suspicious activity: 21 requests in 10s from IP 12*******
2025-07-08 02:11:07,892 - INFO - Response: {"request_id": "1751951467723", "status_code": 200, "duration_ms": 168}
2025-07-08 02:11:08,206 - INFO - Request: {"timestamp": "2025-07-08T02:11:08.206317", "method": "GET", "path": "/api/admin/torneio/info", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951468206"}
2025-07-08 02:11:08,207 - ERROR - SECURITY ALERT: Suspicious activity: 22 requests in 10s from IP 12*******
2025-07-08 02:11:08,298 - INFO - Response: {"request_id": "1751951468206", "status_code": 403, "duration_ms": 91}
2025-07-08 02:11:09,436 - INFO - Request: {"timestamp": "2025-07-08T02:11:09.435248", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951469435"}
2025-07-08 02:11:09,985 - INFO - Response: {"request_id": "1751951469435", "status_code": 200, "duration_ms": 550}
2025-07-08 02:11:13,355 - INFO - Request: {"timestamp": "2025-07-08T02:11:13.355423", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951473355"}
2025-07-08 02:11:13,625 - INFO - Response: {"request_id": "1751951473355", "status_code": 200, "duration_ms": 270}
2025-07-08 02:11:20,031 - INFO - Request: {"timestamp": "2025-07-08T02:11:20.030919", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951480030"}
2025-07-08 02:11:20,246 - INFO - Response: {"request_id": "1751951480030", "status_code": 200, "duration_ms": 215}
2025-07-08 02:11:23,051 - INFO - Request: {"timestamp": "2025-07-08T02:11:23.051610", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951483051"}
2025-07-08 02:11:23,320 - INFO - Response: {"request_id": "1751951483051", "status_code": 200, "duration_ms": 269}
2025-07-08 02:11:30,324 - INFO - Request: {"timestamp": "2025-07-08T02:11:30.323300", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951490323"}
2025-07-08 02:11:30,596 - INFO - Response: {"request_id": "1751951490323", "status_code": 200, "duration_ms": 273}
2025-07-08 02:11:33,353 - INFO - Request: {"timestamp": "2025-07-08T02:11:33.353588", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951493353"}
2025-07-08 02:11:33,657 - INFO - Response: {"request_id": "1751951493353", "status_code": 200, "duration_ms": 303}
2025-07-08 02:11:40,021 - INFO - Request: {"timestamp": "2025-07-08T02:11:40.021822", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951500021"}
2025-07-08 02:11:40,440 - INFO - Response: {"request_id": "1751951500021", "status_code": 200, "duration_ms": 418}
2025-07-08 02:11:43,051 - INFO - Request: {"timestamp": "2025-07-08T02:11:43.051805", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951503051"}
2025-07-08 02:11:43,319 - INFO - Response: {"request_id": "1751951503051", "status_code": 200, "duration_ms": 267}
2025-07-08 02:11:50,325 - INFO - Request: {"timestamp": "2025-07-08T02:11:50.325443", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951510325"}
2025-07-08 02:11:50,563 - INFO - Response: {"request_id": "1751951510325", "status_code": 200, "duration_ms": 238}
2025-07-08 02:11:53,354 - INFO - Request: {"timestamp": "2025-07-08T02:11:53.353995", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951513353"}
2025-07-08 02:11:53,618 - INFO - Response: {"request_id": "1751951513353", "status_code": 200, "duration_ms": 264}
2025-07-08 02:12:00,022 - INFO - Request: {"timestamp": "2025-07-08T02:12:00.022643", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951520022"}
2025-07-08 02:12:00,236 - INFO - Response: {"request_id": "1751951520022", "status_code": 200, "duration_ms": 213}
2025-07-08 02:12:03,050 - INFO - Request: {"timestamp": "2025-07-08T02:12:03.050882", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951523050"}
2025-07-08 02:12:03,333 - INFO - Response: {"request_id": "1751951523050", "status_code": 200, "duration_ms": 282}
2025-07-08 02:12:13,354 - INFO - Request: {"timestamp": "2025-07-08T02:12:13.354086", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951533354"}
2025-07-08 02:12:13,746 - INFO - Response: {"request_id": "1751951533354", "status_code": 200, "duration_ms": 392}
2025-07-08 02:12:23,050 - INFO - Request: {"timestamp": "2025-07-08T02:12:23.050776", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951543050"}
2025-07-08 02:12:23,395 - INFO - Response: {"request_id": "1751951543050", "status_code": 200, "duration_ms": 344}
2025-07-08 02:12:32,854 - INFO - Request: {"timestamp": "2025-07-08T02:12:32.854956", "method": "GET", "path": "/api/jogador", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951552854"}
2025-07-08 02:12:32,984 - INFO - Response: {"request_id": "1751951552854", "status_code": 200, "duration_ms": 128}
2025-07-08 02:12:33,105 - INFO - Request: {"timestamp": "2025-07-08T02:12:33.105992", "method": "POST", "path": "/api/mineracao/coletar-offline", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951553105"}
2025-07-08 02:12:33,254 - INFO - Response: {"request_id": "1751951553105", "status_code": 200, "duration_ms": 148}
2025-07-08 02:12:33,292 - INFO - Request: {"timestamp": "2025-07-08T02:12:33.292340", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951553292"}
2025-07-08 02:12:33,738 - INFO - Response: {"request_id": "1751951553292", "status_code": 200, "duration_ms": 445}
2025-07-08 02:12:42,324 - INFO - Request: {"timestamp": "2025-07-08T02:12:42.324465", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951562324"}
2025-07-08 02:12:42,520 - INFO - Response: {"request_id": "1751951562324", "status_code": 200, "duration_ms": 195}
2025-07-08 02:12:43,052 - INFO - Request: {"timestamp": "2025-07-08T02:12:43.052473", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951563052"}
2025-07-08 02:12:43,416 - INFO - Response: {"request_id": "1751951563052", "status_code": 200, "duration_ms": 363}
2025-07-08 02:12:53,355 - INFO - Request: {"timestamp": "2025-07-08T02:12:53.355388", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951573355"}
2025-07-08 02:12:53,900 - INFO - Response: {"request_id": "1751951573355", "status_code": 200, "duration_ms": 544}
2025-07-08 02:12:55,319 - INFO - Request: {"timestamp": "2025-07-08T02:12:55.318122", "method": "GET", "path": "/api/jogador", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951575318"}
2025-07-08 02:12:55,456 - INFO - Response: {"request_id": "1751951575318", "status_code": 200, "duration_ms": 138}
2025-07-08 02:12:55,620 - INFO - Request: {"timestamp": "2025-07-08T02:12:55.620437", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951575620"}
2025-07-08 02:12:55,794 - INFO - Response: {"request_id": "1751951575620", "status_code": 200, "duration_ms": 174}
2025-07-08 02:12:55,858 - INFO - Request: {"timestamp": "2025-07-08T02:12:55.858785", "method": "POST", "path": "/api/mineracao/coletar-offline", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951575858"}
2025-07-08 02:12:56,052 - INFO - Response: {"request_id": "1751951575858", "status_code": 200, "duration_ms": 193}
2025-07-08 02:12:56,632 - INFO - Request: {"timestamp": "2025-07-08T02:12:56.632698", "method": "GET", "path": "/", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951576632"}
2025-07-08 02:12:56,633 - INFO - Response: {"request_id": "1751951576632", "status_code": 200, "duration_ms": 0}
2025-07-08 02:12:56,671 - INFO - Request: {"timestamp": "2025-07-08T02:12:56.671759", "method": "GET", "path": "/static/js/simple-auth.js", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951576671"}
2025-07-08 02:12:56,679 - INFO - Response: {"request_id": "1751951576671", "status_code": 304, "duration_ms": 7}
2025-07-08 02:12:56,899 - INFO - Request: {"timestamp": "2025-07-08T02:12:56.899358", "method": "GET", "path": "/static/js/main.js", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951576898"}
2025-07-08 02:12:56,900 - INFO - Response: {"request_id": "1751951576898", "status_code": 304, "duration_ms": 1}
2025-07-08 02:12:57,220 - INFO - Request: {"timestamp": "2025-07-08T02:12:57.220790", "method": "GET", "path": "/api/csrf-token", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951577220"}
2025-07-08 02:12:57,221 - INFO - Response: {"request_id": "1751951577220", "status_code": 200, "duration_ms": 0}
2025-07-08 02:12:57,309 - INFO - Request: {"timestamp": "2025-07-08T02:12:57.309722", "method": "GET", "path": "/favicon.ico", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951577309"}
2025-07-08 02:12:57,311 - INFO - Response: {"request_id": "1751951577309", "status_code": 200, "duration_ms": 1}
2025-07-08 02:12:57,470 - INFO - Request: {"timestamp": "2025-07-08T02:12:57.470383", "method": "GET", "path": "/api/jogador", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951577470"}
2025-07-08 02:12:57,609 - INFO - Response: {"request_id": "1751951577470", "status_code": 200, "duration_ms": 138}
2025-07-08 02:12:57,617 - INFO - Request: {"timestamp": "2025-07-08T02:12:57.617837", "method": "POST", "path": "/api/mineracao/coletar-offline", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951577617"}
2025-07-08 02:12:57,770 - INFO - Response: {"request_id": "1751951577617", "status_code": 200, "duration_ms": 152}
2025-07-08 02:12:57,930 - INFO - Request: {"timestamp": "2025-07-08T02:12:57.930379", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951577930"}
2025-07-08 02:12:58,073 - INFO - Response: {"request_id": "1751951577930", "status_code": 200, "duration_ms": 143}
2025-07-08 02:12:59,238 - INFO - Request: {"timestamp": "2025-07-08T02:12:59.238596", "method": "GET", "path": "/api/scan", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951579238"}
2025-07-08 02:12:59,367 - INFO - Response: {"request_id": "1751951579238", "status_code": 200, "duration_ms": 128}
2025-07-08 02:13:01,079 - INFO - Request: {"timestamp": "2025-07-08T02:13:01.079467", "method": "POST", "path": "/api/alvo/**************/exploit", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951581079"}
2025-07-08 02:13:01,766 - INFO - Response: {"request_id": "1751951581079", "status_code": 200, "duration_ms": 687}
2025-07-08 02:13:03,052 - INFO - Request: {"timestamp": "2025-07-08T02:13:03.052353", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951583052"}
2025-07-08 02:13:03,329 - INFO - Response: {"request_id": "1751951583052", "status_code": 200, "duration_ms": 277}
2025-07-08 02:13:03,950 - INFO - Request: {"timestamp": "2025-07-08T02:13:03.950906", "method": "POST", "path": "/api/alvo/deface", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951583950"}
2025-07-08 02:13:04,194 - INFO - Response: {"request_id": "1751951583950", "status_code": 200, "duration_ms": 243}
2025-07-08 02:13:07,639 - INFO - Request: {"timestamp": "2025-07-08T02:13:07.639879", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951587639"}
2025-07-08 02:13:07,987 - INFO - Response: {"request_id": "1751951587639", "status_code": 200, "duration_ms": 347}
2025-07-08 02:13:11,207 - INFO - Request: {"timestamp": "2025-07-08T02:13:11.207547", "method": "POST", "path": "/api/alvo/*************/exploit", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951591207"}
2025-07-08 02:13:11,956 - INFO - Response: {"request_id": "1751951591207", "status_code": 200, "duration_ms": 749}
2025-07-08 02:13:13,352 - INFO - Request: {"timestamp": "2025-07-08T02:13:13.352523", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951593352"}
2025-07-08 02:13:13,609 - INFO - Response: {"request_id": "1751951593352", "status_code": 200, "duration_ms": 256}
2025-07-08 02:13:14,993 - INFO - Request: {"timestamp": "2025-07-08T02:13:14.993047", "method": "POST", "path": "/api/alvo/deface", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951594993"}
2025-07-08 02:13:15,064 - INFO - Response: {"request_id": "1751951594993", "status_code": 429, "duration_ms": 70}
2025-07-08 02:13:16,391 - INFO - Request: {"timestamp": "2025-07-08T02:13:16.391406", "method": "POST", "path": "/api/alvo/deface", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951596391"}
2025-07-08 02:13:16,441 - INFO - Response: {"request_id": "1751951596391", "status_code": 429, "duration_ms": 49}
2025-07-08 02:13:17,645 - INFO - Request: {"timestamp": "2025-07-08T02:13:17.645747", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951597644"}
2025-07-08 02:13:17,810 - INFO - Response: {"request_id": "1751951597644", "status_code": 200, "duration_ms": 165}
2025-07-08 02:13:18,768 - INFO - Request: {"timestamp": "2025-07-08T02:13:18.768340", "method": "POST", "path": "/api/alvo/deface", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951598768"}
2025-07-08 02:13:18,848 - INFO - Response: {"request_id": "1751951598768", "status_code": 429, "duration_ms": 80}
2025-07-08 02:13:22,394 - INFO - Request: {"timestamp": "2025-07-08T02:13:22.394820", "method": "GET", "path": "/", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951602394"}
2025-07-08 02:13:22,395 - INFO - Response: {"request_id": "1751951602394", "status_code": 200, "duration_ms": 0}
2025-07-08 02:13:22,706 - INFO - Request: {"timestamp": "2025-07-08T02:13:22.705409", "method": "GET", "path": "/static/js/simple-auth.js", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951602705"}
2025-07-08 02:13:22,707 - INFO - Request: {"timestamp": "2025-07-08T02:13:22.707367", "method": "GET", "path": "/static/js/main.js", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951602707"}
2025-07-08 02:13:22,708 - INFO - Response: {"request_id": "1751951602705", "status_code": 304, "duration_ms": 2}
2025-07-08 02:13:22,709 - INFO - Response: {"request_id": "1751951602707", "status_code": 304, "duration_ms": 1}
2025-07-08 02:13:22,959 - INFO - Request: {"timestamp": "2025-07-08T02:13:22.958415", "method": "GET", "path": "/api/csrf-token", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951602958"}
2025-07-08 02:13:22,960 - INFO - Response: {"request_id": "1751951602958", "status_code": 200, "duration_ms": 1}
2025-07-08 02:13:23,028 - INFO - Request: {"timestamp": "2025-07-08T02:13:23.028720", "method": "GET", "path": "/favicon.ico", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951603028"}
2025-07-08 02:13:23,030 - INFO - Response: {"request_id": "1751951603028", "status_code": 200, "duration_ms": 1}
2025-07-08 02:13:23,061 - INFO - Request: {"timestamp": "2025-07-08T02:13:23.061477", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951603061"}
2025-07-08 02:13:23,079 - INFO - Request: {"timestamp": "2025-07-08T02:13:23.079582", "method": "GET", "path": "/api/jogador", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951603079"}
2025-07-08 02:13:23,277 - INFO - Response: {"request_id": "1751951603079", "status_code": 200, "duration_ms": 196}
2025-07-08 02:13:23,292 - INFO - Request: {"timestamp": "2025-07-08T02:13:23.292014", "method": "POST", "path": "/api/mineracao/coletar-offline", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951603292"}
2025-07-08 02:13:23,537 - INFO - Response: {"request_id": "1751951603292", "status_code": 200, "duration_ms": 245}
2025-07-08 02:13:23,572 - INFO - Response: {"request_id": "1751951603061", "status_code": 200, "duration_ms": 511}
2025-07-08 02:13:23,605 - INFO - Request: {"timestamp": "2025-07-08T02:13:23.605082", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951603605"}
2025-07-08 02:13:23,815 - INFO - Response: {"request_id": "1751951603605", "status_code": 200, "duration_ms": 210}
2025-07-08 02:13:29,791 - INFO - Request: {"timestamp": "2025-07-08T02:13:29.791477", "method": "GET", "path": "/api/logs/atividade", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951609790"}
2025-07-08 02:13:30,022 - INFO - Response: {"request_id": "1751951609790", "status_code": 200, "duration_ms": 231}
2025-07-08 02:13:32,564 - INFO - Request: {"timestamp": "2025-07-08T02:13:32.564871", "method": "GET", "path": "/api/logs/atividade", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951612564"}
2025-07-08 02:13:32,962 - INFO - Response: {"request_id": "1751951612564", "status_code": 200, "duration_ms": 398}
2025-07-08 02:13:33,052 - INFO - Request: {"timestamp": "2025-07-08T02:13:33.052733", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951613052"}
2025-07-08 02:13:33,301 - INFO - Request: {"timestamp": "2025-07-08T02:13:33.301734", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951613301"}
2025-07-08 02:13:33,336 - INFO - Response: {"request_id": "1751951613052", "status_code": 200, "duration_ms": 284}
2025-07-08 02:13:33,456 - INFO - Response: {"request_id": "1751951613301", "status_code": 200, "duration_ms": 155}
2025-07-08 02:13:43,354 - INFO - Request: {"timestamp": "2025-07-08T02:13:43.354110", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951623354"}
2025-07-08 02:13:43,659 - INFO - Request: {"timestamp": "2025-07-08T02:13:43.659755", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951623659"}
2025-07-08 02:13:43,735 - INFO - Response: {"request_id": "1751951623354", "status_code": 200, "duration_ms": 381}
2025-07-08 02:13:43,937 - INFO - Response: {"request_id": "1751951623659", "status_code": 200, "duration_ms": 277}
2025-07-08 02:13:51,818 - INFO - Request: {"timestamp": "2025-07-08T02:13:51.818103", "method": "GET", "path": "/api/scan", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951631818"}
2025-07-08 02:13:51,956 - INFO - Response: {"request_id": "1751951631818", "status_code": 200, "duration_ms": 138}
2025-07-08 02:13:53,052 - INFO - Request: {"timestamp": "2025-07-08T02:13:53.052467", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951633052"}
2025-07-08 02:13:53,420 - INFO - Response: {"request_id": "1751951633052", "status_code": 200, "duration_ms": 367}
2025-07-08 02:13:53,602 - INFO - Request: {"timestamp": "2025-07-08T02:13:53.602767", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951633602"}
2025-07-08 02:13:53,793 - INFO - Response: {"request_id": "1751951633602", "status_code": 200, "duration_ms": 190}
2025-07-08 02:13:54,091 - INFO - Request: {"timestamp": "2025-07-08T02:13:54.091518", "method": "POST", "path": "/api/alvo/*************/exploit", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951634091"}
2025-07-08 02:13:54,860 - INFO - Response: {"request_id": "1751951634091", "status_code": 200, "duration_ms": 768}
2025-07-08 02:13:58,121 - INFO - Request: {"timestamp": "2025-07-08T02:13:58.121053", "method": "POST", "path": "/api/alvo/deface", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951638121"}
2025-07-08 02:13:58,577 - INFO - Response: {"request_id": "1751951638121", "status_code": 200, "duration_ms": 456}
2025-07-08 02:13:58,585 - INFO - Request: {"timestamp": "2025-07-08T02:13:58.585611", "method": "GET", "path": "/api/jogador", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951638585"}
2025-07-08 02:13:58,701 - INFO - Response: {"request_id": "1751951638585", "status_code": 200, "duration_ms": 116}
2025-07-08 02:13:59,015 - INFO - Request: {"timestamp": "2025-07-08T02:13:59.015325", "method": "POST", "path": "/api/mineracao/coletar-offline", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951639015"}
2025-07-08 02:13:59,154 - INFO - Response: {"request_id": "1751951639015", "status_code": 200, "duration_ms": 139}
2025-07-08 02:14:02,882 - INFO - Request: {"timestamp": "2025-07-08T02:14:02.882749", "method": "GET", "path": "/api/jogador", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951642882"}
2025-07-08 02:14:03,121 - INFO - Request: {"timestamp": "2025-07-08T02:14:03.121716", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951643121"}
2025-07-08 02:14:03,196 - INFO - Response: {"request_id": "1751951642882", "status_code": 200, "duration_ms": 313}
2025-07-08 02:14:03,395 - INFO - Request: {"timestamp": "2025-07-08T02:14:03.395810", "method": "POST", "path": "/api/mineracao/coletar-offline", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951643395"}
2025-07-08 02:14:03,747 - INFO - Response: {"request_id": "1751951643395", "status_code": 200, "duration_ms": 352}
2025-07-08 02:14:03,908 - INFO - Response: {"request_id": "1751951643121", "status_code": 200, "duration_ms": 786}
2025-07-08 02:14:04,030 - INFO - Request: {"timestamp": "2025-07-08T02:14:04.030165", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951644030"}
2025-07-08 02:14:04,600 - INFO - Response: {"request_id": "1751951644030", "status_code": 200, "duration_ms": 570}
2025-07-08 02:14:13,052 - INFO - Request: {"timestamp": "2025-07-08T02:14:13.052812", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951653052"}
2025-07-08 02:14:13,672 - INFO - Response: {"request_id": "1751951653052", "status_code": 200, "duration_ms": 619}
2025-07-08 02:14:14,324 - INFO - Request: {"timestamp": "2025-07-08T02:14:14.324819", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951654324"}
2025-07-08 02:14:14,555 - INFO - Response: {"request_id": "1751951654324", "status_code": 200, "duration_ms": 230}
2025-07-08 02:14:23,356 - INFO - Request: {"timestamp": "2025-07-08T02:14:23.356512", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951663356"}
2025-07-08 02:14:23,917 - INFO - Response: {"request_id": "1751951663356", "status_code": 200, "duration_ms": 560}
2025-07-08 02:14:24,022 - INFO - Request: {"timestamp": "2025-07-08T02:14:24.022601", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951664022"}
2025-07-08 02:14:24,193 - INFO - Response: {"request_id": "1751951664022", "status_code": 200, "duration_ms": 170}
2025-07-08 02:14:32,702 - INFO - Request: {"timestamp": "2025-07-08T02:14:32.702137", "method": "GET", "path": "/api/torneio/ranking-deface", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951672702"}
2025-07-08 02:14:33,139 - INFO - Response: {"request_id": "1751951672702", "status_code": 200, "duration_ms": 437}
2025-07-08 02:14:33,355 - INFO - Request: {"timestamp": "2025-07-08T02:14:33.354492", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951673354"}
2025-07-08 02:14:33,663 - INFO - Response: {"request_id": "1751951673354", "status_code": 200, "duration_ms": 308}
2025-07-08 02:14:34,326 - INFO - Request: {"timestamp": "2025-07-08T02:14:34.326111", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951674326"}
2025-07-08 02:14:34,596 - INFO - Response: {"request_id": "1751951674326", "status_code": 200, "duration_ms": 270}
2025-07-08 02:14:35,280 - INFO - Request: {"timestamp": "2025-07-08T02:14:35.280754", "method": "GET", "path": "/", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951675280"}
2025-07-08 02:14:35,282 - INFO - Response: {"request_id": "1751951675280", "status_code": 200, "duration_ms": 0}
2025-07-08 02:14:35,530 - INFO - Request: {"timestamp": "2025-07-08T02:14:35.530795", "method": "GET", "path": "/static/js/simple-auth.js", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951675530"}
2025-07-08 02:14:35,532 - INFO - Response: {"request_id": "1751951675530", "status_code": 304, "duration_ms": 1}
2025-07-08 02:14:35,596 - INFO - Request: {"timestamp": "2025-07-08T02:14:35.595244", "method": "GET", "path": "/static/js/main.js", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951675595"}
2025-07-08 02:14:35,596 - INFO - Response: {"request_id": "1751951675595", "status_code": 304, "duration_ms": 1}
2025-07-08 02:14:35,602 - INFO - Request: {"timestamp": "2025-07-08T02:14:35.602603", "method": "GET", "path": "/api/csrf-token", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951675602"}
2025-07-08 02:14:35,603 - INFO - Response: {"request_id": "1751951675602", "status_code": 200, "duration_ms": 0}
2025-07-08 02:14:35,947 - INFO - Request: {"timestamp": "2025-07-08T02:14:35.946398", "method": "GET", "path": "/api/jogador", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951675946"}
2025-07-08 02:14:35,960 - INFO - Request: {"timestamp": "2025-07-08T02:14:35.960072", "method": "GET", "path": "/favicon.ico", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951675960"}
2025-07-08 02:14:35,963 - INFO - Response: {"request_id": "1751951675960", "status_code": 200, "duration_ms": 3}
2025-07-08 02:14:36,044 - INFO - Response: {"request_id": "1751951675946", "status_code": 200, "duration_ms": 97}
2025-07-08 02:14:36,197 - INFO - Request: {"timestamp": "2025-07-08T02:14:36.197357", "method": "POST", "path": "/api/mineracao/coletar-offline", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951676197"}
2025-07-08 02:14:36,353 - INFO - Request: {"timestamp": "2025-07-08T02:14:36.353599", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951676353"}
2025-07-08 02:14:36,461 - INFO - Request: {"timestamp": "2025-07-08T02:14:36.461994", "method": "GET", "path": "/api/grupo", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951676461"}
2025-07-08 02:14:36,778 - INFO - Response: {"request_id": "1751951676197", "status_code": 200, "duration_ms": 581}
2025-07-08 02:14:36,993 - INFO - Response: {"request_id": "1751951676353", "status_code": 200, "duration_ms": 639}
2025-07-08 02:14:37,066 - INFO - Response: {"request_id": "1751951676461", "status_code": 200, "duration_ms": 604}
2025-07-08 02:14:37,374 - INFO - Request: {"timestamp": "2025-07-08T02:14:37.373659", "method": "GET", "path": "/api/torneio/ranking-deface", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951677373"}
2025-07-08 02:14:37,532 - INFO - Response: {"request_id": "1751951677373", "status_code": 200, "duration_ms": 159}
2025-07-08 02:14:37,641 - INFO - Request: {"timestamp": "2025-07-08T02:14:37.641220", "method": "GET", "path": "/api/admin/torneio/info", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951677641"}
2025-07-08 02:14:37,730 - INFO - Response: {"request_id": "1751951677641", "status_code": 403, "duration_ms": 89}
2025-07-08 02:14:38,260 - INFO - Request: {"timestamp": "2025-07-08T02:14:38.260471", "method": "GET", "path": "/api/torneio/ranking-deface", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951678260"}
2025-07-08 02:14:38,458 - INFO - Response: {"request_id": "1751951678260", "status_code": 200, "duration_ms": 198}
2025-07-08 02:14:39,054 - INFO - Request: {"timestamp": "2025-07-08T02:14:39.054948", "method": "GET", "path": "/api/admin/torneio/info", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951679054"}
2025-07-08 02:14:39,152 - INFO - Response: {"request_id": "1751951679054", "status_code": 403, "duration_ms": 97}
2025-07-08 02:14:41,181 - INFO - Request: {"timestamp": "2025-07-08T02:14:41.181652", "method": "GET", "path": "/api/admin/torneio/info", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951681181"}
2025-07-08 02:14:41,284 - INFO - Response: {"request_id": "1751951681181", "status_code": 403, "duration_ms": 102}
2025-07-08 02:14:44,022 - INFO - Request: {"timestamp": "2025-07-08T02:14:44.022188", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951684022"}
2025-07-08 02:14:44,176 - INFO - Response: {"request_id": "1751951684022", "status_code": 200, "duration_ms": 154}
2025-07-08 02:14:47,326 - INFO - Request: {"timestamp": "2025-07-08T02:14:47.326011", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951687326"}
2025-07-08 02:14:47,600 - INFO - Response: {"request_id": "1751951687326", "status_code": 200, "duration_ms": 274}
2025-07-08 02:14:47,877 - INFO - Request: {"timestamp": "2025-07-08T02:14:47.877888", "method": "GET", "path": "/api/jogador", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951687877"}
2025-07-08 02:14:47,964 - INFO - Response: {"request_id": "1751951687877", "status_code": 200, "duration_ms": 86}
2025-07-08 02:14:47,989 - INFO - Request: {"timestamp": "2025-07-08T02:14:47.989209", "method": "POST", "path": "/api/mineracao/coletar-offline", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951687989"}
2025-07-08 02:14:48,079 - INFO - Response: {"request_id": "1751951687989", "status_code": 200, "duration_ms": 89}
2025-07-08 02:14:49,121 - INFO - Request: {"timestamp": "2025-07-08T02:14:49.121039", "method": "POST", "path": "/api/conexoes/status", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951689121"}
2025-07-08 02:14:49,467 - INFO - Response: {"request_id": "1751951689121", "status_code": 200, "duration_ms": 346}
2025-07-08 02:14:49,782 - INFO - Request: {"timestamp": "2025-07-08T02:14:49.782716", "method": "GET", "path": "/api/terminal/history", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951689782"}
2025-07-08 02:14:49,919 - INFO - Response: {"request_id": "1751951689782", "status_code": 200, "duration_ms": 136}
2025-07-08 02:14:50,286 - INFO - Request: {"timestamp": "2025-07-08T02:14:50.286594", "method": "POST", "path": "/api/terminal/bruteforce", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951690286"}
2025-07-08 02:14:50,596 - INFO - Response: {"request_id": "1751951690286", "status_code": 200, "duration_ms": 310}
2025-07-08 02:14:50,906 - INFO - Request: {"timestamp": "2025-07-08T02:14:50.906271", "method": "POST", "path": "/api/conexoes/status", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951690906"}
2025-07-08 02:14:51,208 - INFO - Response: {"request_id": "1751951690906", "status_code": 200, "duration_ms": 302}
2025-07-08 02:14:53,623 - INFO - Request: {"timestamp": "2025-07-08T02:14:53.623173", "method": "POST", "path": "/api/terminal/bruteforce", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951693623"}
2025-07-08 02:14:53,962 - INFO - Response: {"request_id": "1751951693623", "status_code": 200, "duration_ms": 338}
2025-07-08 02:14:54,272 - INFO - Request: {"timestamp": "2025-07-08T02:14:54.272620", "method": "POST", "path": "/api/conexoes/status", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951694272"}
2025-07-08 02:14:54,323 - INFO - Request: {"timestamp": "2025-07-08T02:14:54.323397", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951694323"}
2025-07-08 02:14:54,506 - INFO - Response: {"request_id": "1751951694323", "status_code": 200, "duration_ms": 182}
2025-07-08 02:14:54,602 - INFO - Response: {"request_id": "1751951694272", "status_code": 200, "duration_ms": 330}
2025-07-08 02:14:56,059 - INFO - Request: {"timestamp": "2025-07-08T02:14:56.059410", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951696059"}
2025-07-08 02:14:56,408 - INFO - Response: {"request_id": "1751951696059", "status_code": 200, "duration_ms": 349}
2025-07-08 02:14:57,609 - INFO - Request: {"timestamp": "2025-07-08T02:14:57.609263", "method": "GET", "path": "/api/appstore", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951697609"}
2025-07-08 02:14:57,712 - INFO - Response: {"request_id": "1751951697609", "status_code": 200, "duration_ms": 103}
2025-07-08 02:15:06,052 - INFO - Request: {"timestamp": "2025-07-08T02:15:06.052640", "method": "GET", "path": "/api/jogador", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951706052"}
2025-07-08 02:15:06,059 - INFO - Request: {"timestamp": "2025-07-08T02:15:06.059468", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951706059"}
2025-07-08 02:15:06,421 - INFO - Request: {"timestamp": "2025-07-08T02:15:06.421308", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951706421"}
2025-07-08 02:15:06,431 - INFO - Response: {"request_id": "1751951706052", "status_code": 200, "duration_ms": 378}
2025-07-08 02:15:06,949 - INFO - Response: {"request_id": "1751951706059", "status_code": 200, "duration_ms": 889}
2025-07-08 02:15:06,959 - INFO - Response: {"request_id": "1751951706421", "status_code": 200, "duration_ms": 538}
2025-07-08 02:15:07,003 - INFO - Request: {"timestamp": "2025-07-08T02:15:07.003693", "method": "POST", "path": "/api/mineracao/coletar-offline", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951707003"}
2025-07-08 02:15:07,179 - INFO - Response: {"request_id": "1751951707003", "status_code": 200, "duration_ms": 176}
2025-07-08 02:15:07,348 - INFO - Request: {"timestamp": "2025-07-08T02:15:07.348023", "method": "POST", "path": "/api/conexoes/status", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951707348"}
2025-07-08 02:15:07,757 - INFO - Response: {"request_id": "1751951707348", "status_code": 200, "duration_ms": 408}
2025-07-08 02:15:08,075 - INFO - Request: {"timestamp": "2025-07-08T02:15:08.075164", "method": "GET", "path": "/api/terminal/history", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951708074"}
2025-07-08 02:15:08,302 - INFO - Response: {"request_id": "1751951708074", "status_code": 200, "duration_ms": 228}
2025-07-08 02:15:09,930 - INFO - Request: {"timestamp": "2025-07-08T02:15:09.930308", "method": "POST", "path": "/api/terminal/bruteforce", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951709930"}
2025-07-08 02:15:10,294 - INFO - Response: {"request_id": "1751951709930", "status_code": 200, "duration_ms": 364}
2025-07-08 02:15:10,604 - INFO - Request: {"timestamp": "2025-07-08T02:15:10.604684", "method": "POST", "path": "/api/conexoes/status", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951710604"}
2025-07-08 02:15:11,003 - INFO - Response: {"request_id": "1751951710604", "status_code": 200, "duration_ms": 398}
2025-07-08 02:15:13,303 - INFO - Request: {"timestamp": "2025-07-08T02:15:13.303079", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951713303"}
2025-07-08 02:15:13,476 - INFO - Response: {"request_id": "1751951713303", "status_code": 200, "duration_ms": 173}
2025-07-08 02:15:16,361 - INFO - Request: {"timestamp": "2025-07-08T02:15:16.361068", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951716361"}
2025-07-08 02:15:16,650 - INFO - Response: {"request_id": "1751951716361", "status_code": 200, "duration_ms": 289}
2025-07-08 02:15:20,087 - INFO - Request: {"timestamp": "2025-07-08T02:15:20.087869", "method": "POST", "path": "/api/terminal/bruteforce", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951720087"}
2025-07-08 02:15:20,450 - INFO - Response: {"request_id": "1751951720087", "status_code": 200, "duration_ms": 362}
2025-07-08 02:15:20,459 - INFO - Request: {"timestamp": "2025-07-08T02:15:20.458577", "method": "POST", "path": "/api/conexoes/status", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951720458"}
2025-07-08 02:15:20,861 - INFO - Response: {"request_id": "1751951720458", "status_code": 200, "duration_ms": 401}
2025-07-08 02:15:23,604 - INFO - Request: {"timestamp": "2025-07-08T02:15:23.604543", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951723604"}
2025-07-08 02:15:24,015 - INFO - Response: {"request_id": "1751951723604", "status_code": 200, "duration_ms": 411}
2025-07-08 02:15:25,025 - INFO - Request: {"timestamp": "2025-07-08T02:15:25.025349", "method": "GET", "path": "/api/terminal/connections", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951725025"}
2025-07-08 02:15:25,367 - INFO - Response: {"request_id": "1751951725025", "status_code": 200, "duration_ms": 342}
2025-07-08 02:15:27,325 - INFO - Request: {"timestamp": "2025-07-08T02:15:27.325667", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951727325"}
2025-07-08 02:15:27,626 - INFO - Response: {"request_id": "1751951727325", "status_code": 200, "duration_ms": 300}
2025-07-08 02:15:33,302 - INFO - Request: {"timestamp": "2025-07-08T02:15:33.302414", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951733302"}
2025-07-08 02:15:33,536 - INFO - Response: {"request_id": "1751951733302", "status_code": 200, "duration_ms": 233}
2025-07-08 02:15:37,025 - INFO - Request: {"timestamp": "2025-07-08T02:15:37.025557", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951737025"}
2025-07-08 02:15:37,326 - INFO - Response: {"request_id": "1751951737025", "status_code": 200, "duration_ms": 301}
2025-07-08 02:15:38,650 - INFO - Request: {"timestamp": "2025-07-08T02:15:38.650313", "method": "POST", "path": "/api/conexoes/status", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951738650"}
2025-07-08 02:15:39,080 - INFO - Response: {"request_id": "1751951738650", "status_code": 200, "duration_ms": 430}
2025-07-08 02:15:43,305 - INFO - Request: {"timestamp": "2025-07-08T02:15:43.305630", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951743305"}
2025-07-08 02:15:43,503 - INFO - Response: {"request_id": "1751951743305", "status_code": 200, "duration_ms": 197}
2025-07-08 02:15:47,326 - INFO - Request: {"timestamp": "2025-07-08T02:15:47.326016", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951747326"}
2025-07-08 02:15:47,637 - INFO - Response: {"request_id": "1751951747326", "status_code": 200, "duration_ms": 311}
2025-07-08 02:15:53,607 - INFO - Request: {"timestamp": "2025-07-08T02:15:53.607723", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951753607"}
2025-07-08 02:15:53,836 - INFO - Response: {"request_id": "1751951753607", "status_code": 200, "duration_ms": 229}
2025-07-08 02:15:55,027 - INFO - Request: {"timestamp": "2025-07-08T02:15:55.027503", "method": "GET", "path": "/api/terminal/connections", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951755027"}
2025-07-08 02:15:55,417 - INFO - Response: {"request_id": "1751951755027", "status_code": 200, "duration_ms": 389}
2025-07-08 02:15:57,324 - INFO - Request: {"timestamp": "2025-07-08T02:15:57.324915", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951757324"}
2025-07-08 02:15:57,630 - INFO - Response: {"request_id": "1751951757324", "status_code": 200, "duration_ms": 304}
2025-07-08 02:16:03,303 - INFO - Request: {"timestamp": "2025-07-08T02:16:03.303058", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951763303"}
2025-07-08 02:16:03,624 - INFO - Response: {"request_id": "1751951763303", "status_code": 200, "duration_ms": 321}
2025-07-08 02:16:07,023 - INFO - Request: {"timestamp": "2025-07-08T02:16:07.023460", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951767023"}
2025-07-08 02:16:07,323 - INFO - Response: {"request_id": "1751951767023", "status_code": 200, "duration_ms": 300}
2025-07-08 02:16:08,649 - INFO - Request: {"timestamp": "2025-07-08T02:16:08.649208", "method": "POST", "path": "/api/conexoes/status", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951768649"}
2025-07-08 02:16:09,094 - INFO - Request: {"timestamp": "2025-07-08T02:16:09.094557", "method": "GET", "path": "/api/terminal/connections", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951769094"}
2025-07-08 02:16:09,862 - INFO - Response: {"request_id": "1751951768649", "status_code": 200, "duration_ms": 1213}
2025-07-08 02:16:10,065 - INFO - Response: {"request_id": "1751951769094", "status_code": 200, "duration_ms": 970}
2025-07-08 02:16:13,604 - INFO - Request: {"timestamp": "2025-07-08T02:16:13.604459", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951773604"}
2025-07-08 02:16:13,793 - INFO - Response: {"request_id": "1751951773604", "status_code": 200, "duration_ms": 188}
2025-07-08 02:16:17,364 - INFO - Request: {"timestamp": "2025-07-08T02:16:17.363604", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951777363"}
2025-07-08 02:16:17,938 - INFO - Response: {"request_id": "1751951777363", "status_code": 200, "duration_ms": 574}
2025-07-08 02:16:22,727 - INFO - Request: {"timestamp": "2025-07-08T02:16:22.727348", "method": "GET", "path": "/api/jogador", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951782727"}
2025-07-08 02:16:23,124 - INFO - Response: {"request_id": "1751951782727", "status_code": 200, "duration_ms": 397}
2025-07-08 02:16:23,461 - INFO - Request: {"timestamp": "2025-07-08T02:16:23.461385", "method": "POST", "path": "/api/mineracao/coletar-offline", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951783461"}
2025-07-08 02:16:23,633 - INFO - Response: {"request_id": "1751951783461", "status_code": 200, "duration_ms": 172}
2025-07-08 02:16:23,669 - INFO - Request: {"timestamp": "2025-07-08T02:16:23.668585", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951783668"}
2025-07-08 02:16:23,990 - INFO - Response: {"request_id": "1751951783668", "status_code": 200, "duration_ms": 321}
2025-07-08 02:16:25,022 - INFO - Request: {"timestamp": "2025-07-08T02:16:25.022559", "method": "GET", "path": "/api/terminal/connections", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951785022"}
2025-07-08 02:16:25,391 - INFO - Response: {"request_id": "1751951785022", "status_code": 200, "duration_ms": 369}
2025-07-08 02:16:33,304 - INFO - Request: {"timestamp": "2025-07-08T02:16:33.304186", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951793304"}
2025-07-08 02:16:33,684 - INFO - Response: {"request_id": "1751951793304", "status_code": 200, "duration_ms": 380}
2025-07-08 02:16:38,650 - INFO - Request: {"timestamp": "2025-07-08T02:16:38.650471", "method": "POST", "path": "/api/conexoes/status", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951798650"}
2025-07-08 02:16:39,277 - INFO - Response: {"request_id": "1751951798650", "status_code": 200, "duration_ms": 626}
2025-07-08 02:16:42,340 - INFO - Request: {"timestamp": "2025-07-08T02:16:42.340903", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951802340"}
2025-07-08 02:16:42,653 - INFO - Response: {"request_id": "1751951802340", "status_code": 200, "duration_ms": 312}
2025-07-08 02:16:43,302 - INFO - Request: {"timestamp": "2025-07-08T02:16:43.302075", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951803302"}
2025-07-08 02:16:43,487 - INFO - Response: {"request_id": "1751951803302", "status_code": 200, "duration_ms": 185}
2025-07-08 02:16:53,607 - INFO - Request: {"timestamp": "2025-07-08T02:16:53.606103", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951813606"}
2025-07-08 02:16:54,050 - INFO - Response: {"request_id": "1751951813606", "status_code": 200, "duration_ms": 444}
2025-07-08 02:16:55,025 - INFO - Request: {"timestamp": "2025-07-08T02:16:55.025078", "method": "GET", "path": "/api/terminal/connections", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951815025"}
2025-07-08 02:16:55,379 - INFO - Response: {"request_id": "1751951815025", "status_code": 200, "duration_ms": 354}
2025-07-08 02:17:03,302 - INFO - Request: {"timestamp": "2025-07-08T02:17:03.302885", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951823302"}
2025-07-08 02:17:03,530 - INFO - Response: {"request_id": "1751951823302", "status_code": 200, "duration_ms": 227}
2025-07-08 02:17:08,649 - INFO - Request: {"timestamp": "2025-07-08T02:17:08.649584", "method": "POST", "path": "/api/conexoes/status", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951828649"}
2025-07-08 02:17:09,081 - INFO - Response: {"request_id": "1751951828649", "status_code": 200, "duration_ms": 431}
2025-07-08 02:17:10,313 - INFO - Request: {"timestamp": "2025-07-08T02:17:10.313062", "method": "GET", "path": "/api/terminal/check/**************", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951830313"}
2025-07-08 02:17:10,610 - INFO - Response: {"request_id": "1751951830313", "status_code": 200, "duration_ms": 297}
2025-07-08 02:17:13,604 - INFO - Request: {"timestamp": "2025-07-08T02:17:13.604107", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951833604"}
2025-07-08 02:17:13,761 - INFO - Response: {"request_id": "1751951833604", "status_code": 200, "duration_ms": 157}
2025-07-08 02:17:18,062 - INFO - Activity monitoring initialized
2025-07-08 02:17:20,790 - INFO - Request: {"timestamp": "2025-07-08T02:17:20.790615", "method": "GET", "path": "/api/terminal/check/*************", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951840790"}
2025-07-08 02:17:21,165 - INFO - Response: {"request_id": "1751951840790", "status_code": 200, "duration_ms": 374}
2025-07-08 02:17:23,303 - INFO - Request: {"timestamp": "2025-07-08T02:17:23.303207", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951843303"}
2025-07-08 02:17:23,534 - INFO - Response: {"request_id": "1751951843303", "status_code": 200, "duration_ms": 231}
2025-07-08 02:17:25,325 - INFO - Request: {"timestamp": "2025-07-08T02:17:25.325262", "method": "GET", "path": "/api/terminal/connections", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951845325"}
2025-07-08 02:17:25,728 - INFO - Response: {"request_id": "1751951845325", "status_code": 200, "duration_ms": 403}
2025-07-08 02:17:36,414 - INFO - Activity monitoring initialized
2025-07-08 02:17:36,500 - INFO - Request: {"timestamp": "2025-07-08T02:17:36.500169", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951856500"}
2025-07-08 02:17:36,694 - INFO - Response: {"request_id": "1751951856500", "status_code": 200, "duration_ms": 194}
2025-07-08 02:17:38,376 - INFO - Request: {"timestamp": "2025-07-08T02:17:38.376346", "method": "POST", "path": "/api/conexoes/status", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951858376"}
2025-07-08 02:17:38,973 - INFO - Response: {"request_id": "1751951858376", "status_code": 200, "duration_ms": 597}
2025-07-08 02:17:42,528 - INFO - Request: {"timestamp": "2025-07-08T02:17:42.528439", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951862528"}
2025-07-08 02:17:42,869 - INFO - Response: {"request_id": "1751951862528", "status_code": 200, "duration_ms": 341}
2025-07-08 02:17:43,620 - INFO - Request: {"timestamp": "2025-07-08T02:17:43.620701", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951863620"}
2025-07-08 02:17:43,829 - INFO - Response: {"request_id": "1751951863620", "status_code": 200, "duration_ms": 209}
2025-07-08 02:17:52,721 - INFO - Request: {"timestamp": "2025-07-08T02:17:52.720597", "method": "GET", "path": "/api/jogador", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951872720"}
2025-07-08 02:17:52,870 - INFO - Response: {"request_id": "1751951872720", "status_code": 200, "duration_ms": 149}
2025-07-08 02:17:53,183 - INFO - Request: {"timestamp": "2025-07-08T02:17:53.182215", "method": "POST", "path": "/api/mineracao/coletar-offline", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951873182"}
2025-07-08 02:17:53,333 - INFO - Response: {"request_id": "1751951873182", "status_code": 200, "duration_ms": 151}
2025-07-08 02:17:53,430 - INFO - Request: {"timestamp": "2025-07-08T02:17:53.430913", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951873430"}
2025-07-08 02:17:53,608 - INFO - Response: {"request_id": "1751951873430", "status_code": 200, "duration_ms": 177}
2025-07-08 02:17:55,023 - INFO - Request: {"timestamp": "2025-07-08T02:17:55.023831", "method": "GET", "path": "/api/terminal/connections", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951875023"}
2025-07-08 02:17:55,425 - INFO - Response: {"request_id": "1751951875023", "status_code": 200, "duration_ms": 401}
2025-07-08 02:18:03,312 - INFO - Request: {"timestamp": "2025-07-08T02:18:03.312082", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951883312"}
2025-07-08 02:18:03,558 - INFO - Response: {"request_id": "1751951883312", "status_code": 200, "duration_ms": 246}
2025-07-08 02:18:09,954 - INFO - Activity monitoring initialized
2025-07-08 02:18:10,047 - INFO - Request: {"timestamp": "2025-07-08T02:18:10.047336", "method": "GET", "path": "/api/terminal/connections", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951890047"}
2025-07-08 02:18:10,086 - INFO - Request: {"timestamp": "2025-07-08T02:18:10.086394", "method": "POST", "path": "/api/conexoes/status", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951890086"}
2025-07-08 02:18:10,596 - INFO - Response: {"request_id": "1751951890047", "status_code": 200, "duration_ms": 549}
2025-07-08 02:18:10,710 - INFO - Response: {"request_id": "1751951890086", "status_code": 200, "duration_ms": 624}
2025-07-08 02:18:13,604 - INFO - Request: {"timestamp": "2025-07-08T02:18:13.604695", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951893603"}
2025-07-08 02:18:13,763 - INFO - Response: {"request_id": "1751951893603", "status_code": 200, "duration_ms": 160}
2025-07-08 02:18:24,581 - INFO - Activity monitoring initialized
2025-07-08 02:18:24,668 - INFO - Request: {"timestamp": "2025-07-08T02:18:24.668514", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951904668"}
2025-07-08 02:18:24,830 - INFO - Response: {"request_id": "1751951904668", "status_code": 200, "duration_ms": 161}
2025-07-08 02:18:33,302 - INFO - Request: {"timestamp": "2025-07-08T02:18:33.301532", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951913301"}
2025-07-08 02:18:33,508 - INFO - Response: {"request_id": "1751951913301", "status_code": 200, "duration_ms": 207}
2025-07-08 02:18:38,673 - INFO - Request: {"timestamp": "2025-07-08T02:18:38.673395", "method": "POST", "path": "/api/conexoes/status", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951918673"}
2025-07-08 02:18:39,298 - INFO - Response: {"request_id": "1751951918673", "status_code": 200, "duration_ms": 624}
2025-07-08 02:18:43,654 - INFO - Activity monitoring initialized
2025-07-08 02:18:43,744 - INFO - Request: {"timestamp": "2025-07-08T02:18:43.744592", "method": "GET", "path": "/api/terminal/check/*************", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951923744"}
2025-07-08 02:18:43,747 - INFO - Request: {"timestamp": "2025-07-08T02:18:43.747526", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951923747"}
2025-07-08 02:18:43,750 - INFO - Request: {"timestamp": "2025-07-08T02:18:43.750453", "method": "GET", "path": "/api/terminal/connections", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951923750"}
2025-07-08 02:18:43,756 - INFO - Request: {"timestamp": "2025-07-08T02:18:43.756312", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951923756"}
2025-07-08 02:18:44,008 - INFO - Response: {"request_id": "1751951923756", "status_code": 200, "duration_ms": 252}
2025-07-08 02:18:44,110 - INFO - Response: {"request_id": "1751951923747", "status_code": 200, "duration_ms": 362}
2025-07-08 02:18:44,110 - INFO - Response: {"request_id": "1751951923750", "status_code": 200, "duration_ms": 360}
2025-07-08 02:18:44,111 - INFO - Response: {"request_id": "1751951923744", "status_code": 200, "duration_ms": 367}
2025-07-08 02:18:44,156 - INFO - Request: {"timestamp": "2025-07-08T02:18:44.156445", "method": "POST", "path": "/api/conexoes/status", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951924156"}
2025-07-08 02:18:44,550 - INFO - Response: {"request_id": "1751951924156", "status_code": 200, "duration_ms": 394}
2025-07-08 02:18:54,379 - INFO - Activity monitoring initialized
2025-07-08 02:18:54,461 - INFO - Request: {"timestamp": "2025-07-08T02:18:54.461848", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951934461"}
2025-07-08 02:18:54,628 - INFO - Response: {"request_id": "1751951934461", "status_code": 200, "duration_ms": 166}
2025-07-08 02:19:03,302 - INFO - Request: {"timestamp": "2025-07-08T02:19:03.302067", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951943302"}
2025-07-08 02:19:03,730 - INFO - Response: {"request_id": "1751951943302", "status_code": 200, "duration_ms": 428}
2025-07-08 02:19:08,673 - INFO - Request: {"timestamp": "2025-07-08T02:19:08.672071", "method": "POST", "path": "/api/conexoes/status", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951948672"}
2025-07-08 02:19:09,232 - INFO - Response: {"request_id": "1751951948672", "status_code": 200, "duration_ms": 560}
2025-07-08 02:19:13,302 - INFO - Request: {"timestamp": "2025-07-08T02:19:13.302522", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951953302"}
2025-07-08 02:19:13,476 - INFO - Response: {"request_id": "1751951953302", "status_code": 200, "duration_ms": 173}
2025-07-08 02:19:15,324 - INFO - Request: {"timestamp": "2025-07-08T02:19:15.324822", "method": "GET", "path": "/api/terminal/connections", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951955324"}
2025-07-08 02:19:15,719 - INFO - Response: {"request_id": "1751951955324", "status_code": 200, "duration_ms": 394}
2025-07-08 02:19:23,022 - INFO - Request: {"timestamp": "2025-07-08T02:19:23.022337", "method": "GET", "path": "/api/jogador", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951963022"}
2025-07-08 02:19:23,250 - INFO - Response: {"request_id": "1751951963022", "status_code": 200, "duration_ms": 228}
2025-07-08 02:19:23,276 - INFO - Request: {"timestamp": "2025-07-08T02:19:23.275767", "method": "POST", "path": "/api/mineracao/coletar-offline", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951963275"}
2025-07-08 02:19:23,430 - INFO - Response: {"request_id": "1751951963275", "status_code": 200, "duration_ms": 154}
2025-07-08 02:19:23,559 - INFO - Request: {"timestamp": "2025-07-08T02:19:23.559559", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951963559"}
2025-07-08 02:19:23,722 - INFO - Response: {"request_id": "1751951963559", "status_code": 200, "duration_ms": 162}
2025-07-08 02:19:33,301 - INFO - Request: {"timestamp": "2025-07-08T02:19:33.301754", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951973301"}
2025-07-08 02:19:33,504 - INFO - Response: {"request_id": "1751951973301", "status_code": 200, "duration_ms": 202}
2025-07-08 02:19:38,648 - INFO - Request: {"timestamp": "2025-07-08T02:19:38.648747", "method": "POST", "path": "/api/conexoes/status", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951978648"}
2025-07-08 02:19:39,197 - INFO - Response: {"request_id": "1751951978648", "status_code": 200, "duration_ms": 549}
2025-07-08 02:19:42,034 - INFO - Request: {"timestamp": "2025-07-08T02:19:42.034564", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951982034"}
2025-07-08 02:19:42,304 - INFO - Response: {"request_id": "1751951982034", "status_code": 200, "duration_ms": 270}
2025-07-08 02:19:43,301 - INFO - Request: {"timestamp": "2025-07-08T02:19:43.301857", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951983301"}
2025-07-08 02:19:43,461 - INFO - Response: {"request_id": "1751951983301", "status_code": 200, "duration_ms": 159}
2025-07-08 02:19:45,322 - INFO - Request: {"timestamp": "2025-07-08T02:19:45.322291", "method": "GET", "path": "/api/terminal/connections", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951985322"}
2025-07-08 02:19:45,719 - INFO - Response: {"request_id": "1751951985322", "status_code": 200, "duration_ms": 397}
2025-07-08 02:19:53,602 - INFO - Request: {"timestamp": "2025-07-08T02:19:53.602937", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751951993602"}
2025-07-08 02:19:53,973 - INFO - Response: {"request_id": "1751951993602", "status_code": 200, "duration_ms": 370}
2025-07-08 02:20:03,302 - INFO - Request: {"timestamp": "2025-07-08T02:20:03.302348", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952003302"}
2025-07-08 02:20:03,498 - INFO - Response: {"request_id": "1751952003302", "status_code": 200, "duration_ms": 196}
2025-07-08 02:20:08,650 - INFO - Request: {"timestamp": "2025-07-08T02:20:08.650999", "method": "POST", "path": "/api/conexoes/status", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952008650"}
2025-07-08 02:20:09,192 - INFO - Response: {"request_id": "1751952008650", "status_code": 200, "duration_ms": 541}
2025-07-08 02:20:13,307 - INFO - Request: {"timestamp": "2025-07-08T02:20:13.307379", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952013307"}
2025-07-08 02:20:13,536 - INFO - Response: {"request_id": "1751952013307", "status_code": 200, "duration_ms": 229}
2025-07-08 02:20:15,025 - INFO - Request: {"timestamp": "2025-07-08T02:20:15.025217", "method": "GET", "path": "/api/terminal/connections", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952015024"}
2025-07-08 02:20:15,395 - INFO - Response: {"request_id": "1751952015024", "status_code": 200, "duration_ms": 371}
2025-07-08 02:20:23,603 - INFO - Request: {"timestamp": "2025-07-08T02:20:23.603322", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952023603"}
2025-07-08 02:20:23,831 - INFO - Response: {"request_id": "1751952023603", "status_code": 200, "duration_ms": 228}
2025-07-08 02:20:24,408 - INFO - Request: {"timestamp": "2025-07-08T02:20:24.408476", "method": "GET", "path": "/", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952024408"}
2025-07-08 02:20:24,426 - INFO - Response: {"request_id": "1751952024408", "status_code": 200, "duration_ms": 17}
2025-07-08 02:20:24,710 - INFO - Request: {"timestamp": "2025-07-08T02:20:24.710733", "method": "GET", "path": "/static/js/simple-auth.js", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952024710"}
2025-07-08 02:20:24,743 - INFO - Request: {"timestamp": "2025-07-08T02:20:24.743490", "method": "GET", "path": "/static/js/main.js", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952024743"}
2025-07-08 02:20:24,787 - INFO - Response: {"request_id": "1751952024710", "status_code": 304, "duration_ms": 76}
2025-07-08 02:20:24,823 - INFO - Response: {"request_id": "1751952024743", "status_code": 304, "duration_ms": 79}
2025-07-08 02:20:25,140 - INFO - Request: {"timestamp": "2025-07-08T02:20:25.140731", "method": "GET", "path": "/api/csrf-token", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952025140"}
2025-07-08 02:20:25,156 - INFO - Response: {"request_id": "1751952025140", "status_code": 200, "duration_ms": 16}
2025-07-08 02:20:25,245 - INFO - Request: {"timestamp": "2025-07-08T02:20:25.245302", "method": "GET", "path": "/favicon.ico", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952025245"}
2025-07-08 02:20:25,247 - INFO - Response: {"request_id": "1751952025245", "status_code": 200, "duration_ms": 1}
2025-07-08 02:20:25,387 - INFO - Request: {"timestamp": "2025-07-08T02:20:25.387484", "method": "GET", "path": "/api/jogador", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952025387"}
2025-07-08 02:20:25,540 - INFO - Response: {"request_id": "1751952025387", "status_code": 200, "duration_ms": 153}
2025-07-08 02:20:25,551 - INFO - Request: {"timestamp": "2025-07-08T02:20:25.551542", "method": "POST", "path": "/api/mineracao/coletar-offline", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952025551"}
2025-07-08 02:20:25,713 - INFO - Response: {"request_id": "1751952025551", "status_code": 200, "duration_ms": 160}
2025-07-08 02:20:25,862 - INFO - Request: {"timestamp": "2025-07-08T02:20:25.862147", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952025862"}
2025-07-08 02:20:26,262 - INFO - Response: {"request_id": "1751952025862", "status_code": 200, "duration_ms": 400}
2025-07-08 02:20:26,283 - INFO - Request: {"timestamp": "2025-07-08T02:20:26.283102", "method": "GET", "path": "/api/grupo", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952026283"}
2025-07-08 02:20:26,670 - INFO - Response: {"request_id": "1751952026283", "status_code": 200, "duration_ms": 387}
2025-07-08 02:20:26,979 - INFO - Request: {"timestamp": "2025-07-08T02:20:26.979005", "method": "GET", "path": "/api/torneio/ranking-deface", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952026979"}
2025-07-08 02:20:27,184 - INFO - Response: {"request_id": "1751952026979", "status_code": 200, "duration_ms": 205}
2025-07-08 02:20:27,231 - INFO - Request: {"timestamp": "2025-07-08T02:20:27.231916", "method": "GET", "path": "/api/admin/torneio/info", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952027231"}
2025-07-08 02:20:27,411 - INFO - Response: {"request_id": "1751952027231", "status_code": 200, "duration_ms": 179}
2025-07-08 02:20:28,888 - INFO - Request: {"timestamp": "2025-07-08T02:20:28.888294", "method": "GET", "path": "/api/torneio/ranking-deface", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952028888"}
2025-07-08 02:20:29,145 - INFO - Response: {"request_id": "1751952028888", "status_code": 200, "duration_ms": 256}
2025-07-08 02:20:32,388 - INFO - Request: {"timestamp": "2025-07-08T02:20:32.388234", "method": "GET", "path": "/api/torneio/ranking-deface", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952032388"}
2025-07-08 02:20:32,571 - INFO - Response: {"request_id": "1751952032388", "status_code": 200, "duration_ms": 183}
2025-07-08 02:20:32,638 - INFO - Request: {"timestamp": "2025-07-08T02:20:32.638218", "method": "GET", "path": "/api/ranking/deface-individual", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952032638"}
2025-07-08 02:20:32,806 - INFO - Response: {"request_id": "1751952032638", "status_code": 200, "duration_ms": 167}
2025-07-08 02:20:33,821 - INFO - Request: {"timestamp": "2025-07-08T02:20:33.821929", "method": "GET", "path": "/", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952033821"}
2025-07-08 02:20:33,822 - INFO - Response: {"request_id": "1751952033821", "status_code": 200, "duration_ms": 0}
2025-07-08 02:20:34,123 - INFO - Request: {"timestamp": "2025-07-08T02:20:34.123285", "method": "GET", "path": "/static/js/simple-auth.js", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952034123"}
2025-07-08 02:20:34,125 - INFO - Response: {"request_id": "1751952034123", "status_code": 304, "duration_ms": 1}
2025-07-08 02:20:34,143 - INFO - Request: {"timestamp": "2025-07-08T02:20:34.143815", "method": "GET", "path": "/static/js/main.js", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952034143"}
2025-07-08 02:20:34,145 - INFO - Response: {"request_id": "1751952034143", "status_code": 304, "duration_ms": 1}
2025-07-08 02:20:34,464 - INFO - Request: {"timestamp": "2025-07-08T02:20:34.464692", "method": "GET", "path": "/api/csrf-token", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952034464"}
2025-07-08 02:20:34,465 - INFO - Response: {"request_id": "1751952034464", "status_code": 200, "duration_ms": 0}
2025-07-08 02:20:34,578 - INFO - Request: {"timestamp": "2025-07-08T02:20:34.577159", "method": "GET", "path": "/favicon.ico", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952034577"}
2025-07-08 02:20:34,582 - INFO - Response: {"request_id": "1751952034577", "status_code": 200, "duration_ms": 3}
2025-07-08 02:20:34,715 - INFO - Request: {"timestamp": "2025-07-08T02:20:34.715538", "method": "GET", "path": "/api/jogador", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952034715"}
2025-07-08 02:20:34,854 - INFO - Response: {"request_id": "1751952034715", "status_code": 200, "duration_ms": 139}
2025-07-08 02:20:34,866 - INFO - Request: {"timestamp": "2025-07-08T02:20:34.866445", "method": "POST", "path": "/api/mineracao/coletar-offline", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952034866"}
2025-07-08 02:20:35,031 - INFO - Response: {"request_id": "1751952034866", "status_code": 200, "duration_ms": 165}
2025-07-08 02:20:35,175 - INFO - Request: {"timestamp": "2025-07-08T02:20:35.175603", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952035175"}
2025-07-08 02:20:35,344 - INFO - Response: {"request_id": "1751952035175", "status_code": 200, "duration_ms": 168}
2025-07-08 02:20:36,089 - INFO - Request: {"timestamp": "2025-07-08T02:20:36.089874", "method": "GET", "path": "/api/grupo", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952036089"}
2025-07-08 02:20:36,504 - INFO - Response: {"request_id": "1751952036089", "status_code": 200, "duration_ms": 414}
2025-07-08 02:20:36,510 - INFO - Request: {"timestamp": "2025-07-08T02:20:36.509367", "method": "GET", "path": "/api/torneio/ranking-deface", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952036509"}
2025-07-08 02:20:36,667 - INFO - Response: {"request_id": "1751952036509", "status_code": 200, "duration_ms": 158}
2025-07-08 02:20:36,978 - INFO - Request: {"timestamp": "2025-07-08T02:20:36.978086", "method": "GET", "path": "/api/admin/torneio/info", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952036978"}
2025-07-08 02:20:37,243 - INFO - Response: {"request_id": "1751952036978", "status_code": 200, "duration_ms": 265}
2025-07-08 02:20:38,216 - INFO - Request: {"timestamp": "2025-07-08T02:20:38.216387", "method": "GET", "path": "/api/torneio/ranking-deface", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952038216"}
2025-07-08 02:20:38,401 - INFO - Response: {"request_id": "1751952038216", "status_code": 200, "duration_ms": 185}
2025-07-08 02:20:38,711 - INFO - Request: {"timestamp": "2025-07-08T02:20:38.711031", "method": "GET", "path": "/api/ranking/deface-individual", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952038711"}
2025-07-08 02:20:38,877 - INFO - Response: {"request_id": "1751952038711", "status_code": 200, "duration_ms": 165}
2025-07-08 02:20:42,328 - INFO - Request: {"timestamp": "2025-07-08T02:20:42.328524", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952042328"}
2025-07-08 02:20:42,572 - INFO - Response: {"request_id": "1751952042328", "status_code": 200, "duration_ms": 244}
2025-07-08 02:20:44,881 - INFO - Request: {"timestamp": "2025-07-08T02:20:44.881181", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952044881"}
2025-07-08 02:20:45,020 - INFO - Request: {"timestamp": "2025-07-08T02:20:45.020408", "method": "GET", "path": "/api/terminal/connections", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952045020"}
2025-07-08 02:20:45,034 - INFO - Response: {"request_id": "1751952044881", "status_code": 200, "duration_ms": 152}
2025-07-08 02:20:45,405 - INFO - Response: {"request_id": "1751952045020", "status_code": 200, "duration_ms": 385}
2025-07-08 02:20:50,214 - INFO - Request: {"timestamp": "2025-07-08T02:20:50.214424", "method": "GET", "path": "/api/jogador", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952050214"}
2025-07-08 02:20:50,236 - INFO - Request: {"timestamp": "2025-07-08T02:20:50.236885", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952050236"}
2025-07-08 02:20:50,462 - INFO - Response: {"request_id": "1751952050214", "status_code": 200, "duration_ms": 248}
2025-07-08 02:20:50,509 - INFO - Request: {"timestamp": "2025-07-08T02:20:50.509332", "method": "POST", "path": "/api/mineracao/coletar-offline", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952050509"}
2025-07-08 02:20:50,785 - INFO - Response: {"request_id": "1751952050509", "status_code": 200, "duration_ms": 276}
2025-07-08 02:20:50,816 - INFO - Response: {"request_id": "1751952050236", "status_code": 200, "duration_ms": 580}
2025-07-08 02:20:51,534 - INFO - Request: {"timestamp": "2025-07-08T02:20:51.534650", "method": "GET", "path": "/api/jogador", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952051534"}
2025-07-08 02:20:51,634 - INFO - Response: {"request_id": "1751952051534", "status_code": 200, "duration_ms": 99}
2025-07-08 02:20:51,786 - INFO - Request: {"timestamp": "2025-07-08T02:20:51.786122", "method": "POST", "path": "/api/mineracao/coletar-offline", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952051786"}
2025-07-08 02:20:51,874 - INFO - Response: {"request_id": "1751952051786", "status_code": 200, "duration_ms": 87}
2025-07-08 02:20:53,027 - INFO - Request: {"timestamp": "2025-07-08T02:20:53.027974", "method": "GET", "path": "/", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952053027"}
2025-07-08 02:20:53,028 - INFO - Response: {"request_id": "1751952053027", "status_code": 200, "duration_ms": 0}
2025-07-08 02:20:53,347 - INFO - Request: {"timestamp": "2025-07-08T02:20:53.347821", "method": "GET", "path": "/static/js/simple-auth.js", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952053347"}
2025-07-08 02:20:53,348 - INFO - Request: {"timestamp": "2025-07-08T02:20:53.348795", "method": "GET", "path": "/static/js/main.js", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952053348"}
2025-07-08 02:20:53,349 - INFO - Response: {"request_id": "1751952053347", "status_code": 304, "duration_ms": 1}
2025-07-08 02:20:53,350 - INFO - Response: {"request_id": "1751952053348", "status_code": 304, "duration_ms": 1}
2025-07-08 02:20:53,599 - INFO - Request: {"timestamp": "2025-07-08T02:20:53.599758", "method": "GET", "path": "/api/csrf-token", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952053599"}
2025-07-08 02:20:53,600 - INFO - Response: {"request_id": "1751952053599", "status_code": 200, "duration_ms": 0}
2025-07-08 02:20:53,668 - INFO - Request: {"timestamp": "2025-07-08T02:20:53.668115", "method": "GET", "path": "/favicon.ico", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952053668"}
2025-07-08 02:20:53,669 - INFO - Response: {"request_id": "1751952053668", "status_code": 200, "duration_ms": 0}
2025-07-08 02:20:53,732 - INFO - Request: {"timestamp": "2025-07-08T02:20:53.732560", "method": "GET", "path": "/api/jogador", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952053732"}
2025-07-08 02:20:53,835 - INFO - Response: {"request_id": "1751952053732", "status_code": 200, "duration_ms": 102}
2025-07-08 02:20:53,923 - INFO - Request: {"timestamp": "2025-07-08T02:20:53.923958", "method": "POST", "path": "/api/mineracao/coletar-offline", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952053923"}
2025-07-08 02:20:54,037 - INFO - Response: {"request_id": "1751952053923", "status_code": 200, "duration_ms": 113}
2025-07-08 02:20:54,146 - INFO - Request: {"timestamp": "2025-07-08T02:20:54.146596", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952054146"}
2025-07-08 02:20:54,446 - INFO - Response: {"request_id": "1751952054146", "status_code": 200, "duration_ms": 299}
2025-07-08 02:20:55,188 - INFO - Request: {"timestamp": "2025-07-08T02:20:55.188145", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952055188"}
2025-07-08 02:20:55,336 - INFO - Response: {"request_id": "1751952055188", "status_code": 200, "duration_ms": 148}
2025-07-08 02:20:58,938 - INFO - Request: {"timestamp": "2025-07-08T02:20:58.938167", "method": "GET", "path": "/api/scan", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952058938"}
2025-07-08 02:20:59,027 - INFO - Response: {"request_id": "1751952058938", "status_code": 200, "duration_ms": 88}
2025-07-08 02:21:04,171 - INFO - Request: {"timestamp": "2025-07-08T02:21:04.171669", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952064171"}
2025-07-08 02:21:04,496 - INFO - Response: {"request_id": "1751952064171", "status_code": 200, "duration_ms": 324}
2025-07-08 02:21:05,025 - INFO - Request: {"timestamp": "2025-07-08T02:21:05.024724", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952065024"}
2025-07-08 02:21:05,196 - INFO - Response: {"request_id": "1751952065024", "status_code": 200, "duration_ms": 171}
2025-07-08 02:21:14,023 - INFO - Request: {"timestamp": "2025-07-08T02:21:14.023418", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952074023"}
2025-07-08 02:21:14,387 - INFO - Response: {"request_id": "1751952074023", "status_code": 200, "duration_ms": 364}
2025-07-08 02:21:15,325 - INFO - Request: {"timestamp": "2025-07-08T02:21:15.325096", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952075325"}
2025-07-08 02:21:15,487 - INFO - Response: {"request_id": "1751952075325", "status_code": 200, "duration_ms": 162}
2025-07-08 02:21:24,322 - INFO - Request: {"timestamp": "2025-07-08T02:21:24.322511", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952084322"}
2025-07-08 02:21:24,646 - INFO - Response: {"request_id": "1751952084322", "status_code": 200, "duration_ms": 324}
2025-07-08 02:21:25,024 - INFO - Request: {"timestamp": "2025-07-08T02:21:25.024158", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952085024"}
2025-07-08 02:21:25,171 - INFO - Response: {"request_id": "1751952085024", "status_code": 200, "duration_ms": 147}
2025-07-08 02:21:29,671 - INFO - Request: {"timestamp": "2025-07-08T02:21:29.671279", "method": "GET", "path": "/api/jogador", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952089671"}
2025-07-08 02:21:29,918 - INFO - Response: {"request_id": "1751952089671", "status_code": 200, "duration_ms": 247}
2025-07-08 02:21:30,227 - INFO - Request: {"timestamp": "2025-07-08T02:21:30.227938", "method": "POST", "path": "/api/mineracao/coletar-offline", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952090227"}
2025-07-08 02:21:30,319 - INFO - Response: {"request_id": "1751952090227", "status_code": 200, "duration_ms": 91}
2025-07-08 02:21:33,865 - INFO - Request: {"timestamp": "2025-07-08T02:21:33.864213", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952093864"}
2025-07-08 02:21:34,145 - INFO - Response: {"request_id": "1751952093864", "status_code": 200, "duration_ms": 281}
2025-07-08 02:21:35,327 - INFO - Request: {"timestamp": "2025-07-08T02:21:35.327737", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952095327"}
2025-07-08 02:21:35,495 - INFO - Response: {"request_id": "1751952095327", "status_code": 200, "duration_ms": 167}
2025-07-08 02:21:44,171 - INFO - Request: {"timestamp": "2025-07-08T02:21:44.171672", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952104171"}
2025-07-08 02:21:44,835 - INFO - Request: {"timestamp": "2025-07-08T02:21:44.834323", "method": "GET", "path": "/", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952104834"}
2025-07-08 02:21:44,836 - INFO - Response: {"request_id": "1751952104834", "status_code": 200, "duration_ms": 1}
2025-07-08 02:21:44,974 - INFO - Response: {"request_id": "1751952104171", "status_code": 200, "duration_ms": 803}
2025-07-08 02:21:45,029 - INFO - Request: {"timestamp": "2025-07-08T02:21:45.028253", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952105028"}
2025-07-08 02:21:45,202 - INFO - Request: {"timestamp": "2025-07-08T02:21:45.202230", "method": "GET", "path": "/static/js/simple-auth.js", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952105202"}
2025-07-08 02:21:45,205 - INFO - Response: {"request_id": "1751952105202", "status_code": 304, "duration_ms": 2}
2025-07-08 02:21:45,217 - INFO - Request: {"timestamp": "2025-07-08T02:21:45.217401", "method": "GET", "path": "/static/js/main.js", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952105217"}
2025-07-08 02:21:45,219 - INFO - Response: {"request_id": "1751952105217", "status_code": 304, "duration_ms": 1}
2025-07-08 02:21:45,268 - INFO - Response: {"request_id": "1751952105028", "status_code": 200, "duration_ms": 239}
2025-07-08 02:21:45,407 - INFO - Request: {"timestamp": "2025-07-08T02:21:45.407450", "method": "GET", "path": "/api/csrf-token", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952105407"}
2025-07-08 02:21:45,410 - INFO - Response: {"request_id": "1751952105407", "status_code": 200, "duration_ms": 2}
2025-07-08 02:21:45,533 - INFO - Request: {"timestamp": "2025-07-08T02:21:45.533040", "method": "GET", "path": "/favicon.ico", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952105533"}
2025-07-08 02:21:45,535 - INFO - Response: {"request_id": "1751952105533", "status_code": 200, "duration_ms": 2}
2025-07-08 02:21:45,596 - INFO - Request: {"timestamp": "2025-07-08T02:21:45.596591", "method": "GET", "path": "/api/jogador", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952105596"}
2025-07-08 02:21:45,713 - INFO - Response: {"request_id": "1751952105596", "status_code": 200, "duration_ms": 116}
2025-07-08 02:21:45,726 - INFO - Request: {"timestamp": "2025-07-08T02:21:45.726998", "method": "POST", "path": "/api/mineracao/coletar-offline", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952105726"}
2025-07-08 02:21:45,839 - INFO - Response: {"request_id": "1751952105726", "status_code": 200, "duration_ms": 112}
2025-07-08 02:21:46,032 - INFO - Request: {"timestamp": "2025-07-08T02:21:46.032637", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952106032"}
2025-07-08 02:21:46,325 - INFO - Response: {"request_id": "1751952106032", "status_code": 200, "duration_ms": 292}
2025-07-08 02:21:47,496 - INFO - Request: {"timestamp": "2025-07-08T02:21:47.496298", "method": "GET", "path": "/api/scan", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952107496"}
2025-07-08 02:21:47,577 - INFO - Response: {"request_id": "1751952107496", "status_code": 200, "duration_ms": 81}
2025-07-08 02:21:50,693 - INFO - Request: {"timestamp": "2025-07-08T02:21:50.693585", "method": "POST", "path": "/api/alvo/12*******/exploit", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952110693"}
2025-07-08 02:21:51,314 - INFO - Response: {"request_id": "1751952110693", "status_code": 200, "duration_ms": 619}
2025-07-08 02:21:54,340 - INFO - Request: {"timestamp": "2025-07-08T02:21:54.340076", "method": "POST", "path": "/api/alvo/deface", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952114340"}
2025-07-08 02:21:54,705 - INFO - Response: {"request_id": "1751952114340", "status_code": 200, "duration_ms": 365}
2025-07-08 02:21:54,711 - INFO - Request: {"timestamp": "2025-07-08T02:21:54.711287", "method": "GET", "path": "/api/jogador", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952114711"}
2025-07-08 02:21:54,799 - INFO - Response: {"request_id": "1751952114711", "status_code": 200, "duration_ms": 87}
2025-07-08 02:21:55,107 - INFO - Request: {"timestamp": "2025-07-08T02:21:55.107744", "method": "POST", "path": "/api/mineracao/coletar-offline", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952115107"}
2025-07-08 02:21:55,227 - INFO - Response: {"request_id": "1751952115107", "status_code": 200, "duration_ms": 120}
2025-07-08 02:21:55,328 - INFO - Request: {"timestamp": "2025-07-08T02:21:55.327455", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952115327"}
2025-07-08 02:21:55,538 - INFO - Response: {"request_id": "1751952115327", "status_code": 200, "duration_ms": 210}
2025-07-08 02:21:55,730 - INFO - Request: {"timestamp": "2025-07-08T02:21:55.730751", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952115730"}
2025-07-08 02:21:56,015 - INFO - Response: {"request_id": "1751952115730", "status_code": 200, "duration_ms": 284}
2025-07-08 02:21:57,787 - INFO - Request: {"timestamp": "2025-07-08T02:21:57.787955", "method": "GET", "path": "/", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952117787"}
2025-07-08 02:21:57,788 - INFO - Response: {"request_id": "1751952117787", "status_code": 200, "duration_ms": 0}
2025-07-08 02:21:58,038 - INFO - Request: {"timestamp": "2025-07-08T02:21:58.038915", "method": "GET", "path": "/static/js/simple-auth.js", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952118038"}
2025-07-08 02:21:58,040 - INFO - Response: {"request_id": "1751952118038", "status_code": 304, "duration_ms": 1}
2025-07-08 02:21:58,102 - INFO - Request: {"timestamp": "2025-07-08T02:21:58.101928", "method": "GET", "path": "/static/js/main.js", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952118101"}
2025-07-08 02:21:58,103 - INFO - Response: {"request_id": "1751952118101", "status_code": 304, "duration_ms": 1}
2025-07-08 02:21:58,111 - INFO - Request: {"timestamp": "2025-07-08T02:21:58.111694", "method": "GET", "path": "/api/csrf-token", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952118111"}
2025-07-08 02:21:58,114 - INFO - Response: {"request_id": "1751952118111", "status_code": 200, "duration_ms": 2}
2025-07-08 02:21:58,488 - INFO - Request: {"timestamp": "2025-07-08T02:21:58.488699", "method": "GET", "path": "/api/jogador", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952118487"}
2025-07-08 02:21:58,511 - INFO - Request: {"timestamp": "2025-07-08T02:21:58.508232", "method": "GET", "path": "/favicon.ico", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952118508"}
2025-07-08 02:21:58,513 - INFO - Response: {"request_id": "1751952118508", "status_code": 200, "duration_ms": 4}
2025-07-08 02:21:58,582 - INFO - Response: {"request_id": "1751952118487", "status_code": 200, "duration_ms": 94}
2025-07-08 02:21:58,740 - INFO - Request: {"timestamp": "2025-07-08T02:21:58.740636", "method": "POST", "path": "/api/mineracao/coletar-offline", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952118740"}
2025-07-08 02:21:58,832 - INFO - Response: {"request_id": "1751952118740", "status_code": 200, "duration_ms": 91}
2025-07-08 02:21:58,897 - INFO - Request: {"timestamp": "2025-07-08T02:21:58.897387", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952118897"}
2025-07-08 02:21:59,096 - INFO - Request: {"timestamp": "2025-07-08T02:21:59.096661", "method": "GET", "path": "/api/grupo", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952119096"}
2025-07-08 02:21:59,180 - INFO - Response: {"request_id": "1751952118897", "status_code": 200, "duration_ms": 282}
2025-07-08 02:21:59,456 - INFO - Response: {"request_id": "1751952119096", "status_code": 200, "duration_ms": 359}
2025-07-08 02:21:59,769 - INFO - Request: {"timestamp": "2025-07-08T02:21:59.769146", "method": "GET", "path": "/api/torneio/ranking-deface", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952119769"}
2025-07-08 02:21:59,935 - INFO - Response: {"request_id": "1751952119769", "status_code": 200, "duration_ms": 166}
2025-07-08 02:22:00,021 - INFO - Request: {"timestamp": "2025-07-08T02:22:00.021084", "method": "GET", "path": "/api/admin/torneio/info", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952120021"}
2025-07-08 02:22:00,109 - INFO - Response: {"request_id": "1751952120021", "status_code": 403, "duration_ms": 88}
2025-07-08 02:22:00,906 - INFO - Request: {"timestamp": "2025-07-08T02:22:00.906372", "method": "GET", "path": "/api/torneio/ranking-deface", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952120906"}
2025-07-08 02:22:01,060 - INFO - Response: {"request_id": "1751952120906", "status_code": 200, "duration_ms": 154}
2025-07-08 02:22:01,417 - INFO - Request: {"timestamp": "2025-07-08T02:22:01.417817", "method": "GET", "path": "/api/admin/torneio/info", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952121417"}
2025-07-08 02:22:01,503 - INFO - Response: {"request_id": "1751952121417", "status_code": 403, "duration_ms": 85}
2025-07-08 02:22:02,634 - INFO - Request: {"timestamp": "2025-07-08T02:22:02.634793", "method": "GET", "path": "/api/torneio/ranking-deface", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952122634"}
2025-07-08 02:22:02,791 - INFO - Response: {"request_id": "1751952122634", "status_code": 200, "duration_ms": 156}
2025-07-08 02:22:03,099 - INFO - Request: {"timestamp": "2025-07-08T02:22:03.099606", "method": "GET", "path": "/api/ranking/deface-individual", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952123099"}
2025-07-08 02:22:03,271 - INFO - Response: {"request_id": "1751952123099", "status_code": 200, "duration_ms": 171}
2025-07-08 02:22:03,511 - INFO - Request: {"timestamp": "2025-07-08T02:22:03.511687", "method": "GET", "path": "/api/admin/torneio/info", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952123511"}
2025-07-08 02:22:03,512 - ERROR - SECURITY ALERT: Suspicious activity: 21 requests in 10s from IP 12*******
2025-07-08 02:22:03,595 - INFO - Response: {"request_id": "1751952123511", "status_code": 403, "duration_ms": 83}
2025-07-08 02:22:08,917 - INFO - Request: {"timestamp": "2025-07-08T02:22:08.916378", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952128916"}
2025-07-08 02:22:09,384 - INFO - Response: {"request_id": "1751952128916", "status_code": 200, "duration_ms": 466}
2025-07-08 02:22:19,023 - INFO - Request: {"timestamp": "2025-07-08T02:22:19.023971", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952139023"}
2025-07-08 02:22:19,333 - INFO - Response: {"request_id": "1751952139023", "status_code": 200, "duration_ms": 309}
2025-07-08 02:22:29,323 - INFO - Request: {"timestamp": "2025-07-08T02:22:29.323312", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952149323"}
2025-07-08 02:22:29,637 - INFO - Response: {"request_id": "1751952149323", "status_code": 200, "duration_ms": 314}
2025-07-08 02:22:39,023 - INFO - Request: {"timestamp": "2025-07-08T02:22:39.023224", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952159023"}
2025-07-08 02:22:39,352 - INFO - Response: {"request_id": "1751952159023", "status_code": 200, "duration_ms": 328}
2025-07-08 02:22:42,078 - INFO - Request: {"timestamp": "2025-07-08T02:22:42.078084", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952162078"}
2025-07-08 02:22:42,304 - INFO - Response: {"request_id": "1751952162078", "status_code": 200, "duration_ms": 226}
2025-07-08 02:22:49,325 - INFO - Request: {"timestamp": "2025-07-08T02:22:49.325098", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952169325"}
2025-07-08 02:22:49,661 - INFO - Response: {"request_id": "1751952169325", "status_code": 200, "duration_ms": 335}
2025-07-08 02:22:59,022 - INFO - Request: {"timestamp": "2025-07-08T02:22:59.021484", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952179021"}
2025-07-08 02:22:59,399 - INFO - Response: {"request_id": "1751952179021", "status_code": 200, "duration_ms": 377}
2025-07-08 02:23:09,462 - INFO - Request: {"timestamp": "2025-07-08T02:23:09.462670", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952189462"}
2025-07-08 02:23:10,914 - INFO - Response: {"request_id": "1751952189462", "status_code": 200, "duration_ms": 1452}
2025-07-08 02:23:42,024 - INFO - Request: {"timestamp": "2025-07-08T02:23:42.024973", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952222024"}
2025-07-08 02:23:42,332 - INFO - Request: {"timestamp": "2025-07-08T02:23:42.331590", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952222331"}
2025-07-08 02:23:42,516 - INFO - Response: {"request_id": "1751952222331", "status_code": 200, "duration_ms": 185}
2025-07-08 02:23:42,557 - INFO - Response: {"request_id": "1751952222024", "status_code": 200, "duration_ms": 532}
2025-07-08 02:23:56,037 - INFO - Request: {"timestamp": "2025-07-08T02:23:56.036669", "method": "GET", "path": "/api/jogador", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952236036"}
2025-07-08 02:23:56,337 - INFO - Request: {"timestamp": "2025-07-08T02:23:56.337424", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952236337"}
2025-07-08 02:23:56,455 - INFO - Response: {"request_id": "1751952236036", "status_code": 200, "duration_ms": 418}
2025-07-08 02:23:56,567 - INFO - Response: {"request_id": "1751952236337", "status_code": 200, "duration_ms": 230}
2025-07-08 02:23:56,768 - INFO - Request: {"timestamp": "2025-07-08T02:23:56.768107", "method": "POST", "path": "/api/mineracao/coletar-offline", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952236768"}
2025-07-08 02:23:56,905 - INFO - Response: {"request_id": "1751952236768", "status_code": 200, "duration_ms": 137}
2025-07-08 02:23:57,320 - INFO - Request: {"timestamp": "2025-07-08T02:23:57.320809", "method": "GET", "path": "/", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952237320"}
2025-07-08 02:23:57,322 - INFO - Response: {"request_id": "1751952237320", "status_code": 200, "duration_ms": 1}
2025-07-08 02:23:57,365 - INFO - Request: {"timestamp": "2025-07-08T02:23:57.365734", "method": "GET", "path": "/static/js/simple-auth.js", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952237365"}
2025-07-08 02:23:57,367 - INFO - Response: {"request_id": "1751952237365", "status_code": 304, "duration_ms": 1}
2025-07-08 02:23:57,570 - INFO - Request: {"timestamp": "2025-07-08T02:23:57.570795", "method": "GET", "path": "/static/js/main.js", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952237570"}
2025-07-08 02:23:57,571 - INFO - Response: {"request_id": "1751952237570", "status_code": 304, "duration_ms": 0}
2025-07-08 02:23:57,887 - INFO - Request: {"timestamp": "2025-07-08T02:23:57.887197", "method": "GET", "path": "/api/csrf-token", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952237887"}
2025-07-08 02:23:57,888 - INFO - Response: {"request_id": "1751952237887", "status_code": 200, "duration_ms": 0}
2025-07-08 02:23:57,954 - INFO - Request: {"timestamp": "2025-07-08T02:23:57.954561", "method": "GET", "path": "/favicon.ico", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952237954"}
2025-07-08 02:23:57,956 - INFO - Response: {"request_id": "1751952237954", "status_code": 200, "duration_ms": 1}
2025-07-08 02:23:58,139 - INFO - Request: {"timestamp": "2025-07-08T02:23:58.138142", "method": "GET", "path": "/api/jogador", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952238138"}
2025-07-08 02:23:58,270 - INFO - Response: {"request_id": "1751952238138", "status_code": 200, "duration_ms": 132}
2025-07-08 02:23:58,285 - INFO - Request: {"timestamp": "2025-07-08T02:23:58.285605", "method": "POST", "path": "/api/mineracao/coletar-offline", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952238285"}
2025-07-08 02:23:58,430 - INFO - Response: {"request_id": "1751952238285", "status_code": 200, "duration_ms": 144}
2025-07-08 02:23:58,601 - INFO - Request: {"timestamp": "2025-07-08T02:23:58.601113", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952238601"}
2025-07-08 02:23:58,743 - INFO - Response: {"request_id": "1751952238601", "status_code": 200, "duration_ms": 142}
2025-07-08 02:23:59,072 - INFO - Request: {"timestamp": "2025-07-08T02:23:59.070579", "method": "GET", "path": "/api/grupo", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952239070"}
2025-07-08 02:23:59,466 - INFO - Response: {"request_id": "1751952239070", "status_code": 200, "duration_ms": 395}
2025-07-08 02:23:59,472 - INFO - Request: {"timestamp": "2025-07-08T02:23:59.472895", "method": "GET", "path": "/api/torneio/ranking-deface", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952239472"}
2025-07-08 02:23:59,645 - INFO - Response: {"request_id": "1751952239472", "status_code": 200, "duration_ms": 171}
2025-07-08 02:24:00,020 - INFO - Request: {"timestamp": "2025-07-08T02:24:00.020713", "method": "GET", "path": "/api/admin/torneio/info", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952240020"}
2025-07-08 02:24:00,184 - INFO - Response: {"request_id": "1751952240020", "status_code": 200, "duration_ms": 163}
2025-07-08 02:24:01,863 - INFO - Request: {"timestamp": "2025-07-08T02:24:01.863103", "method": "GET", "path": "/api/torneio/ranking-deface", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952241863"}
2025-07-08 02:24:02,046 - INFO - Response: {"request_id": "1751952241863", "status_code": 200, "duration_ms": 183}
2025-07-08 02:24:03,741 - INFO - Request: {"timestamp": "2025-07-08T02:24:03.741287", "method": "GET", "path": "/api/torneio/ranking-deface", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952243741"}
2025-07-08 02:24:03,910 - INFO - Response: {"request_id": "1751952243741", "status_code": 200, "duration_ms": 168}
2025-07-08 02:24:03,992 - INFO - Request: {"timestamp": "2025-07-08T02:24:03.992244", "method": "GET", "path": "/api/ranking/deface-individual", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952243992"}
2025-07-08 02:24:04,150 - INFO - Response: {"request_id": "1751952243992", "status_code": 200, "duration_ms": 158}
2025-07-08 02:24:06,282 - INFO - Request: {"timestamp": "2025-07-08T02:24:06.282857", "method": "GET", "path": "/api/jogador", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952246282"}
2025-07-08 02:24:06,324 - INFO - Request: {"timestamp": "2025-07-08T02:24:06.324845", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952246324"}
2025-07-08 02:24:06,373 - INFO - Response: {"request_id": "1751952246282", "status_code": 200, "duration_ms": 90}
2025-07-08 02:24:06,534 - INFO - Request: {"timestamp": "2025-07-08T02:24:06.533817", "method": "POST", "path": "/api/mineracao/coletar-offline", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952246533"}
2025-07-08 02:24:06,609 - INFO - Response: {"request_id": "1751952246324", "status_code": 200, "duration_ms": 284}
2025-07-08 02:24:06,789 - INFO - Response: {"request_id": "1751952246533", "status_code": 200, "duration_ms": 255}
2025-07-08 02:24:08,289 - INFO - Request: {"timestamp": "2025-07-08T02:24:08.289494", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952248289"}
2025-07-08 02:24:08,429 - INFO - Response: {"request_id": "1751952248289", "status_code": 200, "duration_ms": 140}
2025-07-08 02:24:08,514 - INFO - Request: {"timestamp": "2025-07-08T02:24:08.513637", "method": "GET", "path": "/", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952248513"}
2025-07-08 02:24:08,515 - INFO - Response: {"request_id": "1751952248513", "status_code": 200, "duration_ms": 1}
2025-07-08 02:24:08,832 - INFO - Request: {"timestamp": "2025-07-08T02:24:08.832952", "method": "GET", "path": "/static/js/simple-auth.js", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952248832"}
2025-07-08 02:24:08,833 - INFO - Request: {"timestamp": "2025-07-08T02:24:08.833929", "method": "GET", "path": "/static/js/main.js", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952248833"}
2025-07-08 02:24:08,834 - INFO - Response: {"request_id": "1751952248832", "status_code": 304, "duration_ms": 1}
2025-07-08 02:24:08,836 - INFO - Response: {"request_id": "1751952248833", "status_code": 304, "duration_ms": 2}
2025-07-08 02:24:09,083 - INFO - Request: {"timestamp": "2025-07-08T02:24:09.083911", "method": "GET", "path": "/api/csrf-token", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952249083"}
2025-07-08 02:24:09,084 - INFO - Response: {"request_id": "1751952249083", "status_code": 200, "duration_ms": 0}
2025-07-08 02:24:09,151 - INFO - Request: {"timestamp": "2025-07-08T02:24:09.151290", "method": "GET", "path": "/favicon.ico", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952249151"}
2025-07-08 02:24:09,152 - INFO - Response: {"request_id": "1751952249151", "status_code": 200, "duration_ms": 0}
2025-07-08 02:24:09,199 - INFO - Request: {"timestamp": "2025-07-08T02:24:09.199142", "method": "GET", "path": "/api/jogador", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952249199"}
2025-07-08 02:24:09,293 - INFO - Response: {"request_id": "1751952249199", "status_code": 200, "duration_ms": 94}
2025-07-08 02:24:09,403 - INFO - Request: {"timestamp": "2025-07-08T02:24:09.403227", "method": "POST", "path": "/api/mineracao/coletar-offline", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952249403"}
2025-07-08 02:24:09,513 - INFO - Response: {"request_id": "1751952249403", "status_code": 200, "duration_ms": 109}
2025-07-08 02:24:09,604 - INFO - Request: {"timestamp": "2025-07-08T02:24:09.604443", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952249604"}
2025-07-08 02:24:09,904 - INFO - Response: {"request_id": "1751952249604", "status_code": 200, "duration_ms": 300}
2025-07-08 02:24:09,980 - INFO - Request: {"timestamp": "2025-07-08T02:24:09.980908", "method": "GET", "path": "/api/grupo", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952249980"}
2025-07-08 02:24:10,383 - INFO - Response: {"request_id": "1751952249980", "status_code": 200, "duration_ms": 402}
2025-07-08 02:24:10,691 - INFO - Request: {"timestamp": "2025-07-08T02:24:10.691276", "method": "GET", "path": "/api/torneio/ranking-deface", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952250691"}
2025-07-08 02:24:10,840 - INFO - Response: {"request_id": "1751952250691", "status_code": 200, "duration_ms": 148}
2025-07-08 02:24:10,942 - INFO - Request: {"timestamp": "2025-07-08T02:24:10.942235", "method": "GET", "path": "/api/admin/torneio/info", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952250942"}
2025-07-08 02:24:11,022 - INFO - Response: {"request_id": "1751952250942", "status_code": 403, "duration_ms": 80}
2025-07-08 02:24:12,030 - INFO - Request: {"timestamp": "2025-07-08T02:24:12.030103", "method": "GET", "path": "/api/admin/torneio/info", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952252030"}
2025-07-08 02:24:12,218 - INFO - Response: {"request_id": "1751952252030", "status_code": 403, "duration_ms": 188}
2025-07-08 02:24:12,746 - INFO - Request: {"timestamp": "2025-07-08T02:24:12.746920", "method": "GET", "path": "/api/torneio/ranking-deface", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952252746"}
2025-07-08 02:24:12,903 - INFO - Response: {"request_id": "1751952252746", "status_code": 200, "duration_ms": 156}
2025-07-08 02:24:13,549 - INFO - Request: {"timestamp": "2025-07-08T02:24:13.549741", "method": "GET", "path": "/api/torneio/ranking-deface", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952253549"}
2025-07-08 02:24:13,715 - INFO - Response: {"request_id": "1751952253549", "status_code": 200, "duration_ms": 166}
2025-07-08 02:24:14,025 - INFO - Request: {"timestamp": "2025-07-08T02:24:14.025299", "method": "GET", "path": "/api/ranking/deface-individual", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952254025"}
2025-07-08 02:24:14,192 - INFO - Response: {"request_id": "1751952254025", "status_code": 200, "duration_ms": 167}
2025-07-08 02:24:14,273 - INFO - Request: {"timestamp": "2025-07-08T02:24:14.273850", "method": "GET", "path": "/api/admin/torneio/info", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952254273"}
2025-07-08 02:24:14,355 - INFO - Response: {"request_id": "1751952254273", "status_code": 403, "duration_ms": 81}
2025-07-08 02:24:14,935 - INFO - Request: {"timestamp": "2025-07-08T02:24:14.935136", "method": "GET", "path": "/api/torneio/ranking-deface", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952254935"}
2025-07-08 02:24:14,935 - ERROR - SECURITY ALERT: Suspicious activity: 21 requests in 10s from IP 12*******
2025-07-08 02:24:15,126 - INFO - Response: {"request_id": "1751952254935", "status_code": 200, "duration_ms": 191}
2025-07-08 02:24:16,044 - INFO - Request: {"timestamp": "2025-07-08T02:24:16.044180", "method": "GET", "path": "/api/torneio/ranking-deface", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952256044"}
2025-07-08 02:24:16,045 - ERROR - SECURITY ALERT: Suspicious activity: 22 requests in 10s from IP 12*******
2025-07-08 02:24:16,215 - INFO - Response: {"request_id": "1751952256044", "status_code": 200, "duration_ms": 170}
2025-07-08 02:24:16,294 - INFO - Request: {"timestamp": "2025-07-08T02:24:16.294162", "method": "GET", "path": "/api/ranking/deface-individual", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952256294"}
2025-07-08 02:24:16,295 - ERROR - SECURITY ALERT: Suspicious activity: 22 requests in 10s from IP 12*******
2025-07-08 02:24:16,461 - INFO - Response: {"request_id": "1751952256294", "status_code": 200, "duration_ms": 166}
2025-07-08 02:24:16,522 - INFO - Request: {"timestamp": "2025-07-08T02:24:16.522661", "method": "GET", "path": "/api/ranking/deface-individual", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952256522"}
2025-07-08 02:24:16,522 - ERROR - SECURITY ALERT: Suspicious activity: 22 requests in 10s from IP 12*******
2025-07-08 02:24:16,682 - INFO - Response: {"request_id": "1751952256522", "status_code": 200, "duration_ms": 160}
2025-07-08 02:24:18,666 - INFO - Request: {"timestamp": "2025-07-08T02:24:18.666814", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952258666"}
2025-07-08 02:24:18,810 - INFO - Response: {"request_id": "1751952258666", "status_code": 200, "duration_ms": 143}
2025-07-08 02:24:20,022 - INFO - Request: {"timestamp": "2025-07-08T02:24:20.022784", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952260022"}
2025-07-08 02:24:20,318 - INFO - Response: {"request_id": "1751952260022", "status_code": 200, "duration_ms": 295}
2025-07-08 02:24:28,286 - INFO - Request: {"timestamp": "2025-07-08T02:24:28.286868", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952268286"}
2025-07-08 02:24:28,500 - INFO - Response: {"request_id": "1751952268286", "status_code": 200, "duration_ms": 213}
2025-07-08 02:24:30,374 - INFO - Request: {"timestamp": "2025-07-08T02:24:30.374779", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952270374"}
2025-07-08 02:24:30,674 - INFO - Response: {"request_id": "1751952270374", "status_code": 200, "duration_ms": 299}
2025-07-08 02:24:38,588 - INFO - Request: {"timestamp": "2025-07-08T02:24:38.588522", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952278588"}
2025-07-08 02:24:38,823 - INFO - Response: {"request_id": "1751952278588", "status_code": 200, "duration_ms": 235}
2025-07-08 02:24:40,023 - INFO - Request: {"timestamp": "2025-07-08T02:24:40.023097", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952280023"}
2025-07-08 02:24:40,350 - INFO - Response: {"request_id": "1751952280023", "status_code": 200, "duration_ms": 327}
2025-07-08 02:24:48,287 - INFO - Request: {"timestamp": "2025-07-08T02:24:48.287308", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952288287"}
2025-07-08 02:24:48,506 - INFO - Response: {"request_id": "1751952288287", "status_code": 200, "duration_ms": 218}
2025-07-08 02:24:50,325 - INFO - Request: {"timestamp": "2025-07-08T02:24:50.324816", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952290324"}
2025-07-08 02:24:50,842 - INFO - Response: {"request_id": "1751952290324", "status_code": 200, "duration_ms": 517}
2025-07-08 02:24:58,588 - INFO - Request: {"timestamp": "2025-07-08T02:24:58.588843", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952298588"}
2025-07-08 02:24:59,001 - INFO - Response: {"request_id": "1751952298588", "status_code": 200, "duration_ms": 413}
2025-07-08 02:25:00,022 - INFO - Request: {"timestamp": "2025-07-08T02:25:00.022942", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952300022"}
2025-07-08 02:25:00,302 - INFO - Response: {"request_id": "1751952300022", "status_code": 200, "duration_ms": 279}
2025-07-08 02:25:08,287 - INFO - Request: {"timestamp": "2025-07-08T02:25:08.287048", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952308287"}
2025-07-08 02:25:08,503 - INFO - Response: {"request_id": "1751952308287", "status_code": 200, "duration_ms": 216}
2025-07-08 02:25:10,326 - INFO - Request: {"timestamp": "2025-07-08T02:25:10.326702", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952310326"}
2025-07-08 02:25:10,602 - INFO - Response: {"request_id": "1751952310326", "status_code": 200, "duration_ms": 275}
2025-07-08 02:25:18,603 - INFO - Request: {"timestamp": "2025-07-08T02:25:18.603296", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952318603"}
2025-07-08 02:25:18,827 - INFO - Response: {"request_id": "1751952318603", "status_code": 200, "duration_ms": 224}
2025-07-08 02:25:27,588 - INFO - Request: {"timestamp": "2025-07-08T02:25:27.588701", "method": "GET", "path": "/api/jogador", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952327588"}
2025-07-08 02:25:27,902 - INFO - Response: {"request_id": "1751952327588", "status_code": 200, "duration_ms": 313}
2025-07-08 02:25:28,210 - INFO - Request: {"timestamp": "2025-07-08T02:25:28.210729", "method": "POST", "path": "/api/mineracao/coletar-offline", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952328210"}
2025-07-08 02:25:28,345 - INFO - Response: {"request_id": "1751952328210", "status_code": 200, "duration_ms": 135}
2025-07-08 02:25:28,461 - INFO - Request: {"timestamp": "2025-07-08T02:25:28.461752", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952328461"}
2025-07-08 02:25:28,609 - INFO - Response: {"request_id": "1751952328461", "status_code": 200, "duration_ms": 147}
2025-07-08 02:25:38,286 - INFO - Request: {"timestamp": "2025-07-08T02:25:38.286355", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952338286"}
2025-07-08 02:25:38,472 - INFO - Response: {"request_id": "1751952338286", "status_code": 200, "duration_ms": 186}
2025-07-08 02:25:42,029 - INFO - Request: {"timestamp": "2025-07-08T02:25:42.029305", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952342029"}
2025-07-08 02:25:42,298 - INFO - Response: {"request_id": "1751952342029", "status_code": 200, "duration_ms": 269}
2025-07-08 02:25:48,592 - INFO - Request: {"timestamp": "2025-07-08T02:25:48.592003", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952348592"}
2025-07-08 02:25:49,008 - INFO - Response: {"request_id": "1751952348592", "status_code": 200, "duration_ms": 416}
2025-07-08 02:25:58,297 - INFO - Request: {"timestamp": "2025-07-08T02:25:58.297998", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952358297"}
2025-07-08 02:25:58,504 - INFO - Response: {"request_id": "1751952358297", "status_code": 200, "duration_ms": 206}
2025-07-08 02:26:08,588 - INFO - Request: {"timestamp": "2025-07-08T02:26:08.588162", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952368588"}
2025-07-08 02:26:08,781 - INFO - Response: {"request_id": "1751952368588", "status_code": 200, "duration_ms": 193}
2025-07-08 02:26:18,287 - INFO - Request: {"timestamp": "2025-07-08T02:26:18.287428", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952378287"}
2025-07-08 02:26:18,494 - INFO - Response: {"request_id": "1751952378287", "status_code": 200, "duration_ms": 207}
2025-07-08 02:26:28,588 - INFO - Request: {"timestamp": "2025-07-08T02:26:28.588740", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952388588"}
2025-07-08 02:26:28,786 - INFO - Response: {"request_id": "1751952388588", "status_code": 200, "duration_ms": 198}
2025-07-08 02:26:38,286 - INFO - Request: {"timestamp": "2025-07-08T02:26:38.286515", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952398286"}
2025-07-08 02:26:38,512 - INFO - Response: {"request_id": "1751952398286", "status_code": 200, "duration_ms": 225}
2025-07-08 02:26:42,329 - INFO - Request: {"timestamp": "2025-07-08T02:26:42.329945", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952402329"}
2025-07-08 02:26:42,604 - INFO - Response: {"request_id": "1751952402329", "status_code": 200, "duration_ms": 274}
2025-07-08 02:26:48,588 - INFO - Request: {"timestamp": "2025-07-08T02:26:48.588153", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952408588"}
2025-07-08 02:26:48,789 - INFO - Response: {"request_id": "1751952408588", "status_code": 200, "duration_ms": 201}
2025-07-08 02:26:57,587 - INFO - Request: {"timestamp": "2025-07-08T02:26:57.587469", "method": "GET", "path": "/api/jogador", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952417587"}
2025-07-08 02:26:57,972 - INFO - Response: {"request_id": "1751952417587", "status_code": 200, "duration_ms": 385}
2025-07-08 02:26:58,281 - INFO - Request: {"timestamp": "2025-07-08T02:26:58.280326", "method": "POST", "path": "/api/mineracao/coletar-offline", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952418280"}
2025-07-08 02:26:58,425 - INFO - Response: {"request_id": "1751952418280", "status_code": 200, "duration_ms": 145}
2025-07-08 02:26:58,530 - INFO - Request: {"timestamp": "2025-07-08T02:26:58.530811", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952418530"}
2025-07-08 02:26:58,689 - INFO - Response: {"request_id": "1751952418530", "status_code": 200, "duration_ms": 158}
2025-07-08 02:27:08,286 - INFO - Request: {"timestamp": "2025-07-08T02:27:08.286803", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952428286"}
2025-07-08 02:27:08,521 - INFO - Response: {"request_id": "1751952428286", "status_code": 200, "duration_ms": 234}
2025-07-08 02:27:22,162 - INFO - Activity monitoring initialized
2025-07-08 02:27:22,269 - INFO - Request: {"timestamp": "2025-07-08T02:27:22.269652", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952442269"}
2025-07-08 02:27:22,437 - INFO - Response: {"request_id": "1751952442269", "status_code": 200, "duration_ms": 168}
2025-07-08 02:27:28,288 - INFO - Request: {"timestamp": "2025-07-08T02:27:28.287200", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952448287"}
2025-07-08 02:27:28,527 - INFO - Response: {"request_id": "1751952448287", "status_code": 200, "duration_ms": 240}
2025-07-08 02:27:38,067 - INFO - Activity monitoring initialized
2025-07-08 02:27:38,703 - INFO - Request: {"timestamp": "2025-07-08T02:27:38.702229", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952458702"}
2025-07-08 02:27:38,896 - INFO - Response: {"request_id": "1751952458702", "status_code": 200, "duration_ms": 193}
2025-07-08 02:27:42,335 - INFO - Request: {"timestamp": "2025-07-08T02:27:42.335270", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952462335"}
2025-07-08 02:27:42,622 - INFO - Response: {"request_id": "1751952462335", "status_code": 200, "duration_ms": 287}
2025-07-08 02:27:48,286 - INFO - Request: {"timestamp": "2025-07-08T02:27:48.286869", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952468286"}
2025-07-08 02:27:48,516 - INFO - Response: {"request_id": "1751952468286", "status_code": 200, "duration_ms": 229}
2025-07-08 02:27:57,925 - INFO - Activity monitoring initialized
2025-07-08 02:27:58,591 - INFO - Request: {"timestamp": "2025-07-08T02:27:58.591154", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952478591"}
2025-07-08 02:27:58,737 - INFO - Response: {"request_id": "1751952478591", "status_code": 200, "duration_ms": 146}
2025-07-08 02:28:08,288 - INFO - Request: {"timestamp": "2025-07-08T02:28:08.287679", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952488287"}
2025-07-08 02:28:08,484 - INFO - Response: {"request_id": "1751952488287", "status_code": 200, "duration_ms": 196}
2025-07-08 02:28:19,363 - INFO - Request: {"timestamp": "2025-07-08T02:28:19.363233", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952499363"}
2025-07-08 02:28:19,646 - INFO - Response: {"request_id": "1751952499363", "status_code": 200, "duration_ms": 283}
2025-07-08 02:28:29,023 - INFO - Request: {"timestamp": "2025-07-08T02:28:29.023311", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952509023"}
2025-07-08 02:28:29,237 - INFO - Response: {"request_id": "1751952509023", "status_code": 200, "duration_ms": 213}
2025-07-08 02:28:36,678 - INFO - Activity monitoring initialized
2025-07-08 02:28:39,329 - INFO - Request: {"timestamp": "2025-07-08T02:28:39.329413", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952519329"}
2025-07-08 02:28:39,498 - INFO - Response: {"request_id": "1751952519329", "status_code": 200, "duration_ms": 169}
2025-07-08 02:28:42,336 - INFO - Request: {"timestamp": "2025-07-08T02:28:42.336849", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952522336"}
2025-07-08 02:28:42,659 - INFO - Response: {"request_id": "1751952522336", "status_code": 200, "duration_ms": 322}
2025-07-08 02:28:49,021 - INFO - Request: {"timestamp": "2025-07-08T02:28:49.021612", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952529021"}
2025-07-08 02:28:49,227 - INFO - Response: {"request_id": "1751952529021", "status_code": 200, "duration_ms": 206}
2025-07-08 02:28:59,325 - INFO - Request: {"timestamp": "2025-07-08T02:28:59.325757", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952539325"}
2025-07-08 02:28:59,519 - INFO - Response: {"request_id": "1751952539325", "status_code": 200, "duration_ms": 193}
2025-07-08 02:29:05,724 - INFO - Activity monitoring initialized
2025-07-08 02:29:09,330 - INFO - Request: {"timestamp": "2025-07-08T02:29:09.330747", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952549330"}
2025-07-08 02:29:09,490 - INFO - Response: {"request_id": "1751952549330", "status_code": 200, "duration_ms": 160}
2025-07-08 02:29:42,216 - INFO - Request: {"timestamp": "2025-07-08T02:29:42.216206", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952582216"}
2025-07-08 02:29:42,709 - INFO - Request: {"timestamp": "2025-07-08T02:29:42.709180", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952582709"}
2025-07-08 02:29:43,012 - INFO - Response: {"request_id": "1751952582216", "status_code": 200, "duration_ms": 796}
2025-07-08 02:29:43,222 - INFO - Response: {"request_id": "1751952582709", "status_code": 200, "duration_ms": 513}
2025-07-08 02:30:42,030 - INFO - Request: {"timestamp": "2025-07-08T02:30:42.030162", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952642030"}
2025-07-08 02:30:42,330 - INFO - Request: {"timestamp": "2025-07-08T02:30:42.330926", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952642330"}
2025-07-08 02:30:42,453 - INFO - Response: {"request_id": "1751952642030", "status_code": 200, "duration_ms": 423}
2025-07-08 02:30:42,535 - INFO - Response: {"request_id": "1751952642330", "status_code": 200, "duration_ms": 205}
2025-07-08 02:31:42,043 - INFO - Request: {"timestamp": "2025-07-08T02:31:42.043304", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952702043"}
2025-07-08 02:31:42,352 - INFO - Request: {"timestamp": "2025-07-08T02:31:42.352399", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952702352"}
2025-07-08 02:31:42,746 - INFO - Response: {"request_id": "1751952702043", "status_code": 200, "duration_ms": 703}
2025-07-08 02:31:42,928 - INFO - Response: {"request_id": "1751952702352", "status_code": 200, "duration_ms": 575}
2025-07-08 02:32:42,102 - INFO - Request: {"timestamp": "2025-07-08T02:32:42.102053", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952762102"}
2025-07-08 02:32:42,411 - INFO - Request: {"timestamp": "2025-07-08T02:32:42.411491", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952762411"}
2025-07-08 02:32:42,662 - INFO - Response: {"request_id": "1751952762102", "status_code": 200, "duration_ms": 560}
2025-07-08 02:32:42,678 - INFO - Response: {"request_id": "1751952762411", "status_code": 200, "duration_ms": 266}
2025-07-08 02:33:42,032 - INFO - Request: {"timestamp": "2025-07-08T02:33:42.032501", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952822032"}
2025-07-08 02:33:42,332 - INFO - Request: {"timestamp": "2025-07-08T02:33:42.332285", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952822332"}
2025-07-08 02:33:42,469 - INFO - Response: {"request_id": "1751952822032", "status_code": 200, "duration_ms": 436}
2025-07-08 02:33:42,727 - INFO - Response: {"request_id": "1751952822332", "status_code": 200, "duration_ms": 395}
2025-07-08 02:34:42,029 - INFO - Request: {"timestamp": "2025-07-08T02:34:42.029932", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952882029"}
2025-07-08 02:34:42,329 - INFO - Request: {"timestamp": "2025-07-08T02:34:42.329253", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952882329"}
2025-07-08 02:34:42,463 - INFO - Response: {"request_id": "1751952882029", "status_code": 200, "duration_ms": 433}
2025-07-08 02:34:42,534 - INFO - Response: {"request_id": "1751952882329", "status_code": 200, "duration_ms": 205}
2025-07-08 02:35:42,032 - INFO - Request: {"timestamp": "2025-07-08T02:35:42.032039", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952942032"}
2025-07-08 02:35:42,420 - INFO - Request: {"timestamp": "2025-07-08T02:35:42.420839", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952942420"}
2025-07-08 02:35:42,889 - INFO - Response: {"request_id": "1751952942032", "status_code": 200, "duration_ms": 857}
2025-07-08 02:35:43,117 - INFO - Response: {"request_id": "1751952942420", "status_code": 200, "duration_ms": 696}
2025-07-08 02:36:37,222 - INFO - Request: {"timestamp": "2025-07-08T02:36:37.222268", "method": "GET", "path": "/api/jogador", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952997222"}
2025-07-08 02:36:37,222 - INFO - Request: {"timestamp": "2025-07-08T02:36:37.222268", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952997222"}
2025-07-08 02:36:37,695 - INFO - Response: {"request_id": "1751952997222", "status_code": 200, "duration_ms": 473}
2025-07-08 02:36:37,717 - INFO - Request: {"timestamp": "2025-07-08T02:36:37.717356", "method": "POST", "path": "/api/mineracao/coletar-offline", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952997717"}
2025-07-08 02:36:37,759 - INFO - Response: {"request_id": "1751952997222", "status_code": 200, "duration_ms": 537}
2025-07-08 02:36:37,896 - INFO - Response: {"request_id": "1751952997717", "status_code": 200, "duration_ms": 178}
2025-07-08 02:36:38,454 - INFO - Request: {"timestamp": "2025-07-08T02:36:38.454151", "method": "GET", "path": "/", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952998454"}
2025-07-08 02:36:38,469 - INFO - Response: {"request_id": "1751952998454", "status_code": 200, "duration_ms": 15}
2025-07-08 02:36:38,472 - INFO - Request: {"timestamp": "2025-07-08T02:36:38.472707", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952998472"}
2025-07-08 02:36:38,673 - INFO - Response: {"request_id": "1751952998472", "status_code": 200, "duration_ms": 201}
2025-07-08 02:36:38,706 - INFO - Request: {"timestamp": "2025-07-08T02:36:38.706089", "method": "GET", "path": "/static/js/simple-auth.js", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952998706"}
2025-07-08 02:36:38,761 - INFO - Response: {"request_id": "1751952998706", "status_code": 304, "duration_ms": 55}
2025-07-08 02:36:38,786 - INFO - Request: {"timestamp": "2025-07-08T02:36:38.786161", "method": "GET", "path": "/static/js/main.js", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952998786"}
2025-07-08 02:36:38,787 - INFO - Response: {"request_id": "1751952998786", "status_code": 304, "duration_ms": 0}
2025-07-08 02:36:38,798 - INFO - Request: {"timestamp": "2025-07-08T02:36:38.798473", "method": "GET", "path": "/api/csrf-token", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952998798"}
2025-07-08 02:36:38,799 - INFO - Response: {"request_id": "1751952998798", "status_code": 200, "duration_ms": 0}
2025-07-08 02:36:39,176 - INFO - Request: {"timestamp": "2025-07-08T02:36:39.176993", "method": "GET", "path": "/api/jogador", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952999176"}
2025-07-08 02:36:39,178 - INFO - Request: {"timestamp": "2025-07-08T02:36:39.178953", "method": "GET", "path": "/favicon.ico", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952999178"}
2025-07-08 02:36:39,182 - INFO - Response: {"request_id": "1751952999178", "status_code": 200, "duration_ms": 3}
2025-07-08 02:36:39,361 - INFO - Response: {"request_id": "1751952999176", "status_code": 200, "duration_ms": 184}
2025-07-08 02:36:39,427 - INFO - Request: {"timestamp": "2025-07-08T02:36:39.427049", "method": "POST", "path": "/api/mineracao/coletar-offline", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952999427"}
2025-07-08 02:36:39,603 - INFO - Response: {"request_id": "1751952999427", "status_code": 200, "duration_ms": 176}
2025-07-08 02:36:39,675 - INFO - Request: {"timestamp": "2025-07-08T02:36:39.675083", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751952999675"}
2025-07-08 02:36:39,893 - INFO - Response: {"request_id": "1751952999675", "status_code": 200, "duration_ms": 218}
2025-07-08 02:36:40,992 - INFO - Request: {"timestamp": "2025-07-08T02:36:40.992318", "method": "GET", "path": "/api/scan", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751953000992"}
2025-07-08 02:36:41,100 - INFO - Response: {"request_id": "1751953000992", "status_code": 200, "duration_ms": 108}
2025-07-08 02:36:42,057 - INFO - Request: {"timestamp": "2025-07-08T02:36:42.057069", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751953002057"}
2025-07-08 02:36:42,477 - INFO - Response: {"request_id": "1751953002057", "status_code": 200, "duration_ms": 420}
2025-07-08 02:36:44,273 - INFO - Request: {"timestamp": "2025-07-08T02:36:44.273695", "method": "POST", "path": "/api/alvo/*************/exploit", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751953004273"}
2025-07-08 02:36:45,008 - INFO - Response: {"request_id": "1751953004273", "status_code": 200, "duration_ms": 734}
2025-07-08 02:36:46,637 - INFO - Request: {"timestamp": "2025-07-08T02:36:46.637882", "method": "POST", "path": "/api/alvo/deface", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751953006637"}
2025-07-08 02:36:47,158 - INFO - Response: {"request_id": "1751953006637", "status_code": 200, "duration_ms": 520}
2025-07-08 02:36:47,468 - INFO - Request: {"timestamp": "2025-07-08T02:36:47.468454", "method": "GET", "path": "/api/jogador", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751953007468"}
2025-07-08 02:36:47,579 - INFO - Response: {"request_id": "1751953007468", "status_code": 200, "duration_ms": 110}
2025-07-08 02:36:47,720 - INFO - Request: {"timestamp": "2025-07-08T02:36:47.720183", "method": "POST", "path": "/api/mineracao/coletar-offline", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751953007720"}
2025-07-08 02:36:47,860 - INFO - Response: {"request_id": "1751953007720", "status_code": 200, "duration_ms": 140}
2025-07-08 02:36:49,396 - INFO - Request: {"timestamp": "2025-07-08T02:36:49.396600", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751953009396"}
2025-07-08 02:36:49,592 - INFO - Response: {"request_id": "1751953009396", "status_code": 200, "duration_ms": 196}
2025-07-08 02:36:50,515 - INFO - Request: {"timestamp": "2025-07-08T02:36:50.515356", "method": "GET", "path": "/api/grupo", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751953010515"}
2025-07-08 02:36:50,901 - INFO - Response: {"request_id": "1751953010515", "status_code": 200, "duration_ms": 386}
2025-07-08 02:36:50,908 - INFO - Request: {"timestamp": "2025-07-08T02:36:50.908430", "method": "GET", "path": "/api/torneio/ranking-deface", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751953010908"}
2025-07-08 02:36:51,076 - INFO - Response: {"request_id": "1751953010908", "status_code": 200, "duration_ms": 168}
2025-07-08 02:36:51,386 - INFO - Request: {"timestamp": "2025-07-08T02:36:51.386051", "method": "GET", "path": "/api/admin/torneio/info", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751953011386"}
2025-07-08 02:36:51,568 - INFO - Response: {"request_id": "1751953011386", "status_code": 200, "duration_ms": 182}
2025-07-08 02:36:52,488 - INFO - Request: {"timestamp": "2025-07-08T02:36:52.488672", "method": "GET", "path": "/api/torneio/ranking-deface", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751953012488"}
2025-07-08 02:36:52,686 - INFO - Response: {"request_id": "1751953012488", "status_code": 200, "duration_ms": 197}
2025-07-08 02:36:54,505 - INFO - Request: {"timestamp": "2025-07-08T02:36:54.505275", "method": "GET", "path": "/api/torneio/ranking-deface", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751953014505"}
2025-07-08 02:36:54,693 - INFO - Response: {"request_id": "1751953014505", "status_code": 200, "duration_ms": 188}
2025-07-08 02:36:54,758 - INFO - Request: {"timestamp": "2025-07-08T02:36:54.758256", "method": "GET", "path": "/api/ranking/deface-individual", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751953014758"}
2025-07-08 02:36:54,857 - INFO - Response: {"request_id": "1751953014758", "status_code": 200, "duration_ms": 98}
2025-07-08 02:36:57,429 - INFO - Request: {"timestamp": "2025-07-08T02:36:57.429049", "method": "GET", "path": "/api/jogador", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751953017429"}
2025-07-08 02:36:57,452 - INFO - Request: {"timestamp": "2025-07-08T02:36:57.452487", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751953017452"}
2025-07-08 02:36:57,557 - INFO - Response: {"request_id": "1751953017429", "status_code": 200, "duration_ms": 128}
2025-07-08 02:36:57,680 - INFO - Request: {"timestamp": "2025-07-08T02:36:57.680010", "method": "POST", "path": "/api/mineracao/coletar-offline", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751953017680"}
2025-07-08 02:36:57,744 - INFO - Response: {"request_id": "1751953017452", "status_code": 200, "duration_ms": 291}
2025-07-08 02:36:58,064 - INFO - Response: {"request_id": "1751953017680", "status_code": 200, "duration_ms": 384}
2025-07-08 02:36:58,438 - INFO - Request: {"timestamp": "2025-07-08T02:36:58.438425", "method": "GET", "path": "/", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751953018438"}
2025-07-08 02:36:58,440 - INFO - Response: {"request_id": "1751953018438", "status_code": 200, "duration_ms": 1}
2025-07-08 02:36:58,759 - INFO - Request: {"timestamp": "2025-07-08T02:36:58.759687", "method": "GET", "path": "/static/js/simple-auth.js", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751953018759"}
2025-07-08 02:36:58,760 - INFO - Request: {"timestamp": "2025-07-08T02:36:58.760664", "method": "GET", "path": "/static/js/main.js", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751953018760"}
2025-07-08 02:36:58,761 - INFO - Response: {"request_id": "1751953018759", "status_code": 304, "duration_ms": 1}
2025-07-08 02:36:58,762 - INFO - Response: {"request_id": "1751953018760", "status_code": 304, "duration_ms": 1}
2025-07-08 02:36:59,012 - INFO - Request: {"timestamp": "2025-07-08T02:36:59.012156", "method": "GET", "path": "/api/csrf-token", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751953019012"}
2025-07-08 02:36:59,013 - INFO - Response: {"request_id": "1751953019012", "status_code": 200, "duration_ms": 0}
2025-07-08 02:36:59,082 - INFO - Request: {"timestamp": "2025-07-08T02:36:59.082020", "method": "GET", "path": "/favicon.ico", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751953019082"}
2025-07-08 02:36:59,082 - INFO - Response: {"request_id": "1751953019082", "status_code": 200, "duration_ms": 0}
2025-07-08 02:36:59,149 - INFO - Request: {"timestamp": "2025-07-08T02:36:59.148418", "method": "GET", "path": "/api/jogador", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751953019148"}
2025-07-08 02:36:59,266 - INFO - Response: {"request_id": "1751953019148", "status_code": 200, "duration_ms": 118}
2025-07-08 02:36:59,327 - INFO - Request: {"timestamp": "2025-07-08T02:36:59.326208", "method": "POST", "path": "/api/mineracao/coletar-offline", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751953019326"}
2025-07-08 02:36:59,381 - INFO - Request: {"timestamp": "2025-07-08T02:36:59.380895", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751953019380"}
2025-07-08 02:36:59,478 - INFO - Response: {"request_id": "1751953019326", "status_code": 200, "duration_ms": 152}
2025-07-08 02:36:59,576 - INFO - Request: {"timestamp": "2025-07-08T02:36:59.576192", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751953019576"}
2025-07-08 02:36:59,597 - INFO - Response: {"request_id": "1751953019380", "status_code": 200, "duration_ms": 216}
2025-07-08 02:36:59,727 - INFO - Request: {"timestamp": "2025-07-08T02:36:59.727557", "method": "GET", "path": "/api/grupo", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751953019727"}
2025-07-08 02:36:59,937 - INFO - Response: {"request_id": "1751953019576", "status_code": 200, "duration_ms": 361}
2025-07-08 02:37:00,145 - INFO - Response: {"request_id": "1751953019727", "status_code": 200, "duration_ms": 417}
2025-07-08 02:37:00,455 - INFO - Request: {"timestamp": "2025-07-08T02:37:00.455551", "method": "GET", "path": "/api/torneio/ranking-deface", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751953020455"}
2025-07-08 02:37:00,628 - INFO - Response: {"request_id": "1751953020455", "status_code": 200, "duration_ms": 172}
2025-07-08 02:37:00,706 - INFO - Request: {"timestamp": "2025-07-08T02:37:00.706571", "method": "GET", "path": "/api/admin/torneio/info", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751953020706"}
2025-07-08 02:37:00,802 - INFO - Response: {"request_id": "1751953020706", "status_code": 403, "duration_ms": 96}
2025-07-08 02:37:01,366 - INFO - Request: {"timestamp": "2025-07-08T02:37:01.366478", "method": "GET", "path": "/api/torneio/ranking-deface", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751953021366"}
2025-07-08 02:37:01,540 - INFO - Response: {"request_id": "1751953021366", "status_code": 200, "duration_ms": 174}
2025-07-08 02:37:01,848 - INFO - Request: {"timestamp": "2025-07-08T02:37:01.848984", "method": "GET", "path": "/api/ranking/deface-individual", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751953021848"}
2025-07-08 02:37:01,933 - INFO - Response: {"request_id": "1751953021848", "status_code": 200, "duration_ms": 84}
2025-07-08 02:37:02,100 - INFO - Request: {"timestamp": "2025-07-08T02:37:02.100920", "method": "GET", "path": "/api/admin/torneio/info", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751953022100"}
2025-07-08 02:37:02,100 - ERROR - SECURITY ALERT: Suspicious activity: 21 requests in 10s from IP 12*******
2025-07-08 02:37:02,201 - INFO - Response: {"request_id": "1751953022100", "status_code": 403, "duration_ms": 100}
2025-07-08 02:37:04,208 - INFO - Request: {"timestamp": "2025-07-08T02:37:04.208928", "method": "GET", "path": "/api/admin/torneio/info", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751953024208"}
2025-07-08 02:37:04,208 - ERROR - SECURITY ALERT: Suspicious activity: 21 requests in 10s from IP 12*******
2025-07-08 02:37:04,309 - INFO - Response: {"request_id": "1751953024208", "status_code": 403, "duration_ms": 100}
2025-07-08 02:37:09,683 - INFO - Request: {"timestamp": "2025-07-08T02:37:09.683494", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751953029683"}
2025-07-08 02:37:09,896 - INFO - Response: {"request_id": "1751953029683", "status_code": 200, "duration_ms": 212}
2025-07-08 02:37:10,325 - INFO - Request: {"timestamp": "2025-07-08T02:37:10.325117", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751953030325"}
2025-07-08 02:37:10,658 - INFO - Response: {"request_id": "1751953030325", "status_code": 200, "duration_ms": 333}
2025-07-08 02:37:19,380 - INFO - Request: {"timestamp": "2025-07-08T02:37:19.380030", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751953039380"}
2025-07-08 02:37:19,571 - INFO - Response: {"request_id": "1751953039380", "status_code": 200, "duration_ms": 191}
2025-07-08 02:37:20,021 - INFO - Request: {"timestamp": "2025-07-08T02:37:20.021646", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751953040021"}
2025-07-08 02:37:20,314 - INFO - Response: {"request_id": "1751953040021", "status_code": 200, "duration_ms": 292}
2025-07-08 02:37:29,681 - INFO - Request: {"timestamp": "2025-07-08T02:37:29.681809", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751953049681"}
2025-07-08 02:37:30,048 - INFO - Response: {"request_id": "1751953049681", "status_code": 200, "duration_ms": 366}
2025-07-08 02:37:30,324 - INFO - Request: {"timestamp": "2025-07-08T02:37:30.324652", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751953050324"}
2025-07-08 02:37:30,598 - INFO - Response: {"request_id": "1751953050324", "status_code": 200, "duration_ms": 273}
2025-07-08 02:37:39,380 - INFO - Request: {"timestamp": "2025-07-08T02:37:39.380243", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751953059380"}
2025-07-08 02:37:39,560 - INFO - Response: {"request_id": "1751953059380", "status_code": 200, "duration_ms": 180}
2025-07-08 02:37:40,021 - INFO - Request: {"timestamp": "2025-07-08T02:37:40.021810", "method": "GET", "path": "/api/security/invasions", "ip": "12*******", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1751953060021"}
2025-07-08 02:37:40,286 - INFO - Response: {"request_id": "1751953060021", "status_code": 200, "duration_ms": 264}
