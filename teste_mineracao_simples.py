#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.supabase_client import supabase_client
import game.new_models as models

def teste_simples():
    print("🔍 TESTE SIMPLES DA MINERAÇÃO")
    print("=" * 40)
    
    # 1. Verificar conexão
    print("1. Verificando conexão...")
    if not supabase_client.is_connected():
        print("❌ Não conectado!")
        return
    print("✅ Conectado!")
    
    # 2. Buscar usuários
    print("\n2. Buscando usuários...")
    usuarios = supabase_client.get_ranking_usuarios(10)
    print(f"Encontrados: {len(usuarios)} usuários")
    
    if not usuarios:
        print("❌ Nenhum usuário!")
        return
    
    # 3. Mostrar primeiros usuários
    print("\n3. Primeiros usuários:")
    for i, usuario in enumerate(usuarios[:5]):
        uid = usuario.get('uid')
        nick = usuario.get('nick', 'Unknown')
        nivel_mineradora = usuario.get('nivel_mineradora', 1)
        dinheiro = usuario.get('dinheiro', 0)
        
        print(f"   {i+1}. {nick}")
        print(f"      UID: {uid}")
        print(f"      Nível Mineradora: {nivel_mineradora}")
        print(f"      Dinheiro: ${dinheiro}")
        print()
    
    # 4. Verificar cache
    print("4. Verificando cache...")
    try:
        cache = models.MINERACAO_AUTOMATICA_CACHE
        print(f"   Cache tem {len(cache)} entradas")
        
        # Limpar cache para teste
        if cache:
            print("   Limpando cache para teste...")
            cache.clear()
            print("   Cache limpo!")
        
    except Exception as e:
        print(f"   Erro no cache: {e}")
    
    # 5. Executar mineração
    print("\n5. Executando mineração...")
    try:
        resultado = models.processar_mineracao_automatica_dinheiro()
        print(f"   Resultado: {resultado}")
        
        sucesso = resultado.get('sucesso', False)
        processados = resultado.get('jogadores_processados', 0)
        total_gerado = resultado.get('total_dinheiro_gerado', 0)
        mensagem = resultado.get('mensagem', 'N/A')
        
        print(f"\n   📊 RESUMO:")
        print(f"   Sucesso: {sucesso}")
        print(f"   Processados: {processados}")
        print(f"   Total gerado: ${total_gerado}")
        print(f"   Mensagem: {mensagem}")
        
        if processados > 0:
            print("\n🎉 FUNCIONOU!")
        else:
            print("\n❌ Nenhum jogador processado!")
            
    except Exception as e:
        print(f"   ❌ Erro: {e}")

if __name__ == "__main__":
    teste_simples()
