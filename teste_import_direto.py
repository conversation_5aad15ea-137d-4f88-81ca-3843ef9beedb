#!/usr/bin/env python3
"""
Teste com importação direta do arquivo
"""

import sys
import os
from dotenv import load_dotenv

# Carrega variáveis de ambiente
load_dotenv()

def main():
    print("📁 TESTE COM IMPORTAÇÃO DIRETA")
    print("=" * 40)
    
    try:
        # Adicionar o diretório atual ao path
        current_dir = os.path.dirname(os.path.abspath(__file__))
        if current_dir not in sys.path:
            sys.path.insert(0, current_dir)
        
        # Importar supabase primeiro
        print("📦 Importando supabase...")
        from database.supabase_client import supabase_client
        print("✅ Supabase importado")
        
        # Limpar cache de módulos
        print("\n🧹 Limpando cache de módulos...")
        modules_to_remove = [key for key in sys.modules.keys() if 'new_models' in key]
        for module in modules_to_remove:
            del sys.modules[module]
            print(f"   Removido: {module}")
        
        # Importar new_models novamente
        print("\n📦 Importando new_models...")
        from game import new_models as models
        print("✅ new_models importado")
        
        # Verificar se a função de teste existe
        print("\n🧪 Verificando função de teste...")
        if hasattr(models, 'teste_funcao_simples'):
            print("✅ Função de teste encontrada!")
            resultado = models.teste_funcao_simples()
            print(f"📊 Resultado: {resultado}")
        else:
            print("❌ Função de teste NÃO encontrada")
        
        # Testar função de mineração com logs
        print("\n⛏️ Testando função de mineração...")
        print("🔍 Procurando pelos logs únicos que adicionamos...")
        
        resultado_mineracao = models.processar_mineracao_automatica_dinheiro()
        
        print(f"\n📊 Resultado da mineração:")
        print(f"   Sucesso: {resultado_mineracao.get('sucesso', False)}")
        print(f"   Processados: {resultado_mineracao.get('jogadores_processados', 0)}")
        print(f"   Total gerado: ${resultado_mineracao.get('total_dinheiro_gerado', 0)}")
        print(f"   Mensagem: {resultado_mineracao.get('mensagem', 'N/A')}")
        
        # Verificar se os logs únicos apareceram
        print("\n🔍 ANÁLISE DOS LOGS:")
        print("Se você viu os logs '🚨🚨🚨 [TESTE CRÍTICO]' acima, a função está funcionando.")
        print("Se NÃO viu esses logs, há um problema na execução da função.")
        
    except Exception as e:
        print(f"❌ ERRO: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
