#!/usr/bin/env python3
"""
Apply the nick_color schema to the database
"""

import sys
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def apply_nick_color_schema():
    print("🎨 APPLYING NICK COLOR SCHEMA")
    print("=" * 40)
    
    try:
        from database.supabase_client import supabase_client
        
        if not supabase_client.is_connected():
            print("❌ Database not connected!")
            return False
        
        print("✅ Database connected")
        
        # Step 1: Add nick_color column
        print("\n1. 📊 ADDING nick_color COLUMN...")
        try:
            # Check if column already exists
            result = supabase_client.client.table('usuarios').select('nick_color').limit(1).execute()
            print("✅ nick_color column already exists")
        except Exception as e:
            if 'does not exist' in str(e):
                print("⚠️ nick_color column doesn't exist - needs manual creation")
                print("📋 SQL to run in Supabase dashboard:")
                print("   ALTER TABLE usuarios ADD COLUMN nick_color VARCHAR(7) DEFAULT NULL;")
                print("   COMMENT ON COLUMN usuarios.nick_color IS 'Cor personalizada do nickname em formato hex (#FF0000). Apenas para supporters.';")
            else:
                print(f"❌ Error checking column: {e}")
        
        # Step 2: Test basic functionality
        print("\n2. 🧪 TESTING BASIC FUNCTIONALITY...")
        
        # Test if we can update a user with nick_color (if column exists)
        try:
            # Find a test user
            users = supabase_client.client.table('usuarios').select('uid, nick').limit(1).execute()
            if users.data:
                test_uid = users.data[0]['uid']
                test_nick = users.data[0]['nick']
                
                print(f"   Testing with user: {test_nick} ({test_uid})")
                
                # Try to set a color (this will fail if column doesn't exist)
                try:
                    result = supabase_client.client.table('usuarios').update({
                        'nick_color': '#FF0000'
                    }).eq('uid', test_uid).execute()
                    
                    print("✅ Successfully set test color")
                    
                    # Reset to null
                    supabase_client.client.table('usuarios').update({
                        'nick_color': None
                    }).eq('uid', test_uid).execute()
                    
                    print("✅ Successfully reset color")
                    
                except Exception as e:
                    if 'does not exist' in str(e):
                        print("❌ nick_color column needs to be created manually")
                    else:
                        print(f"❌ Error testing color update: {e}")
            else:
                print("⚠️ No users found to test with")
                
        except Exception as e:
            print(f"❌ Error in testing: {e}")
        
        # Step 3: Check shop integration
        print("\n3. 🛍️ TESTING SHOP INTEGRATION...")
        try:
            # Import the shop processing function
            from game.new_models import processar_compra_cor_nickname
            print("✅ Shop processing function imported successfully")
            
            # Test with dummy data
            dummy_jogador = {'is_supporter': True, 'spoints': 100}
            result = processar_compra_cor_nickname('test-uid', 'nick_color_red', dummy_jogador, 100)
            
            # This will fail because test-uid doesn't exist, but it tests the function structure
            if 'Erro' in result.get('mensagem', ''):
                print("✅ Shop function structure is correct")
            else:
                print(f"⚠️ Unexpected shop result: {result}")
                
        except Exception as e:
            print(f"❌ Error testing shop: {e}")
        
        # Step 4: Manual instructions
        print(f"\n🔧 MANUAL SETUP REQUIRED:")
        print(f"=" * 30)
        print(f"1. Go to Supabase Dashboard > SQL Editor")
        print(f"2. Run this SQL:")
        print(f"")
        print(f"   -- Add nick_color column")
        print(f"   ALTER TABLE usuarios ADD COLUMN IF NOT EXISTS nick_color VARCHAR(7) DEFAULT NULL;")
        print(f"")
        print(f"   -- Add comment")
        print(f"   COMMENT ON COLUMN usuarios.nick_color IS 'Cor personalizada do nickname em formato hex (#FF0000). Apenas para supporters.';")
        print(f"")
        print(f"   -- Create validation function")
        print(f"   CREATE OR REPLACE FUNCTION validate_hex_color(color_value TEXT)")
        print(f"   RETURNS BOOLEAN AS $$")
        print(f"   BEGIN")
        print(f"       IF color_value IS NULL THEN RETURN TRUE; END IF;")
        print(f"       IF color_value ~ '^#[0-9A-Fa-f]{{6}}$' THEN RETURN TRUE; END IF;")
        print(f"       RETURN FALSE;")
        print(f"   END;")
        print(f"   $$ LANGUAGE plpgsql;")
        print(f"")
        print(f"   -- Add constraint")
        print(f"   ALTER TABLE usuarios ADD CONSTRAINT IF NOT EXISTS check_nick_color_format")
        print(f"   CHECK (validate_hex_color(nick_color));")
        print(f"")
        print(f"3. After running the SQL, restart the server")
        print(f"4. Test by logging in as a supporter and buying a nickname color")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    success = apply_nick_color_schema()
    
    if success:
        print(f"\n🎉 SCHEMA APPLICATION COMPLETED!")
        print(f"📋 Manual SQL execution required in Supabase dashboard")
        print(f"🚀 After SQL execution, the colored nickname system will be fully functional")
    else:
        print(f"\n❌ SCHEMA APPLICATION FAILED!")
        print(f"🔧 Check database connection and try again")

if __name__ == "__main__":
    main()
