#!/usr/bin/env python3
"""
Script para testar e corrigir o sistema de ranking de deface
"""

import sys
import os
sys.path.append('.')

def test_ranking_system():
    try:
        print("🔧 Iniciando teste do sistema de ranking...")
        
        # Importar módulos
        from game.tournament_ttl import torneio_manager
        from game import new_models
        
        print("✅ Módulos importados com sucesso")
        
        # Teste 1: Adicionar pontos com dados completos
        print("\n📊 Teste 1: Adicionando pontos de deface...")
        resultado = torneio_manager.adicionar_pontos_deface(
            jogador_uid='test-player-001',
            pontos=75,
            nick='TestPlayer001',
            grupo_id='test-group-001',
            grupo_nome='Test Group Alpha'
        )
        
        print(f"Resultado da adição: {resultado}")
        
        if resultado.get('sucesso'):
            print("✅ Pontos adicionados com sucesso!")
        else:
            print(f"❌ Erro ao adicionar pontos: {resultado.get('mensagem')}")
            return False
        
        # Teste 2: Verificar ranking individual
        print("\n📊 Teste 2: Verificando ranking individual...")
        ranking_individual = new_models.get_individual_deface_ranking()
        
        if ranking_individual.get('sucesso'):
            jogadores = ranking_individual.get('ranking_jogadores', [])
            print(f"✅ Ranking individual funcionando! {len(jogadores)} jogadores encontrados")
            
            for i, jogador in enumerate(jogadores[:3]):  # Mostrar top 3
                print(f"  {i+1}. {jogador.get('nick', 'Sem nick')} - {jogador.get('deface_points', 0)} pontos")
        else:
            print(f"❌ Erro no ranking individual: {ranking_individual.get('mensagem')}")
        
        # Teste 3: Verificar ranking de grupos
        print("\n📊 Teste 3: Verificando ranking de grupos...")
        ranking_grupos = new_models.get_groups_deface_ranking()
        
        if ranking_grupos.get('sucesso'):
            grupos = ranking_grupos.get('data', {}).get('grupos', [])
            print(f"✅ Ranking de grupos funcionando! {len(grupos)} grupos encontrados")
            
            for i, grupo in enumerate(grupos[:3]):  # Mostrar top 3
                print(f"  {i+1}. {grupo.get('nome', 'Sem nome')} - {grupo.get('pontos', 0)} pontos")
        else:
            print(f"❌ Erro no ranking de grupos: {ranking_grupos.get('mensagem')}")
        
        print("\n🎉 Teste concluído!")
        return True
        
    except Exception as e:
        print(f"❌ Erro durante o teste: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = test_ranking_system()
    if success:
        print("\n✅ Sistema de ranking funcionando corretamente!")
    else:
        print("\n❌ Sistema de ranking com problemas!")
    
    sys.exit(0 if success else 1)
