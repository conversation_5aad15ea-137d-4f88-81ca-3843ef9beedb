-- Tabela para gerenciar defaces ativos
-- Esta tabela controla quais alvos estão com deface ativo e por quanto tempo

CREATE TABLE IF NOT EXISTS deface_ativo (
    id SERIAL PRIMARY KEY,
    alvo_uid TEXT NOT NULL,
    atacante_uid TEXT NOT NULL,
    grupo_id TEXT NOT NULL,
    criado_em TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    expira_em TIMESTAMPTZ NOT NULL,
    tempo_restante_minutos INTEGER NOT NULL DEFAULT 30,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Índices para melhor performance
CREATE INDEX IF NOT EXISTS idx_deface_ativo_alvo_uid ON deface_ativo(alvo_uid);
CREATE INDEX IF NOT EXISTS idx_deface_ativo_atacante_uid ON deface_ativo(atacante_uid);
CREATE INDEX IF NOT EXISTS idx_deface_ativo_grupo_id ON deface_ativo(grupo_id);
CREATE INDEX IF NOT EXISTS idx_deface_ativo_expira_em ON deface_ativo(expira_em);

-- Constraint para garantir que um alvo só pode ter um deface ativo por vez
CREATE UNIQUE INDEX IF NOT EXISTS idx_deface_ativo_alvo_unico ON deface_ativo(alvo_uid);

-- Função para limpeza automática de defaces expirados
CREATE OR REPLACE FUNCTION limpar_defaces_expirados()
RETURNS INTEGER AS $$
DECLARE
    count_removidos INTEGER;
BEGIN
    DELETE FROM deface_ativo 
    WHERE expira_em < NOW();
    
    GET DIAGNOSTICS count_removidos = ROW_COUNT;
    RETURN count_removidos;
END;
$$ LANGUAGE plpgsql;

-- Trigger para atualizar updated_at automaticamente
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_deface_ativo_updated_at 
    BEFORE UPDATE ON deface_ativo 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Comentários para documentação
COMMENT ON TABLE deface_ativo IS 'Tabela que controla defaces ativos por 30 minutos';
COMMENT ON COLUMN deface_ativo.alvo_uid IS 'UID do jogador que sofreu o deface';
COMMENT ON COLUMN deface_ativo.atacante_uid IS 'UID do jogador que aplicou o deface';
COMMENT ON COLUMN deface_ativo.grupo_id IS 'ID do grupo que aplicou o deface';
COMMENT ON COLUMN deface_ativo.criado_em IS 'Timestamp de quando o deface foi aplicado';
COMMENT ON COLUMN deface_ativo.expira_em IS 'Timestamp de quando o deface expira';
COMMENT ON COLUMN deface_ativo.tempo_restante_minutos IS 'Duração do deface em minutos (padrão: 30)';
