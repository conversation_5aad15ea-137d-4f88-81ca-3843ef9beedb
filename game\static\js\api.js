import { logoutUser } from './auth.js'; 

/**
 * Função auxiliar para fazer requisições à API Flask com o token de autenticação.
 * EXPORTA esta função para que outros módulos possam usá-la.
 */
export async function fetchAPI(endpoint, method = 'GET', body = null) { // Adicione 'export' aqui
    try {
        const user = window.auth.currentUser; // Obtém o usuário logado atualmente (acessando globalmente)
        if (!user) {
            throw new Error("Nenhum usuário logado. Token de autenticação não disponível.");
        }

        const idToken = await user.getIdToken(); 

        const headers = {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${idToken}` 
        };

        const options = { method, headers }; 
        if (body) {
            options.body = JSON.stringify(body);
        }

        const response = await fetch(endpoint, options);
        const data = await response.json();

        if (response.status === 401) {
            console.error("Token expirado ou inválido. Forçando logout.");
            logoutUser(); 
        }

        return data;

    } catch (error) {
        console.error("Erro na requisição à API:", error);
        throw error;
    }
}