#!/usr/bin/env python3
"""
Simple test for tournament reset API endpoint
"""

import requests
import json

def test_reset_api():
    print("🧪 TESTING TOURNAMENT RESET API")
    print("=" * 40)
    
    # Test the API endpoint directly
    url = "http://localhost:5000/api/torneio/reset-deface"
    
    # Use a test token (this will fail auth but we can see the error)
    headers = {
        "Authorization": "Bearer test-token",
        "Content-Type": "application/json"
    }
    
    try:
        print("📡 Sending POST request to reset endpoint...")
        response = requests.post(url, headers=headers, timeout=10)
        
        print(f"📊 Response Status: {response.status_code}")
        print(f"📄 Response Headers: {dict(response.headers)}")
        
        try:
            response_data = response.json()
            print(f"📋 Response Data:")
            print(json.dumps(response_data, indent=2, ensure_ascii=False))
            
            if response.status_code == 403:
                print("✅ Expected 403 - Admin verification working")
            elif response.status_code == 401:
                print("✅ Expected 401 - Token verification working")
            elif "SupabaseClient" in str(response_data):
                print("❌ Still has SupabaseClient error!")
            else:
                print("✅ No SupabaseClient error detected")
                
        except json.JSONDecodeError:
            print(f"📄 Raw Response: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection failed - server not running?")
    except Exception as e:
        print(f"❌ Error: {e}")

def test_server_status():
    print("\n🔍 TESTING SERVER STATUS")
    print("=" * 30)
    
    try:
        response = requests.get("http://localhost:5000/", timeout=5)
        print(f"✅ Server is running - Status: {response.status_code}")
        return True
    except:
        print("❌ Server is not running")
        return False

def main():
    if test_server_status():
        test_reset_api()
    else:
        print("\n🚨 Please start the server first with: python app.py")

if __name__ == "__main__":
    main()
