#!/usr/bin/env python3
"""
Script para testar se os logs duplicados foram corrigidos
"""

import sys
import requests
import json
sys.path.append('.')

def test_exploit_logs():
    """Testa se os logs de exploit não estão mais duplicados"""
    # print de debug removido
    
    try:
        # Criar sessão
        session = requests.Session()
        
        # 1. Fazer login
        print("1. 🔑 Fazendo login...")
        login_response = session.get("http://localhost:5000/api/auth/auto-login")
        if login_response.status_code != 200:
            # print de debug removido
            return False
        
        login_data = login_response.json()
        # print de debug removido}")
        
        # 2. Buscar alvos para exploit
        print("2. 🎯 Buscando alvos...")
        scan_response = session.get("http://localhost:5000/api/scan")
        if scan_response.status_code != 200:
            # print de debug removido
            return False
        
        scan_data = scan_response.json()
        if not scan_data.get('sucesso') or not scan_data.get('alvos'):
            # print de debug removido
            return False
        
        alvo = scan_data['alvos'][0]
        alvo_uid = alvo.get('uid')
        # print de debug removido} (UID: {alvo_uid[:8]}...)")
        
        # 3. Verificar logs antes do exploit
        print("3. 📊 Verificando logs antes do exploit...")
        logs_antes_response = session.get("http://localhost:5000/api/logs")
        logs_antes = []
        if logs_antes_response.status_code == 200:
            logs_antes_data = logs_antes_response.json()
            if logs_antes_data.get('sucesso'):
                logs_antes = logs_antes_data.get('logs', [])
        
        print(f"📈 Logs antes: {len(logs_antes)} registros")
        
        # 4. Realizar exploit
        print("4. ⚡ Realizando exploit...")
        exploit_response = session.post("http://localhost:5000/api/alvo/exploit", 
                                      json={'alvo_uid': alvo_uid})
        
        if exploit_response.status_code != 200:
            # print de debug removido
            return False
        
        exploit_data = exploit_response.json()
        # print de debug removido}")
        
        # 5. Verificar logs após o exploit
        print("5. 📊 Verificando logs após o exploit...")
        logs_depois_response = session.get("http://localhost:5000/api/logs")
        logs_depois = []
        if logs_depois_response.status_code == 200:
            logs_depois_data = logs_depois_response.json()
            if logs_depois_data.get('sucesso'):
                logs_depois = logs_depois_data.get('logs', [])
        
        print(f"📈 Logs depois: {len(logs_depois)} registros")
        
        # 6. Analisar diferença
        novos_logs = len(logs_depois) - len(logs_antes)
        # print de debug removido
        
        if novos_logs == 1:
            # print de debug removido")
            
            # Verificar se o log tem a lógica de ProxyVPN
            ultimo_log = logs_depois[0] if logs_depois else None
            if ultimo_log:
                dados = ultimo_log.get('dados', {})
                proxy_vpn_usado = dados.get('proxy_vpn_usado')
                atacante_ip = dados.get('atacante_ip')
                
                # print de debug removido
                print(f"   - Tipo: {dados.get('tipo')}")
                print(f"   - Atacante IP: {atacante_ip}")
                print(f"   - ProxyVPN usado: {proxy_vpn_usado}")
                print(f"   - Mensagem: {dados.get('mensagem')}")
                
                if proxy_vpn_usado is not None:
                    # print de debug removido
                    if atacante_ip == "unknown":
                        print("🔒 Atacante está anônimo (ProxyVPN >= Firewall)")
                    else:
                        print("👁️ Atacante foi identificado (ProxyVPN < Firewall)")
                else:
                    # print de debug removido
            
            return True
        elif novos_logs == 2:
            # print de debug removido")
            return False
        elif novos_logs == 0:
            # print de debug removido")
            return False
        else:
            # print de debug removido
            return False
        
    except Exception as e:
        # print de debug removido
        return False

def test_proxy_vpn_logic():
    """Testa especificamente a lógica de ProxyVPN vs Firewall"""
    print("\n🔒 Testando lógica ProxyVPN vs Firewall...")
    
    try:
        from game import new_models
        
        # Simular dados de teste
        atacante_mock = {
            'uid': 'test-atacante',
            'nick': 'TestAttacker',
            'ip': '*************',
            'proxyvpn': 3  # ProxyVPN nível 3
        }
        
        alvo_mock = {
            'uid': 'test-alvo',
            'nick': 'TestTarget',
            'ip': '*************',
            'firewall': 2  # Firewall nível 2
        }
        
        print(f"🎯 Teste: ProxyVPN {atacante_mock['proxyvpn']} vs Firewall {alvo_mock['firewall']}")
        
        # Simular lógica
        atacante_proxy_vpn = atacante_mock.get('proxyvpn', 0)
        alvo_firewall = alvo_mock.get('firewall', 0)
        
        if atacante_proxy_vpn >= alvo_firewall:
            atacante_ip_log = "unknown"
            atacante_nick_log = "unknown"
            resultado = "ANÔNIMO"
        else:
            atacante_ip_log = atacante_mock.get('ip', 'IP desconhecido')
            atacante_nick_log = atacante_mock.get('nick', 'Desconhecido')
            resultado = "IDENTIFICADO"
        
        # print de debug removido
        print(f"   - IP no log: {atacante_ip_log}")
        print(f"   - Nick no log: {atacante_nick_log}")
        
        return True
        
    except Exception as e:
        # print de debug removido
        return False

if __name__ == "__main__":
    print("🚀 Iniciando testes de logs...")
    
    # Teste 1: Verificar duplicação
    sucesso_duplicacao = test_exploit_logs()
    
    # Teste 2: Verificar lógica ProxyVPN
    sucesso_proxy = test_proxy_vpn_logic()
    
    print("\n" + "="*50)
    # print de debug removido
    print(f"   ✅ Duplicação corrigida: {'SIM' if sucesso_duplicacao else 'NÃO'}")
    print(f"   ✅ Lógica ProxyVPN OK: {'SIM' if sucesso_proxy else 'NÃO'}")
    
    if sucesso_duplicacao and sucesso_proxy:
        print("\n🎉 TODOS OS TESTES PASSARAM!")
        print("   - Logs não estão mais duplicados")
        print("   - Lógica de anonimato funcionando")
    else:
        print("\n❌ ALGUNS TESTES FALHARAM!")
        print("   Verifique os detalhes acima")
