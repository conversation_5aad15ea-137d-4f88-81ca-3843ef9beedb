#!/usr/bin/env python3
"""
Diagnóstico completo do sistema de mineração automática
"""

import os
import sys
from dotenv import load_dotenv
from datetime import datetime, timezone

# Carrega variáveis de ambiente
load_dotenv()

def test_imports():
    """Testa importações básicas"""
    print("🔍 Testando importações...")
    
    try:
        from database.supabase_client import supabase_client
        print("✅ Supabase client importado")
        
        from game import new_models as models
        print("✅ Models importado")
        
        return supabase_client, models
    except Exception as e:
        print(f"❌ Erro nas importações: {e}")
        return None, None

def test_database_connection(supabase_client):
    """Testa conexão com banco"""
    print("\n🔗 Testando conexão com banco...")
    
    if not supabase_client.is_connected():
        print("❌ Supabase não conectado")
        return False
    
    print("✅ Supabase conectado")
    return True

def analyze_user_data(supabase_client):
    """Analisa dados dos usuários"""
    print("\n👥 Analisando dados dos usuários...")
    
    try:
        usuarios = supabase_client.get_ranking_usuarios(5)
        if not usuarios:
            print("❌ Nenhum usuário encontrado")
            return []
        
        print(f"📊 {len(usuarios)} usuários encontrados")
        
        for i, usuario in enumerate(usuarios):
            uid = usuario.get('uid', 'N/A')
            nick = usuario.get('nick', 'Unknown')
            dinheiro = usuario.get('dinheiro', 0)
            nivel_mineradora = usuario.get('nivel_mineradora', 1)
            
            print(f"\n👤 Usuário {i+1}: {nick}")
            print(f"   UID: {uid}")
            print(f"   Dinheiro: ${dinheiro}")
            print(f"   Nível Mineradora: {nivel_mineradora}")
            
            # Calcular dinheiro que deveria gerar
            dinheiro_esperado = nivel_mineradora * 3  # 3 por minuto por nível
            print(f"   Deveria gerar: ${dinheiro_esperado}/min")
        
        return usuarios
        
    except Exception as e:
        print(f"❌ Erro ao analisar usuários: {e}")
        return []

def test_update_function(supabase_client, usuarios):
    """Testa função de atualização"""
    print("\n🔧 Testando função de atualização...")
    
    if not usuarios:
        print("❌ Nenhum usuário para testar")
        return False
    
    usuario_teste = usuarios[0]
    uid = usuario_teste['uid']
    nick = usuario_teste.get('nick', 'Unknown')
    dinheiro_atual = usuario_teste.get('dinheiro', 0)
    
    print(f"🎯 Testando com usuário: {nick}")
    print(f"   Dinheiro atual: ${dinheiro_atual}")
    
    # Teste 1: Atualização simples
    print("\n📝 Teste 1: Atualização simples...")
    novo_valor_teste = dinheiro_atual + 1
    
    try:
        resultado = supabase_client.atualizar_usuario(uid, {
            'dinheiro': novo_valor_teste
        })
        
        print(f"   Resultado: {resultado}")
        
        if resultado.get('sucesso'):
            print("   ✅ Atualização retornou sucesso")
            
            # Verificar se realmente atualizou
            usuario_atualizado = supabase_client.get_user_by_uid(uid)
            if usuario_atualizado:
                dinheiro_verificado = usuario_atualizado.get('dinheiro', 0)
                print(f"   Dinheiro verificado: ${dinheiro_verificado}")
                
                if dinheiro_verificado == novo_valor_teste:
                    print("   ✅ Atualização persistida corretamente!")
                    
                    # Reverter para valor original
                    supabase_client.atualizar_usuario(uid, {'dinheiro': dinheiro_atual})
                    print("   🔄 Valor revertido")
                    return True
                else:
                    print(f"   ❌ Valor não foi persistido! Esperado: ${novo_valor_teste}, Obtido: ${dinheiro_verificado}")
                    return False
            else:
                print("   ❌ Erro ao verificar usuário atualizado")
                return False
        else:
            print(f"   ❌ Atualização falhou: {resultado.get('erro', 'Erro desconhecido')}")
            return False
            
    except Exception as e:
        print(f"   ❌ Erro na atualização: {e}")
        return False

def test_mining_function(models, usuarios):
    """Testa função de mineração"""
    print("\n⛏️ Testando função de mineração...")
    
    if not usuarios:
        print("❌ Nenhum usuário para testar")
        return False
    
    # Salvar saldos antes
    saldos_antes = {}
    for usuario in usuarios:
        uid = usuario['uid']
        saldos_antes[uid] = usuario.get('dinheiro', 0)
    
    print("💰 Saldos antes do processamento:")
    for usuario in usuarios:
        uid = usuario['uid']
        nick = usuario.get('nick', 'Unknown')
        saldo = saldos_antes[uid]
        nivel = usuario.get('nivel_mineradora', 1)
        print(f"   {nick} (Nível {nivel}): ${saldo}")
    
    # Executar mineração
    print("\n⚡ Executando mineração...")
    try:
        resultado = models.processar_mineracao_automatica_dinheiro()
        print(f"📊 Resultado da mineração: {resultado}")
        
        if not resultado.get('sucesso'):
            print(f"❌ Mineração falhou: {resultado.get('mensagem', 'Erro desconhecido')}")
            return False
        
        processados = resultado.get('jogadores_processados', 0)
        total_gerado = resultado.get('total_dinheiro_gerado', 0)
        
        print(f"✅ Mineração executada:")
        print(f"   Jogadores processados: {processados}")
        print(f"   Total gerado: ${total_gerado}")
        
        if processados == 0:
            print("⚠️ Nenhum jogador foi processado!")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Erro na mineração: {e}")
        return False

def main():
    """Função principal de diagnóstico"""
    print("🔍 DIAGNÓSTICO COMPLETO DA MINERAÇÃO AUTOMÁTICA")
    print("=" * 55)
    
    # Teste 1: Importações
    supabase_client, models = test_imports()
    if not supabase_client or not models:
        print("\n❌ FALHA: Problemas nas importações")
        return
    
    # Teste 2: Conexão
    if not test_database_connection(supabase_client):
        print("\n❌ FALHA: Problema na conexão com banco")
        return
    
    # Teste 3: Dados dos usuários
    usuarios = analyze_user_data(supabase_client)
    if not usuarios:
        print("\n❌ FALHA: Nenhum usuário encontrado")
        return
    
    # Teste 4: Função de atualização
    if not test_update_function(supabase_client, usuarios):
        print("\n❌ FALHA: Problema na função de atualização")
        return
    
    # Teste 5: Função de mineração
    if not test_mining_function(models, usuarios):
        print("\n❌ FALHA: Problema na função de mineração")
        return
    
    print("\n✅ DIAGNÓSTICO CONCLUÍDO")
    print("🎯 Todos os testes básicos passaram!")
    print("\n📋 Próximos passos:")
    print("1. Verificar se a mineração está gerando valores adequados")
    print("2. Confirmar que nível 1 gera dinheiro")
    print("3. Testar sistema automático (timer)")

if __name__ == "__main__":
    main()
