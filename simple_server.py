from flask import Flask, jsonify
import sys

print("Iniciando servidor simples...")
print(f"Python version: {sys.version}")

app = Flask(__name__)

@app.route("/")
def home():
    return "Servidor simples funcionando!"

@app.route("/api/shop/items")
def shop_items():
    print("Shop acessado!")
    return jsonify({
        "sucesso": True,
        "items": [
            {
                "id": "test_item",
                "nome": "Item de Teste",
                "descricao": "Teste do shop",
                "preco": 100,
                "categoria": "teste"
            }
        ],
        "is_supporter": True,
        "spoints_disponiveis": 1000,
        "mensagem": "Shop funcionando - servidor simples"
    })

@app.route("/api/shop/test")
def shop_test():
    print("Shop teste acessado!")
    return jsonify({
        "sucesso": True,
        "items": [],
        "is_supporter": True,
        "spoints_disponiveis": 0,
        "mensagem": "Shop teste funcionando"
    })

if __name__ == "__main__":
    print("Iniciando Flask...")
    try:
        app.run(debug=False, host="0.0.0.0", port=5000, threaded=True)
    except Exception as e:
        print(f"Erro ao iniciar servidor: {e}")
