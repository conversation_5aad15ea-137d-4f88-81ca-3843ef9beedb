# ===============================================
# INTEGRAÇÃO PYTHON PARA SISTEMA DE TORNEIOS TTL
# ===============================================
# Funções para integrar o novo sistema de torneios
# com TTL automático no backend Python

import datetime
from typing import Dict, List, Optional, Tuple
from database.supabase_client import supabase_client

class TorneioTTLManager:
    """
    Gerenciador para o novo sistema de torneios com TTL automático.
    Este sistema elimina a necessidade de verificações de reset.
    """
    
    def __init__(self):
        self.client = supabase_client.client
        self.tabelas_existem = self._verificar_tabelas()
        self.modo_fallback = not self.client or not self.tabelas_existem
        
        # Cache local para modo fallback
        self._cache_pontos = {}
        
        if self.modo_fallback:
            print("⚠️  Sistema TTL em modo fallback - usando cache local")
            print("   Configure Supabase e execute migrate_tournament_ttl.py para modo completo")
        
    def _verificar_tabelas(self):
        """
        Verifica se as tabelas TTL existem no banco.
        """
        try:
            if not self.client:
                print("[DEBUG] Cliente Supabase não disponível para verificação de tabelas")
                return False
                
            # Tentar acessar tabela principal
            result = self.client.table('torneios_ativos').select('id').limit(1).execute()
            print("[DEBUG] Tabelas TTL encontradas e acessíveis")
            return True
            
        except Exception as e:
            print(f"[DEBUG] Tabelas TTL não encontradas: {e}")
            return False
    
    # ===============================================
    # FUNÇÕES PRINCIPAIS DE PONTUAÇÃO
    # ===============================================
    
    def adicionar_pontos_deface(self, jogador_uid: str, pontos: int = 1, nick: str = None, 
                               grupo_id: str = None, grupo_nome: str = None) -> Dict:
        """
        Adiciona pontos de deface para um jogador.
        O sistema automaticamente cria torneio se não existir.
        """
        try:
            print(f"[DEBUG] Iniciando adicionar_pontos_deface para UID: {jogador_uid}")
            
            # Modo fallback - usar cache local
            if self.modo_fallback:
                print("[DEBUG] Usando modo fallback - cache local")
                
                # Buscar dados do jogador se necessário
                if not nick or not grupo_id:
                    try:
                        from . import new_models as models
                        jogador = models.get_jogador(jogador_uid)
                        if jogador:
                            nick = nick or jogador.get('nick', f"User_{jogador_uid[:8]}")
                            grupo_id = grupo_id or jogador.get('grupo_id', 'sem_grupo')
                            grupo_nome = grupo_nome or jogador.get('grupo_nome', 'Sem Grupo')
                    except:
                        nick = nick or f"User_{jogador_uid[:8]}"
                        grupo_id = grupo_id or 'sem_grupo'
                        grupo_nome = grupo_nome or 'Sem Grupo'
                
                # Adicionar ao cache local
                if jogador_uid not in self._cache_pontos:
                    self._cache_pontos[jogador_uid] = {
                        'nick': nick,
                        'grupo_id': grupo_id,
                        'grupo_nome': grupo_nome,
                        'pontos_deface': 0,
                        'pontos_upgrade': 0
                    }
                
                self._cache_pontos[jogador_uid]['pontos_deface'] += pontos
                
                print(f"[DEBUG] Cache atualizado: {self._cache_pontos[jogador_uid]}")
                
                return {
                    "sucesso": True,
                    "mensagem": f"Pontos de deface adicionados: +{pontos} (modo fallback)",
                    "pontos_adicionados": pontos,
                    "modo": "fallback"
                }
            
            # Verificar se o cliente está disponível
            if not self.client:
                print("[DEBUG] Cliente Supabase não disponível")
                return {"sucesso": False, "mensagem": "Cliente Supabase não disponível"}
            
            # Verificar se as tabelas existem
            if not self.tabelas_existem:
                print("[DEBUG] Tabelas TTL não existem. Execute primeiro a migração.")
                return {
                    "sucesso": False, 
                    "mensagem": "Sistema TTL não inicializado. Execute: python migrate_tournament_ttl.py"
                }
            
            # Se os dados não foram fornecidos, buscar automaticamente
            if not nick or not grupo_id:
                print(f"[DEBUG] Buscando dados do jogador automaticamente. Nick atual: {nick}, Grupo: {grupo_id}")
                from . import new_models as models
                jogador = models.get_jogador(jogador_uid)
                print(f"[DEBUG] Dados do jogador obtidos: {jogador}")
                
                if jogador:
                    nick = nick or jogador.get('nick')
                    grupo_id = grupo_id or jogador.get('grupo_id')
                    grupo_nome = grupo_nome or jogador.get('grupo_nome')
                    print(f"[DEBUG] Dados finais - Nick: {nick}, Grupo ID: {grupo_id}, Grupo Nome: {grupo_nome}")
                else:
                    print(f"[DEBUG] Nenhum jogador encontrado para UID: {jogador_uid}")
                    return {"sucesso": False, "mensagem": "Jogador não encontrado"}
            
            # Verificar se temos dados mínimos necessários
            if not nick:
                print(f"[DEBUG] Nick não encontrado para o jogador {jogador_uid}")
                return {"sucesso": False, "mensagem": "Nick do jogador não encontrado"}
            
            print(f"[DEBUG] Chamando função RPC com parâmetros:")
            print(f"  - jogador_uid_param: {jogador_uid}")
            print(f"  - tipo_torneio_param: deface")
            print(f"  - pontos_individuais_param: {pontos}")
            print(f"  - pontos_grupo_param: 0")
            print(f"  - nick_param: {nick}")
            print(f"  - grupo_id_param: {grupo_id}")
            print(f"  - grupo_nome_param: {grupo_nome}")
            
            result = self.client.rpc('adicionar_pontos_torneio', {
                'jogador_uid_param': jogador_uid,
                'tipo_torneio_param': 'deface',
                'pontos_individuais_param': pontos,
                'pontos_grupo_param': 0,
                'nick_param': nick,
                'grupo_id_param': grupo_id,
                'grupo_nome_param': grupo_nome
            }).execute()
            
            print(f"[DEBUG] Resultado da função RPC: {result}")
            print(f"[DEBUG] Dados retornados: {result.data}")
            
            if result.data:
                return {
                    "sucesso": True,
                    "mensagem": f"Pontos de deface adicionados: +{pontos}",
                    "pontos_adicionados": pontos,
                    "debug_info": {
                        "nick": nick,
                        "grupo_id": grupo_id,
                        "resultado_rpc": result.data
                    }
                }
            else:
                return {"sucesso": False, "mensagem": "Erro ao adicionar pontos - resultado vazio"}
                
        except Exception as e:
            print(f"[DEBUG] Erro ao adicionar pontos deface: {e}")
            print(f"[DEBUG] Tipo do erro: {type(e)}")
            import traceback
            print(f"[DEBUG] Traceback completo: {traceback.format_exc()}")
            return {"sucesso": False, "mensagem": f"Erro: {str(e)}"}
    
    def adicionar_pontos_upgrade(self, jogador_uid: str, app_type: str, pontos: int = 1, 
                                nick: str = None, grupo_id: str = None, grupo_nome: str = None) -> Dict:
        """
        Adiciona pontos de upgrade para um jogador e seu grupo.
        O sistema automaticamente cria torneio se não existir.
        """
        try:
            # Verificar se é o app do dia
            status_torneio = self.get_status_torneio('upgrade')
            app_do_dia = status_torneio.get('app_do_dia') if status_torneio['sucesso'] else None
            
            # Se não é o app do dia, não dar pontos
            if app_do_dia and app_type != app_do_dia:
                return {
                    "sucesso": False,
                    "mensagem": f"Hoje só upgrades de {app_do_dia} dão pontos no torneio!"
                }
            
            # Se os dados não foram fornecidos, buscar automaticamente
            if not nick or not grupo_id:
                from . import new_models as models
                jogador = models.get_jogador(jogador_uid)
                if jogador:
                    nick = nick or jogador.get('nick')
                    grupo_id = grupo_id or jogador.get('grupo_id')
                    grupo_nome = grupo_nome or jogador.get('grupo_nome')
            
            result = self.client.rpc('adicionar_pontos_torneio', {
                'jogador_uid_param': jogador_uid,
                'tipo_torneio_param': 'upgrade',
                'pontos_individuais_param': pontos,
                'pontos_grupo_param': pontos,  # Pontos também para o grupo
                'nick_param': nick,
                'grupo_id_param': grupo_id,
                'grupo_nome_param': grupo_nome
            }).execute()
            
            if result.data:
                return {
                    "sucesso": True,
                    "mensagem": f"Pontos de upgrade adicionados: +{pontos} (individual e grupo)",
                    "pontos_adicionados": pontos,
                    "app_do_dia": app_do_dia
                }
            else:
                return {"sucesso": False, "mensagem": "Erro ao adicionar pontos"}
                
        except Exception as e:
            print(f"Erro ao adicionar pontos upgrade: {e}")
            return {"sucesso": False, "mensagem": f"Erro: {str(e)}"}
    
    # ===============================================
    # FUNÇÕES DE CONSULTA DE RANKING
    # ===============================================
    
    def get_ranking_torneio(self, tipo: str, modo: str = 'individual', limite: int = 50) -> List[Dict]:
        """
        Função unificada para obter ranking de torneios.
        Agora busca informações completas dos jogadores (nick, grupo).
        
        Args:
            tipo: 'deface' ou 'upgrade'
            modo: 'individual', 'grupo' (apenas para upgrade)
            limite: número máximo de resultados
        """
        try:
            print(f"[DEBUG] get_ranking_torneio - Tipo: {tipo}, Modo: {modo}, Limite: {limite}")
            
            # Verificar se o cliente está disponível
            if not self.client:
                print("[DEBUG] Cliente Supabase não disponível na função ranking")
                return []
            
            # Chamar função RPC que retorna dados básicos
            result = self.client.rpc('get_ranking_torneio', {
                'tipo_param': tipo,
                'limite': limite
            }).execute()
            
            print(f"[DEBUG] Resultado RPC ranking: {result}")
            
            if not result.data:
                print("[DEBUG] Nenhum dado retornado da RPC")
                return []
            
            # Agora enriquecer com dados dos jogadores
            ranking_enriquecido = []
            
            for item in result.data:
                uid = item['jogador_uid']
                pontos = item['total_pontos']
                posicao = item['posicao']
                
                # Buscar dados do jogador
                nick = f'User_{uid[:8]}'  # Valor padrão
                grupo_nome = 'Sem Grupo'  # Valor padrão
                grupo_id = 'sem_grupo'    # Valor padrão
                
                try:
                    # Tentar buscar dados do jogador diretamente do Supabase
                    jogador_result = self.client.table('usuarios').select(
                        'nick, grupo_id'
                    ).eq('uid', uid).limit(1).execute()
                    
                    if jogador_result.data:
                        jogador_data = jogador_result.data[0]
                        nick = jogador_data.get('nick') or f'User_{uid[:8]}'
                        grupo_id = jogador_data.get('grupo_id') or 'sem_grupo'
                        
                        # Buscar nome real do grupo
                        if grupo_id and grupo_id != 'sem_grupo' and grupo_id != 'null' and grupo_id.strip():
                            try:
                                # Buscar nome real do grupo na tabela grupos
                                grupo_result = self.client.table('grupos').select(
                                    'nome'
                                ).eq('id', grupo_id).limit(1).execute()

                                if grupo_result.data:
                                    grupo_nome = grupo_result.data[0].get('nome', f'Grupo {grupo_id[:8]}')
                                    print(f"[DEBUG] Nome do grupo encontrado: {grupo_nome} para ID {grupo_id}")
                                else:
                                    # Fallback se não encontrar o grupo
                                    grupo_nome = f'Grupo {grupo_id[:8]}'
                                    print(f"[DEBUG] Grupo não encontrado, usando fallback: {grupo_nome}")
                            except Exception as e:
                                print(f"[DEBUG] Erro ao buscar nome do grupo {grupo_id}: {e}")
                                grupo_nome = f'Grupo {grupo_id[:8]}'
                        else:
                            grupo_nome = 'Sem Grupo'
                            
                        print(f"[DEBUG] Dados encontrados para {uid}: nick={nick}, grupo_id={grupo_id}, grupo_nome={grupo_nome}")
                        print(f"[DEBUG] Verificando se grupo_nome é válido: '{grupo_nome}' != 'Sem Grupo': {grupo_nome != 'Sem Grupo'}")
                    else:
                        print(f"[DEBUG] Nenhum dado encontrado para {uid} - usando padrões")
                        
                except Exception as e:
                    print(f"[DEBUG] Erro ao buscar dados do jogador {uid}: {e}")
                    # Manter valores padrão já definidos
                
                # Criar item enriquecido com todos os formatos que o frontend espera
                item_enriquecido = {
                    'jogador_uid': uid,
                    'nick': nick,
                    'grupo_nome': grupo_nome,      # Campo principal para grupos
                    'group_name': grupo_nome,      # Campo alternativo
                    'grupo': grupo_nome,           # Campo alternativo  
                    'grupo_id': grupo_id,
                    'pontos': int(pontos),
                    'posicao': int(posicao)
                }
                
                ranking_enriquecido.append(item_enriquecido)
                
            print(f"[DEBUG] Ranking enriquecido com {len(ranking_enriquecido)} itens")
            print(f"[DEBUG] Ranking enriquecido: {ranking_enriquecido}")
            return ranking_enriquecido
            
        except Exception as e:
            print(f"[DEBUG] Erro ao obter ranking {tipo}: {e}")
            import traceback
            print(f"[DEBUG] Traceback: {traceback.format_exc()}")
            return []
    
    def _get_current_timestamp(self):
        """Retorna timestamp atual em formato ISO"""
        from datetime import datetime
        return datetime.now().isoformat()
    
    def _criar_ranking_grupos(self, ranking_individual: List[Dict]) -> List[Dict]:
        """
        Cria ranking de grupos baseado no ranking individual.
        Agrupa jogadores por grupo e soma os pontos.
        """
        try:
            grupos = {}
            
            # Agrupar jogadores por grupo
            for jogador in ranking_individual:
                grupo_id = jogador.get('grupo_id', 'sem_grupo')
                grupo_nome = jogador.get('grupo_nome', 'Sem Grupo')
                pontos = jogador.get('pontos', 0)
                nick = jogador.get('nick', 'Desconhecido')
                
                # Apenas incluir grupos válidos (não "Sem Grupo")
                if grupo_id != 'sem_grupo' and grupo_nome != 'Sem Grupo':
                    if grupo_id not in grupos:
                        grupos[grupo_id] = {
                            'nome': grupo_nome,
                            'grupo_id': grupo_id,
                            'tournament_points': 0,
                            'membros': []
                        }
                    
                    grupos[grupo_id]['tournament_points'] += pontos
                    grupos[grupo_id]['membros'].append({
                        'nick': nick,
                        'pontos': pontos,
                        'uid': jogador.get('jogador_uid')
                    })
            
            # Converter para lista e ordenar por pontos
            ranking_grupos = list(grupos.values())
            ranking_grupos.sort(key=lambda x: x['tournament_points'], reverse=True)
            
            print(f"[DEBUG] Ranking de grupos criado: {len(ranking_grupos)} grupos")
            for grupo in ranking_grupos:
                print(f"   - Nome: {grupo['nome']}, ID: {grupo['grupo_id']}, Pontos: {grupo['tournament_points']} pts ({len(grupo['membros'])} membros)")
                print(f"     Dados completos: {grupo}")
            
            return ranking_grupos
            
        except Exception as e:
            print(f"[DEBUG] Erro ao criar ranking de grupos: {e}")
            return []
    
    def get_ranking_deface(self, limite: int = 50) -> Dict:
        """
        Obtém o ranking atual de deface.
        Não precisa verificar reset - o banco faz automaticamente.
        """
        try:
            print(f"[DEBUG] Iniciando get_ranking_deface com limite: {limite}")
            
            # Modo fallback - usar cache local
            if self.modo_fallback:
                print("[DEBUG] Usando modo fallback para ranking")
                
                # Ordenar cache por pontos de deface
                ranking_cache = []
                for uid, dados in self._cache_pontos.items():
                    if dados['pontos_deface'] > 0:
                        ranking_cache.append({
                            "jogador_uid": uid,
                            "nick": dados['nick'],
                            "pontos": dados['pontos_deface'],
                            "grupo_nome": dados['grupo_nome'],
                            "ultima_atualizacao": "cache"
                        })
                
                # Ordenar por pontos
                ranking_cache.sort(key=lambda x: x['pontos'], reverse=True)
                
                # Adicionar posições
                for i, item in enumerate(ranking_cache[:limite], 1):
                    item['posicao'] = i
                
                print(f"[DEBUG] Ranking do cache: {ranking_cache}")
                
                return {
                    "sucesso": True,
                    "ranking": ranking_cache,
                    "total": len(ranking_cache),
                    "modo": "fallback"
                }
            
            # Verificar se o cliente está disponível
            if not self.client:
                print("[DEBUG] Cliente Supabase não disponível para ranking")
                return {"sucesso": False, "mensagem": "Cliente Supabase não disponível"}
            
            # Verificar se as tabelas existem
            if not self.tabelas_existem:
                print("[DEBUG] Tabelas TTL não existem - retornando ranking vazio")
                return {
                    "sucesso": True,
                    "ranking": [],
                    "total": 0,
                    "mensagem": "Sistema TTL não inicializado. Execute: python migrate_tournament_ttl.py"
                }
            
            ranking_formatado = self.get_ranking_torneio('deface', 'individual', limite)
            print(f"[DEBUG] Ranking obtido: {ranking_formatado}")
            print(f"[DEBUG] Número de registros no ranking: {len(ranking_formatado)}")
            
            return {
                "sucesso": True,
                "ranking": ranking_formatado,
                "total": len(ranking_formatado),
                "ranking_grupos": self._criar_ranking_grupos(ranking_formatado)
            }
                
        except Exception as e:
            print(f"[DEBUG] Erro ao obter ranking deface: {e}")
            import traceback
            print(f"[DEBUG] Traceback: {traceback.format_exc()}")
            return {"sucesso": False, "mensagem": f"Erro: {str(e)}"}
    
    def get_ranking_upgrade(self, limite: int = 50) -> Dict:
        """
        Obtém o ranking atual de upgrade (individual).
        Não precisa verificar reset - o banco faz automaticamente.
        """
        try:
            ranking_individual = self.get_ranking_torneio('upgrade', 'individual', limite)
            
            return {
                "sucesso": True,
                "ranking": ranking_individual,
                "total": len(ranking_individual)
            }
                
        except Exception as e:
            print(f"Erro ao obter ranking upgrade: {e}")
            return {"sucesso": False, "mensagem": f"Erro: {str(e)}"}
    
    # ===============================================
    # FUNÇÕES DE STATUS E INFORMAÇÕES
    # ===============================================
    
    def get_status_torneio(self, tipo: str) -> Dict:
        """
        Obtém status atual do torneio.
        Inclui tempo restante, participantes, etc.
        """
        try:
            result = self.client.rpc('get_status_torneio', {
                'tipo_param': tipo
            }).execute()
            
            if result.data and len(result.data) > 0:
                status = result.data[0]
                
                # Calcular tempo restante em formato amigável
                data_fim_str = status.get('data_fim')
                if data_fim_str:
                    data_fim = datetime.datetime.fromisoformat(data_fim_str.replace('Z', '+00:00'))
                    agora = datetime.datetime.now(datetime.timezone.utc)
                    tempo_restante = data_fim - agora
                    
                    horas = int(tempo_restante.total_seconds() // 3600)
                    minutos = int((tempo_restante.total_seconds() % 3600) // 60)
                    segundos = int(tempo_restante.total_seconds() % 60)
                else:
                    horas, minutos, segundos = 0, 0, 0
                
                return {
                    "sucesso": True,
                    "torneio_id": status.get('torneio_id'),
                    "tipo": status.get('tipo'),
                    "app_do_dia": status.get('app_do_dia'),
                    "temporada": status.get('temporada'),
                    "total_participantes": status.get('total_participantes', 0),
                    "tempo_restante": {
                        "horas": horas,
                        "minutos": minutos,
                        "segundos": segundos,
                        "total_segundos": max(0, int(tempo_restante.total_seconds()))
                    },
                    "data_inicio": status.get('data_inicio'),
                    "data_fim": status.get('data_fim')
                }
            else:
                return {
                    "sucesso": False,
                    "mensagem": f"Nenhum torneio ativo encontrado para tipo: {tipo}"
                }
                
        except Exception as e:
            print(f"Erro ao obter status do torneio: {e}")
            return {"sucesso": False, "mensagem": f"Erro: {str(e)}"}
    
    def get_app_do_dia(self) -> str:
        """
        Obtém o app do dia do torneio de upgrade.
        """
        try:
            status = self.get_status_torneio('upgrade')
            if status['sucesso']:
                return status.get('app_do_dia', 'cpu')
            else:
                # Se não há torneio ativo, escolher app baseado no dia
                apps = ['cpu', 'firewall', 'antivirus', 'malware_kit', 'bruteforce', 'bankguard', 'proxyvpn']
                dia_ano = datetime.datetime.now().timetuple().tm_yday
                return apps[dia_ano % len(apps)]
                
        except Exception as e:
            print(f"Erro ao obter app do dia: {e}")
            return 'cpu'  # Fallback
    
    # ===============================================
    # FUNÇÕES ADMINISTRATIVAS
    # ===============================================
    
    def criar_torneio_manual(self, tipo: str, app_do_dia: str = None) -> Dict:
        """
        Cria um novo torneio manualmente (apenas para admins).
        """
        try:
            result = self.client.rpc('criar_novo_torneio', {
                'tipo_param': tipo,
                'app_do_dia_param': app_do_dia
            }).execute()
            
            if result.data:
                return {
                    "sucesso": True,
                    "mensagem": f"Torneio {tipo} criado com sucesso",
                    "torneio_id": result.data,
                    "app_do_dia": app_do_dia
                }
            else:
                return {"sucesso": False, "mensagem": "Erro ao criar torneio"}
                
        except Exception as e:
            print(f"Erro ao criar torneio: {e}")
            return {"sucesso": False, "mensagem": f"Erro: {str(e)}"}
    
    def get_historico_recompensas(self, jogador_uid: str = None, limite: int = 50) -> Dict:
        """
        Obtém histórico de recompensas distribuídas.
        """
        try:
            query = self.client.table('torneio_historico_recompensas')
            
            if jogador_uid:
                query = query.eq('jogador_uid', jogador_uid)
            
            result = query.order('data_pagamento', desc=True).limit(limite).execute()
            
            if result.data:
                return {
                    "sucesso": True,
                    "historico": result.data,
                    "total": len(result.data)
                }
            else:
                return {
                    "sucesso": True,
                    "historico": [],
                    "total": 0
                }
                
        except Exception as e:
            print(f"Erro ao obter histórico: {e}")
            return {"sucesso": False, "mensagem": f"Erro: {str(e)}"}
    
    def get_pontos_jogador(self, jogador_uid: str, tipo_torneio: str) -> Dict:
        """
        Obtém pontos atuais de um jogador específico.
        """
        try:
            result = self.client.table('torneio_pontuacoes').select(
                'pontos_individuais, pontos_grupo, ultima_atualizacao'
            ).eq('jogador_uid', jogador_uid).eq('tipo_torneio', tipo_torneio).execute()
            
            if result.data and len(result.data) > 0:
                dados = result.data[0]
                return {
                    "sucesso": True,
                    "pontos_individuais": dados.get('pontos_individuais', 0),
                    "pontos_grupo": dados.get('pontos_grupo', 0),
                    "ultima_atualizacao": dados.get('ultima_atualizacao')
                }
            else:
                return {
                    "sucesso": True,
                    "pontos_individuais": 0,
                    "pontos_grupo": 0,
                    "ultima_atualizacao": None
                }
                
        except Exception as e:
            print(f"Erro ao obter pontos do jogador: {e}")
            return {"sucesso": False, "mensagem": f"Erro: {str(e)}"}

# ===============================================
# INSTÂNCIA GLOBAL DO GERENCIADOR
# ===============================================
torneio_manager = TorneioTTLManager()

# ===============================================
# FUNÇÕES DE COMPATIBILIDADE (MIGRAÇÃO GRADUAL)
# ===============================================

def auto_reset_deface_tournaments_if_needed():
    """
    Função de compatibilidade - não faz nada no novo sistema.
    O reset é automático via TTL.
    """
    pass

def auto_reset_tournament_if_needed():
    """
    Função de compatibilidade - não faz nada no novo sistema.
    O reset é automático via TTL.
    """
    pass

def get_groups_deface_ranking():
    """
    Função de compatibilidade para ranking de deface.
    """
    return torneio_manager.get_ranking_deface()

def get_individual_deface_ranking():
    """
    Função de compatibilidade para ranking individual de deface.
    """
    return torneio_manager.get_ranking_deface()

def get_tournament_ranking():
    """
    Função de compatibilidade para ranking de upgrade.
    """
    return torneio_manager.get_ranking_upgrade()

def get_tournament_status():
    """
    Função de compatibilidade para status do torneio.
    """
    return torneio_manager.get_status_torneio('upgrade')

def get_app_do_torneio_hoje():
    """
    Função de compatibilidade para app do dia.
    """
    return torneio_manager.get_app_do_dia()

def get_tournament_time_remaining():
    """
    Função de compatibilidade para tempo restante.
    """
    status = torneio_manager.get_status_torneio('deface')
    if status['sucesso']:
        return status.get('tempo_restante', {})
    return {"horas": 0, "minutos": 0, "segundos": 0}

def realizar_deface(atacante_uid: str, alvo_uid: str):
    """
    Função de compatibilidade - adiciona pontos de deface após deface bem-sucedido.
    """
    # Aqui você mantém a lógica original de deface
    # E adiciona os pontos automaticamente
    
    # [Lógica original de deface aqui...]
    
    # Adicionar pontos automaticamente (os dados do jogador são buscados automaticamente)
    try:
        resultado_pontos = torneio_manager.adicionar_pontos_deface(
            jogador_uid=atacante_uid,
            pontos=1
        )
        
        return {
            "sucesso": True,
            "mensagem": "Deface realizado com sucesso!",
            "pontos_torneio": resultado_pontos
        }
    except Exception as e:
        print(f"Erro ao adicionar pontos após deface: {e}")
    
    return {"sucesso": True, "mensagem": "Deface realizado com sucesso!"}

def comprar_upgrade(jogador_uid: str, item: str, quantidade: int = 1):
    """
    Função de compatibilidade - adiciona pontos de upgrade após compra.
    """
    # Aqui você mantém a lógica original de compra
    # E adiciona os pontos automaticamente se for o app do dia
    
    # [Lógica original de upgrade aqui...]
    
    # Adicionar pontos automaticamente (os dados do jogador são buscados automaticamente)
    try:
        resultado_pontos = torneio_manager.adicionar_pontos_upgrade(
            jogador_uid=jogador_uid,
            app_type=item,
            pontos=quantidade
        )
        
        return {
            "sucesso": True,
            "mensagem": f"Upgrade de {item} realizado com sucesso!",
            "pontos_torneio": resultado_pontos
        }
    except Exception as e:
        print(f"Erro ao adicionar pontos após upgrade: {e}")
    
    return {"sucesso": True, "mensagem": f"Upgrade de {item} realizado com sucesso!"}
