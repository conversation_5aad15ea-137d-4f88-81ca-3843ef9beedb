#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.supabase_client import supabase_client
import game.new_models as models
from datetime import datetime, timezone

def debug_usuarios():
    """Debug específico dos usuários"""
    print("🔍 DEBUG DOS USUÁRIOS")
    print("=" * 40)
    
    # 1. Verificar conexão
    print("1. Verificando conexão...")
    if not supabase_client.is_connected():
        print("❌ Não conectado!")
        return
    print("✅ Conectado!")
    
    # 2. Buscar usuários
    print("\n2. Buscando usuários...")
    usuarios = supabase_client.get_ranking_usuarios(1000)
    print(f"Total usuários: {len(usuarios)}")
    
    if not usuarios:
        print("❌ Nenhum usuário encontrado!")
        return
    
    # 3. Analisar usuários com mineradora
    print("\n3. Analisando usuários com mineradora...")
    usuarios_com_mineradora = []
    
    for usuario in usuarios:
        uid = usuario.get('uid')
        nick = usuario.get('nick', 'Unknown')
        nivel_mineradora = usuario.get('nivel_mineradora', 1)
        
        if uid and nivel_mineradora >= 1:
            usuarios_com_mineradora.append({
                'uid': uid,
                'nick': nick,
                'nivel_mineradora': nivel_mineradora,
                'dinheiro': usuario.get('dinheiro', 0)
            })
    
    print(f"Usuários com mineradora ativa: {len(usuarios_com_mineradora)}")
    
    if usuarios_com_mineradora:
        print("\nPrimeiros 5 usuários com mineradora:")
        for i, usuario in enumerate(usuarios_com_mineradora[:5]):
            print(f"  {i+1}. {usuario['nick']} (UID: {usuario['uid']})")
            print(f"     Nível: {usuario['nivel_mineradora']}, Dinheiro: ${usuario['dinheiro']}")
    
    # 4. Verificar cache
    print(f"\n4. Estado do cache:")
    try:
        cache = models.MINERACAO_AUTOMATICA_CACHE
        print(f"   Entradas: {len(cache)}")
        
        # Mostrar quais usuários estão no cache
        if cache:
            print("   Usuários no cache:")
            timestamp_atual = datetime.now(timezone.utc).timestamp()
            for uid, timestamp in cache.items():
                tempo_desde = timestamp_atual - timestamp
                # Encontrar nick do usuário
                nick = "Unknown"
                for u in usuarios_com_mineradora:
                    if u['uid'] == uid:
                        nick = u['nick']
                        break
                print(f"     {nick} ({uid}): {tempo_desde:.1f}s atrás")
        
        # Limpar cache para teste
        print("\n   Limpando cache...")
        cache.clear()
        print(f"   Cache após limpeza: {len(cache)} entradas")
        
    except Exception as e:
        print(f"   Erro no cache: {e}")
    
    # 5. Testar processamento manual de um usuário
    if usuarios_com_mineradora:
        print(f"\n5. Teste manual com primeiro usuário...")
        usuario_teste = usuarios_com_mineradora[0]
        uid = usuario_teste['uid']
        nick = usuario_teste['nick']
        nivel = usuario_teste['nivel_mineradora']
        dinheiro_atual = usuario_teste['dinheiro']
        
        print(f"   Testando: {nick}")
        print(f"   UID: {uid}")
        print(f"   Nível: {nivel}")
        print(f"   Dinheiro atual: ${dinheiro_atual}")
        
        # Calcular dinheiro esperado
        dinheiro_base = 15 + (nivel - 1) * 20 + (nivel - 1) * 5
        print(f"   Dinheiro base calculado: ${dinheiro_base}")
        
        # Aplicar efeito NFT
        try:
            usuario_completo = None
            for u in usuarios:
                if u.get('uid') == uid:
                    usuario_completo = u
                    break
            
            if usuario_completo:
                dinheiro_final = models.aplicar_efeito_nft_firstsupp_dinheiro(dinheiro_base, usuario_completo)
                print(f"   Dinheiro após NFT: ${dinheiro_final}")
                
                novo_total = dinheiro_atual + dinheiro_final
                print(f"   Novo total esperado: ${novo_total}")
                
                # Testar atualização
                resultado = supabase_client.atualizar_usuario(uid, {
                    'dinheiro': novo_total
                })
                
                print(f"   Resultado atualização: {resultado}")
                
                if resultado.get('sucesso'):
                    print("   ✅ Atualização manual funcionou!")
                else:
                    print(f"   ❌ Atualização falhou: {resultado}")
            else:
                print("   ❌ Usuário completo não encontrado")
                
        except Exception as e:
            print(f"   ❌ Erro no teste manual: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    debug_usuarios()
