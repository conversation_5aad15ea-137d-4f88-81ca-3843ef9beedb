#!/usr/bin/env python3
"""
Verificar pontos nas tabelas de torneio
"""

import sys
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def verificar_pontos():
    print("🔍 VERIFICANDO PONTOS NAS TABELAS DE TORNEIO")
    print("=" * 50)
    
    try:
        # Import modules
        from database.supabase_client import supabase_client
        
        if not supabase_client.is_connected():
            print("❌ Supabase not connected!")
            return
        
        print("✅ Database connected")
        
        # 1. Verificar tabela torneio_pontuacoes
        print("\n📊 TABELA: torneio_pontuacoes")
        print("-" * 30)
        
        try:
            result = supabase_client.client.table('torneio_pontuacoes').select(
                'jogador_uid, nick, pontos_individuais, group_points, grupo_nome'
            ).eq('tipo_torneio', 'deface').gt('pontos_individuais', 0).order('pontos_individuais', desc=True).limit(10).execute()
            
            if result.data:
                print(f"✅ Encontrados {len(result.data)} jogadores com pontos:")
                for i, jogador in enumerate(result.data, 1):
                    nick = jogador.get('nick', 'Unknown')
                    pontos_ind = jogador.get('pontos_individuais', 0)
                    pontos_grupo = jogador.get('group_points', 0)
                    grupo = jogador.get('grupo_nome', 'Sem grupo')
                    print(f"   {i:2d}. {nick}: {pontos_ind} pts individuais, {pontos_grupo} pts grupo ({grupo})")
            else:
                print("❌ Nenhum jogador com pontos encontrado na tabela torneio_pontuacoes")
        except Exception as e:
            print(f"❌ Erro ao consultar torneio_pontuacoes: {e}")
        
        # 2. Verificar tabela usuarios (sistema antigo)
        print("\n👤 TABELA: usuarios (deface_points_individual)")
        print("-" * 40)
        
        try:
            result = supabase_client.client.table('usuarios').select(
                'uid, nick, deface_points_individual'
            ).gt('deface_points_individual', 0).order('deface_points_individual', desc=True).limit(10).execute()
            
            if result.data:
                print(f"✅ Encontrados {len(result.data)} jogadores com pontos:")
                for i, jogador in enumerate(result.data, 1):
                    nick = jogador.get('nick', 'Unknown')
                    pontos = jogador.get('deface_points_individual', 0)
                    print(f"   {i:2d}. {nick}: {pontos} pontos")
            else:
                print("❌ Nenhum jogador com pontos encontrado na tabela usuarios")
        except Exception as e:
            print(f"❌ Erro ao consultar usuarios: {e}")
        
        # 3. Verificar tabela grupos (sistema antigo)
        print("\n🏆 TABELA: grupos (deface_points)")
        print("-" * 30)
        
        try:
            result = supabase_client.client.table('grupos').select(
                'id, nome, deface_points, tournament_points'
            ).gt('deface_points', 0).order('deface_points', desc=True).limit(10).execute()
            
            if result.data:
                print(f"✅ Encontrados {len(result.data)} grupos com pontos:")
                for i, grupo in enumerate(result.data, 1):
                    nome = grupo.get('nome', 'Unknown')
                    pontos_deface = grupo.get('deface_points', 0)
                    pontos_torneio = grupo.get('tournament_points', 0)
                    print(f"   {i:2d}. {nome}: {pontos_deface} deface, {pontos_torneio} torneio")
            else:
                print("❌ Nenhum grupo com pontos encontrado na tabela grupos")
        except Exception as e:
            print(f"❌ Erro ao consultar grupos: {e}")
        
        # 4. Verificar se há função RPC para ranking
        print("\n🔧 TESTANDO FUNÇÕES RPC")
        print("-" * 25)
        
        try:
            result = supabase_client.client.rpc('get_ranking_grupos_deface', {'limite': 3}).execute()
            if result.data:
                print(f"✅ RPC get_ranking_grupos_deface funcionando: {len(result.data)} grupos")
                for grupo in result.data:
                    nome = grupo.get('grupo_nome', 'Unknown')
                    pontos = grupo.get('total_pontos', 0)
                    print(f"   - {nome}: {pontos} pontos")
            else:
                print("⚠️ RPC get_ranking_grupos_deface retornou vazio")
        except Exception as e:
            print(f"❌ RPC get_ranking_grupos_deface não disponível: {e}")
        
        try:
            result = supabase_client.client.rpc('get_ranking_individual_deface', {'limite': 5}).execute()
            if result.data:
                print(f"✅ RPC get_ranking_individual_deface funcionando: {len(result.data)} jogadores")
                for jogador in result.data:
                    nick = jogador.get('nick', 'Unknown')
                    pontos = jogador.get('pontos_individuais', 0)
                    print(f"   - {nick}: {pontos} pontos")
            else:
                print("⚠️ RPC get_ranking_individual_deface retornou vazio")
        except Exception as e:
            print(f"❌ RPC get_ranking_individual_deface não disponível: {e}")
        
        print(f"\n🎯 RESUMO:")
        print(f"✅ Verificação concluída - verifique os dados acima")
        print(f"📋 Se há pontos nas tabelas, o reset deve funcionar")
        print(f"🔧 Se não há pontos, pode ser que já foram resetados ou não há atividade")
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    verificar_pontos()
