#!/usr/bin/env python3
"""
Script para remover/substituir todos os console.log, console.error, etc. do projeto
"""

import os
import re
import glob

def process_js_file(file_path):
    """Processa um arquivo JavaScript removendo console logs"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        changes_made = 0
        
        # Padrões para encontrar console logs
        patterns = [
            # console.log(...);
            (r'console\.log\([^;]*\);?', '// console.log removido'),
            # console.error(...);
            (r'console\.error\([^;]*\);?', '// console.error removido'),
            # console.warn(...);
            (r'console\.warn\([^;]*\);?', '// console.warn removido'),
            # console.info(...);
            (r'console\.info\([^;]*\);?', '// console.info removido'),
            # console.debug(...);
            (r'console\.debug\([^;]*\);?', '// console.debug removido'),
            # console.trace(...);
            (r'console\.trace\([^;]*\);?', '// console.trace removido'),
        ]
        
        # Aplicar substituições
        for pattern, replacement in patterns:
            matches = re.findall(pattern, content)
            if matches:
                content = re.sub(pattern, replacement, content)
                changes_made += len(matches)
        
        # Salvar apenas se houve mudanças
        if changes_made > 0:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            # print de debug removido
            return changes_made
        else:
            print(f"⚪ {file_path}: Nenhum console log encontrado")
            return 0
            
    except Exception as e:
        # print de debug removido
        return 0

def process_python_file(file_path):
    """Processa um arquivo Python removendo prints de debug"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        changes_made = 0
        
        # Padrões para encontrar prints de debug (mais conservador)
        patterns = [
            # print com emojis de debug
            (r'print\(f?"[🔍🐛📊⚡🔄✅❌⚠️💰📋🌐🧪🔧].*?\)', '# print de debug removido'),
            # print com [DEBUG]
            (r'print\(f?".*?\[DEBUG\].*?\)', '# print de debug removido'),
            # debug_print calls
            (r'debug_print\([^)]*\)', '# debug_print removido'),
        ]
        
        # Aplicar substituições
        for pattern, replacement in patterns:
            matches = re.findall(pattern, content)
            if matches:
                content = re.sub(pattern, replacement, content)
                changes_made += len(matches)
        
        # Salvar apenas se houve mudanças
        if changes_made > 0:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            # print de debug removido
            return changes_made
        else:
            print(f"⚪ {file_path}: Nenhum print de debug encontrado")
            return 0
            
    except Exception as e:
        # print de debug removido
        return 0

def main():
    """Função principal"""
    print("🧹 Iniciando remoção de console logs e prints de debug...")
    
    total_js_changes = 0
    total_py_changes = 0
    
    # Processar arquivos JavaScript
    print("\n📄 Processando arquivos JavaScript...")
    js_files = glob.glob('game/static/js/*.js', recursive=True)
    
    for js_file in js_files:
        changes = process_js_file(js_file)
        total_js_changes += changes
    
    # Processar arquivos Python
    print("\n🐍 Processando arquivos Python...")
    py_files = []
    
    # Adicionar arquivos Python específicos
    py_files.extend(glob.glob('*.py'))
    py_files.extend(glob.glob('game/*.py'))
    py_files.extend(glob.glob('database/*.py'))
    
    for py_file in py_files:
        if os.path.exists(py_file):
            changes = process_python_file(py_file)
            total_py_changes += changes
    
    print(f"\n📊 RESUMO:")
    print(f"   JavaScript: {total_js_changes} console logs removidos")
    print(f"   Python: {total_py_changes} prints de debug removidos")
    print(f"   Total: {total_js_changes + total_py_changes} logs removidos")
    
    if total_js_changes + total_py_changes > 0:
        print("\n✅ Limpeza concluída com sucesso!")
        # print de debug removido
        print("   - config_debug.py → CONSOLE_LOGS_ENABLED = True")
        print("   - game/static/js/main.js → CONSOLE_LOGS_ENABLED = true")
    else:
        print("\n⚪ Nenhum log foi encontrado para remoção")

if __name__ == "__main__":
    main()
