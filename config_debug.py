"""
Configuração global de debug para o SHACK Game
Controla logs de debug em todo o sistema
"""

# === CONFIGURAÇÃO PRINCIPAL ===
DEBUG_MODE = False  # Altere para True para ativar todos os logs de debug
CONSOLE_LOGS_ENABLED = False  # Altere para True para ativar console.log, console.error, etc.

# === CONFIGURAÇÕES ESPECÍFICAS ===
DEBUG_AUTH = DEBUG_MODE  # Logs de autenticação
DEBUG_GAME = DEBUG_MODE  # Logs do jogo
DEBUG_API = DEBUG_MODE   # Logs de API
DEBUG_CACHE = DEBUG_MODE # Logs de cache
DEBUG_CHAT = DEBUG_MODE  # Logs do chat
DEBUG_MINING = DEBUG_MODE # Logs de mineração
DEBUG_EXPLOIT = DEBUG_MODE # Logs de exploits

# === FUNÇÕES DE LOG CONDICIONAIS ===
def # debug_print removido:
    """Print condicional para debug geral"""
    if DEBUG_MODE:
        print(*args, **kwargs)

def debug_auth(*args, **kwargs):
    """Print condicional para debug de autenticação"""
    if DEBUG_AUTH:
        print("[AUTH]", *args, **kwargs)

def debug_game(*args, **kwargs):
    """Print condicional para debug do jogo"""
    if DEBUG_GAME:
        print("[GAME]", *args, **kwargs)

def debug_api(*args, **kwargs):
    """Print condicional para debug de API"""
    if DEBUG_API:
        print("[API]", *args, **kwargs)

def debug_cache(*args, **kwargs):
    """Print condicional para debug de cache"""
    if DEBUG_CACHE:
        print("[CACHE]", *args, **kwargs)

def debug_chat(*args, **kwargs):
    """Print condicional para debug do chat"""
    if DEBUG_CHAT:
        print("[CHAT]", *args, **kwargs)

def debug_mining(*args, **kwargs):
    """Print condicional para debug de mineração"""
    if DEBUG_MINING:
        print("[MINING]", *args, **kwargs)

def debug_exploit(*args, **kwargs):
    """Print condicional para debug de exploits"""
    if DEBUG_EXPLOIT:
        print("[EXPLOIT]", *args, **kwargs)

# === CONFIGURAÇÃO DE LOGGING ===
def configure_logging():
    """Configura o sistema de logging baseado no DEBUG_MODE"""
    import logging
    
    if not DEBUG_MODE:
        # Reduzir verbosidade quando debug está desabilitado
        logging.getLogger('werkzeug').setLevel(logging.ERROR)
        logging.getLogger('urllib3').setLevel(logging.ERROR)
        logging.getLogger('requests').setLevel(logging.ERROR)
        
        # Silenciar completamente se CONSOLE_LOGS_ENABLED = False
        if not CONSOLE_LOGS_ENABLED:
            logging.getLogger().setLevel(logging.CRITICAL)
    else:
        # Manter logs detalhados quando debug está habilitado
        logging.basicConfig(level=logging.DEBUG)

# === INSTRUÇÕES DE USO ===
"""
Para ativar logs de debug:
1. Altere DEBUG_MODE = True no topo deste arquivo
2. Ou altere configurações específicas (DEBUG_AUTH, DEBUG_GAME, etc.)
3. Reinicie o servidor

Para ativar console logs (JavaScript):
1. Altere CONSOLE_LOGS_ENABLED = True
2. Recarregue a página

Para usar nos arquivos Python:
from config_debug import debug_print, debug_auth, debug_game
# debug_print removido
debug_auth("Mensagem de debug de autenticação")

Para usar no JavaScript:
// No main.js, a configuração DEBUG_MODE já está definida
debugLog("Mensagem de debug");
debugError("Mensagem de erro");
"""
