#!/usr/bin/env python3
"""
Teste da função simples para verificar se o problema é específico
"""

import sys
import os
from dotenv import load_dotenv

# Carrega variáveis de ambiente
load_dotenv()

def main():
    print("🧪 TESTE DA FUNÇÃO SIMPLES")
    print("=" * 30)
    
    try:
        # Importar módulos
        print("📦 Importando módulos...")
        from database.supabase_client import supabase_client
        from game import new_models as models
        print("✅ Módulos importados")
        
        # Testar função simples
        print("\n🧪 Testando função simples...")
        resultado_simples = models.teste_funcao_simples()
        print(f"📊 Resultado função simples: {resultado_simples}")
        
        # Testar função de mineração
        print("\n⛏️ Testando função de mineração...")
        resultado_mineracao = models.processar_mineracao_automatica_dinheiro()
        print(f"📊 Resultado mineração: {resultado_mineracao}")
        
        # Verificar se os logs apareceram
        print("\n🔍 ANÁLISE:")
        if resultado_simples.get('teste') == 'funcionando':
            print("✅ Função simples funcionou - problema não é de importação")
        else:
            print("❌ Função simples falhou - problema de importação")
            
        if resultado_mineracao.get('sucesso'):
            print("✅ Função de mineração retornou sucesso")
            if resultado_mineracao.get('jogadores_processados', 0) > 0:
                print("✅ Função processou jogadores")
            else:
                print("❌ Função não processou jogadores")
        else:
            print("❌ Função de mineração falhou")
        
    except Exception as e:
        print(f"❌ ERRO: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
