#!/usr/bin/env python3
"""
Comandos para Reset Manual do Torneio SHACK
"""

import sys
import requests
import json
sys.path.append('.')

def show_reset_commands():
    """Mostra os comandos disponíveis para reset manual do torneio"""
    print("🏆 COMANDOS PARA RESET MANUAL DO TORNEIO SHACK")
    print("=" * 60)
    
    print("\n📋 MÉTODOS DISPONÍVEIS:")
    print("1. 🎮 Via Interface do Jogo (Recomendado)")
    print("2. 🔧 Via API direta (Para desenvolvedores)")
    print("3. 🐍 Via Python script")
    
    print("\n" + "=" * 60)
    print("1️⃣ VIA INTERFACE DO JOGO:")
    print("=" * 60)
    
    print("\n🎯 TORNEIO DE DEFACE:")
    print("   1. Acesse o jogo como ADMIN")
    print("   2. Vá para seção 'News'")
    print("   3. Procure o painel 'Admin Tournament Controls'")
    print("   4. Clique em 'Reset Manual do Torneio'")
    print("   5. Confirme a ação")
    
    print("\n🏆 TORNEIO DE UPGRADE:")
    print("   1. Acesse o jogo como ADMIN")
    print("   2. Vá para seção 'Grupos'")
    print("   3. Procure o painel de admin do torneio")
    print("   4. Clique em 'Reset Manual'")
    print("   5. Confirme a ação")
    
    print("\n" + "=" * 60)
    print("2️⃣ VIA API DIRETA:")
    print("=" * 60)
    
    print("\n🎯 RESET TORNEIO DE DEFACE:")
    print("   URL: POST http://localhost:5000/api/torneio/reset-deface")
    print("   Autenticação: Requer login de admin")
    print("   Exemplo curl:")
    print("   curl -X POST http://localhost:5000/api/torneio/reset-deface \\")
    print("        -H 'Content-Type: application/json' \\")
    print("        -b 'session=SEU_COOKIE_DE_SESSAO'")
    
    print("\n🏆 RESET TORNEIO DE UPGRADE:")
    print("   URL: POST http://localhost:5000/api/torneio/reset")
    print("   Autenticação: Requer login de admin")
    print("   Exemplo curl:")
    print("   curl -X POST http://localhost:5000/api/torneio/reset \\")
    print("        -H 'Content-Type: application/json' \\")
    print("        -b 'session=SEU_COOKIE_DE_SESSAO'")
    
    print("\n" + "=" * 60)
    print("3️⃣ VIA PYTHON SCRIPT:")
    print("=" * 60)
    
    print("\n🐍 SCRIPT PYTHON:")
    print("""
import requests

# 1. Fazer login
login_response = requests.get('http://localhost:5000/api/auth/auto-login')
cookies = login_response.cookies

# 2. Reset torneio de deface
deface_response = requests.post(
    'http://localhost:5000/api/torneio/reset-deface',
    cookies=cookies,
    headers={'Content-Type': 'application/json'}
)

# 3. Reset torneio de upgrade  
upgrade_response = requests.post(
    'http://localhost:5000/api/torneio/reset',
    cookies=cookies,
    headers={'Content-Type': 'application/json'}
)

print("Deface:", deface_response.json())
print("Upgrade:", upgrade_response.json())
""")
    
    print("\n" + "=" * 60)
    print("⚠️  IMPORTANTE:")
    print("=" * 60)
    print("• Apenas ADMINISTRADORES podem executar reset manual")
    print("• O reset distribui recompensas antes de zerar pontos")
    print("• Esta ação NÃO PODE ser desfeita")
    print("• Recomenda-se usar a interface do jogo para maior segurança")
    
    print("\n" + "=" * 60)
    print("📊 O QUE O RESET FAZ:")
    print("=" * 60)
    print("✅ Calcula rankings finais")
    print("💰 Distribui recompensas aos vencedores")
    print("🔄 Reseta pontos de todos os participantes")
    print("🎯 Reinicia a competição")
    print("📝 Registra logs da operação")
    
    print("\n🎉 Reset implementado e funcionando!")

def test_reset_apis():
    """Testa se as APIs de reset estão respondendo"""
    print("\n🧪 TESTANDO APIS DE RESET...")
    print("=" * 40)
    
    base_url = "http://localhost:5000"
    
    try:
        # Testar se servidor está rodando
        health_response = requests.get(f"{base_url}/", timeout=5)
        if health_response.status_code == 200:
            print("✅ Servidor SHACK está rodando")
            
            # Fazer login
            login_response = requests.get(f"{base_url}/api/auth/auto-login")
            if login_response.status_code == 200:
                print("✅ Auto-login funcionando")
                cookies = login_response.cookies
                
                # Testar API de reset deface
                deface_test = requests.post(f"{base_url}/api/torneio/reset-deface", cookies=cookies)
                if deface_test.status_code == 403:
                    print("✅ API reset deface: Funcionando (acesso negado - esperado)")
                elif deface_test.status_code == 200:
                    print("✅ API reset deface: Funcionando (reset executado)")
                else:
                    print(f"⚠️ API reset deface: Status {deface_test.status_code}")
                
                # Testar API de reset upgrade
                upgrade_test = requests.post(f"{base_url}/api/torneio/reset", cookies=cookies)
                if upgrade_test.status_code == 403:
                    print("✅ API reset upgrade: Funcionando (acesso negado - esperado)")
                elif upgrade_test.status_code == 200:
                    print("✅ API reset upgrade: Funcionando (reset executado)")
                else:
                    print(f"⚠️ API reset upgrade: Status {upgrade_test.status_code}")
                    
            else:
                print("❌ Erro no auto-login")
        else:
            print("❌ Servidor SHACK não está respondendo")
            
    except requests.exceptions.ConnectionError:
        print("❌ Não foi possível conectar ao servidor")
        print("   Certifique-se de que o servidor está rodando em localhost:5000")
    except Exception as e:
        print(f"❌ Erro no teste: {e}")

if __name__ == "__main__":
    show_reset_commands()
    test_reset_apis()
