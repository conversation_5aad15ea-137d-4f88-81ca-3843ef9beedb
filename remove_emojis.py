#!/usr/bin/env python3
"""
Script para remover emojis do jogo SHACK
Mantém apenas emojis dos dados do usuário na página principal e upgrades
"""

import os
import re

def remove_emojis_from_file(file_path, preserve_patterns=None):
    """Remove emojis de um arquivo, preservando padrões específicos"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Lista de emojis comuns para remover
        emoji_patterns = [
            # Emojis específicos encontrados no código
            r'🔧', r'⚠️', r'🔄', r'✅', r'❌', r'🔍', r'📊', r'📈', r'📉',
            r'💰', r'🎯', r'🚀', r'🎉', r'📱', r'💡', r'📋', r'🎨', r'🌐',
            r'🔒', r'🛡️', r'⚡', r'🏦', r'💻', r'🗑️', r'📅', r'🔥', r'📰',
            r'🎮', r'🏆', r'👑', r'🥈', r'🥉', r'🚨', r'🔓', r'🦠', r'🖥️',
            r'💎', r'⭐', r'🛒', r'🎧', r'👥', r'🔐', r'📡', r'👁️', r'⏱️',
            r'⏳', r'🌟', r'🎪', r'🎭', r'🎲', r'🎰', r'🎸', r'🎺', r'🎻',
            r'🎤', r'🎬', r'📝', r'📤', r'📦', r'🔊', r'🔇', r'🎵', r'🎶',
            r'🎼', r'🎹', r'🥁', r'🎷', r'🎺', r'🎸', r'🎻', r'🎤', r'🎧',
            r'📻', r'📺', r'📷', r'📹', r'📼', r'💿', r'📀', r'💽', r'💾',
            r'💻', r'🖥️', r'🖨️', r'⌨️', r'🖱️', r'🖲️', r'💽', r'💾', r'💿',
            r'📀', r'🧮', r'🎛️', r'⏱️', r'⏲️', r'⏰', r'🕰️', r'⌛', r'⏳'
        ]
        
        changes_made = 0
        
        # Aplicar remoções
        for emoji in emoji_patterns:
            # Contar ocorrências antes da remoção
            matches = re.findall(emoji, content)
            if matches:
                # Verificar se deve preservar baseado nos padrões
                if preserve_patterns:
                    for pattern in preserve_patterns:
                        if re.search(pattern, content):
                            continue
                
                # Remover emoji
                content = re.sub(emoji + r'\s*', '', content)
                changes_made += len(matches)
        
        # Salvar se houve mudanças
        if changes_made > 0:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ {file_path}: {changes_made} emojis removidos")
            return changes_made
        else:
            print(f"⚪ {file_path}: Nenhum emoji encontrado")
            return 0
            
    except Exception as e:
        print(f"❌ Erro ao processar {file_path}: {e}")
        return 0

def remove_emojis_from_js_strings(file_path):
    """Remove emojis de strings JavaScript específicas"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        changes_made = 0
        
        # Padrões específicos para JavaScript
        js_patterns = [
            # Botões e mensagens de interface
            (r"innerHTML = '[^']*🔄[^']*'", lambda m: m.group(0).replace('🔄', '')),
            (r'innerHTML = "[^"]*🔄[^"]*"', lambda m: m.group(0).replace('🔄', '')),
            (r"innerHTML = '[^']*✅[^']*'", lambda m: m.group(0).replace('✅', '')),
            (r'innerHTML = "[^"]*✅[^"]*"', lambda m: m.group(0).replace('✅', '')),
            (r"innerHTML = '[^']*❌[^']*'", lambda m: m.group(0).replace('❌', '')),
            (r'innerHTML = "[^"]*❌[^"]*"', lambda m: m.group(0).replace('❌', '')),
            (r"innerHTML = '[^']*⏱️[^']*'", lambda m: m.group(0).replace('⏱️', '')),
            (r'innerHTML = "[^"]*⏱️[^"]*"', lambda m: m.group(0).replace('⏱️', '')),
            (r"innerHTML = '[^']*💰[^']*'", lambda m: m.group(0).replace('💰', '')),
            (r'innerHTML = "[^"]*💰[^"]*"', lambda m: m.group(0).replace('💰', '')),
            
            # Console logs
            (r"console\.log\('[^']*🔧[^']*'\)", lambda m: m.group(0).replace('🔧', '')),
            (r'console\.log\("[^"]*🔧[^"]*"\)', lambda m: m.group(0).replace('🔧', '')),
            (r"console\.log\('[^']*✅[^']*'\)", lambda m: m.group(0).replace('✅', '')),
            (r'console\.log\("[^"]*✅[^"]*"\)', lambda m: m.group(0).replace('✅', '')),
            
            # Notificações
            (r"showNotification\('[^']*💰[^']*'", lambda m: m.group(0).replace('💰', '')),
            (r'showNotification\("[^"]*💰[^"]*"', lambda m: m.group(0).replace('💰', '')),
            (r"showNotification\('[^']*❌[^']*'", lambda m: m.group(0).replace('❌', '')),
            (r'showNotification\("[^"]*❌[^"]*"', lambda m: m.group(0).replace('❌', '')),
            (r"showNotification\('[^']*✅[^']*'", lambda m: m.group(0).replace('✅', '')),
            (r'showNotification\("[^"]*✅[^"]*"', lambda m: m.group(0).replace('✅', '')),
        ]
        
        for pattern, replacement_func in js_patterns:
            matches = re.findall(pattern, content)
            if matches:
                content = re.sub(pattern, replacement_func, content)
                changes_made += len(matches)
        
        # Salvar se houve mudanças
        if changes_made > 0:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ {file_path}: {changes_made} emojis JS removidos")
            return changes_made
        else:
            print(f"⚪ {file_path}: Nenhum emoji JS encontrado")
            return 0
            
    except Exception as e:
        print(f"❌ Erro ao processar JS {file_path}: {e}")
        return 0

def main():
    """Função principal"""
    print("🧹 Removendo emojis do jogo SHACK...")
    print("📋 Mantendo apenas emojis dos dados do usuário na página principal e upgrades")
    print("=" * 60)
    
    # Arquivos para processar
    files_to_process = [
        # JavaScript
        'game/static/js/main.js',
        
        # Templates HTML (exceto dados do usuário)
        'game/templates/news.html',
        'game/templates/terminal.html',
        'game/templates/base.html',
        
        # Python (apenas logs e mensagens)
        'game/new_models.py',
        'game/routes.py',
        'database/supabase_client.py',
        'app.py',
        'auth_routes.py',
    ]
    
    total_removed = 0
    
    for file_path in files_to_process:
        if os.path.exists(file_path):
            print(f"\n📄 Processando {file_path}...")
            
            # Padrões para preservar (dados do usuário na página principal e upgrades)
            preserve_patterns = None
            if 'main.js' in file_path:
                preserve_patterns = [
                    r'renderDashboard',  # Página principal
                    r'loadAppStoreCards',  # Upgrades
                    r'itemNames.*icon',  # Ícones dos itens de upgrade
                ]
            
            # Remover emojis gerais
            removed = remove_emojis_from_file(file_path, preserve_patterns)
            total_removed += removed
            
            # Para arquivos JS, fazer limpeza adicional
            if file_path.endswith('.js'):
                removed_js = remove_emojis_from_js_strings(file_path)
                total_removed += removed_js
        else:
            print(f"⚠️ Arquivo não encontrado: {file_path}")
    
    print(f"\n📊 RESUMO:")
    print(f"   Total de emojis removidos: {total_removed}")
    
    if total_removed > 0:
        print("\n✅ Remoção de emojis concluída!")
        print("🔧 Emojis preservados:")
        print("   - Dados do usuário na página principal")
        print("   - Ícones dos upgrades na App Store")
        print("   - Indicadores de recursos (dinheiro, shacks, etc.)")
    else:
        print("\n⚪ Nenhum emoji foi removido")
    
    print("\n🎯 Para verificar:")
    print("   1. Inicie o servidor: python app.py")
    print("   2. Acesse o jogo e navegue pelas seções")
    print("   3. Verifique se apenas os dados do usuário têm emojis")

if __name__ == "__main__":
    main()
