#!/usr/bin/env python3
"""
Teste manual da mineração - execução passo a passo
"""

import os
import sys
from dotenv import load_dotenv

# Carrega variáveis de ambiente
load_dotenv()

print("🔍 TESTE MANUAL DA MINERAÇÃO AUTOMÁTICA")
print("=" * 45)

# Passo 1: Importar módulos
print("\n📦 Passo 1: Importando módulos...")
try:
    from database.supabase_client import supabase_client
    print("✅ supabase_client importado")
    
    from game import new_models as models
    print("✅ new_models importado")
except Exception as e:
    print(f"❌ Erro na importação: {e}")
    sys.exit(1)

# Passo 2: Verificar conexão
print("\n🔗 Passo 2: Verificando conexão...")
if not supabase_client.is_connected():
    print("❌ Supabase não conectado")
    sys.exit(1)
print("✅ Supabase conectado")

# Passo 3: Buscar usuários
print("\n👥 Passo 3: Buscando usuários...")
usuarios = supabase_client.get_ranking_usuarios(3)
if not usuarios:
    print("❌ Nenhum usuário encontrado")
    sys.exit(1)

print(f"📊 {len(usuarios)} usuários encontrados:")
for i, usuario in enumerate(usuarios):
    nick = usuario.get('nick', 'Unknown')
    dinheiro = usuario.get('dinheiro', 0)
    nivel = usuario.get('nivel_mineradora', 1)
    print(f"   {i+1}. {nick}: ${dinheiro} (Nível {nivel})")

# Passo 4: Testar atualização direta
print("\n🧪 Passo 4: Testando atualização direta...")
usuario_teste = usuarios[0]
uid_teste = usuario_teste['uid']
nick_teste = usuario_teste.get('nick', 'Unknown')
dinheiro_original = usuario_teste.get('dinheiro', 0)

print(f"👤 Usuário de teste: {nick_teste}")
print(f"💰 Dinheiro original: ${dinheiro_original}")

# Incrementar $5 para teste
novo_valor = dinheiro_original + 5
print(f"📝 Tentando atualizar para: ${novo_valor}")

resultado_update = supabase_client.atualizar_usuario(uid_teste, {
    'dinheiro': novo_valor
})

print(f"📊 Resultado da atualização: {resultado_update}")

if resultado_update.get('sucesso'):
    print("✅ Atualização reportada como sucesso")
    
    # Verificar se persistiu
    usuario_verificado = supabase_client.get_user_by_uid(uid_teste)
    if usuario_verificado:
        dinheiro_verificado = usuario_verificado.get('dinheiro', 0)
        print(f"🔍 Dinheiro verificado: ${dinheiro_verificado}")
        
        if dinheiro_verificado == novo_valor:
            print("✅ Atualização persistida com sucesso!")
            
            # Reverter
            print("🔄 Revertendo...")
            supabase_client.atualizar_usuario(uid_teste, {'dinheiro': dinheiro_original})
            print("✅ Valor revertido")
        else:
            print(f"❌ Valor não persistiu! Esperado: ${novo_valor}, Obtido: ${dinheiro_verificado}")
    else:
        print("❌ Erro ao verificar usuário")
else:
    print(f"❌ Falha na atualização: {resultado_update.get('erro', 'Erro desconhecido')}")

# Passo 5: Testar função de mineração
print("\n⛏️ Passo 5: Testando função de mineração...")

# Limpar cache se existir
try:
    if hasattr(models, 'MINERACAO_AUTOMATICA_CACHE'):
        models.MINERACAO_AUTOMATICA_CACHE.clear()
        print("🧹 Cache limpo")
except:
    print("⚠️ Cache não encontrado")

# Salvar saldos antes
print("💰 Saldos ANTES da mineração:")
saldos_antes = {}
for usuario in usuarios:
    uid = usuario['uid']
    nick = usuario.get('nick', 'Unknown')
    dinheiro = usuario.get('dinheiro', 0)
    nivel = usuario.get('nivel_mineradora', 1)
    saldos_antes[uid] = dinheiro
    
    # Calcular dinheiro esperado
    dinheiro_esperado = 15 + (nivel - 1) * 20 + (nivel - 1) * 5
    print(f"   {nick}: ${dinheiro} (deveria gerar ${dinheiro_esperado})")

# Executar mineração
print("\n⚡ Executando mineração...")
try:
    resultado_mineracao = models.processar_mineracao_automatica_dinheiro()
    print(f"📊 Resultado da mineração: {resultado_mineracao}")
except Exception as e:
    print(f"❌ Erro na mineração: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)

# Verificar resultados
print("\n🔍 Verificando resultados...")
usuarios_depois = supabase_client.get_ranking_usuarios(3)

print("💰 Saldos DEPOIS da mineração:")
mudancas_detectadas = 0
total_incrementado = 0

for usuario in usuarios_depois:
    uid = usuario['uid']
    nick = usuario.get('nick', 'Unknown')
    dinheiro_depois = usuario.get('dinheiro', 0)
    dinheiro_antes = saldos_antes.get(uid, 0)
    diferenca = dinheiro_depois - dinheiro_antes
    
    if diferenca > 0:
        print(f"   {nick}: ${dinheiro_antes} → ${dinheiro_depois} (+${diferenca}) ✅")
        mudancas_detectadas += 1
        total_incrementado += diferenca
    else:
        print(f"   {nick}: ${dinheiro_depois} (sem mudança)")

# Resumo final
print(f"\n📈 RESUMO FINAL:")
print(f"   Usuários com mudança: {mudancas_detectadas}")
print(f"   Total incrementado: ${total_incrementado}")
print(f"   Processados reportados: {resultado_mineracao.get('jogadores_processados', 0)}")
print(f"   Total gerado reportado: ${resultado_mineracao.get('total_dinheiro_gerado', 0)}")

if mudancas_detectadas > 0:
    print("\n🎉 SUCESSO! A mineração está funcionando!")
    print("✅ O sistema está atualizando o banco de dados corretamente")
else:
    print("\n❌ FALHA! A mineração não está funcionando!")
    print("❌ O dinheiro não está sendo incrementado no banco")

print("\n🔧 DIAGNÓSTICO:")
if resultado_mineracao.get('sucesso'):
    if resultado_mineracao.get('jogadores_processados', 0) > 0:
        if mudancas_detectadas > 0:
            print("✅ Tudo funcionando perfeitamente!")
        else:
            print("⚠️ Função executa mas não persiste no banco")
    else:
        print("⚠️ Função executa mas não processa ninguém (cache/cooldown?)")
else:
    print("❌ Função de mineração falha na execução")
