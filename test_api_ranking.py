#!/usr/bin/env python3
"""
Script para testar a API de ranking individual de deface
"""

import requests
import json

def test_ranking_api():
    base_url = "http://localhost:5000"
    
    try:
        print("🔧 Testando API de ranking individual de deface...")
        
        # Criar sessão para manter cookies
        session = requests.Session()
        
        # Fazer auto-login primeiro
        print("1. Fazendo auto-login...")
        login_response = session.get(f"{base_url}/api/auth/auto-login")
        
        if login_response.status_code == 200:
            login_data = login_response.json()
            print(f"✅ Login realizado: {login_data.get('mensagem')}")
        else:
            print(f"❌ Erro no login: {login_response.status_code}")
            return False
        
        # Testar API de ranking
        print("2. Testando API de ranking individual...")
        ranking_response = session.get(f"{base_url}/api/ranking/deface-individual")
        
        if ranking_response.status_code == 200:
            ranking_data = ranking_response.json()
            print(f"✅ API funcionando: sucesso={ranking_data.get('sucesso')}")
            
            if ranking_data.get('sucesso'):
                ranking = ranking_data.get('ranking', [])
                total = ranking_data.get('total_jogadores', 0)
                print(f"📊 Total de jogadores: {total}")
                
                if ranking:
                    print("🏆 Top 5 jogadores:")
                    for i, jogador in enumerate(ranking[:5]):
                        nick = jogador.get('nick', 'Sem nick')
                        pontos = jogador.get('deface_points', 0)
                        grupo = jogador.get('grupo_nome', 'Sem grupo')
                        posicao = jogador.get('position', i+1)
                        print(f"  {posicao}. {nick} - {pontos} pontos - Grupo: {grupo}")
                else:
                    print("❌ Nenhum jogador no ranking")
            else:
                print(f"❌ API retornou erro: {ranking_data.get('mensagem')}")
        else:
            print(f"❌ Erro na API: {ranking_response.status_code}")
            print(f"Resposta: {ranking_response.text}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Erro durante teste: {e}")
        return False

if __name__ == "__main__":
    success = test_ranking_api()
    if success:
        print("\n✅ Teste da API concluído com sucesso!")
    else:
        print("\n❌ Teste da API falhou!")
