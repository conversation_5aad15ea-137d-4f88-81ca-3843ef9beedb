#!/usr/bin/env python3
"""
Teste direto da atualização de usuário
"""

import os
from dotenv import load_dotenv
from datetime import datetime, timezone

# Carrega variáveis de ambiente
load_dotenv()

from database.supabase_client import supabase_client

def testar_atualizacao():
    """Testa a atualização de usuário"""
    print("🧪 Testando atualização de usuário...")
    
    # Buscar um usuário
    usuarios = supabase_client.get_ranking_usuarios(1)
    if not usuarios:
        print("❌ Nenhum usuário encontrado")
        return False
    
    usuario = usuarios[0]
    uid = usuario['uid']
    nick = usuario.get('nick', 'Unknown')
    dinheiro_antes = usuario.get('dinheiro', 0)
    
    print(f"👤 Usuário: {nick} (UID: {uid})")
    print(f"💰 Dinheiro antes: ${dinheiro_antes}")
    
    # Testar atualização simples
    agora = datetime.now(timezone.utc)
    updates = {
        'ultimo_dinheiro_mineracao_timestamp': agora.isoformat()
    }
    
    print(f"⚡ Testando atualização simples...")
    resultado = supabase_client.atualizar_usuario(uid, updates)
    print(f"Resultado: {resultado}")
    
    if not resultado.get('sucesso'):
        print(f"❌ Erro na atualização simples: {resultado.get('erro', 'Desconhecido')}")
        return False
    
    # Testar atualização de dinheiro
    novo_dinheiro = dinheiro_antes + 10
    updates_dinheiro = {
        'dinheiro': novo_dinheiro,
        'ultimo_dinheiro_mineracao_timestamp': agora.isoformat()
    }
    
    print(f"💰 Testando atualização de dinheiro para ${novo_dinheiro}...")
    resultado_dinheiro = supabase_client.atualizar_usuario(uid, updates_dinheiro)
    print(f"Resultado: {resultado_dinheiro}")
    
    if not resultado_dinheiro.get('sucesso'):
        print(f"❌ Erro na atualização de dinheiro: {resultado_dinheiro.get('erro', 'Desconhecido')}")
        return False
    
    # Verificar se a atualização funcionou
    usuario_atualizado = supabase_client.get_user_by_uid(uid)
    if usuario_atualizado:
        dinheiro_depois = usuario_atualizado.get('dinheiro', 0)
        print(f"💰 Dinheiro depois: ${dinheiro_depois}")
        
        if dinheiro_depois == novo_dinheiro:
            print("✅ Atualização funcionou!")
            return True
        else:
            print(f"❌ Atualização não refletiu: esperado ${novo_dinheiro}, obtido ${dinheiro_depois}")
            return False
    else:
        print("❌ Erro ao buscar usuário atualizado")
        return False

def testar_mineracao_manual():
    """Testa mineração manual para um usuário"""
    print("\n🔧 Testando mineração manual...")
    
    # Buscar usuário
    usuarios = supabase_client.get_ranking_usuarios(1)
    if not usuarios:
        print("❌ Nenhum usuário encontrado")
        return False
    
    usuario = usuarios[0]
    uid = usuario['uid']
    nick = usuario.get('nick', 'Unknown')
    nivel_mineradora = usuario.get('nivel_mineradora', 1)
    dinheiro_antes = usuario.get('dinheiro', 0)
    
    print(f"👤 Usuário: {nick}")
    print(f"⛏️ Nível mineradora: {nivel_mineradora}")
    print(f"💰 Dinheiro antes: ${dinheiro_antes}")
    
    # Calcular dinheiro a ser gerado
    dinheiro_por_minuto = nivel_mineradora * 3
    dinheiro_gerado = dinheiro_por_minuto  # 1 minuto
    novo_dinheiro = dinheiro_antes + dinheiro_gerado
    
    print(f"📊 Deveria gerar: ${dinheiro_gerado} (${dinheiro_por_minuto}/min)")
    print(f"📊 Novo saldo esperado: ${novo_dinheiro}")
    
    # Resetar timestamp para forçar processamento
    agora = datetime.now(timezone.utc)
    reset_result = supabase_client.atualizar_usuario(uid, {
        'ultimo_dinheiro_mineracao_timestamp': None
    })
    
    if not reset_result.get('sucesso'):
        print(f"⚠️ Não foi possível resetar timestamp: {reset_result.get('erro', 'Desconhecido')}")
    else:
        print("✅ Timestamp resetado")
    
    # Executar mineração manual
    from game import new_models as models
    
    print("⚡ Executando processamento...")
    resultado = models.processar_mineracao_automatica_dinheiro()
    
    print(f"📊 Resultado do processamento:")
    print(f"   Sucesso: {resultado.get('sucesso', False)}")
    print(f"   Jogadores processados: {resultado.get('jogadores_processados', 0)}")
    print(f"   Dinheiro gerado: ${resultado.get('total_dinheiro_gerado', 0)}")
    print(f"   Mensagem: {resultado.get('mensagem', 'N/A')}")
    
    # Verificar resultado
    usuario_final = supabase_client.get_user_by_uid(uid)
    if usuario_final:
        dinheiro_final = usuario_final.get('dinheiro', 0)
        diferenca = dinheiro_final - dinheiro_antes
        
        print(f"💰 Dinheiro final: ${dinheiro_final}")
        print(f"📈 Diferença: +${diferenca}")
        
        if diferenca > 0:
            print("✅ Mineração funcionou!")
            return True
        else:
            print("❌ Nenhum dinheiro foi gerado")
            return False
    else:
        print("❌ Erro ao buscar usuário final")
        return False

if __name__ == "__main__":
    print("🔍 TESTE DE ATUALIZAÇÃO DE USUÁRIO")
    print("=" * 40)
    
    if not supabase_client.is_connected():
        print("❌ Supabase não conectado")
        exit(1)
    
    print("✅ Supabase conectado")
    
    # Teste 1: Atualização básica
    if testar_atualizacao():
        print("\n✅ Teste de atualização passou!")
    else:
        print("\n❌ Teste de atualização falhou!")
        exit(1)
    
    # Teste 2: Mineração manual
    if testar_mineracao_manual():
        print("\n✅ Teste de mineração passou!")
    else:
        print("\n❌ Teste de mineração falhou!")
