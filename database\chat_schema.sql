-- Schema para sistema de chat com auto-limpeza e anti-spam
-- Mensagens são automaticamente apagadas após 6 horas
-- Sistema anti-spam bloqueia mensagens idênticas consecutivas

-- <PERSON><PERSON><PERSON> tabela de mensagens de chat
CREATE TABLE IF NOT EXISTS chat_messages (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_uid TEXT NOT NULL,
    nick VARCHAR(100) NOT NULL,
    message TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_blocked BOOLEAN DEFAULT FALSE,
    blocked_reason TEXT,
    user_ip TEXT,
    CONSTRAINT chat_messages_message_length CHECK (char_length(message) <= 200)
);

-- Índices para performance
CREATE INDEX IF NOT EXISTS idx_chat_messages_created_at ON chat_messages(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_chat_messages_user_uid ON chat_messages(user_uid);
CREATE INDEX IF NOT EXISTS idx_chat_messages_user_nick ON chat_messages(nick);
CREATE INDEX IF NOT EXISTS idx_chat_messages_active ON chat_messages(created_at DESC) WHERE is_blocked = FALSE;

-- Função para limpar mensagens antigas (6 horas)
CREATE OR REPLACE FUNCTION cleanup_old_chat_messages()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    -- Deletar mensagens com mais de 6 horas
    DELETE FROM chat_messages 
    WHERE created_at < NOW() - INTERVAL '6 hours';
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    -- Log da limpeza
    RAISE NOTICE 'Chat cleanup: % mensagens antigas removidas', deleted_count;
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Função para verificar anti-spam
CREATE OR REPLACE FUNCTION check_antispam(
    p_user_uid TEXT,
    p_message TEXT,
    p_nick TEXT,
    p_user_ip TEXT DEFAULT NULL
)
RETURNS TABLE(
    allowed BOOLEAN,
    reason TEXT,
    last_message TEXT,
    time_diff_seconds INTEGER
) AS $$
DECLARE
    last_msg_record RECORD;
    time_diff INTEGER;
    cooldown_seconds INTEGER := 1; -- 1 segundo de cooldown mínimo
BEGIN
    -- Buscar última mensagem do usuário
    SELECT message, created_at INTO last_msg_record
    FROM chat_messages 
    WHERE user_uid = p_user_uid 
    AND is_blocked = FALSE
    ORDER BY created_at DESC 
    LIMIT 1;
    
    -- Se não há mensagem anterior, permitir
    IF NOT FOUND THEN
        RETURN QUERY SELECT TRUE, NULL::TEXT, NULL::TEXT, 0;
        RETURN;
    END IF;
    
    -- Calcular diferença de tempo
    time_diff := EXTRACT(EPOCH FROM (NOW() - last_msg_record.created_at))::INTEGER;
    
    -- Verificar cooldown
    IF time_diff < cooldown_seconds THEN
        RETURN QUERY SELECT FALSE, 'COOLDOWN', last_msg_record.message, time_diff;
        RETURN;
    END IF;
    
    -- Verificar mensagem idêntica
    IF TRIM(LOWER(p_message)) = TRIM(LOWER(last_msg_record.message)) THEN
        RETURN QUERY SELECT FALSE, 'DUPLICATE', last_msg_record.message, time_diff;
        RETURN;
    END IF;
    
    -- Verificar spam de caracteres repetidos
    IF LENGTH(p_message) > 10 AND p_message ~ '^(.)\1{9,}$' THEN
        RETURN QUERY SELECT FALSE, 'SPAM_CHARS', last_msg_record.message, time_diff;
        RETURN;
    END IF;
    
    -- Mensagem permitida
    RETURN QUERY SELECT TRUE, NULL::TEXT, last_msg_record.message, time_diff;
END;
$$ LANGUAGE plpgsql;

-- Função para enviar mensagem com verificação anti-spam
CREATE OR REPLACE FUNCTION send_chat_message(
    p_user_uid TEXT,
    p_nick TEXT,
    p_message TEXT,
    p_user_ip TEXT DEFAULT NULL
)
RETURNS TABLE(
    success BOOLEAN,
    message_id UUID,
    error_message TEXT,
    blocked_reason TEXT
) AS $$
DECLARE
    spam_check RECORD;
    new_message_id UUID;
    clean_message TEXT;
BEGIN
    -- Limpar mensagem
    clean_message := TRIM(p_message);
    
    -- Verificar se mensagem não está vazia
    IF clean_message = '' OR clean_message IS NULL THEN
        RETURN QUERY SELECT FALSE, NULL::UUID, 'Mensagem não pode estar vazia', NULL::TEXT;
        RETURN;
    END IF;
    
    -- Verificar tamanho da mensagem
    IF LENGTH(clean_message) > 200 THEN
        RETURN QUERY SELECT FALSE, NULL::UUID, 'Mensagem muito longa (máximo 200 caracteres)', NULL::TEXT;
        RETURN;
    END IF;
    
    -- Verificar anti-spam
    SELECT * INTO spam_check FROM check_antispam(p_user_uid, clean_message, p_nick, p_user_ip);
    
    IF NOT spam_check.allowed THEN
        -- Inserir mensagem bloqueada para histórico
        INSERT INTO chat_messages (user_uid, nick, message, is_blocked, blocked_reason, user_ip)
        VALUES (p_user_uid, p_nick, clean_message, TRUE, spam_check.reason, p_user_ip)
        RETURNING id INTO new_message_id;
        
        -- Retornar erro específico
        CASE spam_check.reason
            WHEN 'COOLDOWN' THEN
                RETURN QUERY SELECT FALSE, new_message_id, 'Aguarde antes de enviar outra mensagem', spam_check.reason;
            WHEN 'DUPLICATE' THEN
                RETURN QUERY SELECT FALSE, new_message_id, 'Não é possível enviar mensagens idênticas consecutivas', spam_check.reason;
            WHEN 'SPAM_CHARS' THEN
                RETURN QUERY SELECT FALSE, new_message_id, 'Mensagem detectada como spam', spam_check.reason;
            ELSE
                RETURN QUERY SELECT FALSE, new_message_id, 'Mensagem bloqueada pelo anti-spam', spam_check.reason;
        END CASE;
        RETURN;
    END IF;
    
    -- Inserir mensagem válida
    INSERT INTO chat_messages (user_uid, nick, message, is_blocked, user_ip)
    VALUES (p_user_uid, p_nick, clean_message, FALSE, p_user_ip)
    RETURNING id INTO new_message_id;
    
    RETURN QUERY SELECT TRUE, new_message_id, NULL::TEXT, NULL::TEXT;
END;
$$ LANGUAGE plpgsql;

-- Função para buscar mensagens recentes
CREATE OR REPLACE FUNCTION get_recent_chat_messages(limite INTEGER DEFAULT 50)
RETURNS TABLE(
    id UUID,
    user_uid TEXT,
    nick VARCHAR(100),
    message TEXT,
    created_at TIMESTAMP WITH TIME ZONE,
    is_blocked BOOLEAN,
    blocked_reason TEXT,
    time_ago TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        cm.id,
        cm.user_uid,
        cm.nick,
        cm.message,
        cm.created_at,
        cm.is_blocked,
        cm.blocked_reason,
        CASE 
            WHEN EXTRACT(EPOCH FROM (NOW() - cm.created_at)) < 60 THEN 
                EXTRACT(EPOCH FROM (NOW() - cm.created_at))::INTEGER || 's'
            WHEN EXTRACT(EPOCH FROM (NOW() - cm.created_at)) < 3600 THEN 
                (EXTRACT(EPOCH FROM (NOW() - cm.created_at)) / 60)::INTEGER || 'm'
            ELSE 
                (EXTRACT(EPOCH FROM (NOW() - cm.created_at)) / 3600)::INTEGER || 'h'
        END as time_ago
    FROM chat_messages cm
    WHERE cm.is_blocked = FALSE
    ORDER BY cm.created_at DESC
    LIMIT limite;
END;
$$ LANGUAGE plpgsql;

-- Criar trigger para limpeza automática
CREATE OR REPLACE FUNCTION trigger_cleanup_chat()
RETURNS TRIGGER AS $$
BEGIN
    -- A cada 100 mensagens inseridas, fazer limpeza
    IF (SELECT COUNT(*) FROM chat_messages) % 100 = 0 THEN
        PERFORM cleanup_old_chat_messages();
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Aplicar trigger
DROP TRIGGER IF EXISTS chat_cleanup_trigger ON chat_messages;
CREATE TRIGGER chat_cleanup_trigger
    AFTER INSERT ON chat_messages
    FOR EACH ROW
    EXECUTE FUNCTION trigger_cleanup_chat();

-- Configurar limpeza automática via cron (se disponível)
-- Esta parte pode precisar ser configurada manualmente no Supabase
-- SELECT cron.schedule('chat-cleanup', '*/30 * * * *', 'SELECT cleanup_old_chat_messages();');

-- Comentários para documentação
COMMENT ON TABLE chat_messages IS 'Mensagens do chat global com auto-limpeza de 6 horas';
COMMENT ON COLUMN chat_messages.is_blocked IS 'Indica se a mensagem foi bloqueada pelo anti-spam';
COMMENT ON COLUMN chat_messages.blocked_reason IS 'Motivo do bloqueio: COOLDOWN, DUPLICATE, SPAM_CHARS';
COMMENT ON FUNCTION cleanup_old_chat_messages() IS 'Remove mensagens com mais de 6 horas automaticamente';
COMMENT ON FUNCTION check_antispam(TEXT, TEXT, TEXT, TEXT) IS 'Verifica se mensagem deve ser bloqueada pelo anti-spam';
COMMENT ON FUNCTION send_chat_message(TEXT, TEXT, TEXT, TEXT) IS 'Envia mensagem com verificação anti-spam integrada';

-- Verificação final
DO $$
BEGIN
    -- Verificar se a tabela foi criada
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'chat_messages') THEN
        RAISE NOTICE '✅ Tabela chat_messages criada com sucesso';
    ELSE
        RAISE NOTICE '❌ Erro: Tabela chat_messages não foi criada';
    END IF;
    
    -- Verificar se as funções foram criadas
    IF EXISTS (SELECT 1 FROM information_schema.routines WHERE routine_name = 'send_chat_message') THEN
        RAISE NOTICE '✅ Função send_chat_message criada com sucesso';
    ELSE
        RAISE NOTICE '❌ Erro: Função send_chat_message não foi criada';
    END IF;
END $$;
