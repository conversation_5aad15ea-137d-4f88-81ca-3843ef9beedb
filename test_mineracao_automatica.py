#!/usr/bin/env python3
"""
Script para testar a mineração automática de dinheiro
"""

import os
from dotenv import load_dotenv
from database.supabase_client import supabase_client
from game import new_models as models

# Carrega variáveis de ambiente
load_dotenv()

def testar_conexao():
    """Testa a conexão com o Supabase"""
    print("🔍 Testando conexão com Supabase...")
    if supabase_client.is_connected():
        print("✅ Conexão com Supabase OK")
        return True
    else:
        print("❌ Erro: Conexão com Supabase falhou")
        return False

def verificar_usuarios_com_mineradora():
    """Verifica usuários que têm mineradora"""
    print("\n📊 Verificando usuários com mineradora...")
    try:
        usuarios = supabase_client.get_ranking_usuarios(10)
        print(f"Total de usuários encontrados: {len(usuarios)}")
        
        usuarios_com_mineradora = []
        for usuario in usuarios:
            nivel_mineradora = usuario.get('nivel_mineradora', 1)
            dinheiro_atual = usuario.get('dinheiro', 0)
            ultimo_timestamp = usuario.get('ultimo_dinheiro_mineracao_timestamp', 'Nunca')
            
            print(f"\n👤 {usuario.get('nick', 'Unknown')} (UID: {usuario.get('uid', 'N/A')})")
            print(f"   Nível Mineradora: {nivel_mineradora}")
            print(f"   Dinheiro Atual: ${dinheiro_atual}")
            print(f"   Último Processamento: {ultimo_timestamp}")
            
            if nivel_mineradora >= 1:
                dinheiro_por_minuto = nivel_mineradora * 3
                print(f"   Geração: {dinheiro_por_minuto} dinheiro/min")
                usuarios_com_mineradora.append(usuario)
        
        print(f"\n📈 Usuários com mineradora ativa: {len(usuarios_com_mineradora)}")
        return usuarios_com_mineradora
        
    except Exception as e:
        print(f"❌ Erro ao verificar usuários: {e}")
        return []

def testar_processamento_manual():
    """Testa o processamento manual da mineração"""
    print("\n🔧 Testando processamento manual...")
    try:
        resultado = models.processar_mineracao_automatica_dinheiro()
        
        print(f"✅ Resultado do processamento:")
        print(f"   Sucesso: {resultado.get('sucesso', False)}")
        print(f"   Jogadores processados: {resultado.get('jogadores_processados', 0)}")
        print(f"   Total dinheiro gerado: ${resultado.get('total_dinheiro_gerado', 0)}")
        print(f"   Mensagem: {resultado.get('mensagem', 'N/A')}")
        
        return resultado.get('sucesso', False)
        
    except Exception as e:
        print(f"❌ Erro no processamento manual: {e}")
        return False

def verificar_mudancas_saldo():
    """Verifica se houve mudanças no saldo após processamento"""
    print("\n🔍 Verificando mudanças no saldo...")
    
    # Buscar usuários antes
    usuarios_antes = supabase_client.get_ranking_usuarios(5)
    saldos_antes = {u['uid']: u.get('dinheiro', 0) for u in usuarios_antes}
    
    print("💰 Saldos antes do processamento:")
    for uid, saldo in saldos_antes.items():
        nick = next((u['nick'] for u in usuarios_antes if u['uid'] == uid), 'Unknown')
        print(f"   {nick}: ${saldo}")
    
    # Processar mineração
    print("\n⚡ Executando processamento...")
    resultado = models.processar_mineracao_automatica_dinheiro()
    
    # Buscar usuários depois
    usuarios_depois = supabase_client.get_ranking_usuarios(5)
    saldos_depois = {u['uid']: u.get('dinheiro', 0) for u in usuarios_depois}
    
    print("\n💰 Saldos após o processamento:")
    mudancas = 0
    for uid, saldo_depois in saldos_depois.items():
        saldo_antes = saldos_antes.get(uid, 0)
        diferenca = saldo_depois - saldo_antes
        nick = next((u['nick'] for u in usuarios_depois if u['uid'] == uid), 'Unknown')
        
        if diferenca > 0:
            print(f"   {nick}: ${saldo_antes} → ${saldo_depois} (+${diferenca}) ✅")
            mudancas += 1
        else:
            print(f"   {nick}: ${saldo_depois} (sem mudança)")
    
    print(f"\n📊 Usuários com mudança no saldo: {mudancas}")
    return mudancas > 0

def testar_usuario_especifico():
    """Testa processamento para um usuário específico"""
    print("\n👤 Testando usuário específico...")
    
    # Buscar primeiro usuário com mineradora
    usuarios = supabase_client.get_ranking_usuarios(10)
    usuario_teste = None
    
    for usuario in usuarios:
        if usuario.get('nivel_mineradora', 1) >= 1:
            usuario_teste = usuario
            break
    
    if not usuario_teste:
        print("❌ Nenhum usuário com mineradora encontrado")
        return False
    
    uid = usuario_teste['uid']
    nick = usuario_teste.get('nick', 'Unknown')
    nivel = usuario_teste.get('nivel_mineradora', 1)
    saldo_antes = usuario_teste.get('dinheiro', 0)
    
    print(f"🎯 Testando usuário: {nick} (Nível {nivel})")
    print(f"   Saldo antes: ${saldo_antes}")
    
    # Resetar timestamp para forçar processamento
    try:
        supabase_client.atualizar_usuario(uid, {
            'ultimo_dinheiro_mineracao_timestamp': None
        })
        print("   Timestamp resetado para forçar processamento")
    except Exception as e:
        print(f"   Aviso: Não foi possível resetar timestamp: {e}")
    
    # Processar
    resultado = models.processar_mineracao_automatica_dinheiro()
    
    # Verificar resultado
    usuario_atualizado = models.get_jogador(uid)
    if usuario_atualizado:
        saldo_depois = usuario_atualizado.get('dinheiro', 0)
        diferenca = saldo_depois - saldo_antes
        
        print(f"   Saldo depois: ${saldo_depois}")
        print(f"   Diferença: +${diferenca}")
        
        if diferenca > 0:
            print("   ✅ Processamento funcionou!")
            return True
        else:
            print("   ❌ Nenhum dinheiro foi gerado")
            return False
    else:
        print("   ❌ Erro ao buscar usuário atualizado")
        return False

def main():
    """Função principal"""
    print("🧪 TESTE DA MINERAÇÃO AUTOMÁTICA DE DINHEIRO")
    print("=" * 50)
    
    # Teste 1: Conexão
    if not testar_conexao():
        return
    
    # Teste 2: Verificar usuários
    usuarios_com_mineradora = verificar_usuarios_com_mineradora()
    if not usuarios_com_mineradora:
        print("❌ Nenhum usuário com mineradora encontrado")
        return
    
    # Teste 3: Processamento manual
    print("\n" + "=" * 50)
    if not testar_processamento_manual():
        print("❌ Processamento manual falhou")
        return
    
    # Teste 4: Verificar mudanças
    print("\n" + "=" * 50)
    if verificar_mudancas_saldo():
        print("✅ Sistema funcionando - saldos foram atualizados!")
    else:
        print("⚠️ Nenhuma mudança detectada nos saldos")
    
    # Teste 5: Usuário específico
    print("\n" + "=" * 50)
    if testar_usuario_especifico():
        print("✅ Teste específico passou!")
    else:
        print("❌ Teste específico falhou")
    
    print("\n🎯 DIAGNÓSTICO:")
    print("Se os testes falharam, possíveis causas:")
    print("1. Timestamp muito recente (< 1 minuto)")
    print("2. Nível de mineradora = 0")
    print("3. Problema na conexão com Supabase")
    print("4. Erro na função de atualização")
    
    print("\n🔧 Para forçar processamento:")
    print("1. Resetar ultimo_dinheiro_mineracao_timestamp para NULL")
    print("2. Verificar se nivel_mineradora >= 1")
    print("3. Executar processamento manual")

if __name__ == "__main__":
    main()
