#!/usr/bin/env python3
"""
Nova implementação da mineração automática em arquivo separado
"""

import os
import sys
from datetime import datetime, timezone
from typing import Dict, Any
from dotenv import load_dotenv

# Carrega variáveis de ambiente
load_dotenv()

# Adicionar path
sys.path.append('.')

def processar_mineracao_automatica_nova() -> Dict[str, Any]:
    """
    Nova implementação da mineração automática
    
    Mining Level Rewards:
    - Level 1: $15 per minute
    - Level 2: $30 per minute  
    - Level 3: $60 per minute
    - Level 4+: $100 per minute (scaling)
    """
    print("🚨🚨🚨 [NOVA MINERACAO] FUNÇÃO NOVA SENDO EXECUTADA!")
    print("⛏️ [NOVA MINERACAO] Iniciando mineração automática...")
    
    try:
        # Importar supabase
        from database.supabase_client import supabase_client
        
        # Verificar conexão
        if not supabase_client.is_connected():
            print("❌ [NOVA MINERACAO] Database não conectado")
            return {"sucesso": False, "mensagem": "Database não disponível"}

        # Buscar mineradores
        print("🔍 [NOVA MINERACAO] Buscando mineradores...")
        
        result = supabase_client.client.table('usuarios').select(
            'uid, nick, dinheiro, nivel_mineradora, ultimo_dinheiro_mineracao_timestamp'
        ).gte('nivel_mineradora', 1).execute()
        
        miners = result.data if result.data else []
        print(f"⛏️ [NOVA MINERACAO] Encontrados {len(miners)} mineradores")
        
        if not miners:
            print("ℹ️ [NOVA MINERACAO] Nenhum minerador encontrado")
            return {"sucesso": True, "jogadores_processados": 0, "total_dinheiro_gerado": 0, "mensagem": "Nenhum minerador ativo"}

        # Processar cada minerador
        players_processed = 0
        total_money_generated = 0
        current_time = datetime.now(timezone.utc)
        timestamp_iso = current_time.isoformat()
        
        print(f"🔄 [NOVA MINERACAO] Processando {len(miners)} mineradores...")

        for i, miner in enumerate(miners, 1):
            print(f"\n👤 [NOVA MINERACAO] Processando {i}/{len(miners)}")
            
            try:
                # Extrair dados
                uid = miner.get('uid')
                nick = miner.get('nick', 'Unknown')
                current_money = miner.get('dinheiro', 0)
                mining_level = miner.get('nivel_mineradora', 1)
                
                print(f"⛏️ [NOVA MINERACAO] {nick} - Nível {mining_level} - ${current_money}")

                # Validar dados
                if not uid or mining_level < 1:
                    print(f"❌ [NOVA MINERACAO] {nick}: Dados inválidos, pulando")
                    continue

                # Calcular dinheiro por nível
                if mining_level == 1:
                    money_per_minute = 15
                elif mining_level == 2:
                    money_per_minute = 30
                elif mining_level == 3:
                    money_per_minute = 60
                else:
                    # Level 4+: $100 + $50 por nível adicional
                    money_per_minute = 100 + ((mining_level - 4) * 50)
                
                print(f"💰 [NOVA MINERACAO] {nick}: Nível {mining_level} gera ${money_per_minute}/min")

                # Aplicar efeitos NFT (se disponível)
                try:
                    from game.new_models import aplicar_efeito_nft_firstsupp_dinheiro
                    final_money = aplicar_efeito_nft_firstsupp_dinheiro(money_per_minute, miner)
                    print(f"💎 [NOVA MINERACAO] {nick}: Após NFT = ${final_money}/min")
                except Exception as nft_error:
                    print(f"⚠️ [NOVA MINERACAO] {nick}: Erro NFT, usando base: {nft_error}")
                    final_money = money_per_minute

                # Garantir que gera pelo menos algum dinheiro
                money_generated = int(final_money)
                if money_generated <= 0:
                    print(f"❌ [NOVA MINERACAO] {nick}: Gerou $0, pulando")
                    continue

                # Calcular novo saldo
                new_balance = current_money + money_generated
                print(f"🏦 [NOVA MINERACAO] {nick}: ${current_money} + ${money_generated} = ${new_balance}")

                # Atualizar no banco
                print(f"📝 [NOVA MINERACAO] {nick}: Atualizando banco...")
                
                update_result = supabase_client.atualizar_usuario(uid, {
                    'dinheiro': new_balance,
                    'ultimo_dinheiro_mineracao_timestamp': timestamp_iso
                })
                
                print(f"📊 [NOVA MINERACAO] {nick}: Resultado = {update_result}")
                
                if update_result.get('sucesso'):
                    players_processed += 1
                    total_money_generated += money_generated
                    print(f"✅ [NOVA MINERACAO] {nick}: Sucesso! +${money_generated}")
                else:
                    error_msg = update_result.get('mensagem', 'Erro desconhecido')
                    print(f"❌ [NOVA MINERACAO] {nick}: Falha na atualização - {error_msg}")
                    
            except Exception as player_error:
                print(f"❌ [NOVA MINERACAO] Erro processando {miner.get('nick', 'Unknown')}: {player_error}")
                continue

        # Resumo final
        print(f"\n📊 [NOVA MINERACAO] CONCLUÍDO: {players_processed}/{len(miners)} processados, ${total_money_generated} gerado")

        return {
            "sucesso": True,
            "jogadores_processados": players_processed,
            "total_dinheiro_gerado": total_money_generated,
            "mensagem": f"Nova mineração: {players_processed} jogadores, ${total_money_generated} gerado"
        }

    except Exception as e:
        print(f"❌ [NOVA MINERACAO] ERRO GERAL: {e}")
        import traceback
        traceback.print_exc()
        return {"sucesso": False, "mensagem": f"Erro: {str(e)}"}

def main():
    """Teste da nova função"""
    print("🧪 TESTANDO NOVA FUNÇÃO DE MINERAÇÃO")
    print("=" * 45)
    
    resultado = processar_mineracao_automatica_nova()
    
    print(f"\n📊 RESULTADO FINAL:")
    print(f"   Sucesso: {resultado.get('sucesso', False)}")
    print(f"   Processados: {resultado.get('jogadores_processados', 0)}")
    print(f"   Total gerado: ${resultado.get('total_dinheiro_gerado', 0)}")
    print(f"   Mensagem: {resultado.get('mensagem', 'N/A')}")
    
    if resultado.get('sucesso') and resultado.get('jogadores_processados', 0) > 0:
        print("\n🎉 NOVA FUNÇÃO FUNCIONOU!")
    else:
        print("\n❌ Nova função falhou ou não processou jogadores")

if __name__ == "__main__":
    main()
