#!/usr/bin/env python3
"""
Script para debugar o ranking e verificar se há problemas
"""

import requests
import json

def debug_ranking():
    base_url = "http://localhost:5000"
    
    try:
        print("🔧 Debug do sistema de ranking...")
        
        # Criar sessão para manter cookies
        session = requests.Session()
        
        # 1. Fazer auto-login
        print("\n1. 🔑 Fazendo auto-login...")
        login_response = session.get(f"{base_url}/api/auth/auto-login")
        
        if login_response.status_code == 200:
            login_data = login_response.json()
            print(f"✅ Login OK: {login_data.get('mensagem')}")
        else:
            print(f"❌ Erro no login: {login_response.status_code}")
            return False
        
        # 2. Testar API de ranking individual
        print("\n2. 📊 Testando API de ranking individual...")
        ranking_response = session.get(f"{base_url}/api/ranking/deface-individual")
        
        if ranking_response.status_code == 200:
            ranking_data = ranking_response.json()
            print(f"✅ API OK: sucesso={ranking_data.get('sucesso')}")
            
            if ranking_data.get('sucesso'):
                ranking = ranking_data.get('ranking', [])
                total = ranking_data.get('total_jogadores', 0)
                print(f"📈 Total de jogadores: {total}")
                
                if ranking:
                    print("\n🏆 Estrutura dos dados:")
                    for i, jogador in enumerate(ranking[:3]):
                        print(f"  Jogador {i+1}:")
                        print(f"    - nick: {jogador.get('nick')}")
                        print(f"    - deface_points: {jogador.get('deface_points')}")
                        print(f"    - grupo_nome: {jogador.get('grupo_nome')}")
                        print(f"    - position: {jogador.get('position')}")
                        print(f"    - nivel: {jogador.get('nivel')}")
                        print()
                else:
                    print("❌ Array de ranking vazio")
            else:
                print(f"❌ API retornou erro: {ranking_data.get('mensagem')}")
        else:
            print(f"❌ Erro HTTP: {ranking_response.status_code}")
            print(f"Resposta: {ranking_response.text}")
            return False
        
        # 3. Testar API antiga para comparação
        print("\n3. 🔄 Testando API antiga para comparação...")
        try:
            old_ranking_response = session.get(f"{base_url}/api/ranking/deface")
            if old_ranking_response.status_code == 200:
                old_data = old_ranking_response.json()
                print(f"✅ API antiga OK: sucesso={old_data.get('sucesso')}")
                if old_data.get('ranking_jogadores'):
                    print(f"📈 Jogadores na API antiga: {len(old_data.get('ranking_jogadores', []))}")
            else:
                print(f"⚠️ API antiga não disponível: {old_ranking_response.status_code}")
        except Exception as e:
            print(f"⚠️ Erro na API antiga: {e}")
        
        # 4. Verificar dados na tabela torneio_pontuacoes
        print("\n4. 🗄️ Verificando dados na tabela...")
        try:
            # Simular uma consulta direta (isso seria feito no backend)
            print("   (Esta verificação seria feita diretamente no banco)")
            print("   Sugestão: Verificar se há dados na tabela 'torneio_pontuacoes'")
        except Exception as e:
            print(f"⚠️ Erro ao verificar tabela: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro durante debug: {e}")
        return False

if __name__ == "__main__":
    success = debug_ranking()
    if success:
        print("\n✅ Debug concluído!")
        print("\n💡 Próximos passos:")
        print("   1. Verificar se o frontend está usando a API correta")
        print("   2. Limpar cache do navegador")
        print("   3. Verificar console do navegador para erros JavaScript")
        print("   4. Verificar se há dados na tabela 'torneio_pontuacoes'")
    else:
        print("\n❌ Debug falhou!")
