#!/usr/bin/env python3
"""
Verifica o schema da tabela usuarios
"""

import os
from dotenv import load_dotenv

# Carrega variáveis de ambiente
load_dotenv()

from database.supabase_client import supabase_client

def verificar_schema():
    """Verifica as colunas da tabela usuarios"""
    print("🔍 Verificando schema da tabela usuarios...")
    
    try:
        # Buscar um usuário para ver as colunas disponíveis
        usuarios = supabase_client.get_ranking_usuarios(1)
        if not usuarios:
            print("❌ Nenhum usuário encontrado")
            return
        
        usuario = usuarios[0]
        print(f"👤 Usuário exemplo: {usuario.get('nick', 'Unknown')}")
        print(f"\n📊 Colunas disponíveis:")
        
        for coluna, valor in usuario.items():
            tipo_valor = type(valor).__name__
            print(f"   {coluna}: {valor} ({tipo_valor})")
        
        # Verificar se existe alguma coluna relacionada a timestamp
        colunas_timestamp = [col for col in usuario.keys() if 'timestamp' in col.lower() or 'time' in col.lower() or 'data' in col.lower() or 'ultimo' in col.lower()]
        
        print(f"\n⏰ Colunas relacionadas a tempo:")
        if colunas_timestamp:
            for col in colunas_timestamp:
                print(f"   ✅ {col}: {usuario.get(col)}")
        else:
            print("   ❌ Nenhuma coluna de timestamp encontrada")
        
        # Verificar colunas relacionadas a mineração
        colunas_mineracao = [col for col in usuario.keys() if 'miner' in col.lower() or 'mineracao' in col.lower() or 'mineradora' in col.lower()]
        
        print(f"\n⛏️ Colunas relacionadas a mineração:")
        if colunas_mineracao:
            for col in colunas_mineracao:
                print(f"   ✅ {col}: {usuario.get(col)}")
        else:
            print("   ❌ Nenhuma coluna de mineração encontrada")
            
    except Exception as e:
        print(f"❌ Erro ao verificar schema: {e}")

def sugerir_solucoes():
    """Sugere soluções para o problema"""
    print(f"\n💡 SOLUÇÕES POSSÍVEIS:")
    print(f"=" * 40)
    
    print(f"1. 🔧 USAR COLUNA EXISTENTE:")
    print(f"   - Usar 'updated_at' ou 'last_login' se existir")
    print(f"   - Modificar código para usar coluna disponível")
    
    print(f"\n2. 🗃️ CRIAR NOVA COLUNA:")
    print(f"   - Adicionar 'ultimo_dinheiro_mineracao_timestamp' no Supabase")
    print(f"   - Tipo: timestamp with time zone")
    print(f"   - Valor padrão: NULL")
    
    print(f"\n3. 🚀 SOLUÇÃO TEMPORÁRIA:")
    print(f"   - Remover dependência do timestamp")
    print(f"   - Sempre processar (com limite de frequência)")
    print(f"   - Usar cache em memória")
    
    print(f"\n4. 📝 SQL PARA CRIAR COLUNA:")
    print(f"   ALTER TABLE usuarios ADD COLUMN ultimo_dinheiro_mineracao_timestamp TIMESTAMPTZ;")

if __name__ == "__main__":
    print("🗃️ VERIFICAÇÃO DO SCHEMA DO BANCO")
    print("=" * 40)
    
    if not supabase_client.is_connected():
        print("❌ Supabase não conectado")
        exit(1)
    
    verificar_schema()
    sugerir_solucoes()
