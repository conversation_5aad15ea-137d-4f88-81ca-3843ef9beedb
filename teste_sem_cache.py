#!/usr/bin/env python3
"""
Teste da mineração sem depender do cache
"""

import os
from dotenv import load_dotenv

# Carrega variáveis de ambiente
load_dotenv()

from database.supabase_client import supabase_client
from game import new_models as models

print("🎯 TESTE DA MINERAÇÃO SEM CACHE")
print("=" * 35)

# Verificar conexão
if not supabase_client.is_connected():
    print("❌ Supabase não conectado")
    exit(1)

print("✅ Supabase conectado")

# Buscar usuários antes
print("\n👥 Buscando usuários...")
usuarios_antes = supabase_client.get_ranking_usuarios(3)
if not usuarios_antes:
    print("❌ Nenhum usuário encontrado")
    exit(1)

print(f"📊 {len(usuarios_antes)} usuários encontrados")

# Mostrar saldos antes
print("\n💰 SALDOS ANTES:")
saldos_antes = {}
for usuario in usuarios_antes:
    uid = usuario['uid']
    nick = usuario.get('nick', 'Unknown')
    dinheiro = usuario.get('dinheiro', 0)
    nivel = usuario.get('nivel_mineradora', 1)
    saldos_antes[uid] = dinheiro
    
    # Nova fórmula: 15 + (nivel-1) * 20 + (nivel-1) * 5
    dinheiro_esperado = 15 + (nivel - 1) * 20 + (nivel - 1) * 5
    print(f"   {nick} (Nível {nivel}): ${dinheiro} (deveria gerar ${dinheiro_esperado}/min)")

# Tentar limpar cache se existir
print("\n🧹 Tentando limpar cache...")
try:
    if hasattr(models, 'MINERACAO_AUTOMATICA_CACHE'):
        models.MINERACAO_AUTOMATICA_CACHE.clear()
        print("✅ Cache limpo")
    else:
        print("⚠️ Cache não encontrado, continuando...")
except Exception as e:
    print(f"⚠️ Erro ao limpar cache: {e}")

# Executar mineração
print("\n⚡ EXECUTANDO MINERAÇÃO...")
print("-" * 30)

try:
    resultado = models.processar_mineracao_automatica_dinheiro()
    print("-" * 30)
    print(f"\n📊 RESULTADO:")
    print(f"   Sucesso: {resultado.get('sucesso', False)}")
    print(f"   Jogadores processados: {resultado.get('jogadores_processados', 0)}")
    print(f"   Total gerado: ${resultado.get('total_dinheiro_gerado', 0)}")
    print(f"   Mensagem: {resultado.get('mensagem', 'N/A')}")
    
    if not resultado.get('sucesso'):
        print("❌ Mineração falhou!")
        exit(1)
        
    if resultado.get('jogadores_processados', 0) == 0:
        print("⚠️ Nenhum jogador foi processado!")
        
except Exception as e:
    print(f"❌ Erro na mineração: {e}")
    import traceback
    traceback.print_exc()
    exit(1)

# Verificar resultados
print("\n🔍 VERIFICANDO RESULTADOS...")
usuarios_depois = supabase_client.get_ranking_usuarios(3)

print("\n💰 SALDOS DEPOIS:")
mudancas_detectadas = 0
total_incrementado = 0

for usuario in usuarios_depois:
    uid = usuario['uid']
    nick = usuario.get('nick', 'Unknown')
    dinheiro_depois = usuario.get('dinheiro', 0)
    dinheiro_antes = saldos_antes.get(uid, 0)
    diferenca = dinheiro_depois - dinheiro_antes
    
    if diferenca > 0:
        print(f"   {nick}: ${dinheiro_antes} → ${dinheiro_depois} (+${diferenca}) ✅")
        mudancas_detectadas += 1
        total_incrementado += diferenca
    else:
        print(f"   {nick}: ${dinheiro_depois} (sem mudança)")

# Resumo final
print(f"\n📈 RESUMO:")
print(f"   Usuários com mudança: {mudancas_detectadas}")
print(f"   Total incrementado: ${total_incrementado}")

if mudancas_detectadas > 0:
    print("\n🎉 SUCESSO!")
    print("✅ A mineração automática está funcionando!")
    print("✅ O dinheiro está sendo incrementado no banco!")
    
    print("\n🚀 SISTEMA PRONTO!")
    print("📋 Para ativar:")
    print("1. Iniciar servidor: python app.py")
    print("2. A mineração rodará automaticamente a cada minuto")
    
else:
    print("\n❌ FALHA!")
    print("A mineração não está incrementando o dinheiro")
    
    if resultado.get('jogadores_processados', 0) > 0:
        print("⚠️ Função processa mas não persiste")
    else:
        print("⚠️ Função não processa nenhum jogador")

print(f"\n📊 STATUS FINAL:")
print(f"   Função executou: {'✅' if resultado.get('sucesso') else '❌'}")
print(f"   Banco atualizado: {'✅' if mudancas_detectadas > 0 else '❌'}")
print(f"   Sistema funcional: {'✅' if mudancas_detectadas > 0 else '❌'}")
