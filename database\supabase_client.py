"""
Cliente Supabase para o jogo SHACK
Substitui completamente o Firebase
"""

import os
from supabase import create_client, Client
from typing import Dict, List, Any, Optional
from datetime import datetime, timezone
import uuid
import hashlib
import random

# Carregar variáveis do arquivo .env
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    print("⚠️ python-dotenv não encontrado - usando variáveis do sistema")

class SupabaseClient:
    def __init__(self):
        self.client: Optional[Client] = None
        self.url = os.environ.get('SUPABASE_URL')
        self.key = os.environ.get('SUPABASE_ANON_KEY')
        self._init_client()
    
    def _init_client(self):
        """Inicializa cliente Supabase"""
        try:
            if not self.url or not self.key:
                print("⚠️ SUPABASE_URL e SUPABASE_ANON_KEY não configurados")
                print("📝 Configure no arquivo .env:")
                print("SUPABASE_URL=https://sua-instancia.supabase.co")
                print("SUPABASE_ANON_KEY=sua-chave-anonima")
                self.client = None
                return
            
            self.client = create_client(self.url, self.key)
            print("✅ Conexão com Supabase estabelecida")
        
        except Exception as e:
            print(f"❌ Erro ao conectar com Supabase: {e}")
            self.client = None
    
    def is_connected(self) -> bool:
        """Verifica se está conectado ao Supabase"""
        return self.client is not None
    
    def get_client(self) -> Optional[Client]:
        """Retorna cliente Supabase"""
        if not self.client:
            self._init_client()
        return self.client

    # --- OPERAÇÕES DE USUÁRIOS ---
    def create_user(self, user_data: Dict[str, Any]) -> Dict[str, Any]:
        """Cria um novo usuário"""
        try:
            if not self.client:
                return {"sucesso": False, "erro": "Cliente Supabase não disponível"}
            
            result = self.client.table('usuarios').insert(user_data).execute()
            return {"sucesso": True, "data": result.data[0] if result.data else None}
        except Exception as e:
            return {"sucesso": False, "erro": str(e)}
    
    def get_user_by_uid(self, uid: str) -> Optional[Dict[str, Any]]:
        """Busca usuário por UID"""
        try:
            if not self.client:
                print(f"[DEBUG SUPABASE] Cliente não conectado para UID: {uid}")
                return None
            
            print(f"[DEBUG SUPABASE] Buscando usuário com UID: '{uid}' (tipo: {type(uid)})")
            result = self.client.table('usuarios').select("*").eq('uid', uid).execute()
            print(f"[DEBUG SUPABASE] Query executada. Resultado: {result.data}")
            print(f"[DEBUG SUPABASE] Número de registros encontrados: {len(result.data) if result.data else 0}")
            
            if result.data:
                user = result.data[0]
                print(f"[DEBUG SUPABASE] Usuário encontrado: {user.get('nick', 'Nome não encontrado')} (UID: {user.get('uid')})")
                return user
            else:
                print(f"[DEBUG SUPABASE] Nenhum usuário encontrado para UID: '{uid}'")
                return None
        except Exception as e:
            print(f"[DEBUG SUPABASE] Erro ao buscar usuário {uid}: {e}")
            return None
    
    def get_user_by_nick(self, nick: str) -> Optional[Dict[str, Any]]:
        """Busca usuário por nickname"""
        try:
            if not self.client:
                return None
            result = self.client.table('usuarios').select("*").eq('nick', nick).execute()
            return result.data[0] if result.data else None
        except Exception as e:
            print(f"Erro ao buscar usuário {nick}: {e}")
            return None
    
    def get_user_by_email(self, email: str) -> Optional[Dict[str, Any]]:
        """Busca usuário por email"""
        try:
            if not self.client:
                return None
            result = self.client.table('usuarios').select("*").eq('email', email).execute()
            return result.data[0] if result.data else None
        except Exception as e:
            print(f"Erro ao buscar usuário por email {email}: {e}")
            return None

    def update_user(self, uid: str, updates: Dict[str, Any]) -> Dict[str, Any]:
        """Atualiza dados do usuário"""
        try:
            if not self.client:
                return {"sucesso": False, "erro": "Cliente Supabase não disponível"}
            
            result = self.client.table('usuarios').update(updates).eq('uid', uid).execute()
            return {"sucesso": True, "data": result.data[0] if result.data else None}
        except Exception as e:
            return {"sucesso": False, "erro": str(e)}
    
    def get_all_users(self) -> List[Dict[str, Any]]:
        """Retorna todos os usuários"""
        try:
            if not self.client:
                return []
            result = self.client.table('usuarios').select("*").execute()
            return result.data
        except Exception as e:
            print(f"Erro ao buscar usuários: {e}")
            return []

    # --- OPERAÇÕES DE GRUPOS ---
    def create_group(self, group_data: Dict[str, Any]) -> Dict[str, Any]:
        """Cria um novo grupo"""
        try:
            if not self.client:
                return {"sucesso": False, "erro": "Cliente Supabase não disponível"}
            
            result = self.client.table('grupos').insert(group_data).execute()
            return {"sucesso": True, "data": result.data[0] if result.data else None}
        except Exception as e:
            return {"sucesso": False, "erro": str(e)}
    
    def get_group_by_name(self, nome: str) -> Optional[Dict[str, Any]]:
        """Busca grupo por nome"""
        try:
            if not self.client:
                return None
            result = self.client.table('grupos').select("*").eq('nome', nome).execute()
            return result.data[0] if result.data else None
        except Exception as e:
            print(f"Erro ao buscar grupo {nome}: {e}")
            return None
    
    def update_group(self, group_id: str, updates: Dict[str, Any]) -> Dict[str, Any]:
        """Atualiza dados do grupo"""
        try:
            if not self.client:
                return {"sucesso": False, "erro": "Cliente Supabase não disponível"}
            
            result = self.client.table('grupos').update(updates).eq('id', group_id).execute()
            return {"sucesso": True, "data": result.data[0] if result.data else None}
        except Exception as e:
            return {"sucesso": False, "erro": str(e)}
    
    def get_all_groups(self) -> List[Dict[str, Any]]:
        """Retorna todos os grupos"""
        try:
            if not self.client:
                return []
            result = self.client.table('grupos').select("*").execute()
            return result.data
        except Exception as e:
            print(f"Erro ao buscar grupos: {e}")
            return []

    # --- OPERAÇÕES DE VÍTIMAS/ALVOS ---
    def get_vitima_por_ip(self, ip: str) -> Optional[Dict[str, Any]]:
        """Busca vítima/alvo por IP"""
        try:
            if not self.client:
                return None
            result = self.client.table('vitimas').select("*").eq('ip', ip).execute()
            return result.data[0] if result.data else None
        except Exception as e:
            print(f"Erro ao buscar vítima por IP {ip}: {e}")
            return None
    
    def atualizar_vitima(self, ip: str, updates: Dict[str, Any]) -> Dict[str, Any]:
        """Atualiza dados de uma vítima"""
        try:
            if not self.client:
                return {"sucesso": False, "erro": "Cliente Supabase não disponível"}
            
            result = self.client.table('vitimas').update(updates).eq('ip', ip).execute()
            return {"sucesso": True, "data": result.data[0] if result.data else None}
        except Exception as e:
            return {"sucesso": False, "erro": str(e)}
    
    def get_todas_vitimas(self) -> List[Dict[str, Any]]:
        """Retorna todas as vítimas/alvos"""
        try:
            if not self.client:
                return []
            result = self.client.table('vitimas').select("*").execute()
            return result.data
        except Exception as e:
            print(f"Erro ao buscar vítimas: {e}")
            return []

    # --- OPERAÇÕES DE HABILIDADES NFT ---
    def create_nft_skill(self, skill_data: Dict[str, Any]) -> Dict[str, Any]:
        """Cria uma nova habilidade NFT"""
        try:
            if not self.client:
                return {"sucesso": False, "erro": "Cliente Supabase não disponível"}
            
            result = self.client.table('habilidades_nft').insert(skill_data).execute()
            return {"sucesso": True, "data": result.data[0] if result.data else None}
        except Exception as e:
            return {"sucesso": False, "erro": str(e)}
    
    def get_nft_skill(self, skill_id: str) -> Optional[Dict[str, Any]]:
        """Busca habilidade NFT por skill_id"""
        try:
            if not self.client:
                return None
            result = self.client.table('habilidades_nft').select("*").eq('skill_id', skill_id).execute()
            return result.data[0] if result.data else None
        except Exception as e:
            print(f"Erro ao buscar habilidade {skill_id}: {e}")
            return None
    
    def update_nft_skill(self, skill_id: str, updates: Dict[str, Any]) -> Dict[str, Any]:
        """Atualiza habilidade NFT"""
        try:
            if not self.client:
                return {"sucesso": False, "erro": "Cliente Supabase não disponível"}
            
            result = self.client.table('habilidades_nft').update(updates).eq('skill_id', skill_id).execute()
            return {"sucesso": True, "data": result.data[0] if result.data else None}
        except Exception as e:
            return {"sucesso": False, "erro": str(e)}
    
    def get_all_nft_skills(self) -> List[Dict[str, Any]]:
        """Retorna todas as habilidades NFT"""
        try:
            if not self.client:
                return []
            result = self.client.table('habilidades_nft').select("*").execute()
            return result.data
        except Exception as e:
            print(f"Erro ao buscar habilidades NFT: {e}")
            return []

    # --- OPERAÇÕES DE LOGS ---
    def log_activity(self, user_uid: str, action: str, details: Dict[str, Any] = None, 
                    ip_address: str = None, user_agent: str = None) -> None:
        """Registra atividade do usuário"""
        try:
            if not self.client:
                return
            
            log_data = {
                'user_uid': user_uid,
                'action': action,
                'details': details or {},
                'ip_address': ip_address,
                'user_agent': user_agent
            }
            self.client.table('activity_logs').insert(log_data).execute()
        except Exception as e:
            print(f"Erro ao registrar log: {e}")

    def get_ranking_usuarios(self, limit: int = None) -> List[Dict[str, Any]]:
        """Busca ranking de usuários ordenados por pontos"""
        try:
            if not self.client:
                return []
            
            query = self.client.table('usuarios').select('*').order('pontos', desc=True)
            if limit:
                query = query.limit(limit)
            
            result = query.execute()
            return result.data if result.data else []
        
        except Exception as e:
            print(f"Erro ao buscar ranking: {e}")
            return []
    
    def atualizar_usuario(self, uid: str, updates: Dict[str, Any]) -> Dict[str, Any]:
        """Atualiza dados do usuário - alias para update_user"""
        return self.update_user(uid, updates)

    def _gerar_ip_realista_unico(self) -> str:
        """
        Gera um IP aleatório único simulando diferentes redes e provedores
        """
        # Definir pools de IPs realistas de diferentes tipos de rede
        ip_pools = [
            # Redes residenciais comuns
            {"base": "10.0", "range_3": (0, 255), "range_4": (1, 254), "weight": 20},
            {"base": "172.16", "range_3": (0, 31), "range_4": (1, 254), "weight": 15},
            {"base": "192.168", "range_3": (0, 255), "range_4": (1, 254), "weight": 25},

            # Redes de provedores brasileiros simuladas
            {"base": "177.12", "range_3": (0, 255), "range_4": (1, 254), "weight": 8},
            {"base": "189.85", "range_3": (0, 255), "range_4": (1, 254), "weight": 8},
            {"base": "201.23", "range_3": (0, 255), "range_4": (1, 254), "weight": 6},
            {"base": "200.147", "range_3": (0, 255), "range_4": (1, 254), "weight": 5},

            # Redes corporativas/educacionais
            {"base": "143.107", "range_3": (0, 255), "range_4": (1, 254), "weight": 3},
            {"base": "150.164", "range_3": (0, 255), "range_4": (1, 254), "weight": 3},
            {"base": "164.41", "range_3": (0, 255), "range_4": (1, 254), "weight": 2},

            # Redes de datacenter/VPS
            {"base": "45.76", "range_3": (0, 255), "range_4": (1, 254), "weight": 2},
            {"base": "104.238", "range_3": (0, 255), "range_4": (1, 254), "weight": 2},
            {"base": "167.99", "range_3": (0, 255), "range_4": (1, 254), "weight": 2},
        ]

        # Criar lista ponderada baseada nos pesos
        weighted_pools = []
        for pool in ip_pools:
            weighted_pools.extend([pool] * pool["weight"])

        tentativas = 0
        max_tentativas = 100

        while tentativas < max_tentativas:
            # Escolher pool aleatório baseado no peso
            pool = random.choice(weighted_pools)

            # Gerar terceiro e quarto octetos
            terceiro = random.randint(pool["range_3"][0], pool["range_3"][1])
            quarto = random.randint(pool["range_4"][0], pool["range_4"][1])

            ip_candidato = f"{pool['base']}.{terceiro}.{quarto}"

            # Verificar se IP já existe
            try:
                ip_existente = self.client.table('usuarios').select('id').eq('ip', ip_candidato).execute()
                if not ip_existente.data:
                    print(f"[IP GENERATOR] IP único gerado: {ip_candidato} (tentativa {tentativas + 1})")
                    return ip_candidato
            except Exception as e:
                print(f"[IP GENERATOR] Erro na verificação: {e}")

            tentativas += 1

        # Fallback para IP simples se falhar
        print(f"[IP GENERATOR] Fallback para IP simples após {max_tentativas} tentativas")
        while True:
            fallback_ip = f"192.168.{random.randint(1, 255)}.{random.randint(1, 254)}"
            try:
                ip_existente = self.client.table('usuarios').select('id').eq('ip', fallback_ip).execute()
                if not ip_existente.data:
                    return fallback_ip
            except:
                continue
    
    def criar_usuario(self, uid: str, nick: str, email: str) -> Dict[str, Any]:
        """Cria usuário no Supabase"""
        try:
            if not self.client:
                return {"sucesso": False, "erro": "Cliente Supabase não disponível"}
            
            # Gera um IP único para o usuário usando o novo sistema
            novo_ip = self._gerar_ip_realista_unico()
            
            user_data = {
                "uid": uid,
                "nick": nick,
                "email": email,
                "ip": novo_ip,
                "dinheiro": 1000,
                "shack": 100,
                "cpu": 1,
                "firewall": 1,
                "antivirus": 1,
                "malware_kit": 1,
                "bruteforce": 1,
                "bankguard": 1,
                "proxyvpn": 1,
                "nivel": 1,
                "xp": 0,
                "nivel_mineradora": 1,
                "habilidades_adquiridas": {},
                "deface_points_individual": 0,
                "tournament_points_individual": 0,
                "ultimo_recurso_coletado_timestamp": datetime.now(timezone.utc).isoformat(),
                "created_at": datetime.now(timezone.utc).isoformat()
            }
            
            result = self.client.table('usuarios').insert(user_data).execute()
            return {"sucesso": True, "data": result.data[0] if result.data else None}
        except Exception as e:
            return {"sucesso": False, "mensagem": str(e)}
    
    def atualizar_habilidade_nft(self, skill_id: str, updates: Dict[str, Any]) -> Dict[str, Any]:
        """Atualiza habilidade NFT - alias para update_nft_skill"""
        return self.update_nft_skill(skill_id, updates)
    
    def log_atividade(self, user_uid: str, action: str, details: Dict[str, Any] = None) -> None:
        """Alias para log_activity"""
        return self.log_activity(user_uid, action, details)

    # --- MÉTODOS ADICIONAIS PARA COMPATIBILIDADE ---
    def get_todas_habilidades_nft(self) -> List[Dict[str, Any]]:
        """Busca todas as habilidades NFT - alias para get_all_nft_skills"""
        return self.get_all_nft_skills()
    
    def get_todas_vitimas(self) -> List[Dict[str, Any]]:
        """Retorna todas as vítimas/alvos - alias para compatibilidade"""
        return self.get_all_victims()
    
    def get_all_victims(self) -> List[Dict[str, Any]]:
        """Retorna todas as vítimas/alvos disponíveis"""
        try:
            if not self.client:
                return []
            result = self.client.table('vitimas').select("*").execute()
            return result.data if result.data else []
        except Exception as e:
            print(f"Erro ao buscar vítimas: {e}")
            return []
    
    def get_todas_noticias(self) -> List[Dict[str, Any]]:
        """Busca todas as notícias"""
        try:
            if not self.client:
                return []
            result = self.client.table('noticias').select("*").order('created_at', desc=True).execute()
            return result.data if result.data else []
        except Exception as e:
            print(f"Erro ao buscar notícias: {e}")
            return []

    def criar_noticia(self, autor_uid: str, titulo: str, conteudo: str, prioridade: bool = False) -> Dict[str, Any]:
        """Cria uma nova notícia"""
        try:
            if not self.client:
                return {"sucesso": False, "erro": "Cliente Supabase não disponível"}

            # Buscar dados do autor
            autor = self.get_user_by_uid(autor_uid)
            if not autor:
                return {"sucesso": False, "erro": "Autor não encontrado"}

            # Dados da notícia (usando estrutura existente da tabela)
            noticia_data = {
                'title': titulo,
                'content': conteudo,
                'priority': prioridade,
                'is_active': True
            }

            result = self.client.table('noticias').insert(noticia_data).execute()

            if result.data and len(result.data) > 0:
                return {"sucesso": True, "data": result.data[0]}
            else:
                return {"sucesso": False, "erro": "Erro ao inserir notícia"}

        except Exception as e:
            return {"sucesso": False, "erro": str(e)}

    def excluir_noticia(self, noticia_id: str) -> Dict[str, Any]:
        """Exclui uma notícia"""
        try:
            if not self.client:
                return {"sucesso": False, "erro": "Cliente Supabase não disponível"}

            result = self.client.table('noticias').delete().eq('id', noticia_id).execute()

            if result.data and len(result.data) > 0:
                return {"sucesso": True, "data": result.data[0]}
            else:
                return {"sucesso": False, "erro": "Notícia não encontrada"}

        except Exception as e:
            return {"sucesso": False, "erro": str(e)}

    # --- OPERAÇÕES DE DEFACE ATIVO ---
    def registrar_deface_ativo(self, alvo_uid: str, atacante_uid: str, grupo_id: str, duracao_minutos: int = 30) -> Dict[str, Any]:
        """Registra um deface ativo na tabela deface_ativo"""
        try:
            if not self.client:
                return {"sucesso": False, "erro": "Cliente Supabase não disponível"}

            from datetime import datetime, timezone, timedelta
            agora = datetime.now(timezone.utc)
            expira_em = agora + timedelta(minutes=duracao_minutos)

            # Remove deface anterior se existir
            self.client.table('deface_ativo').delete().eq('alvo_uid', alvo_uid).execute()

            # Insere novo deface
            deface_data = {
                'alvo_uid': alvo_uid,
                'atacante_uid': atacante_uid,
                'grupo_id': grupo_id,
                'criado_em': agora.isoformat(),
                'expira_em': expira_em.isoformat(),
                'tempo_restante_minutos': duracao_minutos
            }

            result = self.client.table('deface_ativo').insert(deface_data).execute()
            return {"sucesso": True, "data": result.data[0] if result.data else None}

        except Exception as e:
            return {"sucesso": False, "erro": str(e)}

    def verificar_deface_ativo(self, alvo_uid: str) -> Dict[str, Any]:
        """Verifica se um alvo tem deface ativo"""
        try:
            if not self.client:
                return {"tem_deface": False, "erro": "Cliente Supabase não disponível"}

            from datetime import datetime, timezone
            agora = datetime.now(timezone.utc)

            # Busca deface ativo que ainda não expirou
            result = self.client.table('deface_ativo').select("*").eq('alvo_uid', alvo_uid).gt('expira_em', agora.isoformat()).execute()

            if result.data:
                deface = result.data[0]
                # Calcula tempo restante
                expira_em = datetime.fromisoformat(deface['expira_em'].replace('Z', '+00:00'))
                tempo_restante = expira_em - agora
                minutos_restantes = int(tempo_restante.total_seconds() / 60)

                return {
                    "tem_deface": True,
                    "deface": deface,
                    "minutos_restantes": max(0, minutos_restantes)
                }
            else:
                return {"tem_deface": False}

        except Exception as e:
            return {"tem_deface": False, "erro": str(e)}

    def limpar_defaces_expirados(self) -> int:
        """Remove defaces expirados da tabela"""
        try:
            if not self.client:
                return 0

            from datetime import datetime, timezone
            agora = datetime.now(timezone.utc)

            # Remove registros expirados
            result = self.client.table('deface_ativo').delete().lt('expira_em', agora.isoformat()).execute()
            count = len(result.data) if result.data else 0

            if count > 0:
                print(f"[DEFACE] Removidos {count} defaces expirados")

            return count

        except Exception as e:
            print(f"[DEFACE ERROR] Erro ao limpar defaces expirados: {e}")
            return 0

    # === SISTEMA DE COOLDOWNS ===
    def verificar_cooldown_roubo(self, atacante_uid: str, alvo_ip: str) -> Dict[str, Any]:
        """Verifica se o atacante pode roubar do IP específico"""
        try:
            if not self.client:
                return {"pode_roubar": True, "tempo_restante": 0}
            
            # Limpa cooldowns expirados primeiro
            self.client.rpc('limpar_cooldowns_expirados').execute()
            
            # Verifica se existe cooldown ativo
            result = self.client.table('cooldown_roubo').select('expires_at').eq('atacante_uid', atacante_uid).eq('alvo_ip', alvo_ip).execute()
            
            if result.data:
                from datetime import datetime
                expires_at = datetime.fromisoformat(result.data[0]['expires_at'].replace('Z', '+00:00'))
                now = datetime.now(expires_at.tzinfo)
                
                if now < expires_at:
                    tempo_restante = int((expires_at - now).total_seconds())
                    return {"pode_roubar": False, "tempo_restante": tempo_restante}
            
            return {"pode_roubar": True, "tempo_restante": 0}
            
        except Exception as e:
            print(f"Erro ao verificar cooldown de roubo: {e}")
            return {"pode_roubar": True, "tempo_restante": 0}  # Em caso de erro, permite a ação
    
    def adicionar_cooldown_roubo(self, atacante_uid: str, alvo_ip: str) -> bool:
        """Adiciona cooldown de roubo (1 hora)"""
        try:
            if not self.client:
                return False
            
            from datetime import datetime, timedelta
            now = datetime.now()
            expires_at = now + timedelta(hours=1)
            
            # Remove cooldown anterior se existir e adiciona novo
            self.client.table('cooldown_roubo').delete().eq('atacante_uid', atacante_uid).eq('alvo_ip', alvo_ip).execute()
            
            self.client.table('cooldown_roubo').insert({
                'atacante_uid': atacante_uid,
                'alvo_ip': alvo_ip,
                'expires_at': expires_at.isoformat()
            }).execute()
            
            return True
            
        except Exception as e:
            print(f"Erro ao adicionar cooldown de roubo: {e}")
            return False
    
    def verificar_cooldown_deface(self, jogador_uid: str) -> Dict[str, Any]:
        """Verifica se o jogador pode fazer deface"""
        try:
            if not self.client:
                return {"pode_deface": True, "tempo_restante": 0}
            
            # Limpa cooldowns expirados primeiro
            self.client.rpc('limpar_cooldowns_expirados').execute()
            
            # Verifica se existe cooldown ativo
            result = self.client.table('cooldown_deface').select('expires_at').eq('jogador_uid', jogador_uid).execute()
            
            if result.data:
                from datetime import datetime
                expires_at = datetime.fromisoformat(result.data[0]['expires_at'].replace('Z', '+00:00'))
                now = datetime.now(expires_at.tzinfo)
                
                if now < expires_at:
                    tempo_restante = int((expires_at - now).total_seconds())
                    return {"pode_deface": False, "tempo_restante": tempo_restante}
            
            return {"pode_deface": True, "tempo_restante": 0}
            
        except Exception as e:
            print(f"Erro ao verificar cooldown de deface: {e}")
            return {"pode_deface": True, "tempo_restante": 0}  # Em caso de erro, permite a ação
    
    def adicionar_cooldown_deface(self, jogador_uid: str) -> bool:
        """Adiciona cooldown de deface (30 minutos)"""
        try:
            if not self.client:
                return False
            
            from datetime import datetime, timedelta
            now = datetime.now()
            expires_at = now + timedelta(minutes=30)
            
            # Remove cooldown anterior se existir e adiciona novo
            self.client.table('cooldown_deface').delete().eq('jogador_uid', jogador_uid).execute()
            
            self.client.table('cooldown_deface').insert({
                'jogador_uid': jogador_uid,
                'expires_at': expires_at.isoformat()
            }).execute()
            
            return True
            
        except Exception as e:
            print(f"Erro ao adicionar cooldown de deface: {e}")
            return False

# Instância global
supabase_client = SupabaseClient()

# Funções de conveniência (compatibilidade com código existente)
def get_db():
    """Retorna cliente Supabase"""
    return supabase_client.get_client()

def create_user(user_data):
    return supabase_client.create_user(user_data)

def get_user_by_uid(uid):
    return supabase_client.get_user_by_uid(uid)

def get_user_by_nick(nick):
    return supabase_client.get_user_by_nick(nick)

def update_user(uid, updates):
    return supabase_client.update_user(uid, updates)

# Métodos adicionais necessários para compatibilidade
def get_todas_habilidades_nft():
    """Busca todas as habilidades NFT"""
    return supabase_client.get_all_nft_skills()

def get_ranking_usuarios(limit=None):
    """Busca ranking de usuários"""
    return supabase_client.get_ranking_usuarios(limit)

def criar_usuario(uid, nick, email):
    """Cria usuário - função de compatibilidade"""
    return supabase_client.criar_usuario(uid, nick, email)

def atualizar_usuario(uid, updates):
    """Atualiza usuário - função de compatibilidade"""
    return supabase_client.atualizar_usuario(uid, updates)

def get_todas_noticias():
    """Busca todas as notícias"""
    try:
        if not supabase_client.client:
            return []
        result = supabase_client.client.table('noticias').select("*").order('created_at', desc=True).execute()
        return result.data if result.data else []
    except Exception as e:
        print(f"Erro ao buscar notícias: {e}")
        return []

def get_todas_vitimas():
    """Busca todas as vítimas"""
    return supabase_client.get_todas_vitimas()

def get_vitima_por_ip(ip):
    """Busca vítima por IP"""
    return supabase_client.get_vitima_por_ip(ip)

def atualizar_vitima(ip, updates):
    """Atualiza vítima"""
    return supabase_client.atualizar_vitima(ip, updates)

def atualizar_habilidade_nft(skill_id, updates):
    """Atualiza habilidade NFT"""
    return supabase_client.atualizar_habilidade_nft(skill_id, updates)

def log_atividade(user_uid, action, details=None):
    """Log de atividade"""
    return supabase_client.log_atividade(user_uid, action, details)

# === FUNÇÕES WRAPPER PARA COOLDOWNS ===
def verificar_cooldown_roubo(atacante_uid: str, alvo_ip: str):
    """Wrapper para verificar cooldown de roubo"""
    result = supabase_client.verificar_cooldown_roubo(atacante_uid, alvo_ip)
    if not result.get("pode_roubar", True):
        tempo_restante = result.get("tempo_restante", 0)
        horas = tempo_restante // 3600
        minutos = (tempo_restante % 3600) // 60
        if horas > 0:
            return f"Você precisa aguardar {horas}h {minutos}m antes de roubar este IP novamente."
        else:
            return f"Você precisa aguardar {minutos}m antes de roubar este IP novamente."
    return None

def adicionar_cooldown_roubo(atacante_uid: str, alvo_ip: str):
    """Wrapper para adicionar cooldown de roubo"""
    return supabase_client.adicionar_cooldown_roubo(atacante_uid, alvo_ip)

def verificar_cooldown_deface(jogador_uid: str):
    """Wrapper para verificar cooldown de deface"""
    result = supabase_client.verificar_cooldown_deface(jogador_uid)
    if not result.get("pode_deface", True):
        tempo_restante = result.get("tempo_restante", 0)
        minutos = tempo_restante // 60
        return f"Você precisa aguardar {minutos}m antes de fazer outro deface."
    return None

def adicionar_cooldown_deface(jogador_uid: str):
    """Wrapper para adicionar cooldown de deface"""
    return supabase_client.adicionar_cooldown_deface(jogador_uid)
