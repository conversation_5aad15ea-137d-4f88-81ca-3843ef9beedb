#!/usr/bin/env python3
"""
Teste direto da função de mineração com captura de todos os prints
"""

import os
import sys
from dotenv import load_dotenv

# Carrega variáveis de ambiente
load_dotenv()

print("🎯 TESTE DIRETO DA FUNÇÃO DE MINERAÇÃO")
print("=" * 45)

# Importar módulos
try:
    from database.supabase_client import supabase_client
    print("✅ supabase_client importado")
    
    from game import new_models as models
    print("✅ new_models importado")
except Exception as e:
    print(f"❌ Erro na importação: {e}")
    exit(1)

# Verificar conexão
if not supabase_client.is_connected():
    print("❌ Supabase não conectado")
    exit(1)

print("✅ Supabase conectado")

# Buscar usuários antes
print("\n👥 Buscando usuários antes...")
usuarios_antes = supabase_client.get_ranking_usuarios(3)
if not usuarios_antes:
    print("❌ Nenhum usuário encontrado")
    exit(1)

print(f"📊 {len(usuarios_antes)} usuários encontrados")

# Mostrar saldos antes
print("\n💰 SALDOS ANTES:")
saldos_antes = {}
for usuario in usuarios_antes:
    uid = usuario['uid']
    nick = usuario.get('nick', 'Unknown')
    dinheiro = usuario.get('dinheiro', 0)
    nivel = usuario.get('nivel_mineradora', 1)
    saldos_antes[uid] = dinheiro
    print(f"   {nick} (Nível {nivel}): ${dinheiro}")

# Executar função diretamente
print("\n⚡ EXECUTANDO FUNÇÃO DIRETAMENTE...")
print("=" * 50)

try:
    # Chamar a função diretamente
    resultado = models.processar_mineracao_automatica_dinheiro()
    
    print("=" * 50)
    print(f"\n📊 RESULTADO DA FUNÇÃO:")
    print(f"   Tipo: {type(resultado)}")
    print(f"   Conteúdo: {resultado}")
    
    if isinstance(resultado, dict):
        print(f"   Sucesso: {resultado.get('sucesso', 'N/A')}")
        print(f"   Jogadores processados: {resultado.get('jogadores_processados', 'N/A')}")
        print(f"   Total gerado: ${resultado.get('total_dinheiro_gerado', 'N/A')}")
        print(f"   Mensagem: {resultado.get('mensagem', 'N/A')}")
    
except Exception as e:
    print(f"❌ ERRO NA EXECUÇÃO: {e}")
    import traceback
    traceback.print_exc()
    exit(1)

# Verificar resultados
print("\n🔍 VERIFICANDO RESULTADOS...")
usuarios_depois = supabase_client.get_ranking_usuarios(3)

print("\n💰 SALDOS DEPOIS:")
mudancas_detectadas = 0
total_incrementado = 0

for usuario in usuarios_depois:
    uid = usuario['uid']
    nick = usuario.get('nick', 'Unknown')
    dinheiro_depois = usuario.get('dinheiro', 0)
    dinheiro_antes = saldos_antes.get(uid, 0)
    diferenca = dinheiro_depois - dinheiro_antes
    
    if diferenca > 0:
        print(f"   {nick}: ${dinheiro_antes} → ${dinheiro_depois} (+${diferenca}) ✅")
        mudancas_detectadas += 1
        total_incrementado += diferenca
    else:
        print(f"   {nick}: ${dinheiro_depois} (sem mudança)")

# Resumo final
print(f"\n📈 RESUMO FINAL:")
print(f"   Usuários com mudança: {mudancas_detectadas}")
print(f"   Total incrementado: ${total_incrementado}")

if mudancas_detectadas > 0:
    print("\n🎉 SUCESSO!")
    print("✅ A mineração automática está funcionando!")
else:
    print("\n❌ FALHA!")
    print("A mineração não está incrementando o dinheiro")

print(f"\n📊 DIAGNÓSTICO:")
if resultado and isinstance(resultado, dict):
    if resultado.get('sucesso'):
        if resultado.get('jogadores_processados', 0) > 0:
            if mudancas_detectadas > 0:
                print("✅ Tudo funcionando perfeitamente!")
            else:
                print("⚠️ Função processa mas não persiste no banco")
        else:
            print("⚠️ Função executa mas não processa nenhum jogador")
    else:
        print("❌ Função falha na execução")
else:
    print("❌ Função não retorna resultado válido")

print("\n🔧 PRÓXIMOS PASSOS:")
if mudancas_detectadas > 0:
    print("1. Sistema está funcionando!")
    print("2. Iniciar servidor: python app.py")
    print("3. Verificar logs automáticos")
else:
    print("1. Verificar logs detalhados acima")
    print("2. Identificar onde a função está parando")
    print("3. Corrigir o problema específico")
