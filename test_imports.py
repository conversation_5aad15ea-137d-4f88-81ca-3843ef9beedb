#!/usr/bin/env python3
import sys
import os

print("Testando importações...")

try:
    print("1. Importando Flask...")
    from flask import Flask
    print("   ✅ Flask OK")
except Exception as e:
    print(f"   ❌ Erro Flask: {e}")

try:
    print("2. Importando dotenv...")
    from dotenv import load_dotenv
    print("   ✅ dotenv OK")
except Exception as e:
    print(f"   ❌ Erro dotenv: {e}")

try:
    print("3. Carregando .env...")
    load_dotenv()
    print("   ✅ .env carregado")
except Exception as e:
    print(f"   ❌ Erro .env: {e}")

try:
    print("4. Importando database...")
    from database.supabase_client import supabase_client
    print("   ✅ Database OK")
except Exception as e:
    print(f"   ❌ Erro Database: {e}")

try:
    print("5. Importando models...")
    from game import new_models as models
    print("   ✅ Models OK")
except Exception as e:
    print(f"   ❌ Erro Models: {e}")

try:
    print("6. Importando routes...")
    from game.routes import main
    print("   ✅ Routes OK")
except Exception as e:
    print(f"   ❌ Erro Routes: {e}")

print("\nTeste de importações concluído!")
