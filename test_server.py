from flask import Flask, jsonify

app = Flask(__name__)

@app.route("/")
def home():
    return "Servidor de teste funcionando!"

@app.route("/api/shop/items")
def shop_items():
    return jsonify({
        "sucesso": True,
        "items": [
            {
                "id": "test_item",
                "nome": "Item de Teste",
                "descricao": "Teste do shop",
                "preco": 100,
                "categoria": "teste"
            }
        ],
        "is_supporter": True,
        "spoints_disponiveis": 1000,
        "mensagem": "Shop funcionando - servidor de teste"
    })

if __name__ == "__main__":
    print("Iniciando servidor de teste...")
    app.run(debug=True, host="0.0.0.0", port=5000)
