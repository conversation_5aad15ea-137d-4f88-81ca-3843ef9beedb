#!/usr/bin/env python3
"""
Teste simples das APIs de cores de nickname
"""

import sys
import requests
import json
sys.path.append('.')

def test_nickname_colors():
    """Testa as APIs de cores de nickname"""
    print("🎨 Testando APIs de cores de nickname...")
    
    try:
        # Criar sessão
        session = requests.Session()
        
        # 1. Fazer login
        print("1. 🔑 Fazendo login...")
        login_response = session.get("http://localhost:5000/api/auth/auto-login")
        if login_response.status_code != 200:
            print(f"❌ Erro no login: {login_response.status_code}")
            return False
        
        print("✅ Login OK")
        
        # 2. Testar busca de cores
        print("2. 🎨 Testando busca de cores...")
        colors_response = session.get("http://localhost:5000/api/configuracoes/cores-nickname")
        
        print(f"Status busca cores: {colors_response.status_code}")
        
        if colors_response.status_code == 200:
            colors_data = colors_response.json()
            print(f"Dados: {json.dumps(colors_data, indent=2)}")
            
            if colors_data.get('sucesso'):
                cores = colors_data.get('cores', [])
                is_supporter = colors_data.get('is_supporter', False)
                spoints = colors_data.get('spoints_disponiveis', 0)
                
                print(f"✅ API de cores funcionando!")
                print(f"   - É supporter: {is_supporter}")
                print(f"   - SPoints: {spoints}")
                print(f"   - Cores disponíveis: {len(cores)}")
                
                if cores:
                    print("🎨 Cores encontradas:")
                    for cor in cores[:3]:  # Mostrar apenas as 3 primeiras
                        print(f"   - {cor.get('nome')}: {cor.get('color')} ({cor.get('preco')} SP)")
                
                # 3. Testar aplicação de cor (se for supporter)
                if is_supporter and spoints >= 25:
                    print("3. 🖌️ Testando aplicação de cor...")
                    apply_response = session.post("http://localhost:5000/api/configuracoes/cores-nickname/aplicar", 
                                                json={'color_id': 'nick_color_red'})
                    
                    if apply_response.status_code == 200:
                        apply_data = apply_response.json()
                        print(f"Resultado aplicação: {apply_data}")
                        
                        if apply_data.get('sucesso'):
                            print("✅ Cor aplicada com sucesso!")
                        else:
                            print(f"⚠️ Erro na aplicação: {apply_data.get('mensagem')}")
                    else:
                        print(f"❌ Erro HTTP na aplicação: {apply_response.status_code}")
                else:
                    print("3. ⚠️ Não é supporter ou SPoints insuficientes para testar aplicação")
                
            else:
                print(f"⚠️ API de cores retornou erro: {colors_data.get('mensagem')}")
        else:
            print(f"❌ Erro HTTP na busca: {colors_response.status_code}")
            print(f"Resposta: {colors_response.text}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro no teste: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Iniciando teste das cores de nickname...")
    success = test_nickname_colors()
    
    if success:
        print("\n✅ Teste concluído!")
    else:
        print("\n❌ Teste falhou!")
