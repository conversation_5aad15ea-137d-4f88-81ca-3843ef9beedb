#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import datetime, timezone
from database.supabase_client import supabase_client
import game.new_models as models

def debug_mineracao_detalhado():
    """Debug detalhado da mineração automática"""
    print("🔍 DEBUG DETALHADO DA MINERAÇÃO AUTOMÁTICA")
    print("=" * 50)
    
    # 1. Verificar conexão
    print("\n1️⃣ VERIFICANDO CONEXÃO...")
    if not supabase_client.is_connected():
        print("❌ Supabase não conectado!")
        return False
    print("✅ Supabase conectado!")
    
    # 2. Buscar usuários
    print("\n2️⃣ BUSCANDO USUÁRIOS...")
    usuarios = supabase_client.get_ranking_usuarios(1000)
    print(f"👥 Encontrados {len(usuarios)} usuários")
    
    if not usuarios:
        print("❌ Nenhum usuário encontrado!")
        return False
    
    # 3. Analisar usuários
    print("\n3️⃣ ANALISANDO USUÁRIOS...")
    usuarios_validos = 0
    usuarios_com_mineradora = 0
    
    for i, usuario in enumerate(usuarios[:10]):  # Primeiros 10 para debug
        uid = usuario.get('uid')
        nick = usuario.get('nick', 'Unknown')
        nivel_mineradora = usuario.get('nivel_mineradora', 1)
        dinheiro = usuario.get('dinheiro', 0)
        
        print(f"\n👤 Usuário {i+1}: {nick}")
        print(f"   UID: {uid}")
        print(f"   Nível Mineradora: {nivel_mineradora}")
        print(f"   Dinheiro: ${dinheiro}")
        
        if uid:
            usuarios_validos += 1
            if nivel_mineradora >= 1:
                usuarios_com_mineradora += 1
                print(f"   ✅ Válido para mineração!")
            else:
                print(f"   ❌ Mineradora inativa")
        else:
            print(f"   ❌ UID inválido")
    
    print(f"\n📊 RESUMO:")
    print(f"   Total usuários: {len(usuarios)}")
    print(f"   Usuários válidos: {usuarios_validos}")
    print(f"   Com mineradora ativa: {usuarios_com_mineradora}")
    
    # 4. Verificar cache
    print("\n4️⃣ VERIFICANDO CACHE...")
    try:
        cache = models.MINERACAO_AUTOMATICA_CACHE
        print(f"📦 Cache atual: {len(cache)} entradas")
        if cache:
            print("   Primeiras 5 entradas:")
            for i, (uid, timestamp) in enumerate(list(cache.items())[:5]):
                print(f"     {i+1}. {uid}: {timestamp}")
        else:
            print("   Cache vazio")
    except Exception as e:
        print(f"❌ Erro ao acessar cache: {e}")
    
    # 5. Testar processamento de um usuário
    if usuarios_com_mineradora > 0:
        print("\n5️⃣ TESTANDO PROCESSAMENTO...")
        
        # Encontrar primeiro usuário com mineradora
        usuario_teste = None
        for usuario in usuarios:
            if usuario.get('uid') and usuario.get('nivel_mineradora', 1) >= 1:
                usuario_teste = usuario
                break
        
        if usuario_teste:
            uid = usuario_teste['uid']
            nick = usuario_teste.get('nick', 'Unknown')
            nivel = usuario_teste.get('nivel_mineradora', 1)
            dinheiro_antes = usuario_teste.get('dinheiro', 0)
            
            print(f"🎯 Testando com: {nick}")
            print(f"   UID: {uid}")
            print(f"   Nível: {nivel}")
            print(f"   Dinheiro antes: ${dinheiro_antes}")
            
            # Verificar se está no cache
            agora = datetime.now(timezone.utc).timestamp()
            if uid in models.MINERACAO_AUTOMATICA_CACHE:
                ultimo = models.MINERACAO_AUTOMATICA_CACHE[uid]
                tempo_desde = agora - ultimo
                print(f"   Cache: último processamento há {tempo_desde:.1f}s")
                if tempo_desde < 50:
                    print(f"   ⚠️ Usuário em cooldown! Limpando cache para teste...")
                    del models.MINERACAO_AUTOMATICA_CACHE[uid]
            else:
                print(f"   Cache: usuário não encontrado (primeira execução)")
            
            # Calcular dinheiro esperado
            dinheiro_base = 15 + (nivel - 1) * 20 + (nivel - 1) * 5
            print(f"   Dinheiro esperado: ${dinheiro_base}")
            
            # Testar atualização
            novo_dinheiro = dinheiro_antes + dinheiro_base
            print(f"   Novo total esperado: ${novo_dinheiro}")
            
            try:
                resultado = supabase_client.atualizar_usuario(uid, {
                    'dinheiro': novo_dinheiro
                })
                print(f"   Resultado atualização: {resultado}")
                
                if resultado.get('sucesso'):
                    print("   ✅ Atualização funcionou!")
                    
                    # Verificar se realmente atualizou
                    usuario_atualizado = supabase_client.get_user_by_uid(uid)
                    if usuario_atualizado:
                        dinheiro_atual = usuario_atualizado.get('dinheiro', 0)
                        print(f"   Dinheiro atual no banco: ${dinheiro_atual}")
                        
                        if dinheiro_atual == novo_dinheiro:
                            print("   ✅ Banco atualizado corretamente!")
                        else:
                            print(f"   ❌ Banco não atualizou! Esperado: ${novo_dinheiro}, Atual: ${dinheiro_atual}")
                    else:
                        print("   ❌ Erro ao buscar usuário atualizado")
                else:
                    print(f"   ❌ Atualização falhou: {resultado}")
                    
            except Exception as e:
                print(f"   ❌ Erro na atualização: {e}")
    
    # 6. Executar função completa
    print("\n6️⃣ EXECUTANDO FUNÇÃO COMPLETA...")
    try:
        resultado = models.processar_mineracao_automatica_dinheiro()
        print(f"📊 Resultado: {resultado}")
        
        if resultado.get('sucesso'):
            processados = resultado.get('jogadores_processados', 0)
            total_gerado = resultado.get('total_dinheiro_gerado', 0)
            
            print(f"✅ Função executou!")
            print(f"   Processados: {processados}")
            print(f"   Total gerado: ${total_gerado}")
            
            if processados > 0:
                print("🎉 MINERAÇÃO FUNCIONANDO!")
                return True
            else:
                print("❌ Nenhum jogador processado!")
                return False
        else:
            print(f"❌ Função falhou: {resultado.get('mensagem')}")
            return False
            
    except Exception as e:
        print(f"❌ Erro na função: {e}")
        return False

if __name__ == "__main__":
    debug_mineracao_detalhado()
