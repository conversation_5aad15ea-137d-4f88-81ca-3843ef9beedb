#!/usr/bin/env python3
"""
Script para debugar e corrigir a estrutura da tabela de notícias
"""

import os
from dotenv import load_dotenv
from database.supabase_client import supabase_client

# Carrega variáveis de ambiente
load_dotenv()

def verificar_estrutura_noticias():
    """Verifica a estrutura atual da tabela noticias"""
    try:
        if not supabase_client.is_connected():
            print("❌ Erro: Não foi possível conectar ao Supabase")
            return
        
        # Buscar todas as notícias para ver a estrutura
        result = supabase_client.client.table('noticias').select("*").execute()
        
        print("📊 Estrutura atual da tabela 'noticias':")
        print(f"Total de registros: {len(result.data) if result.data else 0}")
        
        if result.data and len(result.data) > 0:
            print("\n📋 Campos disponíveis:")
            primeiro_registro = result.data[0]
            for campo, valor in primeiro_registro.items():
                tipo_valor = type(valor).__name__
                print(f"   - {campo}: {tipo_valor} = {valor}")
            
            print("\n📰 Todas as notícias:")
            for i, noticia in enumerate(result.data):
                print(f"\n{i+1}. ID: {noticia.get('id', 'N/A')}")
                print(f"   Título: {noticia.get('titulo', noticia.get('title', 'N/A'))}")
                print(f"   Conteúdo: {noticia.get('conteudo', noticia.get('content', 'N/A'))[:50]}...")
                print(f"   Autor UID: {noticia.get('autor_uid', noticia.get('author_uid', 'N/A'))}")
                print(f"   Autor Nick: {noticia.get('autor_nick', noticia.get('author_nick', 'N/A'))}")
                print(f"   Prioridade: {noticia.get('prioridade', noticia.get('priority', 'N/A'))}")
                print(f"   Criado em: {noticia.get('created_at', 'N/A')}")
        else:
            print("📭 Nenhuma notícia encontrada")
            
    except Exception as e:
        print(f"❌ Erro ao verificar estrutura: {e}")

def corrigir_estrutura_noticias():
    """Corrige a estrutura da tabela se necessário"""
    try:
        # SQL para garantir que a tabela tenha a estrutura correta
        sql_corrigir = """
        -- Adicionar colunas se não existirem
        DO $$ 
        BEGIN
            -- Verificar e adicionar coluna titulo se não existir
            IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                          WHERE table_name='noticias' AND column_name='titulo') THEN
                ALTER TABLE noticias ADD COLUMN titulo VARCHAR(255);
            END IF;
            
            -- Verificar e adicionar coluna conteudo se não existir
            IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                          WHERE table_name='noticias' AND column_name='conteudo') THEN
                ALTER TABLE noticias ADD COLUMN conteudo TEXT;
            END IF;
            
            -- Verificar e adicionar coluna autor_uid se não existir
            IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                          WHERE table_name='noticias' AND column_name='autor_uid') THEN
                ALTER TABLE noticias ADD COLUMN autor_uid VARCHAR(255);
            END IF;
            
            -- Verificar e adicionar coluna autor_nick se não existir
            IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                          WHERE table_name='noticias' AND column_name='autor_nick') THEN
                ALTER TABLE noticias ADD COLUMN autor_nick VARCHAR(100);
            END IF;
            
            -- Verificar e adicionar coluna prioridade se não existir
            IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                          WHERE table_name='noticias' AND column_name='prioridade') THEN
                ALTER TABLE noticias ADD COLUMN prioridade BOOLEAN DEFAULT FALSE;
            END IF;
        END $$;
        
        -- Migrar dados se existirem colunas antigas
        UPDATE noticias SET 
            titulo = title 
        WHERE titulo IS NULL AND title IS NOT NULL;
        
        UPDATE noticias SET 
            conteudo = content 
        WHERE conteudo IS NULL AND content IS NOT NULL;
        
        UPDATE noticias SET 
            autor_uid = author_uid 
        WHERE autor_uid IS NULL AND author_uid IS NOT NULL;
        
        UPDATE noticias SET 
            autor_nick = author_nick 
        WHERE autor_nick IS NULL AND author_nick IS NOT NULL;
        
        UPDATE noticias SET 
            prioridade = priority 
        WHERE prioridade IS NULL AND priority IS NOT NULL;
        """
        
        print("🔧 Corrigindo estrutura da tabela...")
        
        # Executar correção (pode não funcionar se não tiver função exec_sql)
        try:
            result = supabase_client.client.rpc('exec_sql', {'sql': sql_corrigir}).execute()
            print("✅ Estrutura corrigida via RPC")
        except:
            print("⚠️ Não foi possível usar RPC, tentando correção manual...")
            
            # Tentar correção manual
            try:
                # Buscar notícias existentes
                noticias = supabase_client.client.table('noticias').select("*").execute()
                
                if noticias.data:
                    for noticia in noticias.data:
                        updates = {}
                        
                        # Mapear campos antigos para novos
                        if 'title' in noticia and noticia['title']:
                            updates['titulo'] = noticia['title']
                        if 'content' in noticia and noticia['content']:
                            updates['conteudo'] = noticia['content']
                        if 'author_uid' in noticia and noticia['author_uid']:
                            updates['autor_uid'] = noticia['author_uid']
                        if 'author_nick' in noticia and noticia['author_nick']:
                            updates['autor_nick'] = noticia['author_nick']
                        if 'priority' in noticia and noticia['priority'] is not None:
                            updates['prioridade'] = noticia['priority']
                        
                        # Atualizar se há mudanças
                        if updates:
                            supabase_client.client.table('noticias').update(updates).eq('id', noticia['id']).execute()
                            print(f"✅ Notícia {noticia['id']} atualizada")
                
                print("✅ Correção manual concluída")
                
            except Exception as e2:
                print(f"❌ Erro na correção manual: {e2}")
        
    except Exception as e:
        print(f"❌ Erro ao corrigir estrutura: {e}")

def criar_noticia_teste_correta():
    """Cria uma notícia de teste com a estrutura correta"""
    try:
        # Buscar um usuário admin
        usuarios = supabase_client.client.table('usuarios').select("uid, nick, is_admin").eq('is_admin', True).limit(1).execute()
        
        if not usuarios.data:
            print("⚠️ Nenhum usuário admin encontrado")
            # Usar dados padrão
            autor_uid = "admin"
            autor_nick = "Administrador"
        else:
            admin = usuarios.data[0]
            autor_uid = admin['uid']
            autor_nick = admin['nick']
        
        # Limpar notícias antigas de teste
        supabase_client.client.table('noticias').delete().ilike('titulo', '%Sistema de Notícias%').execute()
        
        # Criar notícia de teste
        noticia_teste = {
            'titulo': '🎉 Sistema de Notícias Funcionando!',
            'conteudo': 'O sistema de notícias do SHACK foi corrigido e está funcionando perfeitamente! Os administradores podem agora publicar atualizações importantes para todos os jogadores. Esta é uma notícia de teste para verificar se tudo está funcionando corretamente.',
            'autor_uid': autor_uid,
            'autor_nick': autor_nick,
            'prioridade': True
        }
        
        result = supabase_client.client.table('noticias').insert(noticia_teste).execute()
        
        if result.data:
            print("✅ Notícia de teste criada com sucesso!")
            print(f"📰 Título: {noticia_teste['titulo']}")
            print(f"👤 Autor: {noticia_teste['autor_nick']}")
            return True
        else:
            print("❌ Erro ao criar notícia de teste")
            return False
            
    except Exception as e:
        print(f"❌ Erro ao criar notícia de teste: {e}")
        return False

def main():
    """Função principal"""
    print("🔍 Debugando sistema de notícias...")
    print("=" * 50)
    
    if not supabase_client.is_connected():
        print("❌ Erro: Conexão com Supabase não disponível")
        return
    
    print("✅ Conexão com Supabase estabelecida")
    
    # Verificar estrutura atual
    print("\n1. Verificando estrutura atual...")
    verificar_estrutura_noticias()
    
    # Corrigir estrutura se necessário
    print("\n2. Corrigindo estrutura...")
    corrigir_estrutura_noticias()
    
    # Criar notícia de teste
    print("\n3. Criando notícia de teste...")
    criar_noticia_teste_correta()
    
    # Verificar resultado final
    print("\n4. Verificando resultado final...")
    verificar_estrutura_noticias()
    
    print("\n🎉 Debug concluído!")

if __name__ == "__main__":
    main()
