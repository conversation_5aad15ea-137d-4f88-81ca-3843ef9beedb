#!/usr/bin/env python3
"""
Verificação completa do banco Supabase para mineração automática
"""

import os
import sys
from dotenv import load_dotenv

# Carrega variáveis de ambiente
load_dotenv()

def verificar_conexao_supabase():
    """Verifica conexão com Supabase"""
    print("🔍 Verificando conexão com Supabase...")
    
    try:
        from database.supabase_client import supabase_client
        
        if supabase_client.is_connected():
            print("✅ Conexão com Supabase estabelecida")
            return supabase_client
        else:
            print("❌ Falha na conexão com Supabase")
            return None
    except Exception as e:
        print(f"❌ Erro ao conectar com Supabase: {e}")
        return None

def verificar_estrutura_tabela_usuarios(supabase_client):
    """Verifica estrutura da tabela usuarios"""
    print("\n📊 Verificando estrutura da tabela 'usuarios'...")
    
    try:
        # Buscar um usuário para ver as colunas disponíveis
        usuarios = supabase_client.get_ranking_usuarios(1)
        if not usuarios:
            print("❌ Nenhum usuário encontrado na tabela")
            return False
        
        usuario = usuarios[0]
        print(f"👤 Usuário exemplo: {usuario.get('nick', 'Unknown')}")
        
        # Listar todas as colunas
        print(f"\n📋 Colunas disponíveis na tabela 'usuarios':")
        colunas_encontradas = []
        for coluna, valor in sorted(usuario.items()):
            tipo_valor = type(valor).__name__
            print(f"   ✅ {coluna}: {valor} ({tipo_valor})")
            colunas_encontradas.append(coluna)
        
        # Verificar colunas essenciais para mineração
        colunas_essenciais = ['uid', 'nick', 'dinheiro', 'nivel_mineradora']
        print(f"\n🔍 Verificando colunas essenciais para mineração:")
        
        todas_essenciais_presentes = True
        for coluna in colunas_essenciais:
            if coluna in colunas_encontradas:
                print(f"   ✅ {coluna}: PRESENTE")
            else:
                print(f"   ❌ {coluna}: AUSENTE")
                todas_essenciais_presentes = False
        
        # Verificar colunas opcionais
        colunas_opcionais = ['ultimo_dinheiro_mineracao_timestamp', 'updated_at', 'created_at']
        print(f"\n🔍 Verificando colunas opcionais:")
        
        for coluna in colunas_opcionais:
            if coluna in colunas_encontradas:
                print(f"   ✅ {coluna}: PRESENTE")
            else:
                print(f"   ⚠️ {coluna}: AUSENTE (não crítico)")
        
        return todas_essenciais_presentes
        
    except Exception as e:
        print(f"❌ Erro ao verificar estrutura da tabela: {e}")
        return False

def testar_atualizacao_direta(supabase_client):
    """Testa atualização direta no banco"""
    print("\n🧪 Testando atualização direta no banco...")
    
    try:
        # Buscar um usuário para teste
        usuarios = supabase_client.get_ranking_usuarios(1)
        if not usuarios:
            print("❌ Nenhum usuário disponível para teste")
            return False
        
        usuario = usuarios[0]
        uid = usuario['uid']
        nick = usuario.get('nick', 'Unknown')
        dinheiro_original = usuario.get('dinheiro', 0)
        
        print(f"👤 Testando com usuário: {nick}")
        print(f"💰 Dinheiro original: ${dinheiro_original}")
        
        # Teste 1: Incrementar dinheiro em $1
        novo_valor = dinheiro_original + 1
        print(f"\n📝 Teste 1: Atualizando dinheiro para ${novo_valor}...")
        
        resultado = supabase_client.atualizar_usuario(uid, {
            'dinheiro': novo_valor
        })
        
        print(f"📊 Resultado da atualização: {resultado}")
        
        if not resultado.get('sucesso'):
            print(f"❌ Falha na atualização: {resultado.get('erro', 'Erro desconhecido')}")
            return False
        
        # Verificar se a atualização foi persistida
        print(f"🔍 Verificando persistência...")
        usuario_atualizado = supabase_client.get_user_by_uid(uid)
        
        if usuario_atualizado:
            dinheiro_verificado = usuario_atualizado.get('dinheiro', 0)
            print(f"💰 Dinheiro verificado: ${dinheiro_verificado}")
            
            if dinheiro_verificado == novo_valor:
                print("✅ Atualização persistida com sucesso!")
                
                # Reverter para valor original
                print(f"🔄 Revertendo para valor original (${dinheiro_original})...")
                supabase_client.atualizar_usuario(uid, {'dinheiro': dinheiro_original})
                print("✅ Valor revertido")
                
                return True
            else:
                print(f"❌ Valor não foi persistido! Esperado: ${novo_valor}, Obtido: ${dinheiro_verificado}")
                return False
        else:
            print("❌ Erro ao buscar usuário atualizado")
            return False
            
    except Exception as e:
        print(f"❌ Erro no teste de atualização: {e}")
        return False

def testar_funcao_mineracao():
    """Testa a função de mineração diretamente"""
    print("\n⛏️ Testando função de mineração diretamente...")
    
    try:
        from game import new_models as models
        print("✅ Módulo new_models importado")
        
        # Verificar se a função existe
        if hasattr(models, 'processar_mineracao_automatica_dinheiro'):
            print("✅ Função processar_mineracao_automatica_dinheiro encontrada")
        else:
            print("❌ Função processar_mineracao_automatica_dinheiro não encontrada")
            return False
        
        # Executar a função
        print("⚡ Executando função de mineração...")
        resultado = models.processar_mineracao_automatica_dinheiro()
        
        print(f"📊 Resultado da execução:")
        print(f"   Sucesso: {resultado.get('sucesso', False)}")
        print(f"   Jogadores processados: {resultado.get('jogadores_processados', 0)}")
        print(f"   Total dinheiro gerado: ${resultado.get('total_dinheiro_gerado', 0)}")
        print(f"   Mensagem: {resultado.get('mensagem', 'N/A')}")
        
        if resultado.get('sucesso') and resultado.get('jogadores_processados', 0) > 0:
            print("✅ Função de mineração executou com sucesso!")
            return True
        else:
            print("❌ Função de mineração falhou ou não processou ninguém")
            return False
            
    except Exception as e:
        print(f"❌ Erro ao testar função de mineração: {e}")
        import traceback
        traceback.print_exc()
        return False

def verificar_timer_app():
    """Verifica configuração do timer no app.py"""
    print("\n⏰ Verificando configuração do timer no app.py...")
    
    try:
        with open('app.py', 'r', encoding='utf-8') as f:
            conteudo = f.read()
        
        # Procurar por configurações do timer
        if 'time.sleep(60)' in conteudo:
            print("✅ Timer de 60 segundos encontrado")
        elif 'time.sleep(3600)' in conteudo:
            print("⚠️ Timer ainda configurado para 3600 segundos (1 hora)")
        else:
            print("❌ Timer não encontrado")
        
        # Procurar pela função de mineração
        if 'processar_mineracao_automatica_dinheiro' in conteudo:
            print("✅ Chamada da função de mineração encontrada")
        else:
            print("❌ Chamada da função de mineração não encontrada")
        
        # Procurar por thread de background
        if 'threading.Thread' in conteudo:
            print("✅ Thread de background configurada")
        else:
            print("❌ Thread de background não encontrada")
            
        return True
        
    except Exception as e:
        print(f"❌ Erro ao verificar app.py: {e}")
        return False

def main():
    """Função principal de verificação"""
    print("🔍 VERIFICAÇÃO COMPLETA DO SISTEMA DE MINERAÇÃO AUTOMÁTICA")
    print("=" * 65)
    
    # Teste 1: Conexão Supabase
    supabase_client = verificar_conexao_supabase()
    if not supabase_client:
        print("\n❌ FALHA CRÍTICA: Não foi possível conectar com Supabase")
        return
    
    # Teste 2: Estrutura da tabela
    if not verificar_estrutura_tabela_usuarios(supabase_client):
        print("\n❌ FALHA CRÍTICA: Colunas essenciais ausentes na tabela usuarios")
        return
    
    # Teste 3: Atualização direta
    if not testar_atualizacao_direta(supabase_client):
        print("\n❌ FALHA CRÍTICA: Não foi possível atualizar dados no banco")
        return
    
    # Teste 4: Função de mineração
    if not testar_funcao_mineracao():
        print("\n❌ FALHA CRÍTICA: Função de mineração não funciona")
        return
    
    # Teste 5: Configuração do timer
    verificar_timer_app()
    
    print("\n✅ VERIFICAÇÃO CONCLUÍDA")
    print("🎯 Todos os componentes básicos estão funcionando!")
    print("\n📋 Próximos passos:")
    print("1. Iniciar o servidor: python app.py")
    print("2. Aguardar 1 minuto para ver logs de mineração")
    print("3. Verificar se o dinheiro dos jogadores aumenta")

if __name__ == "__main__":
    main()
