#!/usr/bin/env python3
"""
Teste completo e robusto da mineração automática corrigida
"""

import os
import sys
from dotenv import load_dotenv
from datetime import datetime, timezone

# Carrega variáveis de ambiente
load_dotenv()

def test_imports():
    """Testa importações"""
    print("🔍 Testando importações...")
    
    try:
        from database.supabase_client import supabase_client
        from game import new_models as models
        print("✅ Importações bem-sucedidas")
        return supabase_client, models
    except Exception as e:
        print(f"❌ Erro nas importações: {e}")
        return None, None

def clear_mining_cache(models):
    """Limpa cache de mineração para forçar processamento"""
    print("🧹 Limpando cache de mineração...")
    models.MINERACAO_AUTOMATICA_CACHE.clear()
    print("✅ Cache limpo")

def test_mining_with_detailed_logs(models, supabase_client):
    """Testa mineração com logs detalhados"""
    print("\n🔬 TESTE DETALHADO DA MINERAÇÃO")
    print("=" * 50)
    
    # Limpar cache primeiro
    clear_mining_cache(models)
    
    # Buscar usuários antes
    print("👥 Buscando usuários...")
    usuarios_antes = supabase_client.get_ranking_usuarios(5)
    if not usuarios_antes:
        print("❌ Nenhum usuário encontrado")
        return False
    
    print(f"📊 {len(usuarios_antes)} usuários encontrados")
    
    # Mostrar dados antes
    print("\n💰 SALDOS ANTES:")
    saldos_antes = {}
    for usuario in usuarios_antes:
        uid = usuario['uid']
        nick = usuario.get('nick', 'Unknown')
        dinheiro = usuario.get('dinheiro', 0)
        nivel = usuario.get('nivel_mineradora', 1)
        saldos_antes[uid] = dinheiro
        
        # Calcular dinheiro esperado com nova fórmula
        dinheiro_esperado = 15 + (nivel - 1) * 20 + (nivel - 1) * 5
        
        print(f"   {nick} (Nível {nivel}): ${dinheiro} (deveria gerar ${dinheiro_esperado}/min)")
    
    # Executar mineração
    print("\n⚡ EXECUTANDO MINERAÇÃO...")
    print("-" * 30)
    
    try:
        resultado = models.processar_mineracao_automatica_dinheiro()
        print("-" * 30)
        print(f"📊 RESULTADO DA MINERAÇÃO:")
        print(f"   Sucesso: {resultado.get('sucesso', False)}")
        print(f"   Jogadores processados: {resultado.get('jogadores_processados', 0)}")
        print(f"   Total gerado: ${resultado.get('total_dinheiro_gerado', 0)}")
        print(f"   Mensagem: {resultado.get('mensagem', 'N/A')}")
        
        if not resultado.get('sucesso'):
            print("❌ Mineração falhou!")
            return False
        
        if resultado.get('jogadores_processados', 0) == 0:
            print("⚠️ Nenhum jogador foi processado!")
            return False
            
    except Exception as e:
        print(f"❌ Erro na mineração: {e}")
        return False
    
    # Verificar resultados
    print("\n🔍 VERIFICANDO RESULTADOS...")
    usuarios_depois = supabase_client.get_ranking_usuarios(5)
    
    print("\n💰 SALDOS DEPOIS:")
    mudancas_detectadas = 0
    total_diferenca = 0
    
    for usuario in usuarios_depois:
        uid = usuario['uid']
        nick = usuario.get('nick', 'Unknown')
        dinheiro_depois = usuario.get('dinheiro', 0)
        dinheiro_antes = saldos_antes.get(uid, 0)
        diferenca = dinheiro_depois - dinheiro_antes
        
        if diferenca > 0:
            print(f"   {nick}: ${dinheiro_antes} → ${dinheiro_depois} (+${diferenca}) ✅")
            mudancas_detectadas += 1
            total_diferenca += diferenca
        else:
            print(f"   {nick}: ${dinheiro_depois} (sem mudança)")
    
    print(f"\n📈 RESUMO:")
    print(f"   Usuários com mudança: {mudancas_detectadas}")
    print(f"   Total incrementado: ${total_diferenca}")
    print(f"   Processados reportados: {resultado.get('jogadores_processados', 0)}")
    print(f"   Total gerado reportado: ${resultado.get('total_dinheiro_gerado', 0)}")
    
    # Verificar consistência
    if mudancas_detectadas > 0 and total_diferenca > 0:
        print("✅ MINERAÇÃO FUNCIONANDO CORRETAMENTE!")
        
        # Verificar se os valores batem
        if total_diferenca == resultado.get('total_dinheiro_gerado', 0):
            print("✅ Valores consistentes entre banco e relatório!")
        else:
            print("⚠️ Inconsistência entre valores do banco e relatório")
        
        return True
    else:
        print("❌ MINERAÇÃO NÃO ESTÁ FUNCIONANDO!")
        return False

def test_level_1_generation(models, supabase_client):
    """Testa especificamente se nível 1 gera dinheiro"""
    print("\n🎯 TESTE ESPECÍFICO: NÍVEL 1 GERA DINHEIRO")
    print("=" * 45)
    
    # Buscar usuário com nível 1
    usuarios = supabase_client.get_ranking_usuarios(10)
    usuario_nivel_1 = None
    
    for usuario in usuarios:
        if usuario.get('nivel_mineradora', 1) == 1:
            usuario_nivel_1 = usuario
            break
    
    if not usuario_nivel_1:
        print("⚠️ Nenhum usuário com nível 1 encontrado")
        return False
    
    uid = usuario_nivel_1['uid']
    nick = usuario_nivel_1.get('nick', 'Unknown')
    dinheiro_antes = usuario_nivel_1.get('dinheiro', 0)
    
    print(f"👤 Testando com: {nick}")
    print(f"💰 Saldo antes: ${dinheiro_antes}")
    print(f"⛏️ Nível mineradora: 1")
    print(f"📊 Deveria gerar: $15/min (nova fórmula)")
    
    # Limpar cache para este usuário
    if uid in models.MINERACAO_AUTOMATICA_CACHE:
        del models.MINERACAO_AUTOMATICA_CACHE[uid]
        print("🧹 Cache limpo para este usuário")
    
    # Executar mineração
    print("\n⚡ Executando mineração...")
    resultado = models.processar_mineracao_automatica_dinheiro()
    
    # Verificar resultado
    usuario_atualizado = supabase_client.get_user_by_uid(uid)
    if usuario_atualizado:
        dinheiro_depois = usuario_atualizado.get('dinheiro', 0)
        diferenca = dinheiro_depois - dinheiro_antes
        
        print(f"💰 Saldo depois: ${dinheiro_depois}")
        print(f"📈 Diferença: +${diferenca}")
        
        if diferenca >= 15:  # Deveria gerar pelo menos $15
            print("✅ NÍVEL 1 ESTÁ GERANDO DINHEIRO CORRETAMENTE!")
            return True
        elif diferenca > 0:
            print(f"⚠️ Nível 1 gerou apenas ${diferenca}, esperado $15+")
            return False
        else:
            print("❌ NÍVEL 1 NÃO GEROU DINHEIRO!")
            return False
    else:
        print("❌ Erro ao buscar usuário atualizado")
        return False

def main():
    """Função principal de teste"""
    print("🧪 TESTE COMPLETO DA MINERAÇÃO AUTOMÁTICA CORRIGIDA")
    print("=" * 55)
    
    # Teste 1: Importações
    supabase_client, models = test_imports()
    if not supabase_client or not models:
        print("\n❌ FALHA: Problemas nas importações")
        return
    
    # Teste 2: Conexão
    if not supabase_client.is_connected():
        print("\n❌ FALHA: Problema na conexão com banco")
        return
    
    print("✅ Conexão com banco OK")
    
    # Teste 3: Mineração geral
    if not test_mining_with_detailed_logs(models, supabase_client):
        print("\n❌ FALHA: Mineração geral não funcionou")
        return
    
    # Teste 4: Nível 1 específico
    if not test_level_1_generation(models, supabase_client):
        print("\n❌ FALHA: Nível 1 não está gerando dinheiro")
        return
    
    print("\n🎉 TODOS OS TESTES PASSARAM!")
    print("✅ A mineração automática está funcionando corretamente!")
    
    print("\n📋 RESUMO DAS CORREÇÕES:")
    print("1. ✅ Função de mineração corrigida")
    print("2. ✅ Nível 1 gera dinheiro ($15/min)")
    print("3. ✅ Valores mais atrativos implementados")
    print("4. ✅ Debugging melhorado")
    print("5. ✅ Persistência no banco funcionando")
    
    print("\n🚀 PRÓXIMOS PASSOS:")
    print("1. Iniciar servidor: python app.py")
    print("2. Mineração rodará automaticamente a cada minuto")
    print("3. Verificar logs no console do servidor")
    print("4. Observar incremento automático dos saldos")

if __name__ == "__main__":
    main()
