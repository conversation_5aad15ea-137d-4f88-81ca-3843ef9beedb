#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import game.new_models as models

def limpar_cache_e_testar():
    """Limpa o cache e testa a mineração"""
    print("🧹 LIMPANDO CACHE E TESTANDO MINERAÇÃO")
    print("=" * 50)
    
    # 1. Verificar cache atual
    print("1. Cache antes da limpeza:")
    try:
        cache = models.MINERACAO_AUTOMATICA_CACHE
        print(f"   Entradas no cache: {len(cache)}")
        if cache:
            print("   Primeiras 5 entradas:")
            for i, (uid, timestamp) in enumerate(list(cache.items())[:5]):
                print(f"     {i+1}. {uid}: {timestamp}")
    except Exception as e:
        print(f"   Erro ao acessar cache: {e}")
    
    # 2. Limpar cache
    print("\n2. Limpando cache...")
    try:
        models.MINERACAO_AUTOMATICA_CACHE.clear()
        print("   ✅ Cache limpo!")
    except Exception as e:
        print(f"   ❌ Erro ao limpar cache: {e}")
    
    # 3. Verificar cache após limpeza
    print("\n3. Cache após limpeza:")
    try:
        cache = models.MINERACAO_AUTOMATICA_CACHE
        print(f"   Entradas no cache: {len(cache)}")
    except Exception as e:
        print(f"   Erro ao acessar cache: {e}")
    
    # 4. Executar mineração
    print("\n4. Executando mineração...")
    try:
        resultado = models.processar_mineracao_automatica_dinheiro()
        print(f"   Resultado: {resultado}")
        
        sucesso = resultado.get('sucesso', False)
        processados = resultado.get('jogadores_processados', 0)
        total_gerado = resultado.get('total_dinheiro_gerado', 0)
        mensagem = resultado.get('mensagem', 'N/A')
        
        print(f"\n   📊 RESUMO:")
        print(f"   Sucesso: {sucesso}")
        print(f"   Processados: {processados}")
        print(f"   Total gerado: ${total_gerado}")
        print(f"   Mensagem: {mensagem}")
        
        if processados > 0:
            print("\n🎉 FUNCIONOU APÓS LIMPAR CACHE!")
        else:
            print("\n❌ Ainda não processou nenhum jogador!")
            
    except Exception as e:
        print(f"   ❌ Erro: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    limpar_cache_e_testar()
