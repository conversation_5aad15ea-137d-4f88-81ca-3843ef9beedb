<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🏆 Teste Final - Ranking Individual de Deface</title>
    <style>
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            margin: 0; 
            padding: 20px; 
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%); 
            color: white; 
            min-height: 100vh;
        }
        .container { 
            max-width: 1200px; 
            margin: 0 auto; 
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(33, 150, 243, 0.3);
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }
        .status {
            background: #2a2a2a;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #4CAF50;
        }
        .ranking-container {
            background: #2a2a2a;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        }
        .ranking-item { 
            background: linear-gradient(135deg, #333 0%, #404040 100%); 
            margin: 15px 0; 
            padding: 20px; 
            border-radius: 12px; 
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: all 0.3s ease;
            border-left: 4px solid #2196F3;
        }
        .ranking-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(33, 150, 243, 0.2);
        }
        .ranking-item.first {
            border-left-color: #FFD700;
            background: linear-gradient(135deg, #4a3c00 0%, #5a4500 100%);
        }
        .ranking-item.second {
            border-left-color: #C0C0C0;
            background: linear-gradient(135deg, #3a3a3a 0%, #4a4a4a 100%);
        }
        .ranking-item.third {
            border-left-color: #CD7F32;
            background: linear-gradient(135deg, #3a2a1a 0%, #4a3520 100%);
        }
        .position { 
            font-weight: bold; 
            font-size: 28px;
            width: 60px;
            text-align: center;
        }
        .position.first { color: #FFD700; }
        .position.second { color: #C0C0C0; }
        .position.third { color: #CD7F32; }
        .position.normal { color: #2196F3; }
        .player-info {
            flex: 1;
            margin-left: 20px;
        }
        .nick { 
            font-size: 22px; 
            font-weight: bold; 
            color: #fff;
            margin-bottom: 5px;
        }
        .group { 
            color: #9C27B0; 
            font-size: 16px;
            opacity: 0.9;
        }
        .points { 
            color: #2196F3; 
            font-size: 28px; 
            font-weight: bold; 
            text-align: right;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
        }
        .points.first { color: #FFD700; }
        .points.second { color: #C0C0C0; }
        .points.third { color: #CD7F32; }
        .error { 
            color: #f44336; 
            background: linear-gradient(135deg, #4a1a1a 0%, #5a2020 100%);
            padding: 20px;
            border-radius: 10px;
            margin: 10px 0;
            border-left: 5px solid #f44336;
        }
        .success { 
            color: #4CAF50; 
            background: linear-gradient(135deg, #1a4a1a 0%, #205a20 100%);
            padding: 20px;
            border-radius: 10px;
            margin: 10px 0;
            border-left: 5px solid #4CAF50;
        }
        button { 
            background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%); 
            color: white; 
            border: none; 
            padding: 15px 30px; 
            border-radius: 8px; 
            cursor: pointer; 
            margin: 10px 5px; 
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(33, 150, 243, 0.3);
        }
        button:hover { 
            background: linear-gradient(135deg, #1976D2 0%, #1565C0 100%);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(33, 150, 243, 0.4);
        }
        .loading {
            text-align: center;
            padding: 40px;
            font-size: 18px;
            color: #888;
        }
        .crown { font-size: 24px; margin-right: 10px; }
        .medal { font-size: 20px; margin-right: 8px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏆 Ranking Individual de Deface - Teste Final</h1>
            <p>Verificação completa do sistema de ranking</p>
        </div>
        
        <div>
            <button onclick="testCompleteRanking()">🚀 Testar Ranking Completo</button>
            <button onclick="clearAll()">🧹 Limpar Tudo</button>
        </div>
        
        <div id="status" class="status" style="display: none;"></div>
        <div id="ranking-container" class="ranking-container" style="display: none;"></div>
    </div>

    <script>
        async function testCompleteRanking() {
            const statusDiv = document.getElementById('status');
            const containerDiv = document.getElementById('ranking-container');
            
            statusDiv.style.display = 'block';
            containerDiv.style.display = 'none';
            statusDiv.innerHTML = '<div class="loading">🔄 Iniciando teste completo...</div>';
            
            try {
                // 1. Login
                statusDiv.innerHTML = '<div class="loading">🔑 Fazendo login...</div>';
                const loginResponse = await fetch('/api/auth/auto-login');
                const loginData = await loginResponse.json();
                
                if (!loginResponse.ok) {
                    throw new Error(`Login falhou: ${loginData.mensagem}`);
                }
                
                // 2. Buscar ranking
                statusDiv.innerHTML = '<div class="loading">📊 Buscando dados do ranking...</div>';
                const timestamp = Date.now();
                const rankingResponse = await fetch(`/api/ranking/deface-individual?t=${timestamp}`);
                const rankingData = await rankingResponse.json();
                
                if (!rankingResponse.ok || !rankingData.sucesso) {
                    throw new Error(`API falhou: ${rankingData.mensagem || 'Erro desconhecido'}`);
                }
                
                // 3. Renderizar ranking
                statusDiv.innerHTML = `<div class="success">✅ Dados carregados com sucesso! ${rankingData.ranking?.length || 0} jogadores encontrados</div>`;
                
                if (rankingData.ranking && rankingData.ranking.length > 0) {
                    renderRanking(rankingData);
                } else {
                    containerDiv.innerHTML = `
                        <div class="error">
                            <h3>❌ Nenhum jogador encontrado</h3>
                            <p>O ranking está vazio. Execute alguns defaces no jogo para gerar dados.</p>
                        </div>
                    `;
                    containerDiv.style.display = 'block';
                }
                
            } catch (error) {
                statusDiv.innerHTML = `<div class="error">❌ ERRO: ${error.message}</div>`;
                console.error('Erro no teste:', error);
            }
        }
        
        function renderRanking(data) {
            const container = document.getElementById('ranking-container');
            
            const rankingHTML = data.ranking.map((jogador, index) => {
                const posicao = index + 1;
                let positionClass = 'normal';
                let itemClass = '';
                let icon = '';
                
                if (posicao === 1) {
                    positionClass = 'first';
                    itemClass = 'first';
                    icon = '<span class="crown">👑</span>';
                } else if (posicao === 2) {
                    positionClass = 'second';
                    itemClass = 'second';
                    icon = '<span class="medal">🥈</span>';
                } else if (posicao === 3) {
                    positionClass = 'third';
                    itemClass = 'third';
                    icon = '<span class="medal">🥉</span>';
                }
                
                return `
                    <div class="ranking-item ${itemClass}">
                        <div class="position ${positionClass}">
                            ${icon}#${posicao}
                        </div>
                        <div class="player-info">
                            <div class="nick">${jogador.nick || 'Sem nick'}</div>
                            <div class="group">Grupo: ${jogador.grupo_nome || 'Sem grupo'}</div>
                        </div>
                        <div class="points ${positionClass}">
                            ${jogador.deface_points || 0} pts
                        </div>
                    </div>
                `;
            }).join('');
            
            container.innerHTML = `
                <h3 style="text-align: center; margin-bottom: 25px; font-size: 24px;">
                    🏆 Ranking Individual de Deface (${data.ranking.length} jogadores)
                </h3>
                ${rankingHTML}
            `;
            
            container.style.display = 'block';
        }
        
        function clearAll() {
            document.getElementById('status').style.display = 'none';
            document.getElementById('ranking-container').style.display = 'none';
        }
        
        // Auto-executar ao carregar
        window.onload = function() {
            console.log('🌐 Página carregada, pronto para testar!');
        };
    </script>
</body>
</html>
