#!/usr/bin/env python3
"""
Servidor simples para testar apenas as APIs de cores de nickname
"""

from flask import Flask, jsonify, request
import sys
import os

# Adicionar o diretório atual ao path
sys.path.append('.')

app = Flask(__name__)

@app.route("/")
def home():
    return "Servidor de teste para cores de nickname funcionando!"

@app.route("/api/configuracoes/cores-nickname", methods=["GET"])
def api_get_nickname_colors():
    """API para buscar cores de nickname disponíveis para supporters"""
    try:
        # Simular dados de um supporter com SPoints
        cores_disponiveis = [
            {
                "id": "nick_color_red",
                "nome": "Vermelho",
                "descricao": "Cor vermelha vibrante",
                "preco": 25,
                "color": "#FF4444",
                "disponivel": True
            },
            {
                "id": "nick_color_blue", 
                "nome": "Azul",
                "descricao": "Cor azul elegante",
                "preco": 25,
                "color": "#4A90E2",
                "disponivel": True
            },
            {
                "id": "nick_color_green",
                "nome": "Verde",
                "descricao": "Cor verde natural",
                "preco": 25,
                "color": "#4CAF50",
                "disponivel": True
            },
            {
                "id": "nick_color_purple",
                "nome": "Roxo",
                "descricao": "Cor roxa misteriosa",
                "preco": 30,
                "color": "#9C27B0",
                "disponivel": True
            },
            {
                "id": "nick_color_orange",
                "nome": "Laranja",
                "descricao": "Cor laranja energética",
                "preco": 25,
                "color": "#FF9800",
                "disponivel": True
            },
            {
                "id": "nick_color_pink",
                "nome": "Rosa",
                "descricao": "Cor rosa delicada",
                "preco": 30,
                "color": "#E91E63",
                "disponivel": True
            },
            {
                "id": "nick_color_gold",
                "nome": "Dourado",
                "descricao": "Cor dourada premium",
                "preco": 50,
                "color": "#FFD700",
                "disponivel": True
            },
            {
                "id": "nick_color_cyan",
                "nome": "Ciano",
                "descricao": "Cor ciano futurística",
                "preco": 35,
                "color": "#00BCD4",
                "disponivel": True
            }
        ]

        return jsonify({
            "sucesso": True,
            "cores": cores_disponiveis,
            "is_supporter": True,  # Simular supporter
            "spoints_disponiveis": 1000,  # Simular SPoints
            "cor_atual": None  # Nenhuma cor aplicada
        })

    except Exception as e:
        return jsonify({"sucesso": False, "mensagem": f"Erro interno: {str(e)}"}), 500

@app.route("/api/configuracoes/cores-nickname/aplicar", methods=["POST"])
def api_aplicar_cor_nickname():
    """API para aplicar cor de nickname"""
    try:
        data = request.get_json()
        color_id = data.get('color_id')

        if not color_id:
            return jsonify({"sucesso": False, "mensagem": "ID da cor é obrigatório."}), 400

        # Simular aplicação bem-sucedida
        cores_info = {
            "nick_color_red": {"nome": "Vermelho", "color": "#FF4444", "preco": 25},
            "nick_color_blue": {"nome": "Azul", "color": "#4A90E2", "preco": 25},
            "nick_color_green": {"nome": "Verde", "color": "#4CAF50", "preco": 25},
            "nick_color_purple": {"nome": "Roxo", "color": "#9C27B0", "preco": 30},
            "nick_color_orange": {"nome": "Laranja", "color": "#FF9800", "preco": 25},
            "nick_color_pink": {"nome": "Rosa", "color": "#E91E63", "preco": 30},
            "nick_color_gold": {"nome": "Dourado", "color": "#FFD700", "preco": 50},
            "nick_color_cyan": {"nome": "Ciano", "color": "#00BCD4", "preco": 35}
        }

        if color_id not in cores_info:
            return jsonify({"sucesso": False, "mensagem": "Cor não encontrada"}), 400

        cor_info = cores_info[color_id]
        
        return jsonify({
            "sucesso": True,
            "mensagem": f"Cor {cor_info['nome']} aplicada com sucesso!",
            "cor_aplicada": cor_info["color"],
            "spoints_restantes": 1000 - cor_info["preco"],
            "preco_pago": cor_info["preco"]
        })

    except Exception as e:
        return jsonify({"sucesso": False, "mensagem": f"Erro interno: {str(e)}"}), 500

@app.route("/api/configuracoes/cores-nickname/remover", methods=["POST"])
def api_remover_cor_nickname():
    """API para remover cor de nickname"""
    try:
        return jsonify({
            "sucesso": True,
            "mensagem": "Cor do nickname removida com sucesso!"
        })

    except Exception as e:
        return jsonify({"sucesso": False, "mensagem": f"Erro interno: {str(e)}"}), 500

if __name__ == "__main__":
    print("🎨 Iniciando servidor de teste para cores de nickname...")
    print("📍 Acesse: http://localhost:5001")
    print("🔗 API de cores: http://localhost:5001/api/configuracoes/cores-nickname")
    
    try:
        app.run(debug=True, host="0.0.0.0", port=5001, threaded=True)
    except Exception as e:
        print(f"❌ Erro ao iniciar servidor: {e}")
